package com.jy.mapper;

import com.jy.bean.po.TableIndex;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * @Author: caolt
 * @Description:
 * @Version:
 * @Date: Created in  2020/02/05
 */
@Mapper
public interface OracleMapper {


    @Update("create table ${tableName} as select * from ${baseTableName} where 1=2 ")
    int createTable(@Param("tableName") String tableName, @Param("baseTableName") String baseTableName);

    @Update("create index ${indexName} on ${tableName}( ${columnName} ) ")
    int createTableIndex(@Param("tableName") String tableName, @Param("indexName") String indexName, @Param("columnName") String columnName);

    @Update("alter table ${tableName} add primary key (${columnName})")
    int createTablePrimary(@Param("columnName") String columnName, @Param("tableName") String tableName);

    @Select("select t.index_name, t.table_name, wm_concat(t.column_name) as column_name,i.uniqueness from user_ind_columns t,user_indexes i where t.index_name = i.index_name and t.table_name = '${baseTableName}' group by t.index_name, t.table_name,i.uniqueness")
    List<TableIndex> selectByTable(@Param("baseTableName") String baseTableName);

    @Select("grant all on ${tableName} to  ${baseDBName}")
    List<TableIndex> grantTable(@Param("tableName") String tableName, @Param("baseDBName") String baseDBName);

    @Select("CREATE OR REPLACE SYNONYM ${baseDBName}.${tableName} FOR ${tableName}")
    List<TableIndex> synonymTable(@Param("tableName") String tableName, @Param("baseDBName") String baseDBName);

}

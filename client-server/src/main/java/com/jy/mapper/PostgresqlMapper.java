package com.jy.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * @Author: caolt
 * @Description:
 * @Version:
 * @Date: Created in  2020/02/05
 */
@Mapper
public interface PostgresqlMapper {

    @Update("create table ${tableName}(like ${baseTableName} including all) ")
    int createTable(@Param("tableName") String tableName, @Param("baseTableName") String baseTableName);
}

package com.jy.mapper;

import com.jy.bean.dto.BaseDataDTO;
import com.jy.bean.dto.CompareDataLayerDTO;
import com.jy.bean.po.Test;
import com.jy.util.SqlMapperProvider;
import org.apache.ibatis.annotations.*;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/4/16
 */
@Mapper
public interface SqlMapper {

    @InsertProvider(type = SqlMapperProvider.class, method = "insert")
    int insert(BaseDataDTO baseDataDTO);

    @DeleteProvider(type = SqlMapperProvider.class, method = "delete")
    int delete(BaseDataDTO baseDataDTO);

    @UpdateProvider(type = SqlMapperProvider.class, method = "update")
    int update(BaseDataDTO baseDataDTO);

    @SelectProvider(type = SqlMapperProvider.class, method = "getOne")
    List<Map<String, Object>> getOne(BaseDataDTO baseDataDTO);

    @SelectProvider(type = SqlMapperProvider.class, method = "count")
    int count(String tableName, CompareDataLayerDTO compareDataLayer);

    @SelectProvider(type = SqlMapperProvider.class, method = "getMaxVersionOId")
    BigInteger getMaxVersionOId(String tableName, CompareDataLayerDTO compareDataLayer);

    @Select("select QYID,PPID,PPBM,ID,BD_4S_PRICE,BD_4S_UPDATE_DATE from JY_PJ_QYLBJJGXXB_BD_03000000 where rownum <#{rownum} order by PPID")
    List<Test> selectByPpbm(Integer rownum);

    @Select("select QYID,PPID,PPBM,ID,BD_4S_PRICE,BD_4S_UPDATE_DATE from JY_PJ_QYLBJJGXXB_BD_03000000 where rownum < 10000 and  rownum > 12000")
    List<Test> selectByPrice(Integer rownum);

    @Select("select * from ${tableName} where del_flag = '0' and ins_code = '${clientCode}'")
    List<Map<String, Object>> listOrgAll(@Param("tableName") String tableName, @Param("clientCode") String clientCode);
}

package com.jy.compare;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2019/8/21
 */
@Component
public class CompareFactory {

    @Autowired
    private DefaultCompare defaultCompare;
    @Autowired
    private FacadeCompare facadeCompare;

    public ClientCompare create(String clientCode) {
        if(clientCode.contains("FACADE")){
            return facadeCompare;
        } else {
            return defaultCompare;
        }
    }
}

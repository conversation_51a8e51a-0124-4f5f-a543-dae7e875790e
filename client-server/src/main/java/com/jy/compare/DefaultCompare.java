package com.jy.compare;

import com.jy.bean.dto.CompareDataDTO;
import com.jy.bean.dto.CompareDataLayerDTO;
import com.jy.service.CompareService;
import com.jy.service.DataService;
import com.jy.service.impl.DataServiceImpl;
import com.jy.util.EmptyUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.math.BigInteger;
import java.net.InetAddress;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/8/21
 */
@Service
public class DefaultCompare implements ClientCompare {
    private static final Logger logger = LogManager.getLogger(DataServiceImpl.class);

    @Autowired
    private DataService dataService;
    @Autowired
    private CompareService compareService;

    @Async
    @Override
    public void compare(List<CompareDataDTO> compareDataDTOs, String compareBatchNo) {

        List<CompareDataDTO> errorList = new ArrayList<>();
        for(CompareDataDTO compareDataDTO : compareDataDTOs){
            try {
                //1、调用server接口获取表分片数据量
                List<CompareDataLayerDTO> compareDataLayerDTOs =
                        compareService.listBranchCountByTableName(compareDataDTO.getBaseTableName(), "", "");
                if(EmptyUtils.isEmpty(compareDataLayerDTOs)){
                    BigInteger maxServerVersonId = new BigInteger("0");
                    this.compareLast(compareBatchNo, compareDataDTO.getBaseTableName(), compareDataDTO.getTableName(), maxServerVersonId);
                    continue ;
                }
                for(CompareDataLayerDTO compareDataLayerDTO : compareDataLayerDTOs){
                    int count = dataService.count(compareDataDTO.getTableName(), compareDataLayerDTO);
                    if(count != compareDataLayerDTO.getCount()){
                        //此表 此分段内数据量不一致   通知server端
                        compareService.pushResendData(compareBatchNo, compareDataDTO.getBaseTableName(), compareDataLayerDTO.getMaxVersionId(), compareDataLayerDTO.getMinVersionId());
                    }
                }
                //判断客户端是否存在大于服务端的版本号
                BigInteger maxServerVersonId = compareDataLayerDTOs.get(compareDataLayerDTOs.size()-1).getMaxVersionId();
                this.compareLast(compareBatchNo, compareDataDTO.getBaseTableName(), compareDataDTO.getTableName(), maxServerVersonId);
            } catch (Exception e) {
                errorList.add(compareDataDTO);
                logger.error(compareDataDTO.getTableName() + "---------------" + e.getMessage());
            }
        }
        //通知server端可开锁
        try {
            compareService.unLock(errorList);
        } catch (Exception e) {
            logger.error("-----unlock error----------" + e.getMessage());
        }
    }

    private void compareLast(String compareBatchNo, String baseTableName, String tableName, BigInteger maxVersionId) throws Exception {
        CompareDataLayerDTO compareDataLayerDTO = new CompareDataLayerDTO();
        compareDataLayerDTO.setMinVersionId(maxVersionId);
        int count = dataService.count(tableName, compareDataLayerDTO);
        if(count > 0){
            BigInteger localMaxVersionId = dataService.getMaxVersionOId(tableName, compareDataLayerDTO);
            compareDataLayerDTO.setMaxVersionId(localMaxVersionId);
            //此表 此分段内数据量不一致   通知server端
            compareService.pushResendData(compareBatchNo, baseTableName, compareDataLayerDTO.getMaxVersionId(), compareDataLayerDTO.getMinVersionId());
        }
    }
}

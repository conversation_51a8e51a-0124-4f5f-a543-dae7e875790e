package com.jy.compare;

import com.jy.bean.dto.CompareDataDTO;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/8/21
 */
@Service
public class FacadeCompare implements ClientCompare {

    @Async
    @Override
    public void compare(List<CompareDataDTO> compareDataDTOs, String compareBatchNo) {

    }
}

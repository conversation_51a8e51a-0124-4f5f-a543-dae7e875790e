package com.jy.aspect;

import com.alibaba.fastjson.JSONObject;
import com.jy.bean.dto.BaseDataDTO;
import com.jy.service.DataService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2018/4/19
 */
@Component
@Aspect
public class OperationRecordAspect {

    @Autowired
    private DataService dataService;

    @Around("@annotation(com.jy.ann.OperationRecord)")
    public Object AroundText(ProceedingJoinPoint joinPoint) throws Throwable {
        String classType = joinPoint.getTarget().getClass().getName();
        String clazzName = Class.forName(classType).getName();
        Logger logger = LogManager.getLogger(clazzName);
        Object[] args = joinPoint.getArgs();     //方法的参数
        BaseDataDTO baseDataDTO = null;
        if(args[0] != null){
            baseDataDTO = (BaseDataDTO) args[0];
        }
        JSONObject oldData = null;
        JSONObject newData = null;

        if("update".equals(baseDataDTO.getOperate()) || "delete".equals(baseDataDTO.getOperate())){
            try {
                oldData = dataService.getOne(baseDataDTO);
            } catch (Exception e) {
                logger.error(e.getMessage());
            }
        }

        Object result = joinPoint.proceed();//方法的结果

        logger.warn("operate： {} , oldData: {} ", baseDataDTO.getOperate(), oldData);
        return result;
    }
}

package com.jy.aspect;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;


/**
 * Created by zy on 2017/11/21.
 */
@Component
@Aspect
public class MethodMonitorAspect {

    private static String[] types = { "java.lang.Integer", "java.lang.Double",
            "java.lang.Float", "java.lang.Long", "java.lang.Short",
            "java.lang.Byte", "java.lang.Boolean", "java.lang.Char",
            "java.lang.String", "int", "double", "long", "short", "byte",
            "boolean", "char", "float" };
    /**
     * 使用AOP对使用了SortDefault 的方法进行代理，设置默认参数
     * @throws Throwable
     */
    @Around("@annotation(com.jy.ann.MethodMonitor)")
    public Object methodAround(ProceedingJoinPoint joinPoint) throws Throwable  {

        String classType = joinPoint.getTarget().getClass().getName();

        Class<?> clazz = Class.forName(classType);
        String clazzName = clazz.getName();
        String methodName = joinPoint.getSignature().getName();

        long start = System.currentTimeMillis();
        Object o =  joinPoint.proceed();
        long end = System.currentTimeMillis();

        Logger logger = LogManager.getLogger(clazzName);

        logger.warn("methodName: {}, duringTime: {} ms ", methodName,  (end - start));
        return o;
    }
}

package com.jy.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: zy
 * @Date: Created in 2018/4/26
 */
@Component
public class FacadeUtils extends BaseFacadeUtils {
    @Autowired
    private Environment env;

    @Override
    @PostConstruct
    /** 项目启动时，从配置文件、或者数据库获取 */
    public void initParam() {
        try {
            Auth auth = new Auth();
            auth.setUsername(env.getProperty("httpClient.username"));
            auth.setPassword(env.getProperty("httpClient.password"));
            auth.setAuthorization(env.getProperty("httpClient.authorization"));
            authMap.put(DEFAULT_AUTH_KEY, auth);
            URL = env.getProperty("httpClient.url");
            tokenPath = env.getProperty("httpClient.tokenPath");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    /**
     * 从缓存中获取token
     * @return
     */
    public String getTokenCache(String authKey) throws Exception {
        if(authMap.get(authKey) == null){
            throw new Exception("BaseFacadeUtils 中的 authKey: " + authKey + " 的用户为空");
        }
        return authMap.get(authKey).getToken();
    }

    @Override
    /**
     * 更新缓存中的token
     * @return
     */
    public void updateTokenCache(String authKey, String token) throws Exception {
        if(authMap.get(authKey) == null){
            throw new Exception("BaseFacadeUtils 中的 authKey: " + authKey + " 的用户为空");
        }
        authMap.get(authKey).setToken(token);
    }


    public static void main(String args[]) throws Exception {
        FacadeUtils facadeUtils = new FacadeUtils();
        Auth auth = facadeUtils.new Auth();
        auth.setUsername("PICC");
        auth.setPassword("picc");
        auth.setAuthorization("YW5kcm9pZDphbmRyb2lk");
        facadeUtils.authMap.put(DEFAULT_AUTH_KEY, auth);
        facadeUtils.URL = "http://localhost:8080/";
        Map<String, String> querys = new HashMap<String, String>();
        querys.put("resend", "resend");
        Map<String, Object> bodys = new HashMap<>();
        bodys.put("clientCode", "PICC");
        bodys.put("tableName", "pj_cllbjdyb_ada0");
        bodys.put("maxVersionId", new BigInteger("2019082600005637760"));
        bodys.put("minVersionId", new BigInteger("2019082600005636760"));

        String str = HttpUtils.doPost("http://localhost:8087/", "compares", new HashMap<String, String>(), querys, JSON.toJSONString(bodys));
        try {
            // 正常调用
//            JSONObject jsonObject = facadeUtils.doPostFile("http://**************:8765/","vehicle-service/imageOCRs", querys, byt, "1.jpg");
            // 压缩调用
            JSONObject jsonObject =  facadeUtils.doGet("data?process", null);
          //  JSONObject jsonObject = facadeUtils.doGet("/clientTable", querys);
            System.out.println(jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}

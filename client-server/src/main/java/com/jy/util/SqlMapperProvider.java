package com.jy.util;

import com.jy.bean.dto.BaseDataDTO;
import com.jy.bean.dto.CompareDataLayerDTO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @date 2018/4/16
 */
@Component
public class SqlMapperProvider {
    private static final Logger logger = LogManager.getLogger(SqlMapperProvider.class);

    public String insert(BaseDataDTO baseDataDTO){
//        baseDataDTO.getFields().put("id", StringUtils.getUUID());
//        baseDataDTO.getFields().put("jlrid", "dataPublisher");
//        baseDataDTO.getFields().put("xgrid", "dataPublisher");
      //  Map<String, String> must = AnalysisBaseDataDTOUtils.filterMap(baseDataDTO.getFields(), baseDataDTO.getMust());
        if(EmptyUtils.isNotEmpty(baseDataDTO.getModify())){
            for (String key : baseDataDTO.getModify().keySet()) {
                baseDataDTO.getFields().put(baseDataDTO.getModify().get(key), "dataPublisher");
                baseDataDTO.getFields().put(key, "dataPublisher");
            }
        }
        StringBuffer sBuffer = new StringBuffer();
        sBuffer.append(baseDataDTO.getOperate()) .append(" into ");
        sBuffer.append(baseDataDTO.getTableName()) .append(" (");
        sBuffer = AnalysisBaseDataDTOUtils.analysisKey(baseDataDTO.getFields(), sBuffer);
        if(EmptyUtils.isNotEmpty(baseDataDTO.getMust())){
            sBuffer.append(", ");
            sBuffer = AnalysisBaseDataDTOUtils.analysisKey(baseDataDTO.getMust(), sBuffer);
        }
        sBuffer.append(", ");
        sBuffer = AnalysisBaseDataDTOUtils.analysisKey(baseDataDTO.getKeys(), sBuffer);
        sBuffer.append(") values (");
        sBuffer = AnalysisBaseDataDTOUtils.analysisValue(baseDataDTO.getFields(), sBuffer);
        if(EmptyUtils.isNotEmpty(baseDataDTO.getMust())){
            sBuffer.append(", ");
            sBuffer = AnalysisBaseDataDTOUtils.analysisValue(baseDataDTO.getMust(), sBuffer);
        }
        sBuffer.append(", ");
        sBuffer = AnalysisBaseDataDTOUtils.analysisValue(baseDataDTO.getKeys(), sBuffer);
        sBuffer.append(") ");
        logger.warn(String.valueOf(sBuffer));
        return String.valueOf(sBuffer);
    }

    public String update(BaseDataDTO baseDataDTO){
//        baseDataDTO.getFields().put("xgrid", "dataPublisher");
        if(EmptyUtils.isNotEmpty(baseDataDTO.getModify())){
            for (String key : baseDataDTO.getModify().keySet()) {
                baseDataDTO.getFields().put(key, "dataPublisher");
            }
        }
        StringBuffer sBuffer = new StringBuffer();
        sBuffer.append(baseDataDTO.getOperate()) .append(" ");
        sBuffer.append(baseDataDTO.getTableName()) .append(" set ");
        sBuffer = AnalysisBaseDataDTOUtils.analysisKeyValue(baseDataDTO.getFields(), sBuffer, new StringBuffer(","));
        sBuffer.append(" where ");
        sBuffer = AnalysisBaseDataDTOUtils.analysisKeyValue(baseDataDTO.getKeys(), sBuffer, new StringBuffer(" AND "));
        logger.warn(String.valueOf(sBuffer));
        return String.valueOf(sBuffer);
    }

    public String delete(BaseDataDTO baseDataDTO){
        StringBuffer sBuffer = new StringBuffer();
        sBuffer.append(baseDataDTO.getOperate()) .append(" from ");
        sBuffer.append(baseDataDTO.getTableName()).append(" where ");

        sBuffer = AnalysisBaseDataDTOUtils.analysisKeyValue(baseDataDTO.getKeys(), sBuffer, new StringBuffer(" AND "));
        logger.warn(String.valueOf(sBuffer));
        return String.valueOf(sBuffer);
    }

    public String getOne(BaseDataDTO baseDataDTO){
        StringBuffer sBuffer = new StringBuffer();
        sBuffer.append("select ");
        if("delete".equals(baseDataDTO.getOperate())){
            sBuffer.append(" * ");
        } else {
            sBuffer = AnalysisBaseDataDTOUtils.analysisKey(baseDataDTO.getFields(), sBuffer);
            sBuffer.append(",");
            sBuffer = AnalysisBaseDataDTOUtils.analysisKey(baseDataDTO.getKeys(), sBuffer);
          //  Map<String, String> must = AnalysisBaseDataDTOUtils.filterMap(baseDataDTO.getFields(), baseDataDTO.getMust());
            if(EmptyUtils.isNotEmpty(baseDataDTO.getMust())){
                sBuffer.append(", ");
                sBuffer = AnalysisBaseDataDTOUtils.analysisKey(baseDataDTO.getMust(), sBuffer);
            }
        }
        sBuffer.append(" from ").append(baseDataDTO.getTableName()).append(" where ");
        sBuffer = AnalysisBaseDataDTOUtils.analysisKeyValue(baseDataDTO.getKeys(), sBuffer, new StringBuffer(" AND "));
        //sBuffer.append(" and rownum=1");
        logger.warn(String.valueOf(sBuffer));
        return String.valueOf(sBuffer);
    }

    public String count(String tableName, CompareDataLayerDTO compareDataLayerDTO){
        StringBuffer sBuffer = new StringBuffer();
        sBuffer.append("select count(1) from ");
        sBuffer.append(tableName);
        sBuffer.append(" where ");
        sBuffer.append(" version_id > " + compareDataLayerDTO.getMinVersionId());
        if(EmptyUtils.isNotEmpty(compareDataLayerDTO.getMaxVersionId())){
            sBuffer.append(" and version_id <= " + compareDataLayerDTO.getMaxVersionId());
        }
        if(EmptyUtils.isNotEmpty(compareDataLayerDTO.getLayerValue()) && EmptyUtils.isNotEmpty(compareDataLayerDTO.getLayerKey())){
            sBuffer.append(" and " + compareDataLayerDTO.getLayerKey() + "=" + compareDataLayerDTO.getLayerValue());
        }
        logger.warn(String.valueOf(sBuffer));
        return String.valueOf(sBuffer);
    }

    public String getMaxVersionOId(String tableName, CompareDataLayerDTO compareDataLayerDTO){
        StringBuffer sBuffer = new StringBuffer();
        sBuffer.append("SELECT * FROM (SELECT maxVersionId from ( select version_id as maxVersionId,rownum from ");
        sBuffer.append(tableName);
        sBuffer.append(" where ");
        sBuffer.append(" version_id > " + compareDataLayerDTO.getMinVersionId());
        sBuffer.append(" ORDER BY VERSION_ID ASC ) ORDER BY maxVersionId desc) WHERE rownum =1");

        logger.warn(String.valueOf(sBuffer));
        return String.valueOf(sBuffer);
    }

}

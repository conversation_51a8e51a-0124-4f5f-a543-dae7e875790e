package com.jy.util;

import org.springframework.cglib.beans.BeanMap;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.util.Map;

/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2018/2/1
 */
public class Beanutils<T> {

    public static <T> T mapToObject(Map<String, Object> map, T bean) throws Exception {
        BeanMap beanMap = BeanMap.create(bean);
        beanMap.putAll(map);
        return bean;
    }


    /**
     * 数组转对象
     * @param bytes
     * @return
     */
    public static <T> T bytes2Object (byte[] bytes, T bean) {
        try {
            ByteArrayInputStream bis = new ByteArrayInputStream (bytes);
            ObjectInputStream ois = new ObjectInputStream (bis);
            bean = (T)ois.readObject();
            ois.close();
            bis.close();
        } catch (IOException ex) {
            ex.printStackTrace();
        } catch (ClassNotFoundException ex) {
            ex.printStackTrace();
        }
        return bean;
    }
}

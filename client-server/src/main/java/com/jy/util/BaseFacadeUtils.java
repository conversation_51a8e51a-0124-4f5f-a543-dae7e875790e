package com.jy.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.xerial.snappy.Snappy;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.GZIPInputStream;

/**
 * @Author: zy
 * @Description: facade接口工具
 * @Version: 1.0.0
 * @Date: Created in 2018/1/4
 */
@Slf4j
public abstract class BaseFacadeUtils {

    protected static Map<String, Auth> authMap = new HashMap<>();

    protected static String DEFAULT_AUTH_KEY = "DEFAULT_AUTH_KEY";

    protected static String URL;

    protected static String tokenPath;

    @Data
    public class Auth {
        /**用户名*/
        private String username;
        /**密码*/
        private String password;
        /**认证秘钥*/
        private String authorization;
        /**token*/
        private String token;
    }

    /**
     * 初始化参数
     * @return
     */
    public abstract void initParam();

    /**
     * 从缓存中获取token
     * @return
     */
    public abstract String getTokenCache(String authKey) throws Exception;

    /**
     * 更新缓存中的token
     * @return
     */
    public abstract void updateTokenCache(String authKey,String token) throws Exception;

    /**
     * 通过用户名密码获取token
     * @param
     * @return
     * @throws Exception
     */
    private String getTokenHttp(String authKey) throws Exception {
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", "Basic " + authMap.get(authKey).getAuthorization());
        Map<String, String> querys = new HashMap<String, String>();
        querys.put("grant_type", "password");
        querys.put("username", authMap.get(authKey).getUsername());
        querys.put("password",  authMap.get(authKey).getPassword());
        String response = HttpUtils.doPost(URL, tokenPath, headers, querys, "");
        JSONObject jsonObject = JSONObject.parseObject(response);
        String accessToken = (String) jsonObject.get("access_token");
        updateTokenCache(authKey, accessToken);
        return accessToken;
    }

    public JSONObject doPost(String path,
                             Map<String, String> querys, String body) throws Exception {
        return this.doPost(DEFAULT_AUTH_KEY, path, querys, body);
    }

    public JSONObject doPost(String authKey, String path, Map<String, String> querys, String body) throws Exception {
        Map<String, String> headers = getHeader(querys);
        String token = getTokenCache(authKey);
        headers.put("Authorization", "Bearer " + token);
        String response = HttpUtils.doPost(URL, path, headers, querys, body);
        JSONObject jsonObject = JSONObject.parseObject(response);
        if(jsonObject.containsKey("error") && "invalid_token".equals(jsonObject.get("Authorization"))){
            String access_token = getTokenHttp(authKey);
            headers.put("Authorization", "Bearer " + access_token);
            response = HttpUtils.doPost(URL, path, headers, querys, body);
            jsonObject = JSONObject.parseObject(response);
        }
        return jsonObject;
    }


    /**
     * @param path    资源路径
     * @param querys    参数
     * @return
     * @throws Exception
     */
    public JSONObject doGet(String path,
                            Map<String, String> querys) throws Exception {
        return doGet(DEFAULT_AUTH_KEY, path, getHeader(querys), querys);
    }


    public JSONObject doCompressGet(String path,
                                    Map<String, String> querys) throws Exception {
        return this.doCompressGet(DEFAULT_AUTH_KEY, path, querys);
    }
    /**
     * 压缩返回结果请求
     * @param path    资源路径
     * @param querys    参数
     * @return
     * @throws Exception
     */
    public JSONObject doCompressGet(String authKey, String path,
                                    Map<String, String> querys) throws Exception {
        Map<String, String> headers = getHeader(querys);
        headers.put("isZip", "1");
        JSONObject jsonObject = doGet(authKey, path, headers, querys);
        Object obj = jsonObject.get("result");
        if(null != obj && obj instanceof JSONObject && ((JSONObject) obj).containsKey("compressType")){
            JSONObject result = (JSONObject) obj;
            byte[] bytes = uncompress(result.getBytes("result"), result.getString("compressType"));
            jsonObject.put("result", JSON.parse(new String (bytes, "UTF-8")));
        }
        return jsonObject;
    }

    /**
     * @param path    资源路径
     * @param headers   头部信息
     * @param querys    参数
     * @return
     * @throws Exception
     */
    private JSONObject doGet(String authKey, String path, Map<String, String> headers,
                             Map<String, String> querys) throws Exception {
        String token = getTokenCache(authKey);
        headers.put("Authorization", "Bearer " + token);
        String response = HttpUtils.doGet(URL, path, headers, querys);
        JSONObject jsonObject = JSONObject.parseObject(response);
        log.debug("及时更新返回错误 {}",jsonObject.getString("error"));
        if(jsonObject.containsKey("error") && "invalid_token".equals(jsonObject.get("error"))){
            String access_token = getTokenHttp(authKey);
            headers.put("Authorization", "Bearer " + access_token);
            response = HttpUtils.doGet(URL, path, headers, querys);
            jsonObject = JSONObject.parseObject(response);
        }
        return jsonObject;
    }


    private byte[] uncompress(byte[] bytes, String compressType){
        switch (compressType) {
            case "GZip":
                return gZipUncompress(bytes);
            default:
                return snappyUncompress(bytes);
        }
    }

    /**
     * Snappy方式压缩
     * @param obj
     * @return
     */
    private byte[] snappyUncompress(byte[] obj){
        try {
            return Snappy.uncompress(obj);
        } catch (IOException e) {
            return obj;
        }
    }

    /**
     * GZip方式压缩
     * @param obj
     * @return
     */
    private byte[] gZipUncompress(byte[] obj) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream in = new ByteArrayInputStream(obj);
        try {
            GZIPInputStream ungzip = new GZIPInputStream(in);
            byte[] buffer = new byte[2048];
            int n;
            while ((n = ungzip.read(buffer)) >= 0) {
                out.write(buffer, 0, n);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return out.toByteArray();
    }

    /**
     * 组装参数至header
     * @param querys
     * @return
     */
    private Map<String, String> getHeader(Map<String, String> querys){
        Map<String, String> headers = new HashMap<String, String>();
        if(querys != null && querys.containsKey("isPage")){
            headers.put("isPage", querys.get("isPage"));
        }
        return headers;
    }
}

package com.jy.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jy.ann.MethodMonitor;
import com.jy.bean.dto.BaseDataDTO;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by zy on 2017/11/13.
 */
public class StringUtils {
    public static boolean isContainChinese(String str) {

        Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
        Matcher m = p.matcher(str);
        return m.find();
    }
    private static Pattern humpPattern = Pattern.compile("[A-Z]");

    /**驼峰转下划线*/
    @MethodMonitor
    public static String humpToLine(String str){
        if(str != null){
            Matcher matcher = humpPattern.matcher(str);
            StringBuffer sb = new StringBuffer();
            while(matcher.find()){
                matcher.appendReplacement(sb, "_"+matcher.group(0).toLowerCase());
            }
            matcher.appendTail(sb);
            return sb.toString();
        } else {
            return null;
        }
    }
    @MethodMonitor
    public static String filterNumAndLetter(String str){
        if(str != null){
            return  str.replaceAll("[^(A-Za-z0-9.%_)]", "");
        } else {
            return null;
        }
    }

    public static boolean isDate(String str){
        String datePattern = "(\\d{1,4}[-|\\/|年|\\.]\\d{1,2}[-|\\/|月|\\.]\\d{1,2}([日|号])?(\\s)*(\\d{1,2}([点|时])?((:)?\\d{1,2}(分)?((:)?\\d{1,2}(秒)?)?)?)?(\\s)*(PM|AM)?)";
        if(str != null){
            Pattern pattern = Pattern.compile(datePattern, Pattern.CASE_INSENSITIVE|Pattern.MULTILINE);
            Matcher match = pattern.matcher(str);
            return match.matches();
        } else {
            return false;
        }
    }

    //验证是否是yyyy-MM-dd HH:mm:ss时间格式的数据(区分平年和闰年)
    public static boolean isDate1(String str){
        if(str != null && !"".equals(str)){
            String datePattern = "^((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29))\\s+([0-1]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$";
            Pattern pattern = Pattern.compile(datePattern);
            Matcher match = pattern.matcher(str);
            return match.matches();
        } else {
            return false;
        }
    }

    public static String getUUID() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }

    public static void main(String[] args) {
    }

}

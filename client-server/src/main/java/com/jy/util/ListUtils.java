package com.jy.util;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2017/12/20
 */
public class ListUtils<T> {

    public static List removeDuplicate(List list) {
        List reList = new ArrayList<>();
        Set set = new HashSet();
        for(Object object : list){
            if(set.add(object))
                reList.add(object);
        }
        return reList;
    }
}

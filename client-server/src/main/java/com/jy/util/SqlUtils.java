package com.jy.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2017/12/26
 */
@Component
public class SqlUtils {

    private static String sqlUrl;
    private static String sequenceName;

    @Autowired
    private Environment env;

    @PostConstruct
    public void initParam() {
        sqlUrl = env.getProperty("spring.datasource.url");
        sequenceName = env.getProperty("maketprice.sequenceName");
    }


    public static String sqlString(String value){
        String sql = "";
        if (StringUtils.isDate1(value)) {
            String dataSourceType = getDataSource();
            switch(dataSourceType){
                case "postgresql":
                    sql = "to_timestamp('" + value + "','yyyy-mm-dd hh24:mi:ss')";
                    break;
                case "mysql":
                    sql = "'" + value + "'";
                    break;
                default:
                    sql = "to_date('" + value + "','yyyy-mm-dd hh24:mi:ss')";
            }
        } else if(EmptyUtils.isNotEmpty(sequenceName) && sequenceName.equals(value)){
            sql =  value;
        } else {
            //解决字符串中本身带有‘问题
            value = value.replaceAll("'", "''");
            sql = "'" + value + "'";
        }
        return sql;
    }

    public static String getDataSource(){
        if(sqlUrl.contains("mysql")){
            return "mysql";
        } else if(sqlUrl.contains("postgresql")){
            return "postgresql";
        } else {
            return "oracle";
        }
    }




}

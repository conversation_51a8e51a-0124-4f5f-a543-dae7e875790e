package com.jy.util;

import com.jy.ann.MethodMonitor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018/4/26
 */
public class DateUtil {
    public static Logger logger = LogManager.getLogger(DateUtil.class);
    public static String datePattern = "yyyy-MM-dd";
    public static String timePattern = "yyyy-MM-dd HH:mm:ss";

    @MethodMonitor
    public static final Date convertStringToDate(String aMask, String strDate) {
        SimpleDateFormat df =  new SimpleDateFormat(aMask);
        Date date = null;
        try {
            date = df.parse(strDate);
        } catch (ParseException pe) {
            logger.info(pe.getMessage());
        }
        return (date);
    }

    @MethodMonitor
    public static final String convertDateToString(String aMask, Date date) {
        SimpleDateFormat df =  new SimpleDateFormat(aMask);
        String dateStr = null;
        try {
            dateStr = df.format(date);
        } catch (Exception pe) {
            logger.info(pe.getMessage());
        }
        return (dateStr);
    }

    @MethodMonitor
    public static final String convertDateToString(Date aDate) {
        return convertDateToString(datePattern, aDate);
    }

    @MethodMonitor
    public static Date convertStringToDate(String strDate) {
        return convertStringToDate(datePattern, strDate);
    }

}

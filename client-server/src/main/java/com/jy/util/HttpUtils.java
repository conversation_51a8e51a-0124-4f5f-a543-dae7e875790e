package com.jy.util;


import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.NameValuePair;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.springframework.core.env.Environment;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.management.*;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.URLEncoder;
import java.net.UnknownHostException;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.*;

/**
 * Created by zy on 2017/10/24.
 */
@Component
public class HttpUtils {
    private static final Logger logger = LogManager.getLogger(HttpUtils.class);

    @Autowired
    private Environment env;

    @PostConstruct
    public void initParam() {
        this.PROXY_HOST = env.getProperty("httpClient.proxyHost");
        this.PROXY_PORT = env.getProperty("httpClient.proxyPort");
    }

    static String PROXY_HOST;
    static String PROXY_PORT;
    static String PROXY_USERNAME;
    static String PROXY_PASSWORD;
    static int MAX_CONNECTION_NUM = 50;
    static int MAX_PER_ROUTE = 50;
    private static int CONNECT_TIME_OUT = 5000;
    private static int SOCKET_TIME_OUT = 30000;
    private static Object LOCAL_LOCK = new Object();

    /**
     * 连接池管理对象
     */
   private static PoolingHttpClientConnectionManager cm = null;

    //https
    private static SSLConnectionSocketFactory socketFactory;
    private static TrustManager manager = new X509TrustManager() {

        @Override
        public void checkClientTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {

        }

        @Override
        public void checkServerTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {

        }

        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return null;
        }
    };

    private static void enableSSL() {
        try {
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, new TrustManager[]{manager}, null);
            socketFactory = new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (KeyManagementException e) {
            e.printStackTrace();
        }
    }
/*    static {
        enableSSL();
    }*/
    /**
     * get
     *
     * @param host
     * @param path
     * @param headers
     * @param querys
     * @return
     * @throws Exception
     */
    public static String doGet(String host, String path,
                                   Map<String, String> headers,
                                   Map<String, String> querys)
            throws Exception {
        RequestConfig requestConfig = getRequestConfig();
        CloseableHttpClient httpClient = getHttpsClient(requestConfig);
        HttpGet request = new HttpGet(buildUrl(host, path, querys));
        request.setConfig(requestConfig);

        for (Map.Entry<String, String> e : headers.entrySet()) {
            request.addHeader(e.getKey(), e.getValue());
        }
        CloseableHttpResponse response = httpClient.execute(request);
        return getEntity(response);
    }

    /**
     * Post String
     *
     * @param host
     * @param path
     * @param headers
     * @param querys
     * @param body
     * @return
     * @throws Exception
     */
    public static String doPost(String host, String path,
                                      Map<String, String> headers,
                                      Map<String, String> querys,
                                      String body)
            throws Exception {

        return doPost(host, path, headers, querys, body, null);
    }

    public static String doPost(String host, String path,
                                Map<String, String> headers,
                                Map<String, String> querys,
                                String body, String contentType)
            throws Exception {

        RequestConfig requestConfig = getRequestConfig();
        CloseableHttpClient httpClient = getHttpsClient(requestConfig);
        HttpPost request = new HttpPost(buildUrl(host, path, querys));
        request.setConfig(requestConfig);

        if(headers != null){
            for (Map.Entry<String, String> e : headers.entrySet()) {
                request.addHeader(e.getKey(), e.getValue());
            }
        }

        if (StringUtils.isNotBlank(body)) {
            request.setEntity(new StringEntity(body, "utf-8"));
        }
        if (StringUtils.isNotBlank(contentType)) {
            request.addHeader("Content-Type", contentType);
        } else {
            request.addHeader("Content-Type", "application/json");
        }

        CloseableHttpResponse response = httpClient.execute(request);
        return getEntity(response);
    }

    /**
     * post form
     *
     * @param host
     * @param path
     * @param headers
     * @param querys
     * @param bodys
     * @return
     * @throws Exception
     */
    public static String doPost(String host, String path,
                                Map<String, String> headers,
                                Map<String, String> querys,
                                Map<String, String> bodys)
            throws Exception {

        RequestConfig requestConfig = getRequestConfig();
        CloseableHttpClient httpClient = getHttpsClient(requestConfig);
        HttpPost request = new HttpPost(buildUrl(host, path, querys));
        request.setConfig(requestConfig);

        for (Map.Entry<String, String> e : headers.entrySet()) {
            request.addHeader(e.getKey(), e.getValue());
        }

        if (bodys != null) {
            List<NameValuePair> nameValuePairList = new ArrayList<NameValuePair>();

            for (String key : bodys.keySet()) {
                nameValuePairList.add(new BasicNameValuePair(key, bodys.get(key)));
            }
            UrlEncodedFormEntity formEntity = new UrlEncodedFormEntity(nameValuePairList, "utf-8");
            formEntity.setContentType("application/x-www-form-urlencoded; charset=UTF-8");
            request.setEntity(formEntity);
        }

        CloseableHttpResponse response = httpClient.execute(request);
        return getEntity(response);
    }
    /**
     * Put String
     * @param host
     * @param path
     * @param headers
     * @param querys
     * @param body
     * @return
     * @throws Exception
     */
    public static String doPut(String host, String path,
                               Map<String, String> headers,
                               Map<String, String> querys,
                               String body)
            throws Exception {

        RequestConfig requestConfig = getRequestConfig();
        CloseableHttpClient httpClient = getHttpsClient(requestConfig);
        HttpPut request = new HttpPut(buildUrl(host, path, querys));
        request.setConfig(requestConfig);

        for (Map.Entry<String, String> e : headers.entrySet()) {
            request.addHeader(e.getKey(), e.getValue());
        }

        if (StringUtils.isNotBlank(body)) {
            request.setEntity(new StringEntity(body, "utf-8"));
        }

        CloseableHttpResponse response = httpClient.execute(request);
        return getEntity(response);
    }

    /**
     * Delete
     *
     * @param host
     * @param path
     * @param headers
     * @param querys
     * @return
     * @throws Exception
     */
    public static String doDelete(String host, String path,
                                  Map<String, String> headers,
                                  Map<String, String> querys)
            throws Exception {

        RequestConfig requestConfig = getRequestConfig();
        CloseableHttpClient httpClient = getHttpsClient(requestConfig);
        HttpDelete request = new HttpDelete(buildUrl(host, path, querys));
        request.setConfig(requestConfig);

        for (Map.Entry<String, String> e : headers.entrySet()) {
            request.addHeader(e.getKey(), e.getValue());
        }

        CloseableHttpResponse response = httpClient.execute(request);
        return getEntity(response);
    }

    private static String buildUrl(String host, String path, Map<String, String> querys) throws UnsupportedEncodingException {
        StringBuilder sbUrl = new StringBuilder();
        sbUrl.append(host);
        if (!StringUtils.isBlank(path)) {
            sbUrl.append(path);
        }
        if (null != querys) {
            StringBuilder sbQuery = new StringBuilder();
            for (Map.Entry<String, String> query : querys.entrySet()) {
                if (0 < sbQuery.length()) {
                    sbQuery.append("&");
                }
                if (StringUtils.isBlank(query.getKey()) && !StringUtils.isBlank(query.getValue())) {
                    sbQuery.append(query.getValue());
                }
                if (!StringUtils.isBlank(query.getKey())) {
                    sbQuery.append(query.getKey());
                    if (!StringUtils.isBlank(query.getValue())) {
                        sbQuery.append("=");
                        sbQuery.append(URLEncoder.encode(query.getValue(), "utf-8"));
                    }
                }
            }
            if (sbUrl.toString().contains("?")) {
                sbUrl.append("&").append(sbQuery);
            } else {
                sbUrl.append("?").append(sbQuery);
            }
        }

        return sbUrl.toString();
    }

    public static String getEntity(CloseableHttpResponse response) throws Exception {
        String res = "";
        try {
            res = EntityUtils.toString(response.getEntity());
        } finally {
            response.close();
        }
        return res;
    }

    /**
     * 请求的iP地址
     * @param
     * @return
     */
    public static String getIpAddress(HttpServletRequest request) {

        // 获取请求主机IP地址,如果通过代理进来，则透过防火墙获取真实IP地址
        String ip = request.getHeader("X-Forwarded-For");

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("X-ClientIP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("WL-Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_CLIENT_IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_X_FORWARDED_FOR");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getRemoteAddr();
            }

        } else if (ip.length() > 15) {
            String[] ips = ip.split(",");
            for (int index = 0; index < ips.length; index++) {
                String strIp = ips[index];
                if (!("unknown".equalsIgnoreCase(strIp))) {
                    ip = strIp;
                    break;
                }
            }
        }
        return ip;
    }

    private static RequestConfig getRequestConfig(){
        RequestConfig config = null;
       if(StringUtils.isNotBlank(PROXY_HOST) && StringUtils.isNotBlank(PROXY_PORT)){
            int httpProxyPort = Integer.valueOf(PROXY_PORT);
            config = RequestConfig.custom()
                    .setProxy(new HttpHost(PROXY_HOST, httpProxyPort))
                    .setConnectTimeout(CONNECT_TIME_OUT)// connectTimeout设置服务器请求超时时间
                    .setSocketTimeout(SOCKET_TIME_OUT).build();// socketTimeout设置服务器响应超时时间
        } else {
            config = RequestConfig.custom()
                    .setConnectTimeout(CONNECT_TIME_OUT)
                    .setSocketTimeout(SOCKET_TIME_OUT).build();
        }
        return config;
    }



    /**
     *
     * 功能描述: <br>
     * 初始化连接池管理对象
     *
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    private static PoolingHttpClientConnectionManager getPoolManager() {
        final String methodName = "getPoolManager";
        if (null == cm) {
            synchronized (LOCAL_LOCK) {
                if (null == cm) {
                    enableSSL();
                   // SSLContextBuilder sslContextBuilder = new SSLContextBuilder();
                    try {
                        /*sslContextBuilder.loadTrustMaterial(null, new TrustSelfSignedStrategy());
                        SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(
                                sslContextBuilder.build());*/
                        Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory> create()
                                .register("https", socketFactory)
                                .register("http", new PlainConnectionSocketFactory())
                                .build();
                        cm = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
                        cm.setMaxTotal(MAX_CONNECTION_NUM);//最大连接数
                        cm.setDefaultMaxPerRoute(MAX_PER_ROUTE);//默认的每个路由的最大连接数
                    } catch (Exception e) {
                        logger.error(methodName, "init PoolingHttpClientConnectionManager Error" + e);
                    }

                }
            }
        }
        logger.debug("连接池状态：" + cm.getTotalStats());
        return cm;
    }


    /**
     * 创建线程安全的HttpClient
     *
     * @param config 客户端超时设置
     *
     * @return
     */
    public static CloseableHttpClient getHttpsClient(RequestConfig config) {
        CloseableHttpClient httpClient = null;
        if(StringUtils.isNotBlank(PROXY_USERNAME) && StringUtils.isNotBlank(PROXY_PASSWORD)){
            CredentialsProvider provider = new BasicCredentialsProvider();
            provider.setCredentials(new AuthScope(config.getProxy()), new UsernamePasswordCredentials(PROXY_USERNAME, PROXY_PASSWORD));
            httpClient = HttpClients.custom().setDefaultRequestConfig(config)
                    .setDefaultCredentialsProvider(provider)
                    .setConnectionManager(getPoolManager())
                    .build();
        } else {
            httpClient = HttpClients.custom().setDefaultRequestConfig(config)
                    .setConnectionManager(getPoolManager())
                    .build();
        }
        return httpClient;
    }

    /**
     * 获取本地IP地址
     * @return
     * @throws UnknownHostException
     */
    public static String getHostAddress() throws UnknownHostException {
        Enumeration<NetworkInterface> netInterfaces = null;
        try {
            netInterfaces = NetworkInterface.getNetworkInterfaces();
            while (netInterfaces.hasMoreElements()) {
                NetworkInterface ni = netInterfaces.nextElement();
                Enumeration<InetAddress> ips = ni.getInetAddresses();
                while (ips.hasMoreElements()) {
                    InetAddress ip = ips.nextElement();
                    if (ip.isSiteLocalAddress()) {
                        return ip.getHostAddress();
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return InetAddress.getLocalHost().getHostAddress();
    }


    /**
     * 获取服务端口号
     * @return 端口号
     * @throws ReflectionException
     * @throws MBeanException
     * @throws InstanceNotFoundException
     * @throws AttributeNotFoundException
     */
    public static String getServerPort(boolean secure) throws AttributeNotFoundException, InstanceNotFoundException, MBeanException, ReflectionException {
        MBeanServer mBeanServer = null;
        if (MBeanServerFactory.findMBeanServer(null).size() > 0) {
            mBeanServer = (MBeanServer)MBeanServerFactory.findMBeanServer(null).get(0);
        }

        if (mBeanServer == null) {
            logger.debug("调用findMBeanServer查询到的结果为null");
            return "";
        }

        Set<ObjectName> names = null;
        try {
            names = mBeanServer.queryNames(new ObjectName("Catalina:type=Connector,*"), null);
        } catch (Exception e) {
            return "";
        }
        Iterator<ObjectName> it = names.iterator();
        ObjectName oname = null;
        while (it.hasNext()) {
            oname = (ObjectName)it.next();
            String protocol = (String)mBeanServer.getAttribute(oname, "protocol");
            String scheme = (String)mBeanServer.getAttribute(oname, "scheme");
            Boolean secureValue = (Boolean)mBeanServer.getAttribute(oname, "secure");
            Boolean SSLEnabled = (Boolean)mBeanServer.getAttribute(oname, "SSLEnabled");
            if (SSLEnabled != null && SSLEnabled) {// tomcat6开始用SSLEnabled
                secureValue = true;// SSLEnabled=true但secure未配置的情况
                scheme = "https";
            }
            if (protocol != null && ("HTTP/1.1".equals(protocol) || protocol.contains("http"))) {
                if (secure && "https".equals(scheme) && secureValue) {
                    return ((Integer)mBeanServer.getAttribute(oname, "port")).toString();
                } else if (!secure && !"https".equals(scheme) && !secureValue) {
                    return ((Integer)mBeanServer.getAttribute(oname, "port")).toString();
                }
            }
        }
        return "";
    }

    public static void main(String[] args) throws Exception {
        Map<String, String > query = new HashMap<>();
        query.put("dataProcess","dataProcess");
     /*   CloseableHttpResponse response = null;
        System.out.println(HttpsUtils.toString(response));*/
        String str = HttpUtils.doGet("http://datapublisher.jingyougroup.com/data", "", new HashMap<String,String>(), query);
        System.out.println(str);

       /* response = HttpsUtils.doPost("http://www.baidu.com/cgi-bin/token?grant_type=client_credential&appid=wxb2ebe42765aad029&secret=720661590f720b1f501ab3f390f80d52",
                new ArrayList<NameValuePair>());
        System.out.println(HttpsUtils.toString(response));*/
    }
}

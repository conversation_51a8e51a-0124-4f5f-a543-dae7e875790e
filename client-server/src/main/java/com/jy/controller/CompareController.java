package com.jy.controller;

import com.jy.ann.MethodMonitor;
import com.jy.bean.dto.CompareDataDTO;
import com.jy.bean.result.JsonResult;
import com.jy.compare.CompareFactory;
import com.jy.service.CompareService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/8/19
 */
@RestController
@RequestMapping("/compares")
public class CompareController {

    @Value("${httpClient.username}")
    private  String username;

    @Autowired
    private CompareFactory compareFactory;

    @MethodMonitor
    @RequestMapping(params="compare", method = RequestMethod.POST)
    public JsonResult<String> compare(@RequestBody List<CompareDataDTO> compareDataDTOs, String compareBatchNo) throws Exception{
        //比对数据-重发
        compareFactory.create(username).compare(compareDataDTOs, compareBatchNo);

        JsonResult jsonResult = new JsonResult();
        jsonResult.setResult("成功");
        return jsonResult;
    }

}

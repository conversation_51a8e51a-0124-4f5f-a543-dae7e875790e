package com.jy.controller;

import com.jy.ann.MethodMonitor;
import com.jy.bean.result.JsonResult;
import com.jy.dbFitter.DbFactory;
import com.jy.service.DataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2019/8/30
 */
@RestController
@RequestMapping("/tables")
public class TableController {

    @Autowired
    private DbFactory dbFactory;

    @MethodMonitor
    @RequestMapping(params="create" , method = RequestMethod.POST)
    public JsonResult<String> createTable(String tableNames, String tableSuffix, String baseTableSuffix) throws Exception{
        String[] array = tableNames.split(",");
        for(String tableName : array){
            dbFactory.create().createTable(tableName, tableSuffix, baseTableSuffix);
        }

        JsonResult jsonResult = new JsonResult();
        jsonResult.setResult("成功");
        return jsonResult;
    }
}

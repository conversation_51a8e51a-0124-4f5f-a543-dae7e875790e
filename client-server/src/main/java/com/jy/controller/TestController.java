package com.jy.controller;

import com.jy.ann.MethodMonitor;
import com.jy.bean.result.JsonResult;
import org.springframework.web.bind.annotation.*;
import java.util.Map;

@RestController
@RequestMapping("/test")
public class TestController {

    @MethodMonitor
    @RequestMapping(value="test", method = RequestMethod.GET)
    public JsonResult<String> listClient(@RequestParam Map<String, Object> map) throws Exception{
        JsonResult jsonResult = new JsonResult();
        jsonResult.setResult("test");
        return jsonResult;
    }

}

package com.jy.bean.dto;

import lombok.Data;

import java.math.BigInteger;

/**
 * <AUTHOR>
 * @date 2019/8/19
 */
@Data
public class CompareDataLayerDTO {

    private Integer count;
    private BigInteger maxVersionId;
    private BigInteger minVersionId;
    //private Map<String, String> cmpareLayer; //维度  （groupId）
    private String layerKey;
    private String layerValue;
    private String baseTableName;

}

package com.jy.bean.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.Map;

/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2018/1/31
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BaseDataDTO {

    private String id;
    /**  insert, update, delete */
    private String operate;
    /**  批次号结束状态 */
    private String batchNo;
    private String mainBatchNo;
    private String batchNoStatus;

    private String tableName;

    private String orgCode;
    private String sqlType;
    /**  主键 */
    private Map<String, String> keys;
    /**  修改人和创建人 */
    private Map<String, String> modify;
    /**  属性信息 */
    private Map<String, String> fields;
    /**  必传属性信息 */
    private Map<String, String> must;
    private int sendTimes;

}

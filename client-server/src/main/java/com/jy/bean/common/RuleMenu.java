package com.jy.bean.common;

/**
 * <AUTHOR>
 * @date 2018/5/11
 */
public enum RuleMenu {

    ID_UUID("id", "uuid", "uuid方式生成id" ),
    ID_SEQUENCE("id","sequence", "序列函数方式生成id"),
    ID_DEFAULT("id","default", "数据库自行处理id");


    private String name;
    private String code;
    private String type;

    RuleMenu(String type, String code, String name) {
        this.code = code;
        this.name = name;
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public String getType() {
        return type;
    }}

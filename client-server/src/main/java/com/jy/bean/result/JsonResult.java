/**
 *
 * __   (__`\
 * (__`\   \\`\
 *  `\\`\   \\ \
 *    `\\`\  \\ \
 *      `\\`\#\\ \#
 *        \_ ##\_ |##
 *        (___)(___)##
 *         (0)  (0)`\##
 *          |~   ~ , \##
 *          |      |  \##
 *          |     /\   \##         __..---'''''-.._.._
 *          |     | \   `\##  _.--'                _  `.
 *          Y     |  \    `##'                     \`\  \
 *         /      |   \                             | `\ \
 *        /_...___|    \                            |   `\\
 *       /        `.    |                          /      ##
 *      |          |    |                         /      ####
 *      |          |    |                        /       ####
 *      | () ()    |     \     |          |  _.-'         ##
 *      `.        .'      `._. |______..| |-'|
 *        `------'           | | | |    | || |
 *                           | | | |    | || |
 *                           | | | |    | || |
 *                           | | | |    | || |     Jia <PERSON>ang
 *                     _____ | | | |____| || |
 *                    /     `` |-`/     ` |` |
 *                    \________\__\_______\__\
 *                     """""""""   """""""'"""
 */
package com.jy.bean.result;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jy.util.EmptyUtils;

/**
 *	2016年12月9日
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class JsonResult<T> {

    private String status;
    private String message;
    private String path;
    private T result;

    public JsonResult() {
        super();
        this.status = ResultStatus.SUCCESS.getStatus();
        this.message = ResultStatus.SUCCESS.getMessage();
    }

    public JsonResult(ResultStatus code  ) {
        super();
        setResultStatus(code);
    }
    public JsonResult(String code, String message ) {
        super();
        this.status = code ;
        this.message = message;
    }


    public void setResultStatus(ResultStatus code ){
        this.status = code.getStatus();
        this.message = code.getMessage();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getResult() {
        return result;
    }

    public void setResult(T result) {
        if(EmptyUtils.isEmpty(result)){
            setResultStatus(ResultStatus.NO_DATA);
        } else{
            this.result = result;
        }
    }

    @JsonIgnore
    public boolean isSuccess(){
        return ResultStatus.SUCCESS.getStatus().equals(this.status);
    }

    @Override
    public String toString() {
        return "JsonResult [status=" + status + ", message=" + message + ", result=" + result + "]";
    }
}

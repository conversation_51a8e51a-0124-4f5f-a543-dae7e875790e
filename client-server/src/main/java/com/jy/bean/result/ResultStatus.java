/**
 *
 * __   (__`\
 * (__`\   \\`\
 *  `\\`\   \\ \
 *    `\\`\  \\ \
 *      `\\`\#\\ \#
 *        \_ ##\_ |##
 *        (___)(___)##
 *         (0)  (0)`\##
 *          |~   ~ , \##
 *          |      |  \##
 *          |     /\   \##         __..---'''''-.._.._
 *          |     | \   `\##  _.--'                _  `.
 *          Y     |  \    `##'                     \`\  \
 *         /      |   \                             | `\ \
 *        /_...___|    \                            |   `\\
 *       /        `.    |                          /      ##
 *      |          |    |                         /      ####
 *      |          |    |                        /       ####
 *      | () ()    |     \     |          |  _.-'         ##
 *      `.        .'      `._. |______..| |-'|
 *        `------'           | | | |    | || |
 *                           | | | |    | || |
 *                           | | | |    | || |
 *                           | | | |    | || |     Jia <PERSON>ang
 *                     _____ | | | |____| || |
 *                    /     `` |-`/     ` |` |
 *                    \________\__\_______\__\
 *                     """""""""   """""""'"""
 */
package com.jy.bean.result;

/**
 *	2016年12月9日
 */
public enum ResultStatus {
    SUCCESS("200", "成功" ),  // [GET/POST/PUT/DELETE]：服务器成功返回用户请求的数据
    SUCCESS_CREATED("201", "请求成功并且服务器创建了新的资源" ), //  [POST/PUT/PATCH]：请求成功并且服务器创建了新的资源
    SUCCESS_ACCEPTED("202", "请求已经进入后台排队" ), //  [*]：表示一个请求已经进入后台排队（异步任务）
    NO_DATA("204", "服务器成功处理了请求，但没有返回任何内容" ),    //  [GET]：服务器成功处理了请求，但没有返回任何内容。
    UNAUTHORIZED("401", "令牌、用户名、密码错误" ),    //  [*]：表示用户没有权限（令牌、用户名、密码错误）
    FORBIDDEN("403", "表示用户没有权限" ),          //  [*] 表示用户得到授权（与401错误相对），但是访问是被禁止的
    INTERNAL_SERVER_ERROR("500", "服务器发生错误" );          // [*]：服务器发生错误，用户将无法判断发出的请求是否成功


    private String status;
    private String message;

    ResultStatus(String status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getStatus() {
        return status;
    }

    public String getMessage() {
        return message;
    }
}

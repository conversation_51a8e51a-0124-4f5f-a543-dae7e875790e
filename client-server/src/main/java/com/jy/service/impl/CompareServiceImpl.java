package com.jy.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jy.bean.dto.CompareDataDTO;
import com.jy.bean.dto.CompareDataLayerDTO;
import com.jy.service.AsyncService;
import com.jy.service.CompareService;
import com.jy.util.FacadeUtils;
import com.jy.util.HttpUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/8/21
 */
@Service
public class CompareServiceImpl implements CompareService {

    @Value("${server.port}")
    private  String port;
    @Value("${httpClient.username}")
    private  String username;
    @Value("${httpClient.comparePath}")
    private  String comparePath;
    @Autowired
    private AsyncService asyncService;
    @Autowired
    private FacadeUtils facadeUtils;

    @Override
    public List<CompareDataLayerDTO> listBranchCountByTableName(String baseTableName, String layerKey, String layerValue) throws Exception {
        Map<String, String> querys = new HashMap<String, String>();
        querys.put("compare", "compare");
        querys.put("baseTableName", baseTableName);
        querys.put("layerKey", layerKey);
        querys.put("layerValue", layerValue);
        JSONObject json = facadeUtils.doGet(comparePath, querys);
        if("000000".equals(json.get("code"))){
            return JSONObject.parseArray(json.getString("result"),CompareDataLayerDTO.class );
        }
        return null;
    }

    @Override
    public void pushResendData(String compareBatchNo, String baseTableName, BigInteger maxVersionId, BigInteger minVersionId) throws Exception {
        String clientUrl = "http://" + HttpUtils.getHostAddress() + ":" + port + "/";
        Map<String, String> querys = new HashMap<String, String>();
        querys.put("resend", "resend");
     //   querys.put("clientUrl", clientUrl);
     //   querys.put("clientCode", username);
        Map<String, Object> bodys = new HashMap<>();
        bodys.put("clientUrl", clientUrl);
        bodys.put("clientCode", username);
        bodys.put("baseTableName", baseTableName);
        bodys.put("maxVersionId", maxVersionId);
        bodys.put("minVersionId", minVersionId);
        bodys.put("compareBatchNo", compareBatchNo);

        asyncService.doPost(comparePath, querys, bodys);
    }

    @Override
    public JSONObject unLock(List<CompareDataDTO> list) throws Exception {
        String clientUrl = "http://" + HttpUtils.getHostAddress() + ":" + port + "/";
        Map<String, String> querys = new HashMap<String, String>();
        querys.put("unlock", "unlock");
        querys.put("clientUrl", clientUrl);
        querys.put("clientCode", username);
        JSONObject json = facadeUtils.doPost(comparePath, querys, JSON.toJSONString(list));
        return json;
    }
}

package com.jy.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jy.ann.MethodMonitor;
import com.jy.ann.OperationRecord;
import com.jy.bean.dto.BaseDataDTO;
import com.jy.bean.dto.CompareDataLayerDTO;
import com.jy.bean.po.*;
import com.jy.bean.result.ResultStatus;
import com.jy.mapper.SqlMapper;
import com.jy.service.DataService;
import com.jy.util.EmptyUtils;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2018/4/16
 */
@Service
public class DataServiceImpl implements DataService {
    private static final Logger logger = LogManager.getLogger(DataServiceImpl.class);

    @Autowired
    private SqlMapper sqlMapper;

    @Autowired
    private SqlSessionTemplate sqlSessionTemplate;

    @Override
    @MethodMonitor
    @OperationRecord
    public int add(BaseDataDTO baseDataDTO)  throws Exception{
       return sqlMapper.insert(baseDataDTO);
    }
    @Transactional(rollbackFor = Exception.class)
    @MethodMonitor
    public List<Map<String, Object>> doBatch(List<BaseDataDTO> baseDataDTOs) throws Exception {
        List<Map<String, Object>> results = new ArrayList<>(baseDataDTOs.size());
        SqlSession batchSqlSession  = this.sqlSessionTemplate
                .getSqlSessionFactory()
                .openSession(ExecutorType.BATCH, false);// 获取批量方式的sqlsession
        //通过新的session获取mapper
        SqlMapper mapper = batchSqlSession.getMapper(SqlMapper.class);
        try {
            for (BaseDataDTO baseDataDTO : baseDataDTOs) {
                String status = ResultStatus.SUCCESS.getStatus();
                String message = ResultStatus.SUCCESS.getMessage();
                try{
                    if(EmptyUtils.isNotEmpty(baseDataDTO.getFields())){
                        if("insert".equals(baseDataDTO.getOperate())){
                            mapper.insert(baseDataDTO);
                        } else if("update".equals(baseDataDTO.getOperate())) {
                            mapper.update(baseDataDTO);
                        }
                    }
                    if("delete".equals(baseDataDTO.getOperate())) {
                        mapper.delete(baseDataDTO);
                    }
                }catch (Exception e){
                    logger.error(e.getMessage());
                    status = ResultStatus.INTERNAL_SERVER_ERROR.getStatus();
                    message = e.getMessage();
                }
                Map<String, Object> result = new HashMap<>();
                result.put("id", baseDataDTO.getId());
                result.put("sendTimes", baseDataDTO.getSendTimes());
                result.put("batchNo", baseDataDTO.getBatchNo());
                result.put("mainBatchNo", baseDataDTO.getMainBatchNo());
                result.put("status", status);
                result.put("message", message);
                if(EmptyUtils.isNotEmpty(baseDataDTO.getBatchNoStatus())){
                    result.put("batchNoStatus", baseDataDTO.getBatchNoStatus());
                }
                results.add(result);
            }
            batchSqlSession.commit();
            batchSqlSession.clearCache();
        } catch (Exception e) {
            batchSqlSession.rollback();
            throw e;
        } finally{
            batchSqlSession.close();
        }
        return results;
    }

    @Override
    @MethodMonitor
    @OperationRecord
    public int delete(BaseDataDTO baseDataDTO)  throws Exception{
        return sqlMapper.delete(baseDataDTO);
    }

    @Override
    @MethodMonitor
    @OperationRecord
    public int update(BaseDataDTO baseDataDTO)  throws Exception{
        return sqlMapper.update(baseDataDTO);
    }

    @Override
    @MethodMonitor
    public JSONObject getOne(BaseDataDTO baseDataDTO)  throws Exception{
        List<Map<String, Object> > list = sqlMapper.getOne(baseDataDTO);
        if(EmptyUtils.isNotEmpty(list)){
            String json = JSON.toJSONString(list.get(0));
            return JSON.parseObject(json);
        }
        return null;
    }

    @Override
    @MethodMonitor
    public int count(String tableName, CompareDataLayerDTO compareDataLayerDTO) throws Exception {
        return sqlMapper.count(tableName, compareDataLayerDTO);
    }

    @Override
    @MethodMonitor
    public BigInteger getMaxVersionOId(String tableName, CompareDataLayerDTO compareDataLayerDTO) throws Exception {
        return sqlMapper.getMaxVersionOId(tableName, compareDataLayerDTO);
    }

    @Override
    @MethodMonitor
    public boolean isExisted(BaseDataDTO baseDataDTO)  throws Exception{
        return EmptyUtils.isNotEmpty(sqlMapper.getOne(baseDataDTO));
    }

    @Override
    @MethodMonitor
    public List<Test> selectByPpbm(Integer rownum)  throws Exception{
        return sqlMapper.selectByPpbm(rownum);
    }

    @Override
    @MethodMonitor
    public List<Test> selectByPrice(Integer rownum) throws Exception {
        return sqlMapper.selectByPrice(rownum);
    }

    @Override
    @MethodMonitor
    public Map<String, Map<String, Object>> mapOrgAll(String tableName, String clientCode) throws Exception {
        List<Map<String, Object>> orgList = sqlMapper.listOrgAll(tableName, clientCode.toUpperCase());
        Map<String, Map<String, Object>> orgMap = new HashMap(orgList.size());
        for(Map<String, Object> org : orgList){
            Map<String, Object> temp = new HashMap<>();
            for (Map.Entry<String, Object> entry : org.entrySet()) {
                temp.put(entry.getKey().toLowerCase(), entry.getValue());
            }
            orgMap.put(String.valueOf(temp.get("code")), temp);
        }
        return orgMap;
    }
}

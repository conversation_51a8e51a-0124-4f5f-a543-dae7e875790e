package com.jy.service;

import com.alibaba.fastjson.JSONObject;
import com.jy.bean.dto.CompareDataDTO;
import com.jy.bean.dto.CompareDataLayerDTO;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/8/21
 */
public interface CompareService {

    List<CompareDataLayerDTO> listBranchCountByTableName(String baseTableName, String layerKey, String layerValue) throws Exception;

    void pushResendData(String compareBatchNo, String baseTableName, BigInteger maxVersionId, BigInteger minVersionId) throws Exception;

    JSONObject unLock(List<CompareDataDTO> list) throws Exception;
}

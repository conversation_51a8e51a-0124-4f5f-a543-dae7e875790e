package com.jy.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jy.bean.dto.BaseDataDTO;
import com.jy.bean.result.ResultStatus;
import com.jy.util.EmptyUtils;
import com.jy.util.FacadeUtils;
import com.jy.util.HttpUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: zy
 * @Date: Created in 2018/4/27
 */
@Service
public class AsyncService {
    private static final Logger logger = LogManager.getLogger(AsyncService.class);

    @Value("${httpClient.callback}")
    private  String callback;
    @Value("${httpClient.username}")
    private  String username;

    @Autowired
    private DataService dataService;
    @Autowired
    private FacadeUtils facadeUtils;

    @Async
    public void batchCallBack(List<Map<String, Object>> results) throws Exception {
        //批量回调
        JSONObject json = new JSONObject();
        json.put("publishTrails", results);
        String jsonString = json.toJSONString();
        //失败重复发送  最多三次
        int i = 0;
        while(i < 3){
            JSONObject object = null;
            try {
                object = facadeUtils.doPost(callback, null, jsonString);
                logger.info("客户端回调结果:"+object);
            } catch (Exception e) {
                logger.error("客户端回调结果失败:" + e.getMessage() + ":数据为:" + jsonString);
            }
            if(EmptyUtils.isNotEmpty(object) && object.getString("status").equals(ResultStatus.SUCCESS.getStatus())){
                break;
            }
            i++;
        }
    }

    @Async
    public void logOldData(BaseDataDTO baseDataDTO){
        if("update".equals(baseDataDTO.getOperate()) || "delete".equals(baseDataDTO.getOperate())){
            try {
                JSONObject oldData = dataService.getOne(baseDataDTO);
                logger.warn("operate： {} , oldData: {} ", baseDataDTO.getOperate(), oldData);
            } catch (Exception e) {
                logger.error(e.getMessage());
            }
        } else if ("insert".equals(baseDataDTO.getOperate())){
            logger.warn("operate： {} , newData: {} ", baseDataDTO.getOperate(), baseDataDTO.getFields());
        }
    }

    @Async
    public void doPost(String path, Map<String, String> querys, Map<String, Object> bodys) throws Exception {
        int i = 0;
        while(i < 3){
            JSONObject object = null;
            try {
                object = facadeUtils.doPost(path, querys, JSON.toJSONString(bodys));
                logger.info("客户端请求补偿结果:"+object);
            } catch (Exception e) {
                logger.error("客户端请求补偿失败:" + e.getMessage() + ":数据为:" + JSON.toJSONString(bodys));
            }
            if(EmptyUtils.isNotEmpty(object) && "000000".equals(object.get("code"))){
                break;
            }
            i++;
        }

    }



}

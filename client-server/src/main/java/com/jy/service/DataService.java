package com.jy.service;

import com.alibaba.fastjson.JSONObject;
import com.jy.ann.MethodMonitor;
import com.jy.bean.dto.BaseDataDTO;
import com.jy.bean.dto.CompareDataLayerDTO;
import com.jy.bean.po.Test;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/4/16
 */
public interface DataService {
    int add(BaseDataDTO baseDataDTO) throws Exception;
    int delete(BaseDataDTO baseDataDTO) throws Exception;
    int update(BaseDataDTO baseDataDTO) throws Exception;
    JSONObject getOne(BaseDataDTO baseDataDTO) throws Exception;

    int count(String tableName, CompareDataLayerDTO compareDataLayerDTO) throws Exception;

    BigInteger getMaxVersionOId(String tableName, CompareDataLayerDTO compareDataLayerDTO) throws Exception;

    List<Map<String, Object>> doBatch(List<BaseDataDTO> baseDataDTOs) throws Exception;

    @MethodMonitor
    boolean isExisted(BaseDataDTO baseDataDTO)  throws Exception;

    List<Test> selectByPpbm(Integer rownum) throws Exception;
    List<Test> selectByPrice(Integer rownum) throws Exception;

    Map<String, Map<String, Object>> mapOrgAll(String tableName, String clientCode) throws Exception;

}

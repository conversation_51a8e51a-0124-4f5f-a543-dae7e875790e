package com.jy.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.support.http.StatViewServlet;
import com.alibaba.druid.support.http.WebStatFilter;
import com.jy.util.EmptyUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.ApplicationContextException;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.Arrays;

/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2018/1/24
 */
@Configuration
public class DruidConfiguration implements EnvironmentAware {

	private static final Logger logger = LogManager.getLogger(DruidConfiguration.class);
	private Environment environment;

	@Override
	public void setEnvironment(Environment environment) {
		this.environment = environment;
	}
	@Bean
	public ServletRegistrationBean druidServlet() {
		ServletRegistrationBean reg = new ServletRegistrationBean();
		reg.setServlet(new StatViewServlet());
		reg.addUrlMappings("/druid/*");
		reg.addInitParameter("loginUsername", environment.getProperty("spring.datasource.username"));
		reg.addInitParameter("loginPassword", environment.getProperty("spring.datasource.password"));
		return reg;
	}

	@Bean
	public FilterRegistrationBean filterRegistrationBean() {
		FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
		filterRegistrationBean.setFilter(new WebStatFilter());
		filterRegistrationBean.addUrlPatterns("/*");
		filterRegistrationBean.addInitParameter("exclusions", "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*");
		return filterRegistrationBean;
	}

	@Bean
	public DataSource druidDataSource() throws SQLException {
		if (EmptyUtils.isEmpty(environment.getProperty("spring.datasource.url"))) {
			System.out.println("Your database connection pool configuration is incorrect!"
				+ " Please check your Spring profile, current profiles are:"
				+ Arrays.toString(environment.getActiveProfiles()));
			throw new ApplicationContextException(
				"Database connection pool is not configured correctly");
		}

		DruidDataSource druidDataSource = Binder.get(environment)
			.bind("spring.datasource", DruidDataSource.class)
			.orElse(null);

		return druidDataSource;
	}
}

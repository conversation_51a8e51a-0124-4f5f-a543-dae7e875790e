package com.jy.dbFitter;

import com.jy.bean.po.TableIndex;
import com.jy.mapper.OracleMapper;
import com.jy.util.EmptyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: caolt
 * @Description:
 * @Version:
 * @Date: Created in  2020/02/05
 */
@Service
public class OracleFitter implements ClientDBFitter {

    @Value("${baseDatasource.baseDBName}")
    private  String baseDBName;

    @Autowired
    private OracleMapper oracleMapper;

    @Override
    public void createTable(String tableName, String tableSuffix, String baseTableSuffix) throws Exception {
        String baseTableName = (tableName + "_" + baseTableSuffix).toUpperCase();
        String localTableName = (tableName + "_" + tableSuffix).toUpperCase();
        oracleMapper.createTable(localTableName, baseTableName);
        List<TableIndex> tableIndices = oracleMapper.selectByTable(baseTableName);
        for(TableIndex tableIndex : tableIndices){
            if("UNIQUE".equals(tableIndex.getUniqueness())){
                System.out.println(tableIndex.getColumnName());
                oracleMapper.createTablePrimary(tableIndex.getColumnName(), localTableName);
            } else {
                String indexName = tableIndex.getIndexName().replace(baseTableSuffix, tableSuffix);
                oracleMapper.createTableIndex(localTableName, indexName, tableIndex.getColumnName());
            }
        }
        if(EmptyUtils.isNotEmpty(baseDBName)){
            oracleMapper.grantTable(localTableName, baseDBName);
            oracleMapper.synonymTable(localTableName, baseDBName);
        }

    }
}

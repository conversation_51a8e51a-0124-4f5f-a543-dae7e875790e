package com.jy.dbFitter;

import com.jy.util.SqlUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: caolt
 * @Description:
 * @Version:
 * @Date: Created in  2020/02/05
 */
@Component
public class DbFactory {

    @Autowired
    private MysqlFitter mysqlFitter;
    @Autowired
    private OracleFitter oracleFitter;
    @Autowired
    private PostgresqlFitter postgresqlFitter;

    public ClientDBFitter create() {
        String dataSourceType = SqlUtils.getDataSource();
        switch(dataSourceType){
            case "postgresql":
                return postgresqlFitter;
            case "oracle":
                return oracleFitter;
            default:
                return mysqlFitter;
        }
    }
}

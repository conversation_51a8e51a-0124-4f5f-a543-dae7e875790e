package com.jy.dbFitter;

import com.jy.mapper.PostgresqlMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: caolt
 * @Description:
 * @Version:
 * @Date: Created in  2020/02/05
 */
@Service
public class PostgresqlFitter implements ClientDBFitter {

    @Autowired
    private PostgresqlMapper postgresqlMapper;

    @Override
    public void createTable(String tableName, String tableSuffix, String baseTableSuffix) throws Exception {
        String baseTableName = (tableName + "_" + baseTableSuffix).toUpperCase();
        String localTableName = (tableName + "_" + tableSuffix).toUpperCase();
        postgresqlMapper.createTable(localTableName, baseTableName);
    }
}

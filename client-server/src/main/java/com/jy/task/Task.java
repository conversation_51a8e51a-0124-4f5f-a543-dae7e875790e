package com.jy.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jy.bean.common.RuleMenu;
import com.jy.bean.dto.BaseDataDTO;
import com.jy.bean.result.ResultStatus;
import com.jy.service.AsyncService;
import com.jy.service.DataService;
import com.jy.util.EmptyUtils;
import com.jy.util.FacadeUtils;
import com.jy.util.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.xerial.snappy.Snappy;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.zip.GZIPInputStream;


/**
 * <AUTHOR>
 * @date 2018/4/16
 */
@Component
public class Task {

    @Value("${httpClient.url}")
    private  String url;
    @Value("${httpClient.processpath}")
    private  String processpath;
    @Value("${httpClient.registerpath}")
    private  String registerpath;
    @Value("${httpClient.clientCode}")
    private  String clientCode;
    @Value("${httpClient.batchLimit}")
    private int batchLimit;
    @Value("${maketprice.idRule}")
    private  String idRule;
    @Value("${maketprice.orgTableName}")
    private  String orgTableName;
    @Value("${maketprice.sequenceName}")
    private  String sequenceName;
    @Value("${maketprice.priceFieldName}")
    private  String priceFieldName;
    @Value("${maketprice.filterField}")
    private  String filterField;
    @Value("${maketprice.replenish.key}")
    private  String replenishKey;
    @Value("${maketprice.replenish.value}")
    private  String replenishValue;

    private static final Logger logger = LogManager.getLogger(Task.class);
    @Autowired
    private DataService dataService;
    @Autowired
    private AsyncService asyncService;
    @Autowired
    private FacadeUtils facadeUtils;

    private Map<String, Map<String, Object>> orgMap;

    @Scheduled(cron="0/5 * * * * ?")
    public void timerPull() {
        try {
            int doneNum = 1;
            while(doneNum > 0){
                doneNum = this.pullBatch();
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage());
        }
    }

    /**
     * 客户端定时访问注册
     */
    @Scheduled(cron="0 0/5 * * * ?")
    public void register() {
        try{
            JSONObject object = facadeUtils.doGet(registerpath, null);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("客户端注册失败:" + e.getMessage());
        }
    }

    public int pullBatch() throws Exception {
        long start = System.currentTimeMillis();
        List<Map<String, Object>> results = new ArrayList<>(batchLimit);

        List<BaseDataDTO> temp = new ArrayList<BaseDataDTO>(batchLimit);
        int doneNum = 0;
        Map<String, String> querys = new HashMap<>();
        querys.put("batchLimit", batchLimit + "");
        JSONObject jsonObject = facadeUtils.doGet(processpath, querys);
        if(!EmptyUtils.isResultStatusSuccess(jsonObject)){
            return doneNum;
        }
        List<BaseDataDTO> baseDataDTOs = getData(jsonObject);
      //  String jsonArray = jsonObject.getString("result");
      //  baseDataDTOs = JSONObject.parseArray(jsonArray, BaseDataDTO.class);
        for(BaseDataDTO baseDataDTO: baseDataDTOs){
            if(baseDataDTO.getBatchNoStatus()!=null){
                temp.add(baseDataDTO);
                List<Map<String, Object>> response = this.doBatch(temp);
                results.addAll(response);
                temp = new ArrayList<>(batchLimit);
            } else {
                try {
                    this.fitLocalPrice(baseDataDTO);
                    temp.add(baseDataDTO);
                } catch (Exception e) {
                    Map<String, Object> result = new HashMap<>();
                    result.put("id", baseDataDTO.getId());
                    result.put("sendTimes", baseDataDTO.getSendTimes());
                    result.put("batchNo", baseDataDTO.getBatchNo());
                    result.put("mainBatchNo", baseDataDTO.getMainBatchNo());
                    result.put("status", ResultStatus.INTERNAL_SERVER_ERROR.getStatus());
                    result.put("message", e.getMessage());
                    results.add(result);
                }
            }
        }
        List<Map<String, Object>> response = this.doBatch(temp);
        results.addAll(response);
        doneNum = baseDataDTOs.size();
        if(EmptyUtils.isNotEmpty(results)){
            //回调
            asyncService.batchCallBack(results);
        }
        long end = System.currentTimeMillis();
        logger.warn("pullBatch() 执行时间： {} ms ", end -start);
        return doneNum;
    }

    private List<Map<String, Object>> doBatch(List<BaseDataDTO> baseDataDTOs){
        List<Map<String, Object>> response = new ArrayList<>();
        if(EmptyUtils.isEmpty(baseDataDTOs)){
            return response;
        }
        try {
            response = dataService.doBatch(baseDataDTOs);
        } catch (Exception e) {
            logger.error(e.getMessage());
            for (int i = 0; i < baseDataDTOs.size(); i++) {
                Map<String, Object> result = new HashMap<>();
                result.put("id", baseDataDTOs.get(i).getId());
                result.put("sendTimes", baseDataDTOs.get(i).getSendTimes());
                result.put("batchNo", baseDataDTOs.get(i).getBatchNo());
                result.put("mainBatchNo", baseDataDTOs.get(i).getMainBatchNo());
                if(EmptyUtils.isNotEmpty(baseDataDTOs.get(i).getBatchNoStatus())){
                    result.put("batchNoStatus", baseDataDTOs.get(i).getBatchNoStatus());
                }
                result.put("status", ResultStatus.INTERNAL_SERVER_ERROR.getStatus());
                result.put("message", e.getMessage());
                response.add(result);
            }
        }
        return response;
    }

    private BaseDataDTO fitLocalPrice(BaseDataDTO baseDataDTO) throws Exception {
       // if("delete".equals(baseDataDTO.getOperate())){ return baseDataDTO; }
        //分省分析价格查询
        if(EmptyUtils.isNotEmpty(baseDataDTO.getOrgCode())){
            if(EmptyUtils.isNotEmpty(replenishKey) && EmptyUtils.isNotEmpty(replenishValue)){
                baseDataDTO.getKeys().put(replenishKey, replenishValue);
            }

            baseDataDTO.getKeys().put("scbz", "0");
            if(EmptyUtils.isEmpty(orgMap) || EmptyUtils.isEmpty(orgMap.get(baseDataDTO.getOrgCode()))){
                orgMap = dataService.mapOrgAll(orgTableName, clientCode);
            }
            if(EmptyUtils.isEmpty(orgMap.get(baseDataDTO.getOrgCode()))){
                throw new Exception("orgCode: " + baseDataDTO.getOrgCode() + " 不存在对应的 qyid");
            }
            Map<String, Object> org = orgMap.get(baseDataDTO.getOrgCode());
            if(EmptyUtils.isEmpty(org.get(priceFieldName))){
                throw new Exception("orgCode: " + baseDataDTO.getOrgCode() + " 不存在对应的本地化价格表");
            }
            baseDataDTO.setTableName(org.get(priceFieldName).toString());
            if("delete".equals(baseDataDTO.getOperate())){ return baseDataDTO; }
            JSONObject oldData = dataService.getOne(baseDataDTO);
            if(EmptyUtils.isEmpty(oldData)){
                baseDataDTO.setOperate("insert");
                baseDataDTO.getFields().put("qyid", org.get("id").toString());
                if(baseDataDTO.getFields().containsKey("xgrq")){
                    baseDataDTO.getFields().put("jlrq", baseDataDTO.getFields().get("xgrq"));
                } else if(baseDataDTO.getFields().containsKey("updated_date")){
                    baseDataDTO.getFields().put("created_date", baseDataDTO.getFields().get("updated_date"));
                }


                /** 分省分析id规则idRule： default  不传id字段数据库自行处理， sequence 调用数据库函数生成  uuid 使用uuid生成id*/
                baseDataDTO.getFields().remove("id");
                if(RuleMenu.ID_SEQUENCE.getCode().equals(idRule)){
                    baseDataDTO.getFields().put("id", sequenceName);
                } else if(RuleMenu.ID_UUID.getCode().equals(idRule)){
                    baseDataDTO.getFields().put("id", StringUtils.getUUID());
                }

            } else {
                if(EmptyUtils.isNotEmpty(filterField)){
                    String[] filterFields = filterField.split(",");
                    for(String field : filterFields){
                        double oldPrice = oldData.containsKey(field.toUpperCase()) ? Double.valueOf(oldData.getString(field.toUpperCase())) : 0.0;
                        double newPrice = baseDataDTO.getFields().containsKey(field.toLowerCase()) ? Double.valueOf(baseDataDTO.getFields().get(field.toLowerCase())) : 0.0;
                        if(oldPrice > 0 && oldPrice < newPrice){
                            baseDataDTO.getFields().remove(field);
                            baseDataDTO.getFields().remove(field.substring(0, field.lastIndexOf("_") + 1) + "update_date");
                            baseDataDTO.getFields().remove(field.substring(0, field.lastIndexOf("_") + 1) + "update_type");
                        }
                    }
                }
                baseDataDTO.setOperate("update");
            }

            asyncService.logOldData(baseDataDTO);
        }

        if("delete".equals(baseDataDTO.getOperate())){ return baseDataDTO; }
        //确定是否需要查询后更新
        //  'sqlType': 'searchSql', 需要查询 在处理数据（更新|新增）
        if("searchSql".equals(baseDataDTO.getSqlType())){
            if(dataService.isExisted(baseDataDTO)){
                if(baseDataDTO.getFields().containsKey("id")){
                    baseDataDTO.getFields().remove("id");
                }
                baseDataDTO.setOperate("update");
            } else {
                baseDataDTO.setOperate("insert");
                if(!baseDataDTO.getFields().containsKey("id")){
                    baseDataDTO.getFields().put("id", StringUtils.getUUID());
                }
                if(!baseDataDTO.getFields().containsKey("jlrq") && baseDataDTO.getFields().containsKey("xgrq")){
                    baseDataDTO.getFields().put("jlrq", baseDataDTO.getFields().get("xgrq"));
                }
            }
            asyncService.logOldData(baseDataDTO);
        }
        return baseDataDTO;
    }


    public List<BaseDataDTO> getData(JSONObject jsonObject) throws Exception {
        List<BaseDataDTO> baseDataDTOs = new ArrayList<>(batchLimit);
        Object obj = jsonObject.get("result");
        if(null != obj && obj instanceof JSONObject && ((JSONObject) obj).containsKey("compressType")){
            JSONObject result = (JSONObject) obj;
            byte[] bytes = uncompress(result.getBytes("result"), result.getString("compressType"));
            String da = (String) JSON.parse(new String (bytes, "UTF-8"));
            baseDataDTOs = JSONObject.parseArray(da, BaseDataDTO.class);
        }
        return baseDataDTOs;
    }

    private byte[] uncompress(byte[] bytes, String compressType){
        switch (compressType) {
            case "GZip":
                return gZipUncompress(bytes);
            default:
                return snappyUncompress(bytes);
        }
    }

    /**
     * Snappy方式压缩
     * @param obj
     * @return
     */
    private byte[] snappyUncompress(byte[] obj){
        try {
            return Snappy.uncompress(obj);
        } catch (IOException e) {
            return obj;
        }
    }

    /**
     * GZip方式压缩
     * @param obj
     * @return
     */
    private byte[] gZipUncompress(byte[] obj) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream in = new ByteArrayInputStream(obj);
        try {
            GZIPInputStream ungzip = new GZIPInputStream(in);
            byte[] buffer = new byte[2048];
            int n;
            while ((n = ungzip.read(buffer)) >= 0) {
                out.write(buffer, 0, n);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return out.toByteArray();
    }




}

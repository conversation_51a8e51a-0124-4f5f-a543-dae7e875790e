server:
  port: 8086
spring:
  application:
    name: client-service
  profiles:
 #   active: dev
 #   active: test
 #   active: preprod
    active: prod
mybatis:
  typeAliasesPackage: com.jy.bean
  mapperLocations: classpath:mapper/*.xml
  configuration:
    #开启驼峰命名转换
    mapUnderscoreToCamelCase: true

management:
  server:
    port: 8099
  endpoints:
    web:
      exposure:
        include: health,info
info:
  version: 2.5
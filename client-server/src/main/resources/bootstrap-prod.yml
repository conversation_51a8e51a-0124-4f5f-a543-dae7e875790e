publickey:
spring:
  application:
    name: client-service

  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    #抓取SQL运行时的参数值
    #mysql数据库连接示例
    # url: ************************************************************************************************************
    # username: 1
    # password: 1&1
    #抓取SQL运行时的参数值
    #driver-class-name: org.gjt.mm.mysql.Driver
    #    url: ******************************************************************************************************************************
    #    username: 1
    #    password: 1
    #    driver-class-name: com.mysql.jdbc.Driver

    #oracle数据库连接示例
    url: ********************************
    username: 1
    password: 1
    driver-class-name: oracle.jdbc.driver.OracleDriver

    #postgresql数据库连接示例
    #url: ****************************************************
    #username: 1
    #password: 1@163
    #driver-class-name: org.postgresql.Driver

    # 初始化大小，最小，最大
    initialSize: 5
    minIdle: 5
    maxActive: 40
    # 配置获取连接等待超时的时间
    maxWait: 60000
    # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
    timeBetweenEvictionRunsMillis: 60000
    # 配置一个连接在池中最小生存的时间，单位是毫秒
    minEvictableIdleTimeMillis: 300000
    validationQuery: SELECT 1 FROM DUAL
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    # 超过时间限制是否回收
    #  removeAbandoned: true
    # 如果连接建立时间超过了5分钟，则强制将其关闭
    #   removeAbandonedTimeout: 300
    # 打开PSCache，并且指定每个连接上PSCache的大小
    poolPreparedStatements: true
    maxPoolPreparedStatementPerConnectionSize: 50
    # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
    filters: stat,wall,slf4j #,config
    # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
    connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000 #;config.decrypt=true;config.decrypt.key=${publickey};
    # 合并多个DruidDataSource的监控数据
    #spring.datasource.useGlobalDataSourceStat=true
logging:
  level:
    org.springframework.security: warn
    org.springframework: warn
    com.jy.mapper: warn
  config: classpath:log4j2-prod.yml
httpClient:
  proxyHost:
  proxyPort:
  url: http://datapublisher.jingyougroup.com/
  #  url: http://*********/
  tokenPath: oauth/token
  processpath: data?dataProcess
  registerpath: data?register
  callback: data?callback
  comparePath: compares
  clientCode: CCBPI
  username: CCBPI
  password: 306dfc3c4b939b3d
  authorization: YW5kcm9pZDphbmRyb2lk
  #每次从即时更新服务端获取数据量配置
  batchLimit: 100
baseDatasource:
  baseDBName:
maketprice:
  #主键id规则  sequence oracle序列函数/uuid  /default 默认数据库自行递增
  idRule: sequence
  #当idRule=sequence时填写序列函数名称 PM_SEQUENCE.nextVal
  sequenceName: PM_SEQUENCE.NEXTVAL
  #orgTableName 为分省分析时验证区域id的表名
  orgTableName: pm_organization_info
  priceFieldName: local_tabname
  #filter 过滤数据价格大于数据库中价格则不更新等  针对 分省分析 ： bd_pp_price,bd_sc_price
  filterField:
  #本地化价格表，表中存在重复数据，价格分类不同  需求： 根据本地化价格类型更新需求
  replenish:
    # key ： price_type  value： 1
    key:
    value:

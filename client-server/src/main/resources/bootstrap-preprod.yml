publickey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAMCVXYv59o2eFBbIiW2Vq66CVHhE8DJKw5uvcZv4B9STDM/d4gGwYZp5pmYJkjLEQL5Riv1LuhibzGuMGMlloXcCAwEAAQ==
spring:
  application:
    name: client-service
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: oracle.jdbc.driver.OracleDriver
    url: *******************************************
    username: picc_data_1
    password: 123

    # 初始化大小，最小，最大
    initialSize: 5
    minIdle: 5
    maxActive: 100
    # 配置获取连接等待超时的时间
    maxWait: 60000
    # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
    timeBetweenEvictionRunsMillis: 60000
    # 配置一个连接在池中最小生存的时间，单位是毫秒
    minEvictableIdleTimeMillis: 300000
    validationQuery: SELECT 'x' FROM DUAL
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    # 打开PSCache，并且指定每个连接上PSCache的大小
    poolPreparedStatements: true
    maxPoolPreparedStatementPerConnectionSize: 50
    # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
    filters: stat,wall,slf4j,config
    # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
    connectionProperties: config.decrypt=true;config.decrypt.key=${publickey};druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    # 合并多个DruidDataSource的监控数据
    #spring.datasource.useGlobalDataSourceStat=true


logging:
  level:
    root: info
    org.springframework.security: warn
    org.springframework: info
    com.jy.mapper: warn
  config: classpath:log4j2-preprod.yml

httpClient:
  proxyHost:
  proxyPort:
  url: http://datapublisher.jingyougroup.com/
  processpath: data?process
  registerpath: data?register
  callback: data?callback
  comparePath: compares
  clientCode: TSBX
  username: TSBX
  password: tsbx
  authorization: YW5kcm9pZDphbmRyb2lk
  batchLimit: 1000
baseDatasource:
  baseDBName: picc_data_2
maketprice:
  idRule: sequence
  sequenceName: PM_SEQUENCE.NEXTVAL
  orgTableName: aq_organization_info
  priceFieldName: local_tabname
  #filter 过滤数据
  filterField: bd_pp_price,bd_sc_price
  replenish:
    key: price_type
    value: 1
Configuration:
  status: warn

  Properties:
    Property:
      -  name: logFormat
         value: '%d{yyyy-MM-dd HH:mm:ss.SSS}{GMT+8}  %-5level  %logger{35}  %msg  %n'
  Appenders:
    Console:  #输出到控制台
      name: CONSOLE
      target: SYSTEM_OUT
      ThresholdFilter:
        level: error
        onMatch: ACCEPT
        onMismatch: DENY
      PatternLayout:
        charset: utf-8
        pattern: ${logFormat}


    RollingFile:
      - name: ROLLING_FILE
        ignoreExceptions: false
        fileName: /jylog/client-server.log
        filePattern: '/jylog/client-server_%d{yyyy-MM-dd-HH-mm-ss}.log'
        Filters:
          ThresholdFilter:
              - level: error
                onMatch: DENY
                onMismatch: NEUTRAL
              - level: warn
                onMatch: ACCEPT
                onMismatch: DENY

        PatternLayout:
          charset: utf-8
          pattern: ${logFormat}
        Policies:
          SizeBasedTriggeringPolicy:
            size: 512MB
        DefaultRolloverStrategy:
          Delete:
            basePath: /jylog/
            maxDepth: 1
            IfFileName:
              glob: 'client-server_*.log'
            IfAccumulatedFileCount:
              exceeds: 50

  Loggers:
    Root:
      level: warn
      AppenderRef:
        - ref: CONSOLE
        - ref: ROLLING_FILE

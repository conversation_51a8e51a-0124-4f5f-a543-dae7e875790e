var pisd = new Vue({
    el: '#table_insert',
    data:{
        dataTable:window.parent.tableList.nowTable
    },
    mounted: function () {

    },
    methods: {
        dealLoadMethod:function () {
            //加载层
            layer.load(0, {
                shade: [0.5,'#fff'] //0.1透明度的白色背景
            });
        },
        saveTable:function(){
            var dataTable = this.dataTable;
            if(dataTable.tableName == ""){
                layer.msg("表名不能为空");
                return;
            }
            this.$http.post('./dataTable/save',JSON.stringify(dataTable)).then(function (response) {
                response = response.data;
                if(response.status == "200"){
                    parent.layer.closeAll();
                    parent.layer.msg("操作成功");
                    window.parent.tableList.getTableList();
                } else {
                    parent.layer.msg(response.message);
                }
            })
        }
    }
});
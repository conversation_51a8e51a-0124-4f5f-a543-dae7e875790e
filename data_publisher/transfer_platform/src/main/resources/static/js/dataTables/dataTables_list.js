var tableList = new Vue({
    el:"#tableList",
    data:{
        nowUser: '',
        tableList: [],
        totalCount: 0,//总数
        all: 0, //总页数
        cur: 1,//当前页码
        pageFlag:'',
        tableName:'',
        nowTable:{}
    },
    mounted:function(){
        this.$nextTick(function () {
            // 代码保证 this.$el 在 document 中
            this.getTableList();
        });
    },
    methods: {
        getTableList: function (type) {
            //如果是查询,需要把page置为1
            if("1" == type){
                this.clearPage();
            }
            var tableDTO = {
                page: this.cur,
                tableName:this.tableName
            };
            this.tableList = [];
            this.$http.get('./dataTable/getList',tableDTO).then(function (response) {
                response = response.data;
                if(response.status == "200"){
                    this.tableList = response.result.data;
                    this.totalCount = response.result.total;
                    this.all = Math.ceil(response.result.total/10);
                } else {
                    layer.msg(response.message);
                }
            })
        },

        //添加
        tableAdd:function (table) {
            if(table != undefined && table != null){
                this.nowTable = table;
            }else{
                this.nowTable = {"id":"","tableName":"","isEnable":"1"};
            }
            layer.open({
                type: 2,
                title: "添加",
                maxmin: false,
                id: 'LAY_layuipro',
                //开启最大化最小化按钮
                area: ['92%', '96%'],
                content:'./dataTable/jump'
            });
        },

        //删除
        deleteTable:function (table) {
            var this_ = this;
            layer.msg('您确定删除么？', {
                time: 0, //不自动关闭
                shade:0.3,
                btn: ['确定', '取消'],
                yes: function(index){
                    this_.doTableDel(table.id);
                }
            });
        },

        doTableDel:function (id) {
            this.$http.get('./dataTable/delete',{"id":id}).then(function (response) {
                response = response.data;
                if(response.status == "200"){
                    layer.msg("删除成功");
                    this.getTableList();
                } else {
                    layer.msg(response.message);
                }
            })
        },
        clearPage: function(){
            this.totalCount = 0, //当前页的页码
            this.cur = 1
        },
        btnClick: function(parCur){//页码点击事件
            if(parCur != this.cur && this.all !=0 && parCur <= this.all && parCur > 0){
                this.cur = parCur;
                this.getTableList(2);
            }
        }
    },
    computed:{
        indexs: function () {
            var left = 1
            var right = this.all
            var ar = []
            if (this.all >= 11) {
                if (this.cur > 5 && this.cur < this.all - 4) {
                    left = this.cur - 5
                    right = this.cur + 4
                } else {
                    if (this.cur <= 5) {
                        left = 1
                        right = 10
                    } else {
                        right = this.all
                        left = this.all - 9
                    }
                }
            }
            while (left <= right) {
                ar.push(left)
                left++
            }
            return ar
        },
        showLast: function () {
            if (this.cur == this.all) {
                return false
            }
            return true
        },
        showFirst: function () {
            if (this.cur == 1) {
                return false
            }
            return true
        }
    },
    watch: {
        cur: function(oldValue , newValue){
            console.log(arguments)
        }
    },
    filters:{

    }
});

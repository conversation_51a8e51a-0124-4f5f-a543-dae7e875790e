/*公共JS*/

/**
 * 公共加载提示
 */
var index;
function loading(){
      index= layer.msg('加载中', { icon: 16,shade: 0.01,time: 5000});
}

/**
 * 关闭加载提示
 */
function closeLoading(){
	// return false;
    layer.close(index);
}

/**
 * api接口文档中.点击点击查看,触发的事件
 * @returns
 */
function showInterFacePwd(){
	$.ajax({
		type: "GET",
		url: "/user/showInterFacePwd",
		async:false,
		success:function(data){
			layer.alert(data.message, {
            	title: '接口密码',
            });
		},
		error: function(){
			layer.alert("获取失败", {
            	title: '接口密码',	
            });

		}
	});
}

function showSource(){
	layer.alert("品牌编码，有两种获取渠道<br/>1：从定型过程中获取品牌编码；<br/>2：OE查询零件，返回时会带着品牌编码", {
    	title: '品牌编码来源',	
    });
}

/**
 * 统一异常处理
 * @param code
 * @param message
 */
function exceptionHandler(response) {
    if (response.code == "400" || response.code == "405" || response.code == "415" || response.code == "500") {
        layer.msg("系统出现错误");
        return false;
    } else if(!response.code){
        if (window.parent.length > 0) {
            window.parent.location = "./index";
        }
        return false;
    } else if(response.code != '100100'){
        layer.msg(response.message);
        return false;
    }
}
function pageClose() {
	var index = parent.layer.getFrameIndex(window.name);
	parent.layer.close(index);
}
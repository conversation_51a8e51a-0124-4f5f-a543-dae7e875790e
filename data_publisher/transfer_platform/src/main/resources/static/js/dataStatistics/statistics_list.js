var statisticsList = new Vue({
    el:"#statisticsList",
    data:{
        nowUser: '',
        statisticsList: [],
        totalCount: 0,//总数
        all: 0, //总页数
        cur: 1,//当前页码
        pageFlag:'',
        mainVersionCode:'',
        state:'',
        nowTable:{},
        groupCode:'',
        indexes:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],
        searchTime:'',
        groupList:[],
        groupId:'',
        flag:true
    },
    mounted:function(){
        this.$nextTick(function () {
            // 代码保证 this.$el 在 document 中
            this.groupId = this.$refs.groupId.value;
            this.searchTime = this.$refs.searchTime.value;
            this.getStatisticsList();
            //this.getGroupList();
        });
    },
    methods: {
        getStatisticsList: function (type) {
            //如果是查询,需要把page置为1
            if("1" == type){
                this.clearPage();
            }
            var tableDTO = {
                groupId:this.groupId,
                date:this.searchTime
            };
            this.tableList = [];
            var _this = this;
            this.$http.get('./admin/dataStatistics/listDataStatistics',tableDTO).then(function (response) {
                response = response.data;
                if(response.status == "200"){
                    _this.statisticsList = response.result;
                    // this.totalCount = response.result.total;
                    // this.all = Math.ceil(response.result.total/20);
                    // if(type == 'search'){
                        layer.msg("请求成功")
                    // }
                } else {
                    layer.msg(response.message);
                }
            })
        },
        exportStatisticsData: function(){

            if(this.searchTime == null || this.searchTime == ''){
                layer.msg("请选择日期");
                return;
            }
            if(this.flag){
                this.flag = false;
            }else{
                layer.msg("有任务正在导出，请稍后。。。");
                return;
            }
            var tableDTO = {
                date: this.searchTime
            };
            this.groupList = [];
            var _this = this;
            this.$http.get('./admin/dataStatistics/exportStatisticsData',tableDTO).then(function (response) {
                response = response.data;
                if(response.status == "200"){
                    location.href = "./admin/dataStatistics/download?fileName="+response.result
                    layer.msg("导出成功");
                } else {
                    layer.msg(response.message);
                }
                _this.flag = true;
            })
        },
        deail: function(group){
            this.groupId = group.groupId;
            this.getStatisticsList();
        },
        returnOne: function(){
            location.href = "./page/statistics?date="+this.searchTime
        },
        getGroupList: function (type) {
            var tableDTO = {
                date: this.searchTime
            };
            this.groupList = [];
            var _this = this;
            this.$http.get('./admin/dataStatistics/getGroupId',tableDTO).then(function (response) {
                response = response.data;
                if(response.status == "200"){
                    _this.groupList = response.result;
                } else {
                    layer.msg(response.message);
                }
            })
        },
        test: function(){
            alert(this.groupId);
        },
        operationTips: function(receive,type){
            var _this = this;
            var msg = type == '1' ? '处理数据' : type == '2' ? '回写数据' : type == '3' ? '发送数据' : '操作';
            layer.msg('您确定重新'+msg+'么？', {
                time: 0, //不自动关闭
                shade:0.3,
                btn: ['确定', '取消'],
                yes: function(index){
                    if(type == '1'){
                        _this.againGetData(receive);
                    }else if(type == '2'){
                        _this.againWriteBack(receive);
                    }else if(type == '3'){
                        _this.againSendByMainVersionCode(receive);
                    }
                }
            });
        },
        againGetData: function(receive){
            var _this = this;
            var tableDTO = {
                mainVersionCode: receive.mainVersionCode,
                groupCode:receive.groupCode,
                receiveDate:receive.receiveDate
            };
            this.$http.get('./admin/receiveGroupData/againGetData',tableDTO).then(function (response) {
                response = response.data;
                if(response.status == "200"){
                    layer.msg("正在重新处理数据,请稍后....");
                    _this.getReceiveList();
                } else {
                    layer.msg(response.message);
                }
            })
        },
        againWriteBack: function(receive){
            var _this = this;
            var tableDTO = {
                mainVersionCode: receive.mainVersionCode,
                dataType:receive.dataType,
                versionCode:receive.versionCode,
                groupId:receive.groupId,
                brandId:receive.brandId,
                brandCode:receive.brandCode,
                suffix:receive.tableSuffix,
                groupCode:receive.groupCode
            };
            this.$http.get('./admin/receiveGroupData/againWriteBack',tableDTO).then(function (response) {
                response = response.data;
                if(response.status == "200"){
                    layer.msg("回写成功");
                    _this.getReceiveList();
                } else {
                    layer.msg(response.message);
                }
            })
        },
        againSendByMainVersionCode: function(receive){
            var _this = this;
            var tableDTO = {
                mainVersionCode: receive.mainVersionCode
            };
            this.$http.get('./admin/receiveGroupData/againSendByMainVersionCode',tableDTO).then(function (response) {
                response = response.data;
                if(response.status == "200"){
                    layer.msg("发送成功");
                    _this.getReceiveList();
                } else {
                    layer.msg(response.message);
                }
            })
        },
        clearPage: function(){
            this.totalCount = 0, //当前页的页码
                this.cur = 1
        },
        btnClick: function(parCur){//页码点击事件
            if(parCur != this.cur && this.all !=0 && parCur <= this.all && parCur > 0){
                this.cur = parCur;
                this.getReceiveList(2);
            }
        }
    },
    computed:{
        indexs: function () {
            var left = 1
            var right = this.all
            var ar = []
            if (this.all >= 11) {
                if (this.cur > 5 && this.cur < this.all - 4) {
                    left = this.cur - 5
                    right = this.cur + 4
                } else {
                    if (this.cur <= 5) {
                        left = 1
                        right = 10
                    } else {
                        right = this.all
                        left = this.all - 9
                    }
                }
            }
            while (left <= right) {
                ar.push(left)
                left++
            }
            return ar
        },
        showLast: function () {
            if (this.cur == this.all) {
                return false
            }
            return true
        },
        showFirst: function () {
            if (this.cur == 1) {
                return false
            }
            return true
        }
    },
    watch: {
        cur: function(oldValue , newValue){
            console.log(arguments)
        }
    },
    filters:{

    }
});


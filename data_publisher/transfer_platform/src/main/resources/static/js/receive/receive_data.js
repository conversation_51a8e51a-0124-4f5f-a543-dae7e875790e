var tableDataList = new Vue({
    el:"#tableDataList",
    data:{
        nowUser: '',
        dataList: [],
        totalCount: 0,//总数
        all: 0, //总页数
        cur: 1,//当前页码
        pageFlag:'',
        mainVersionCode:'',
        state:'',
        nowTable:{},
        tableName:'',
        versionCode:''
    },
    mounted:function(){

    },
    methods: {
        tableDataList: function (type) {
            //如果是查询,需要把page置为1
            if("1" == type){
                this.clearPage();
            }
            var tableDTO = {
                page: this.cur,
                mainVersionCode:this.mainVersionCode,
                tableName:this.tableName,
                versionCode:this.versionCode
            };
            this.tableList = [];
            this.$http.get('./admin/receiveGroupData/selDataByTableName',tableDTO).then(function (response) {
                response = response.data;
                if(response.status == "200"){
                    this.dataList = response.result.data;
                    this.totalCount = response.result.total;
                    this.all = Math.ceil(response.result.total/20);
                    if(type == 'search'){
                        layer.msg("请求成功")
                    }
                } else {
                    layer.msg(response.message);
                }
            })
        },
        clearPage: function(){
            this.totalCount = 0, //当前页的页码
                this.cur = 1
        },
        btnClick: function(parCur){//页码点击事件
            if(parCur != this.cur && this.all !=0 && parCur <= this.all && parCur > 0){
                this.cur = parCur;
                this.tableDataList(2);
            }
        }
    },
    computed:{
        indexs: function () {
            var left = 1
            var right = this.all
            var ar = []
            if (this.all >= 11) {
                if (this.cur > 5 && this.cur < this.all - 4) {
                    left = this.cur - 5
                    right = this.cur + 4
                } else {
                    if (this.cur <= 5) {
                        left = 1
                        right = 10
                    } else {
                        right = this.all
                        left = this.all - 9
                    }
                }
            }
            while (left <= right) {
                ar.push(left)
                left++
            }
            return ar
        },
        showLast: function () {
            if (this.cur == this.all) {
                return false
            }
            return true
        },
        showFirst: function () {
            if (this.cur == 1) {
                return false
            }
            return true
        }
    },
    watch: {
        cur: function(oldValue , newValue){
            console.log(arguments)
        }
    },
    filters:{

    }
});


var receiveList = new Vue({
    el:"#receiveList",
    data:{
        nowUser: '',
        receiveList: [],
        totalCount: 0,//总数
        all: 0, //总页数
        cur: 1,//当前页码
        pageFlag:'',
        mainVersionCode:'',
        state:'',
        nowTable:{},
        groupCode:''
    },
    mounted:function(){
        this.$nextTick(function () {
            // 代码保证 this.$el 在 document 中
            this.getReceiveList()
        });
    },
    methods: {
        getReceiveList: function (type) {
            //如果是查询,需要把page置为1
            if("1" == type){
                this.clearPage();
            }
            var tableDTO = {
                page: this.cur,
                mainVersionCode:this.mainVersionCode,
                state:this.state,
                groupCode:this.groupCode
            };
            this.tableList = [];
            this.$http.get('./admin/receiveGroupData/getList',tableDTO).then(function (response) {
                response = response.data;
                if(response.status == "200"){
                    this.receiveList = response.result.data;
                    this.totalCount = response.result.total;
                    this.all = Math.ceil(response.result.total/20);
                    if(type == 'search'){
                        layer.msg("请求成功")
                    }
                } else {
                    layer.msg(response.message);
                }
            })
        },
        operationTips: function(receive,type){
            var _this = this;
            var msg = type == '1' ? '处理数据' : type == '2' ? '回写数据' : type == '3' ? '发送数据' : '操作';
            layer.msg('您确定重新'+msg+'么？', {
                time: 0, //不自动关闭
                shade:0.3,
                btn: ['确定', '取消'],
                yes: function(index){
                    if(type == '1'){
                        _this.againGetData(receive);
                    }else if(type == '2'){
                        _this.againWriteBack(receive);
                    }else if(type == '3'){
                        _this.againSendByMainVersionCode(receive);
                    }
                }
            });
        },
        againGetData: function(receive){
            var _this = this;
            var tableDTO = {
                mainVersionCode: receive.mainVersionCode,
                groupCode:receive.groupCode,
                receiveDate:receive.receiveDate
            };
            this.$http.get('./admin/receiveGroupData/againGetData',tableDTO).then(function (response) {
                response = response.data;
                if(response.status == "200"){
                    layer.msg("正在重新处理数据,请稍后....");
                    _this.getReceiveList();
                } else {
                    layer.msg(response.message);
                }
            })
        },
        againWriteBack: function(receive){
            var _this = this;
            var tableDTO = {
                mainVersionCode: receive.mainVersionCode,
                dataType:receive.dataType,
                versionCode:receive.versionCode,
                groupId:receive.groupId,
                brandId:receive.brandId,
                brandCode:receive.brandCode,
                suffix:receive.tableSuffix,
                groupCode:receive.groupCode
            };
            this.$http.get('./admin/receiveGroupData/againWriteBack',tableDTO).then(function (response) {
                response = response.data;
                if(response.status == "200"){
                    layer.msg("回写成功");
                    _this.getReceiveList();
                } else {
                    layer.msg(response.message);
                }
            })
        },
        againSendByMainVersionCode: function(receive){
            var _this = this;
            var tableDTO = {
                mainVersionCode: receive.mainVersionCode
            };
            this.$http.get('./admin/receiveGroupData/againSendByMainVersionCode',tableDTO).then(function (response) {
                response = response.data;
                if(response.status == "200"){
                    layer.msg("发送成功");
                    _this.getReceiveList();
                } else {
                    layer.msg(response.message);
                }
            })
        },
        clearPage: function(){
            this.totalCount = 0, //当前页的页码
                this.cur = 1
        },
        btnClick: function(parCur){//页码点击事件
            if(parCur != this.cur && this.all !=0 && parCur <= this.all && parCur > 0){
                this.cur = parCur;
                this.getReceiveList(2);
            }
        }
    },
    computed:{
        indexs: function () {
            var left = 1
            var right = this.all
            var ar = []
            if (this.all >= 11) {
                if (this.cur > 5 && this.cur < this.all - 4) {
                    left = this.cur - 5
                    right = this.cur + 4
                } else {
                    if (this.cur <= 5) {
                        left = 1
                        right = 10
                    } else {
                        right = this.all
                        left = this.all - 9
                    }
                }
            }
            while (left <= right) {
                ar.push(left)
                left++
            }
            return ar
        },
        showLast: function () {
            if (this.cur == this.all) {
                return false
            }
            return true
        },
        showFirst: function () {
            if (this.cur == 1) {
                return false
            }
            return true
        }
    },
    watch: {
        cur: function(oldValue , newValue){
            console.log(arguments)
        }
    },
    filters:{

    }
});


var userInsert = new Vue({
    el: '#user-insert',
    data:{
        usertype:'',//用户类型
        userName:'',
        passWord:'',
        relPassWord:'',
        roleCode:'',//角色
        phone:'',
        email:'',
        userRoleList:[],
    },
    mounted: function () {
        this.$nextTick(function () {
            // 代码保证 this.$el 在 document 中
            this.getUserSelect();
        });
    },
    methods: {
        dealLoadMethod:function () {
            //加载层
            layer.load(0, {
                shade: [0.5,'#fff'] //0.1透明度的白色背景
            });
        },
        getUserSelect:function () {

            this.$http.get('./user/getUserDetail').then(function (response) {
                response = response.data;
                if(response.status == "200"){
                    if (response.result.roleList!=null){
                        this.userRoleList = response.result.roleList;
                    }
                } else {
                    layer.msg(response.message);
                }
            })
        },
        selectUserName:function(){
            var myUser = /^[A-Za-z0-9]{4,20}$/;
            if(this.userName.length>0){
                if(!myUser.test(this.userName)){
                    layer.msg("登录名由字母、数字组成，4-20位");
                    return;
                }
            }
            var UserDTO ={
                userName:this.userName,
            };
            this.$http.get('./user/searchUserName',UserDTO).then(function (response) {
                response = response.data;
                if(response.code == '000000'){
                    layer.msg("此账号已经注册");
                    return;
                }
            })
        },
        saveUser:function () {
            if(!this.userName){
                layer.msg("登录名不能为空");
                return;
            }
            var myUser = /^[A-Za-z0-9]{4,20}$/;
            if(!myUser.test(this.userName)){
                layer.msg("登录名由字母、数字组成，4-20位");
                return;
            }
            if(!this.passWord ){
                layer.msg("密码不能为空");
                return;
            }
            if(!this.relPassWord){
                layer.msg("确认密码不能为空");
                return;
            }
            if(!this.passWord.length>0 || this.passWord.length<5 || this.passWord.length >18){
                layer.msg("密码5~18位");
                return;
            }
            if($("#newPassWord").val() != $("#relPassWord").val()){
                layer.msg("密码不一致");
                return;
            }
            if(!this.roleCode){
                layer.msg("角色不能为空");
                return;
            }
            if(!this.phone){
                layer.msg("联系电话不能为空");
                return;
            }
            if(!(/^1[3456789]\d{9}$/.test(this.phone))){
                layer.msg("联系电话有误，请重填");
                return;
            }
            if(!this.email){
                layer.msg("邮箱不能为空");
                return;
            }
            var myreg = /^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/;
            if(!myreg.test(this.email)){
                layer.msg("邮箱格式错误");
                return;
            }
            this.dealLoadMethod();
            var UserDTO = {
                userName:this.userName,
                passWord:this.passWord,
                roleCode:this.roleCode,
                phone:this.phone,
                email:this.email
            };
            this.$http.post('./user/saveUserDetail',UserDTO).then(function (response) {
                response = response.data;
                if(response.status == "200"){
                    parent.layer.closeAll();
                    parent.layer.msg("操作成功");
                    window.parent.userList.getUserPoList();
                } else {
                    parent.layer.msg(response.message);
                    parent.layer.closeAll('loading');
                }
            })
        },
    },
    filters:{

    }
});
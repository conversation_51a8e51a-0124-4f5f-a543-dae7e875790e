var userDetail = new Vue({
    el: '#table_insert',
    data:{
        // userPoDetail:JSON.parse(JSON.stringify(window.parent.userList.nowUser)),
        userPoDetail:window.parent.userList.nowUser,
        userRoleList:[],
        relPassWord:''
    },
    mounted: function () {
        this.$nextTick(function () {
            this.getUserDetail();
        });
    },
    methods: {
        //获取字典信息
        getUserDetail:function () {
            this.$http.get('./user/getUserDetail').then(function (response) {
                response = response.data;
                if(response.status == "200"){
                    if (response.result.roleList!=null){
                        this.userRoleList = response.result.roleList;
                    }
                } else {
                    layer.msg(response.message);
                }
            })
        },

        //修改用户信息
        saveUser:function (userContent) {
            if(!userContent.userName){
                layer.msg("登录人姓名不能为空");
                return;
            }
            if($("#newPassWord").val() != $("#relPassWord").val()){
                layer.msg("密码不一致");
                return;
            }
            if(!userContent.roleCode){
                layer.msg("角色不能为空");
                return;
            }
            if(!userContent.phone){
                layer.msg("联系电话不能为空");
                return;
            }
            if(!(/^1[3456789]\d{9}$/.test(userContent.phone))){
                layer.msg("联系电话有误，请重填");
                return;
            }
            if(!userContent.email){
                layer.msg("邮箱不能为空");
                return;
            }
            var myreg = /^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/;
            if(!myreg.test(userContent.email)){
                layer.msg("邮箱格式错误");
                return;
            }

            //重置密码
            userContent.mm = this.relPassWord;

            this.$http.post('./user/saveUserDetail',JSON.stringify(userContent)).then(function (response) {
                response = response.data;
                if(response.status == "200"){
                    parent.layer.closeAll();
                    parent.layer.msg("操作成功");
                    window.parent.userList.getUserPoList();
                } else {
                    parent.layer.msg(response.message);
                }
            })
        },
    }
});
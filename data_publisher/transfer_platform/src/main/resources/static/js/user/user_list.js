var userList = new Vue({
    el:"#userList",
    data:{
        nowUser: '',
        getUserList: [],
        totalCount: 0,//总数
        all: 0, //总页数
        cur: 1,//当前页码
        pageFlag:'',
        userName:'',
        userId:''
    },
    mounted:function(){
        this.$nextTick(function () {
            // 代码保证 this.$el 在 document 中
            this.getUserPoList();
        });
    },
    methods: {
        getUserPoList: function (type) {
            //需要把page置为1
            if("1" == type){
                this.clearPage();
            }
            var UserDTO = {
                page: this.cur,
                userName: this.userName
            };
            console.log("用户查询触发了！！");
            this.getUserList = '';
            this.$http.get('./user/userList',UserDTO).then(function (response) {
                response = response.data;
                if(response.status == "200"){
                    this.getUserList = response.result.data;
                    this.totalCount = response.result.total;
                    this.all = Math.ceil(response.result.total/10);
                } else {
                    layer.msg(response.message);
                }
            })
        },

        //用户修改
        userDetailEdit:function (user) {
            this.nowUser = user;
            layer.open({
                type: 2,
                title: "用户详情",
                maxmin: false,
                id: 'LAY_layuipro',
                //开启最大化最小化按钮
                area: ['92%', '96%'],
                content:'./page/userDetailEdit'
            });
        },

        //接口设置
        interfaceSettings:function (user) {
            this.userId = user.id;
            layer.open({
                type: 2,
                title: "接口设置",
                maxmin: false,
                id: 'LAY_layuipro',
                //开启最大化最小化按钮
                area: ['92%', '96%'],
                content:'./page/interfaceSettings?userId='+user.id
            });
        },

        //用户添加
        userAdd:function () {
            layer.open({
                type: 2,
                title: "用户添加",
                maxmin: false,
                id: 'LAY_layuipro',
                //开启最大化最小化按钮
                area: ['92%', '96%'],
                content:'./page/userAdd'
            });
        },

        //用户删除
        userDel:function (user) {
            layer.msg('您确定删除该用户么？', {
                time: 0, //不自动关闭
                shade:0.3,
                btn: ['确定', '取消'],
                yes: function(index){
                    userList.doUserDel(user);
                }
            });
        },

        doUserDel:function (user) {

            var UserDTO = {
                id: user.id
            };
            this.$http.get('./user/userDel',UserDTO).then(function (response) {
                response = response.data;
                if(response.status == "200"){
                    layer.msg("删除成功");
                    this.getUserPoList();
                } else {
                    layer.msg(response.message);
                }
            })

        },
        clearPage: function(){
            this.totalCount = 0, //当前页的页码
            this.cur = 1
        },
        btnClick: function(parCur){//页码点击事件
            if(parCur != this.cur && this.all !=0 && parCur <= this.all && parCur > 0){
                this.cur = parCur;
                userList.getUserPoList(2);
            }
        }
    },
    computed:{
        indexs: function () {
            var left = 1
            var right = this.all
            var ar = []
            if (this.all >= 11) {
                if (this.cur > 5 && this.cur < this.all - 4) {
                    left = this.cur - 5
                    right = this.cur + 4
                } else {
                    if (this.cur <= 5) {
                        left = 1
                        right = 10
                    } else {
                        right = this.all
                        left = this.all - 9
                    }
                }
            }
            while (left <= right) {
                ar.push(left)
                left++
            }
            return ar
        },
        showLast: function () {
            if (this.cur == this.all) {
                return false
            }
            return true
        },
        showFirst: function () {
            if (this.cur == 1) {
                return false
            }
            return true
        }
    },
    watch: {
        cur: function(oldValue , newValue){
            console.log(arguments)
        }
    },
    filters:{

    }
});

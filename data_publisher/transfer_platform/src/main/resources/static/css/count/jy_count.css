body{
	min-width: 1080px;
}
.checkWrap{
	width: 100%;
	background: white;
	padding-top: 5px;
}
.checkWrap input{
	 width: 140px;
	 height: 28px;
	 padding-left: 10px;
	 border-radius: 0px;
	 border: 1px solid #ccc;
 }


.checkList{
	text-align: right;
	margin-bottom: 0px;
	margin-top:  5px;
}
.checkList .checkBtn{
	width: 80px;
	height: 28px;
	line-height: 20px;
	border: none;
	margin-left: 10px;
	margin-bottom: 10px;
	padding-left: 0;
	cursor: pointer;
	color: #FFF;
	background: #00A0E9;
	border-radius: 0px;
}

.checkList .checkGreyBtn{
	width: 80px;
	height: 28px;
	line-height: 20px;
	border: 1px solid #bbb;
	margin-left: 10px;
	padding-left: 0;
	cursor: pointer;
	color: #333;
	background: #f0f0f0;
	border-radius: 0px;
}

.checkList li{
	display: inline-block;
	font-size: 12px;
}
.checkList li:nth-of-type(1){
	margin-left: 1%;
	float: left;
}
.checkList li:nth-of-type(2){
	margin-right: 170px;
}
.checkList li:nth-last-of-type(1){
	text-align: left;
	margin-right: 10px;
	float: right;
}
.insurance_table table {
    border-radius: 1px;
    background-color: #eaedf2;
    border-spacing: 0px;
    width: 100%;
    font-size: 12px;
    color: #333;
}
.insurance_table thead th{
	font-weight: normal;
}
.insurance_table th{
	height: 30px;
	/*background: #EAEDF1;*/
	background: #EAEDE0;
	color: #888;
	font-size: 12px;
	/*text-align: center;*/
	padding-left: 10px;
}
.insurance_table td{
	height: 40px;
	line-height: 40px;
	border-top: 1px solid #00000;
	font-size: 12px;
	text-align: center;
	padding-left: 10px;
}
.insurance_table td:nth-of-type(1){
	text-align: center;
}
.insurance_table td:nth-last-of-type(1){
	text-align: center;
}
.insurance_table tbody tr:nth-child(odd){
	/*background: #f9f9fa;*/
	background: #fff;
}
.insurance_table tbody tr:nth-child(even){
	background: #fff;
	/*background: rgba(61, 155, 98, 0.1);*/
}
.insurance_table .ditailBtn input{
    width: 80px;
    height: 20px;
    line-height: 20px;
    border: none;
    background: #00a0e9;
    color: #fff;
    cursor: pointer;
}
.selectStyle{
	width: 152px;
	height: 28px;
	border: 1px solid #ccc;
	padding-left: 10px;
}
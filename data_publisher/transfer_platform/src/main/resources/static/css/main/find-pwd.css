body{
	/*font-family: "微软雅黑";*/
    font-family: 'PingFangSC','helvetica neue','hiragino sans gb','arial','microsoft yahei ui','microsoft yahei','simsun','sans-serif'!important;
"Helvetica Neue", "Luxi Sans", "DejaVu Sans", <PERSON><PERSON><PERSON>, "Hiragino Sans GB", STHeiti, "Microsoft YaHei";
	font-size: 14px;
	/*background: #eaf0f2;*/
    background: #fff;
}
#loginWrap {
    width: 1200px;
    /* height: 100%; */
    margin: 25px auto;
    /* background: #fff; */
    overflow: hidden;
}
.login_title {
    width: 100%;
    height: 37px;
    margin-top: 40px;
    margin-left: 20%;
    margin-bottom: 18px;
}
.login_title img {
    margin-bottom: -8px;
    margin-right: 20px;
}
.login_title span {
    font-size: 24px;
    color: #31495C;
    line-height: 37px;
}
/******************************/
.login_text {
    width: 440px;
    margin: 0 auto;
    position: relative;
}
.login_text input{
	font-family: "微软雅黑";
	font-size: 12px;
	color: #999;
}
.login_text .login_item {
    width: 250px;
    display: block;
    height: 28px;
    margin-top: 24px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    color: #555;
    outline:none;
    background-color: #F7F7F7;
    background-image: none;
    border: 1px solid #ececec;
    border-radius: 3px;
}
.login_text .login_item:focus{
    border: 1px solid rgba(50,165,217,0.4);
}

.login_text .phoneCode{
	width: 120px;
	/*margin-left: 16px;*/
	display: inline-block;
}
.login_text .recCode{
    width: 110px;
    margin-left: 16px;
    height: 42px;
    padding-right: 0;
	padding-left: 0;
	cursor: pointer;
	background: #32A5D9;
	color: #fff;
	font-size: 12px;
    border: none;
}
.login_text .login_btn{
    width: 276px;
    height: 50px;
    background: #32A5D9;
    color: #FFFFFF;
    font-weight: bold;
    margin-top: 20px;
    /*margin-left: 16px;*/
    border: none;
    border-radius: 3px;
}

.notClick{
	background:#eee !important;
	cursor:not-allowed  !important;
    color: #aaa  !important;
}
.errorPhone{
	width: 170px;
    height: 16px;
    font-size: 14px;
    color: #FF0000;
    margin-left: 300px;
    float: left;
    margin-top: -30px;
}
.clearfloat:after{
	display: block;
	clear: both;
	content: "";
	visibility: hidden;
	height: 0;
}
.clearfloat{
	zoom: 1;
}
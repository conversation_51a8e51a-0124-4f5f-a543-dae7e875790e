body{
    width: 100%;
    /*font-family:"微软雅黑";*/
    /*font-family: "Helvetica Neue", "Luxi Sans", "DejaVu Sans", <PERSON><PERSON><PERSON>, "Hiragino Sans GB", STHeiti, "Microsoft YaHei";*/
    font-family: 'PingFangSC','helvetica neue','hiragino sans gb','arial','microsoft yahei ui','microsoft yahei','simsun','sans-serif'!important;
"Helvetica Neue", "Luxi Sans", "DejaVu Sans", <PERSON><PERSON><PERSON>, "Hiragino Sans GB", STHeiti, "Microsoft YaHei";
    font-size: 14px;
    /*background: #EAF0F2;*/

}
/****************main-content****************/
.main-content{
    width: 100%;
   /* position: relative;*/
}
.left-nav{
    width: 170px;
    height: 100%;
    position: fixed;
    left: 0;

    background: #31495c;
    padding-top: 30px;
    overflow: auto;
    overflow-x: hidden;
    overflow-y: hidden;
}
.left-nav dl,dt,dd{
    display:block;
}

.left-nav dt:hover,
.left-nav dd:hover,
.left-nav .active{
    background: #32A5D9;
    cursor: pointer;
    color: #FFF;
}
.left-nav dt{
    padding-right:10px;
    background-repeat:no-repeat;
    background-position:10px center;
    color:#EAF0F2;
    padding-left: 5px;
    /*font-weight: bold;*/
    line-height:48px;
    cursor:pointer;
}
.left-nav dd{
    background: transparent;
    padding-left:50px;
    font-size: 12px;
    color:#EAF0F2;
    line-height:35px;
}
.left-nav dt .dt_imgL{
    margin-bottom: -9px;
    margin-right: 10px;
}
.left-nav dt .dt_imgR{
    margin-bottom: -1px;
    margin-left: 25px;
}


/**************contentRight**************/
.main-div{
    width: auto;
    /*min-height: 716px;
    max-height: 900px;*/
    height: calc(100% - 20px);
    background: transparent;
    position: absolute;
     top: 16px;
    left: 186px;
    bottom: 0px;
    right: 16px;
    overflow: hidden;
    overflow-x: hidden;
    z-index: -1;
}
.main-div .main-iframe{
    border: none;
    width: 100%;
    height: 100%;
}
.main-iframe .titleTip{
    width: 100%;
    height: 130px;
}


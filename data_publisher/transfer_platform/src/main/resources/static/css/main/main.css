body{
    width: 100%;
    /*min-width: 1300px;*/
    /*font-family:"微软雅黑";*/
    font-family: 'PingFangSC','helvetica neue','hiragino sans gb','arial','microsoft yahei ui','microsoft yahei','simsun','sans-serif'!important;
"Helvetica Neue", "Luxi Sans", "DejaVu Sans", <PERSON><PERSON><PERSON>, "Hiragino Sans GB", STHeiti, "Microsoft YaHei";
    font-size: 14px;
    /*background: #EAF0F2;*/
}
.up-nav{
    width: 100%;
    /*min-width: 1210px;*/
    height: 50px;
    /*background: #FFF;*/
    background: #EAF0F2;
    position: fixed;
    left: 0;
    top: 0;
    /*border-bottom: 1px solid #e1e1e1;*/
}
.up-nav .main-logo{
    width: 170px;
    height: 37px;
    float: left;
    padding-top: 14px;
    font-size: 18px;
    background: #31495c;
    text-align: center;
    display: inline-block;
    vertical-align: text-top;
}
.up-nav .main-logo img{
    width: 135px;
    
}
.up-nav .main-logo span{
    vertical-align: middle;
    display: inline-block;
    color: white
}
.up-nav .main-logo span:nth-child(1){
    width: 26px;
    height: 20px;
}


.up-nav ul{
    /*font-family: "Microsoft YaHei UI";*/
    font-family: 'PingFangSC','helvetica neue','hiragino sans gb','arial','microsoft yahei ui','microsoft yahei','simsun','sans-serif'!important;
"Helvetica Neue", "Luxi Sans", "DejaVu Sans", Tahoma, "Hiragino Sans GB", STHeiti, "Microsoft YaHei";
    font-size: 14px;
    float: left;
    display: inline-block;
}
.up-nav ul li{
    /*width: 100%;*/
    color: #31495C;
    text-align: center;
    line-height: 50px;
    /*margin-left: 90px;*/
    cursor: pointer;
    display: inline-block;
}
.up-nav .up-nav-userTips{
    float: right;
}
.up-nav .up-nav-userTips ul span img{
    vertical-align: middle;
}
.up-nav .liFir{
    margin-left: 45px;
}
.up-nav .liFiv{
    margin-left: 0;
}
.up-nav .liFiv img{
    margin-bottom: -3px;
}
.up-nav .liSix{
    margin-left: 0;
    font-weight: normal;
    color: #ececec;
}
.up-nav .liSev{
    width: 150px;
    height: 30px;
    text-align: right;
    margin-left: 192px;
    font-weight: normal;
    color: #31495C;
    cursor: pointer;
    margin-top: 12px;
}
.up-nav .liSev img{
    margin-left: 10px;
}
.up-nav .nav-left .nav-li{
    width: 90px;
    position: relative;
}

.clearfloat:after{
    display:block;
    clear:both;
    content:"";
    visibility:hidden;
    height:0
}
.clearfloat{
    zoom: 1;
}
/******** content ********/
.content{
    width: calc(100% - 1px);
    height: calc(100% - 50px);
    position: absolute;
    top: 51px;
}
.content iframe{
    border:none;
}
/********下拉菜单*********/
.product{
    position: relative;
}
.product .drapDown{
    width: 100px;
    height: 120px;
    background: #f7f7f7;
    border:1px solid #E1E1E1;
    position: absolute;
    left: 0;
    top: 40px;
    margin-bottom: -5000px;
}
.drapDown li a:link{
    color: #31495C;
}
.drapDown li a:visited{
    color: #31495C;
}
.drapDown li a:hover{
    color: #FFFFFF;
}
.drapDown li a:active{
    color: #31495C;
}
.product .drapDown li{
    width: 100%;
    text-align: center;
    line-height: 32px;
    margin-left: 0;
    font-size: 12px;
}
.drapDown_veh{
    height: 100px !important;
    padding-top: 4px;
    margin-top: -40px;
    margin-left: 100px;
    border-left: 1px solid #e1e1e1;
}
.drapDown_parts{
    height: 66px !important;
    padding-top: 4px;
    margin-left: 100px;
    border-left: 1px solid #e1e1e1;
}
.drapDown li:hover {
    background: #32A5D9;
    color: #FFFFFF;
}
.drapDown li a:hover{
    text-decoration: none;
}
.drapDown li:nth-last-of-type(1){
    border-bottom: none;
}

/*****登陆后个人中心*******/
.personInfo{
    line-height: 48px;
    text-align: right;
    padding-right: 15px;
}

.personWarningCenter{
    width: 180px;
    height: 0;
    position: relative;
    z-index: 10;
    overflow: hidden;
    border: 1px solid #e1e1e1;
    float: right;
    /*margin-right: -20px;*/
    transition:width .5s ;
    -webkit-transition:height .5s;
    margin-bottom: -5000px;
    margin-left: -500px;
}


.personCenter{
    width: 302px;
    height: 0;
    position: relative;
    z-index: 10;
    overflow: hidden;
    border: 1px solid #e1e1e1;
    float: right;
    /*margin-right: -20px;*/
    transition:width .5s ;
    -webkit-transition:height .5s;
    margin-bottom: -5000px;
}

.personWarningCenter .personCenter_Warning_top{
    width: 100%;
    height: 126px;
    overflow: hidden;
    background: #F7F7F7;
    border-bottom: 1px solid #e1e1e1;
}

.personCenter_Warning_top li.detailItem{
    width:100%;
}
.detailWrap{
    width: 190px;
}
.personCenter .personCenter_top{
    width: 100%;
    height: 60px;
    overflow: hidden;
    background: #F7F7F7;
    border-bottom: 1px solid #e1e1e1;
}
.personCenter_top ul{
    width: 100%;
    margin-left: 30px;
    margin-top: 14px;
    color: #616c7e;
}
.personCenter_top ul li{
    margin-left: 0;
    line-height: 14px ;
    width: initial;
}
.personCenter_top ul li img{
    margin-bottom: -6px;
    margin-right: 10px;
}
.personCenter_bot ul li{
    margin-left: 0;
    width: initial;
}
.personCenter_bot ul li input{
    width: 150px;
    height: 50px;
    background: #FFF;
    border: none;
    /*font-family: "微软雅黑";*/
    font-family: 'PingFangSC','helvetica neue','hiragino sans gb','arial','microsoft yahei ui','microsoft yahei','simsun','sans-serif'!important;
"Helvetica Neue", "Luxi Sans", "DejaVu Sans", Tahoma, "Hiragino Sans GB", STHeiti, "Microsoft YaHei";
    font-size: 14px;
    color: #31495C;
    cursor: pointer;
}
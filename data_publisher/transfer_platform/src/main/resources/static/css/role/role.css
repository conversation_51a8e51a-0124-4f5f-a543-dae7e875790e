.checkWrap{
	width: 100%;
}
.checkWrap input{
	width: 140px;
	height: 20px;
	padding-left: 10px;
	border-radius: 3px;
	border: 1px solid #ccc;
}
.checkList{
	text-align: center;
	margin-bottom: 16px;
	margin-top: 50px;
}
.checkList .checkBtn{
	width: 80px;
	height: 20px;
	line-height: 20px;
	border: none;
	float: right;
	/*margin-left: 170px;*/
	padding-left: 0;
	cursor: pointer;
	color: #FFF;
	background: #00A0E9;
}
.checkList li{
	display: inline-block;
	font-size: 12px;
}
.checkList li:nth-of-type(1){
	margin-left: 5%;
	float: left;
}
.checkList li:nth-last-of-type(1){
	width: 410px;
	text-align: left;
	margin-right: 10px;
	float: right;
}
.insurance_table table {
    border-radius: 1px;
    background-color: #eaedf2;
    border-spacing: 0px;
    width: 100%;
    font-size: 12px;
    color: #333;
}
.insurance_table thead th{
	font-weight: normal;
}
.insurance_table th{
	height: 30px;
	background: #EAEDF1;
	color: #888;
	font-size: 12px;
	/*text-align: center;*/
	padding-left: 10px;
}
.insurance_table td{
	height: 40px;
	line-height: 40px;
	border-top: 1px solid #e1e6eb;
	font-size: 12px;
	text-align: center;
	padding-left: 10px;
}
.insurance_table td:nth-of-type(1){
	text-align: center;
}
.insurance_table td:nth-last-of-type(1){
	text-align: center;
}
.insurance_table tbody tr:nth-child(odd){
	background: #f9f9fa;
}
.insurance_table tbody tr:nth-child(even){
	background: #fff;
	/*background: rgba(61, 155, 98, 0.1);*/
}
.insurance_table .ditailBtn input{
    width: 80px;
    height: 20px;
    line-height: 20px;
    border: none;
    background: #00a0e9;
    color: #fff;
    cursor: pointer;
}

.pagination{
	width: 100%;
	margin-top: 20px;
	text-align: left;
}
.pagination li{
	display: inline-block;
	color: #E1E1E1;
	cursor: pointer;
}
.pagination .recordMsg{
	color: #00A0E9;
	margin-right: 60px;
}
.pagination a{
	text-decoration: none;
	color: #00A0E9;
}
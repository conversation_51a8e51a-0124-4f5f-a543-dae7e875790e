/************************veh_vinSeach***************/

.vin-question {
	color: #9d7437;
}
/*************************/
.veh-table{
	margin: -10px -30px;
}
.veh-table table {
   /*  border: 1px solid #e1e6eb; */
    border-radius: 1px;
    background-color: #eaedf2;
    border-spacing: 0px;
    width: 100%;
    font-size: 12px;
    color: #333;
}
.veh-table thead th{
	font-weight: normal;
}
.veh-table th{
	height: 30px;
	background: #EAEDF1;
	color: #888;
	font-size: 12px;
	text-align: left;
	padding-left: 10px;
}
.veh-table td{
	height: 40px;
	line-height: 40px;
	background: #FFFFFF;
	border-top: 1px solid #e1e6eb;
	font-size: 12px;
	text-align: left;
	padding-left: 10px;
}
.veh-table td:nth-last-child(1){
	text-align: center;
}
.veh-table th:nth-last-child(1){
	text-align: center;
}
.veh-table tr:hover{
	background: #f9f9fa;
}
.car-config {
	display: none;
}
.clearfloat:after{
	display:block;
	clear:both;
	content:"";
	visibility:hidden;
	height:0
}
.clearfloat{
	zoom:1
}


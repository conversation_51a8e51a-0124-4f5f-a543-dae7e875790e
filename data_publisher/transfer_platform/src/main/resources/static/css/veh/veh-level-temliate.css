/********************************/
.veh-select-div {
	/*width: 100%;*/
	/*height:600px;*/
	min-height: 448px;
	background: #FFF;
	border: 1px solid #e1e6ea;
}
.veh-select-div .letter-row{
	width: 100%;
	height: 40px;
	border-bottom: 1px solid #e1e6ea;
}
.letter-row li{
	margin-top: 12px;
	color: #666;
	font-size: 14px;
	font-weight: bold;
	margin-right: 25px;
	display: inline-block;
	cursor: pointer;
}
.letter-row li:nth-of-type(1){
	font-size: 14px;
	margin-left: 35px;
	margin-right: 35px;
}
.letter-row .car-letter-select{
	color: #00a1e9;
}
.carsel-list {
	margin-top: 20px;
}
.carsel-list li {
	/*width: 460px;*/
	height: 50px;
	overflow: hidden;
	display: inline-block;
	border: 1px solid #ddd;
	line-height: 50px;
	font-size: 14px;
	color: #666;
	font-weight: bold;
	cursor: pointer;
	margin: 0 0 10px 20px;
	min-width: 240px;
	max-width: 500px;
	padding-left: 30px;
}
@media only screen and (max-width: 1200px) {
	.carsel-list li{
		width: 350px;
	}
}
.carsel-list li:hover {
	border-color: #02A0E9;
	color: #333;
}
.carsel-list li .img {
	width: 30px;
	height: 30px;
	margin: 0 20px 0 16px;
	vertical-align: -10px;
}
.function-row .veh-btn{
	padding-left: 0;
}
.function-row .veh-btn{
	min-width: 120px;
	max-width: 360px;
	height: 32px;
	border: 1px solid #e1e1e1;
	border-radius: 1px;
	font-family: "微软雅黑";
	font-size: 12px;
	color: #89898a;
	background: #F7F7F7;
	cursor: pointer;
	margin-right: 5px;
	outline: none;
}
.function-row .veh-btn:hover{
	border:1px solid #32a5d9;
	color: #32a5d9;
}
.div-content li{
    margin-bottom: 10px;
}
.vehicle_filter {
	padding-bottom:35px;
	padding-top: 5px;
	padding-left: 10px;
}
.vehicle_filter  div{
	float: left;
	padding-left: 20px;
}
.vehicle_filter  div select{
	font-size: 14px;
	font-color:#666;
}
/*整体调整宽度*/


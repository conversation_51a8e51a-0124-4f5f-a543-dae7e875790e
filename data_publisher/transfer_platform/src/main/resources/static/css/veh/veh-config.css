body{
    font-family: "微软雅黑";
    font-size: 12px;
}
/**************************/
#veh-config{
    /*width: 1200px;*/
    position: relative;
}
#veh-config .sidebar{
    display: inline-block;
    vertical-align: top;
    position: fixed;
    top: 46px;
    left: 16px;
}
#veh-config .sidebar dl{
    width: 120px;
    /*height: 466px;*/
    border:1px solid #E1E1E1;
    text-align: center;
}
#veh-config .sidebar dl dt{
    width: 100%;
    height: 38px;
    line-height: 38px;
    color: #666666;
    border-bottom:1px solid #E1E1E1;
}
#veh-config .sidebar dl dt:hover{
    background: #32a5d9 !important;
    color: #ffffff !important;
    cursor: pointer;
}
#veh-config .sidebar dl dt:nth-last-of-type(1){
    border-bottom:none;
}
#veh-config .sidebarT ul{
    /*width: 100%;*/
    font-size: 14px;
    margin-top: 16px;
    margin-bottom: 16px;
    /*color: #31495c;*/
    margin-left: 148px;
    text-align: left;
    /*position: fixed;*/

}
#veh-config .sidebarT ul li{
    display: inline-block;
}
#veh-config .sidebarT ul li:nth-last-of-type(1){
    /*margin-left: 358px;*/
    margin-right: 14px;
}
#veh-config .sidebarTable{
    position: absolute;
    left: 146px;
    top: 30px;
    display: inline-block;
    vertical-align: top;
    /*margin-left: 30px;*/
    background: #f7f7f7;
}
#veh-config .sidebarTable table{
    /*width: 936px;*/
    width: 100%;    
    word-break: break-all;
    border:1px solid #E1E1E1;
    background: transparent;
}
#veh-config .sidebarTable img{
    margin:0 10px -2px;
    cursor: pointer;
}
#veh-config .sidebarTable table tr:nth-of-type(1){
    height: 30px;
    line-height: 30px;
}
#veh-config .sidebarTable table td{
    height: 30px;
    line-height: 30px;
    border-right:1px solid #e1e6eb;
    border-top: 1px solid #e1e6eb;
    text-align: right;
}
#veh-config .sidebarTable table tr:nth-of-type(1){
    width: 100%;
}
#veh-config .sidebarTable table tr td:nth-of-type(odd){
    padding-right: 10px;
    width: 210px;
}
#veh-config .sidebarTable table tr td:nth-of-type(even){
    width: 180px;
    padding-left: 10px;
    text-align: left;
    background: #ffffff;
}
/*************************/
/*table.fold{*/
    /*display: none !important;*/
/*}*/
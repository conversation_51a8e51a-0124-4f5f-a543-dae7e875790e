/*
    修改VIN码数据表格
*/
body{
    font-family: "微软雅黑";
    font-size: 12px;
}
/**************************/
.taskdeal{
    width: 100%;
    position: relative;
    /*text-align: center;*/
}

.taskdeal .sidebar{
    display: inline-block;
    vertical-align: top;
    position: fixed;
    top: 46px;
    left: 16px;
}
.taskdeal .sidebar dl{
    width: 120px;
    /*height: 466px;*/
    border:1px solid #E1E1E1;
    text-align: center;
}
.taskdeal .sidebar dl dt{
    width: 100%;
    height: 38px;
    line-height: 38px;
    color: #666666;
    border-bottom:1px solid #E1E1E1;
}
.taskdeal .sidebar dl dt:hover{
    background: #32a5d9 !important;
    color: #ffffff !important;
    cursor: pointer;
}
.taskdeal .sidebar dl dt:nth-last-of-type(1){
    border-bottom:none;
}
.taskdeal .sidebarT ul{
    /*width: 100%;*/
    font-size: 14px;
    margin-top: 16px;
    margin-bottom: 16px;
    /*color: #31495c;*/
    margin-left: 148px;
    text-align: left;
    /*position: fixed;*/

}
.taskdeal .sidebarT ul li{
    display: inline-block;
}
.taskdeal .sidebarT ul li:nth-last-of-type(1){
    /*margin-left: 358px;*/
    margin-right: 14px;
}
.taskdeal .sidebarTable{
    position: absolute;
    left: 0px;
    top: 5px;
    margin-top: 5px;
    margin-left: 10px;
    display: inline-block;
    vertical-align: top;
    /*margin-left: 30px;*/
    background: #f7f7f7;
    /*background: #ffffff;*/
}

/*.sidebarTable input[type=text]{
    width: 140px;
    height: 28px;
    padding-left: 10px;
    border-radius: 0px;
    border: 1px solid #ccc;
}*/

.sidebarTable input[type=password]{
    width: 200px;
    height: 28px;
    padding-left: 10px;
    border-radius: 0px;
    border: 1px solid #ccc;
}

.taskdeal .sidebarTable table{
    /*width: 936px;*/
    /*width: 100%;*/
    width: 980px;
    word-break: break-all;
    border:1px solid #E1E1E1;
    background: transparent;
    border-collapse: collapse;
}
.taskdeal .sidebarTable img{
    margin:0 10px -2px;
    cursor: pointer;
}
.taskdeal .sidebarTable table tr:nth-of-type(1){
    height: 30px;
    line-height: 30px;
}
.taskdeal .sidebarTable  table td{
    height: 30px;
    width: 204px;
    line-height: 0;
    border:1px solid #e1e6eb;
    text-align: center;
}
.taskdeal .sidebarTable  table tr th{
    border:1px solid #e1e6eb;
}

.taskdeal .sidebarTable #vcActive table td{
    height: 30px;
    line-height: 30px;
    border:1px solid #e1e6eb;
    text-align: center;
}

.taskdeal .sidebarTable table tr:nth-of-type(1){
    width: 100%;
}
.sidebarTable table.vcBase tr td:nth-of-type(odd){
    padding-right: 10px;
    /*width: 100px;*/
    width: 200px;
}
table.vcActive tr td{
    width: 200px;
}
.taskdeal .sidebarTable table tr td:nth-of-type(even){
width: 300px;
padding-left: 10px;
text-align: left;
background: #ffffff;
}
.veh-config-button {
width: 90px;
height: 40px;
line-height: 40px;
background: #00a0e9;
border: none;
color: #fff;
border-radius: 3px;
border-right:2px solid #e1e6eb;
border-top: 2px solid #e1e6eb;
cursor: pointer;
}
.checkBtn{
    width: 80px;
    height: 20px;
    line-height: 20px;
    border: none;
    margin-left: 10px;
    padding-left: 0;
    cursor: pointer;
    color: #fff;
    background: #00a0e9;
}
.taskdeal .sidebarTable p{
    display: inline-block;
}
.sidebarTable input[type="text"]{    
    width:200px;
    height:28px;
    padding-left: 10px;
    border: 1px solid #ccc;
    background: #fff;
}
/*************************/
/*table.fold{*/
    /*display: none !important;*/
/*}*/

/***********自定义样式---居中**************/
.btnCenter{
    text-align: center;
}

.table-tr-color{
    background-color: white;
}
.sidebarTable .finish{
    text-align: center;
    margin-top: 10px;
}

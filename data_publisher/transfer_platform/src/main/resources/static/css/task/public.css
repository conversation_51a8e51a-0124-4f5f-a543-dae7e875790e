html{
  font-size: 625%;
  -webkit-font-size: 625%;
}
body {
  margin: 0;
  font-size: 0.14rem;
  line-height: 0.14rem;
  min-height: 100%;
  background-color: #f6f5fb;
}
* {
  box-sizing: border-box;
}
.page {
  width: 100%;
}
.form-item-part{
  margin-top: 0.1rem;
  line-height: 0.5rem !important;
  font-size: 0.16rem;
  color: #666 !important;
}
.form-item-part input{
  color: #666;
}
.form-item-part label{
  border-bottom: 0.02rem solid #ee4837;
}
.item-label {
  /*width: 1.7rem;*/
  padding-left: 0.06rem;
  padding-right: 0.06rem;
  background: #fff;
}
.item-field-part{
  height: 0.5rem !important;
}
.item-field {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: 0.5rem;
}
.item-field-spec{
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}
.form-text {
  display: block;
  border: 0 none;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 0.1rem;
  padding-right: 0.2rem;
  text-align: right;
  outline: none;
  color: #999;
}
.form-text-spec{
  line-height: 0.2rem;
  min-height: 0.48rem;
  _height: 0.48rem;
  padding: 0.06rem;
  outline: none;
  word-wrap: break-word;
  word-break: break-all;
}
.form-item {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  line-height: 0.5rem;
  overflow: hidden;
  font-size: 0.16rem;
  color: #333;
  background: #FFFFFF;
  padding-left: 0.18rem;
  padding-right: 0.16rem;
}
.form-item:not(:first-of-type)::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  box-sizing: border-box;
  right: 0;
  height: 0;
  border-top: 0.01rem solid #e2e2e2;
}
.form-item-phone::after{
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  box-sizing: border-box;
  right: 0;
  height: 0;
  border-top: 0.01rem solid #dbdbdb;
  z-index: 10;
}
.item-field-select{
  background: url("../../icons/ico_arrowR.png") no-repeat right #fff;
}
.form-select{
  width: 100%;
  height: 100%;
  float: right;
  background: transparent;
  border: none;
  outline: none;
  direction: rtl;
  padding-right: 0.2rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.btnWrap{
  width: 100%;
  text-align: center;
  margin:0.08rem auto;
  background: #FFF;
}
.subBtn{
  width: calc(100% - 0.16rem);
  height: 0.5rem;
  border: none;
  margin: 0.08rem 0;
  border-radius: 0.06rem;
  background: #ee4838;
  color: #fff;
  font-size: 0.16rem;
}
/*****Impl-show*****/
#content{
  width: 100%;
  text-align: center;
}
.ulWrap{
  width: 100%;
  margin-top: 0.1rem;
}
.ulList{
  float: left;
}
.ulList li{
  line-height: 0.2rem;
  border-top: 0.01rem solid #dbdbdb;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.ulList li:nth-last-of-type(1){
  border-bottom: 0.01rem solid #DBDBDB;
}
.ulList-task{
  width: 50%;
}
.ulList-state{
  width: 20%;
}
.ulList-time{
  width: 30%;
}


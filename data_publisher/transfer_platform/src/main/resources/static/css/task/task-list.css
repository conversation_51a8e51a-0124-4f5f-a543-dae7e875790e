/*
	用户及接口表格
*/
body{
	font-size: 14px;
	/*min-width: 1080px;*/
	min-width: 980px;
}
input[type="text"]{
	width:120px;
	height:28px;
	padding-left: 10px;
	margin-left: 10px;
	border: 1px solid #ccc;
	background: #fff;
}
input[type="button"]{
	/*width: 120px;*/
	width: 90px;
	height: 30px;
	border: none;
}
.title-wrap{
	background: #fff;
	/*padding-top: 10px;*/
}
.title-wrap .title{
	padding: 10px 0;
	border-bottom: 1px solid #e1e6eb;
}
.title-wrap .userMsg{
	padding: 10px 0;
}
.title h3{
	display: inline-block;
	margin-right: 16px;
}
.title .addUser{
	background: #00a0e9;
	color: #fff;
}
.userMsg li{
	display: inline-block;
    /* margin-bottom: 10px; */
    /* vertical-align: top; */
    margin-right: 33px;
    height: 31px;
}
.userMsg li:nth-last-child(1){
	/*height: 88px;*/
	border-left: 1px solid #ccc;
}
.userMsg i{
	height: 100%;
	display: inline-block;
	vertical-align: middle;
}
.searchBtn{
	background: #00a0e9;
	display: inline-block;
	margin-bottom: 0 !important;
	color: #fff;
	margin: 0 20px;
	vertical-align: middle;
	cursor: pointer;
}
.checkGreyBtn{
	cursor: pointer;
	color: #333;
	background: #f0f0f0;
	border-radius: 0px;
}
.clearBtn{
	color: #00a0e9;
	display: inline-block;
	vertical-align: middle;
	cursor: pointer;
}
.partCenter{
	text-align: center;
}
li.partRight{
	text-align: right;
	padding-right: 10px;
}
.cycle-wrap ul{
	height: 40px;
	line-height: 40px;
	color: #333;
}
.cycle-wrap ul:nth-of-type(odd){
	/*background: #f9f9fb;*/
	background:#f9f9fa;
}
.cycle-wrap ul:nth-of-type(even){
	background: #fff;
	/*background:#E7D6D6;*/
	/*background: rgba(61, 155, 98, 0.1);*/
}
.aColor{
	color: black;
}
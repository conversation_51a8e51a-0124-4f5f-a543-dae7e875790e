body{
	min-width: 980px;
}

.checkWrap{
	width: 100%;
}
.checkList{
	/*text-align: center;*/
	margin-bottom: 16px;
}
.checkList .checkBtn{
	width: 80px;
/*	height: 20px;
	line-height: 20px;*/
	border: none;
/*	float: right;*/
	margin-left: 170px;
	padding-left: 0;
	cursor: pointer;
	color: #FFF;
	background: #00A0E9;
}
.checkList li{
	display: inline-block;
	font-size: 12px;
	margin-right: 5px;
}
.checkList li:nth-of-type(1){
	margin-left: 5%;
	/*float: left;*/
}
.checkList li:nth-last-of-type(1){
	/*width: 410px;*/
	text-align: left;
	margin-right: 10px;
	float: right;
	display: inline-block;
}
/*
 *
 *说
 *
 */
.insurance_table table {
    /*border-radius: 1px;
    background-color: #eaedf2;

    border-spacing: 0px;*/
	padding-right: 5px;
    width: 100%;
    font-size: 12px;
    color: #333;
    border-collapse: collapse;
}
.insurance_table thead th{
	/*font-weight: normal;*/
	color: black;
	font-weight: 600;
	font-size: 12px;
	border: 1px #eaedf2 solid;

}
.insurance_table th{
	height: 30px;
	/*background: #EAEDF1;*/
	/*background: #EAEDE0;*/
	color: #888;

	/*text-align: center;*/
	padding-left: 10px;
}
.insurance_table td{
	height: 40px;
	line-height: 40px;
	/*border-top: 1px solid #e1e6eb;*/
	font-size: 12px;
	text-align: center;
	padding-left: 10px;
	border: 1px #eaedf2 solid;
}
.insurance_table td:nth-of-type(1){
	text-align: center;
}
.insurance_table td:nth-last-of-type(1){
	text-align: center;
}
.insurance_table tbody tr:nth-child(odd){
	background: #f9f9fa;
}
.insurance_table tbody tr:nth-child(even){
	background: #fff;
	/*background: rgba(61, 155, 98, 0.1);*/
}
.insurance_table .ditailBtn input{
    width: auto;
    height: auto;
    line-height: 20px;
    border: none;
    background: #00a0e9;
    color: #fff;
    padding: 3px 20px;
    margin-right: 10px;
    cursor: pointer;
}
.selectStyle{
	width: 212px;
	height: 28px;
	border: 1px solid #ccc;
	padding-left: 10px;
}

.clearfloat{
	zoom:1;
}
.clearfloat:after{
	content: "";
	clear: both;
	height: 0
}
body {
	display: block;
	margin: 0;
	padding: 0;
    font-family: "Helvetica Neue", "Luxi Sans", "DejaVu Sans", <PERSON><PERSON><PERSON>, "Hiragino Sans GB", <PERSON><PERSON><PERSON><PERSON>, "Microsoft YaHei";
    font-size: 14px;
    line-height:22px;
}
h3{ 
	font-family: "Helvetica Neue", "Luxi Sans", "DejaVu Sans", <PERSON><PERSON><PERSON>, "Hiragino Sans GB", STHeiti, "Microsoft YaHei";
	}
h4{ 
	font-family: "Helvetica Neue", "Luxi Sans", "DejaVu Sans", <PERSON><PERSON><PERSON>, "Hiragino Sans GB", STHeiti, "Microsoft YaHei";
}
span{
	 font-family: "Helvetica Neue", "Luxi Sans", "DejaVu Sans", <PERSON><PERSON><PERSON>, "Hiragino Sans GB", STHeiti, "Microsoft YaHei"
}
b{
	font-weight: bold;
}
footer {
    width: 100%;
    padding-top: 50px;
    padding-bottom: 10px;
    text-align: center;
    background: #26292e;
    color: #FFFFFF;
}
.topbar{
	width: 100%;
	height: 56px;
	border-bottom: 1px solid #e1e1e1;
}
.top_pic_part{
	width: 100%;
	height: 120px;
	background: #008dd0;
}
.toppart_box{
	width: 100%;
	height: 100%;
	max-width: 1200px;
	margin: auto;
	background: url(../../icons/pic_business_toppart.jpg) no-repeat right; 
	color: #fff;
	padding:0 14px ;
}
.toppart_box h3{
	padding: 0;
	margin: 0;
	position: relative;
	top: 36px;	
}
.toppart_box span{
	padding: 0;
	margin: 0;
	color: #fff;
	position: relative;
	top:42px;
	font-size: 14px;
}
.content_contact{
	width: 100%;
	height: 520px;
	max-width: 1200px;
	min-width: 780px;
	margin: auto;
	padding:28px 14px ;
}
.contactbox{
	width:31%;
	height: 220px;
	float: left;
	border: 1px solid #e1e6eb;
	margin: 14px 28px 14px 0;
}
.contactbox h4{
	height: 30px;
	padding: 18px 16px;
	margin: 0;
	background: #ecf5fb;
	color: #008dd0;
	vertical-align: middle;
	line-height: 30px;
}
.contactbox span{
	display: block;
	font-size: 14px;
	color: #333;
	padding: 24px 16px;
}
.bankinfo{
	width: 30%;
	max-width: 380px;
	height: 470px;
	float: right;
	border: 1px solid #e1e6eb;
	background-color:#f5f6fa ;
	background:url(../../icons/pic_business_bankcard_bg.png) no-repeat right top  ,
				url(../../icons/pic_business_bankcard.png) no-repeat bottom ,
				url(../../icons/pic_business_bankcard_bg2.png) repeat ;
	margin: 14px 0;
}
.bankinfo h4{
	height: 30px;
	padding: 24px 16px;
	margin: 0;
	color: #333;
}
.bankinfo span{
	display: block;
	font-size: 14px;
	color: #333;
	padding: 24px 16px;
}

.bankinfo img{
	display: block;
	margin:auto;
}
.mapbox{
	width: 100%;
	height: 500px;
	position: relative;
	/**background: #888;**/
}
.map_adress{
	display: block;
	margin: auto;
	padding: 0 14px;
	width: 260px;
	position: absolute;
	left: 14%;
	z-index: 1;
}
.map_adress_info{
	background:#008DD0;
	max-width: 260px;
	min-width: 160px;
	height: 134px;
	color: #FFFFFF ;
	position: relative;
	top:60px;
}
.map_adress h3{
	height: 30px;
	padding: 24px 16px 12px 16px;
	margin: 0;	
}
.map_adress span{
	display: block;
	font-size: 14px;
	padding: 0px 16px;
}
#allmap {

	width: 100%;
	height: 100%;
	overflow: hidden;
	margin:0;
	font-family:"微软雅黑";
	}
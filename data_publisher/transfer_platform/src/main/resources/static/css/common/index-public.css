body{
	/*font-family: "微软雅黑";*/
	font-family: 'PingFangSC','helvetica neue','hiragino sans gb','arial','microsoft yahei ui','microsoft yahei','simsun','sans-serif'!important;
"Helvetica Neue", "Luxi Sans", "DejaVu Sans", <PERSON><PERSON><PERSON>, "Hiragino Sans GB", STHeiti, "Microsoft YaHei";
	font-size: 12px;
	overflow-x: hidden;
	overflow-y: auto;
}
.titleTip{
	border: 1px solid #e1e6eb;
	padding-top: 22px;
	margin-bottom: 16px;
	background: #FFF;
}
.titleTip h5{
	display: inline-block;
	color: #31495C;
}
.titleTip p{
	/*font-size: 12px;*/
	color: #333;
	margin-top: 10px;
}
.titleTip img{
	width: 4px;
	height: 16px;
	margin-left: 25px;
	margin-right: 15px;
	margin-bottom: -2px;
}
.pwrap{
	margin-top: 20px;
	margin-bottom: 35px;
	padding-left: 43px;
}
/***************tab***************/
.active_btn{
	width: 100px;
	height: 50px;
	/*font-family: "微软雅黑";*/
	font-family: 'PingFangSC','helvetica neue','hiragino sans gb','arial','microsoft yahei ui','microsoft yahei','simsun','sans-serif'!important;
"Helvetica Neue", "Luxi Sans", "DejaVu Sans", Tahoma, "Hiragino Sans GB", STHeiti, "Microsoft YaHei";
	font-size: 12px;				
	border-top: 2px solid #00a0e9;
	border-bottom: 1px solid #e1e1e1;
	border-left: 1px solid #e1e1e1;
	border-right: 1px solid #e1e1e1;
	background: #FFF;
	color: #333;
	cursor: pointer;
	outline: none;
}
.negative_btn{
	width: 100px;
	height: 50px;
	margin-left: -1px;
	/*font-family: "微软雅黑";*/
	font-family: 'PingFangSC','helvetica neue','hiragino sans gb','arial','microsoft yahei ui','microsoft yahei','simsun','sans-serif'!important;
"Helvetica Neue", "Luxi Sans", "DejaVu Sans", Tahoma, "Hiragino Sans GB", STHeiti, "Microsoft YaHei";
	font-size: 12px;				
	border: 1px solid #e1e1e1;
	background: #f7f7f7;
	color: #888888;
	cursor: pointer;
	outline: none;
}
.negative_btn:hover{
	color: #00a0e9;
	background:#ffffff;
	border-color:#e1e1e1;
}
/****************function****************/
.function-content{
    background: #FFF;
    padding-top: 1px;
    /*border: 1px solid #e1e1e1;*/
    margin-top: -1px;
}
.function-row{
	height: auto;
	/*border: 1px solid #e1e6eb;*/
	/*padding: 0px 18px;*/
	background: #f9fafc;
	margin: 12px;
	color: #333;
	/*border-top: 1px solid #e1e6eb;*/
}

.function-row p{
	line-height: 30px;
}
.function-row hr{
	border-top:1px dashed #e5e5e5;
	margin: 10px 0;
}

.function-row ul{
	padding-left: 10px;
}
.function-row .btn{
	width: 60px;
	height: 32px;
	line-height: 32px;
	text-align: center;
	background: #00a0e9;
	border-radius: 1px;
	color: #FFF;
	margin-right: 5px;
	cursor: pointer;
}
.function-row .veh-btn{
	padding-left: 0;
}
/*.function-row .veh-btn:hover{*/
	/*border:1px solid #32a5d9;*/
	/*color: #32a5d9;*/
/*}*/
.function-row select{
	width: 126px;
	height: 30px;
	border: 1px solid #e1e1e1;
	font-family: 'PingFangSC','helvetica neue','hiragino sans gb','arial','microsoft yahei ui','microsoft yahei','simsun','sans-serif'!important;
"Helvetica Neue", "Luxi Sans", "DejaVu Sans", Tahoma, "Hiragino Sans GB", STHeiti, "Microsoft YaHei";
	font-size: 12px;
	color: #333;
	margin-right: 5px;
	padding-left: 10px;
	outline: none;
}

.function-row .tips{
	color: #9d7437;
}

.function-row .tips img{
	margin-bottom: -2px;
	margin-right: 10px;
}
.searchBtn{
	background: #00a0e9;
	display: inline-block;
	margin-bottom: 0 !important;
	color: #fff;
	margin: 0 20px;
	vertical-align: middle;
	cursor: pointer;
	width: 90px;
	height: 30px;
	border: none;
}
.checkGreyBtn{
	cursor: pointer;
	color: #333;
	background: #f0f0f0;
	border-radius: 0px;
	width: 90px;
	height: 30px;
	border: none;
}


.powerBtn {
	min-width: 120px;
	max-width: 360px;
	height: 32px;
	border: 1px solid #e1e1e1;
	border-radius: 1px;
	/*font-family: "微软雅黑";*/
	font-family: 'PingFangSC','helvetica neue','hiragino sans gb','arial','microsoft yahei ui','microsoft yahei','simsun','sans-serif'!important;
"Helvetica Neue", "Luxi Sans", "DejaVu Sans", Tahoma, "Hiragino Sans GB", STHeiti, "Microsoft YaHei";
	font-size: 12px;
	color: #fff;
	background: #00a0e9;
	cursor: pointer;
	margin-right: 5px;
	padding-left: 10px;
	outline: none;
	vertical-align: middle;
}

.function-row input[type=checkbox] {
	min-width: 120px;
	max-width: 360px;
	border: 1px solid #e1e1e1;
	border-radius: 1px;
	/*font-family: "微软雅黑";*/
	font-family: 'PingFangSC','helvetica neue','hiragino sans gb','arial','microsoft yahei ui','microsoft yahei','simsun','sans-serif'!important;
"Helvetica Neue", "Luxi Sans", "DejaVu Sans", Tahoma, "Hiragino Sans GB", STHeiti, "Microsoft YaHei";
	font-size: 12px;
	color: #fff;
	/*background: #00a0e9;*/
	cursor: pointer;
	margin-right: 5px;
	padding-left: 10px;
	outline: none;
	vertical-align: middle;
}
.function-row input:focus,
.function-row select:focus{
	border: 1px solid rgba(50,165,217,0.4);
}
.function-row li{
	margin-right: 18px;
	display: inline-block;
	margin-left: 10px;
}
/*.function-row li:nth-of-type(even){*/
	/*margin-right: 18px;*/
/*}*/
.function-row li a{
	color: #0073d8;
	line-height: 30px;	
}
.function-row li span{
	line-height: 30px;
	margin-right: 20px;
}
.function-row .div-title{
	float: left;
	width: 100px;
	color: #888;
	height: 30px;
	/* margin-right: 50px;
	font-size: 12px; */
}
.function-row .div-content{
	margin-left: 100px;
}

.function-row .intfc-label{
	width: 90px;
	float: left;
	/*margin-bottom: 5px;*/
	margin-left: 10px;
	color: #333;
}
.function-row .intfc-lis{
	margin-left:90px;
	/*margin-bottom: 5px;*/
}


/***************************************/



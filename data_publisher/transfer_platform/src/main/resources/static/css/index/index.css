body{
    width: 100%;
    /*font-family: "微软雅黑";*/
    font-family: 'PingFangSC','helvetica neue','hiragino sans gb','arial','microsoft yahei ui','microsoft yahei','simsun','sans-serif'!important;
"Helvetica Neue", "Luxi Sans", "DejaVu Sans", <PERSON><PERSON><PERSON>, "Hiragino Sans GB", STHeiti, "Microsoft YaHei";
    font-size: 14px;
}
.navWrap{
    width: 100%;
    height: 56px;
    background: #FFF;
}
.nav{
    width: 1210px;
    margin: 0 auto;
}
.nav .nav_ul{
    line-height: 56px;
    /*margin-left: 100px;*/
    display: inline-block;
}
.nav ul li{
    margin-left: 58px;
    display: inline-block;
    width: 80px;
    text-align: center;
}
.nav ul.nav_function li:hover{
    background: #00A2EF;
    cursor: pointer;
}
.nav ul li:hover a{
    color: white;
    text-decoration: none;
}
.nav ul li > a{
    font-size: 14px;
    color: #31495c;
    /*font-weight: bold;*/
}
.nav ul li > a:hover{

}
.clearfloat:after {
    display: block;
    clear: both;
    content: "";
    visibility: hidden;
    height: 0
}
.clearfloat {
    zoom: 1
}
.logo{
    /*width: 156px;*/
    width: 256px;
    margin-top: 14px;
    float: left;
    display: inline-block;
}
.logo h1 a {
    display: block;
   /* width: 156px;*/
    width: 156px;
    height: 32px;
    background: url("../../icons/logo_jy_text.png") scroll no-repeat;
    text-indent: -9999em;
}
.nav_ul input{
    width: 82px;
    height: 24px;
    border: none;
    /*font-family: "微软雅黑";*/
    font-family: 'PingFangSC','helvetica neue','hiragino sans gb','arial','microsoft yahei ui','microsoft yahei','simsun','sans-serif'!important;
"Helvetica Neue", "Luxi Sans", "DejaVu Sans", Tahoma, "Hiragino Sans GB", STHeiti, "Microsoft YaHei";
    font-size: 12px;
    border-radius: 12px;
    outline: none;
}
.nav_ul .register{
    margin-left: 100px;
}
.nav_ul .register > input{
    background: #fff;
    color: #32a5d9;
    /*border: 1px solid #32A5D9;*/
    cursor:pointer;
}
.register > input:hover{
    background:#32A5D9;
    color:#fff;
}
.nav_ul .login{
    margin-left: 10px;
}
.nav_ul .login > input{
    background: rgba(50,165,217,0.85);
    color: #FFF;
    cursor:pointer;
}
.login > input:hover{
    background:#32A5D9;
    color:#ffffff;
    border:1px solid #32A5D9;
}
/************轮播图*************/
/*.banner{*/
    /*width: 100%;*/
    /*height: 420px;*/
    /*background: #32A5D9;*/
/*}*/
/************content************/
.content{
    width: 720px;
    margin: 0 auto;
    padding-top: 80px;
}
.content_text{
    text-align: center;
}
.content_text p{
    margin-bottom: 20px;
}
.content_text p:nth-of-type(1){
    font-size: 16px;
    color: #1f394e;
    font-weight: bold;
}
.content_text p:nth-of-type(2){
    color: #666666;
}
.content_data ul:nth-of-type(1){
    margin-top: 40px;
    margin-bottom: 55px;
}
.content_data ul:nth-last-child(1){
    margin-bottom: 40px;
}
.content_data ul li{
    display: inline-block;
}
.content_data ul li:nth-child(2){
    margin-left: 206px;
}
/*.content_data img{
    width: 254px;
    height: 158px;
}*/
/************content_service***********/
.content_bootom{
    width: 100%;
    height: 464px;
    background: #f9f9f9;
}
.content_bootom .content_service{
    width: 880px;
    margin: 0 auto;
    text-align: center;
    padding-top: 35px;
}
.content_service p:nth-of-type(1){
    font-size: 16px;
    color: #1f394e;
    font-weight: bold;
    margin-bottom: 15px;
}
.content_service p:nth-of-type(2){
    color: #666666;
    margin-bottom: 30px;
}
.content_service ul li{
    display: inline-block;
}
.content_service ul li:nth-of-type(2){
    margin-left: 92px;
    margin-right: 78px;
}
.content_service ul li:nth-of-type(4){
    margin-left: 96px;
}
.content_partners{
    padding-top: 30px;
    padding-bottom: 24px;
    text-align: center;
}
.content_partners p{
	font-size: 16px;
	color: #1f394e;
	font-weight: bold;
	margin-bottom: 20px;
}
/*首页图片样式**/
.index_img_style{
    margin-left: -20px;
    margin-top: -50px;
    height: 450px;
    width: 500px;
}

/**************login************/
.loginWrap{
    width: 960px;
    margin: 120px auto;
}
#loginWrap{
    width: 322px;
    text-align: left;
    margin-left: auto;
    float: right;
    margin-right: 80px;
    border-top: 1px solid transparent;
}
#imgPic{
    width: 510px;
    height: 330px;
    float: left;
}
.login_item{
    width: 290px;
    height: 46px;
    line-height: 40px;
    /*border: 1px solid #ececec;*/
    /*background: #f7f7f7;*/
    border-radius: 3px;
    border: none;
    padding-left: 30px;
    /*margin-top: 22px;*/
    display: block;
    /*font-family: "微软雅黑";*/
    font-family: 'PingFangSC','helvetica neue','hiragino sans gb','arial','microsoft yahei ui','microsoft yahei','simsun','sans-serif'!important;
"Helvetica Neue", "Luxi Sans", "DejaVu Sans", Tahoma, "Hiragino Sans GB", STHeiti, "Microsoft YaHei";
    outline: none;
}
.login_item:focus {
    border: 1px solid rgba(50,165,217,0.4);
}
.right_bottom{
    margin-top: 10px;
}
.login_btn{
    width: 320px;
    height: 40px;
    border: none;
    background: #5f84e0;
    color: #FFF;
    /*font-family: "微软雅黑";*/
    font-family: 'PingFangSC','helvetica neue','hiragino sans gb','arial','microsoft yahei ui','microsoft yahei','simsun','sans-serif'!important;
"Helvetica Neue", "Luxi Sans", "DejaVu Sans", Tahoma, "Hiragino Sans GB", STHeiti, "Microsoft YaHei";
    font-size: 14px;
    margin-top: 20px;
/*     margin-bottom: 22px; */
    border-radius: 3px;
}
.login_span{
    display: block;
    color: #31495C;
    text-align: center;
}
.login_span a{
    color: #31495C;
}
.login_spanS{
    color: #e5e5e5;
}
.login_title{
    width: 100%;
    /*height: 36px;*/
    font-weight: 600;
    margin-bottom: 10px;
    text-align: center;
    line-height: 50px;
 /*   padding-left: 24px;
    border-left: 4px solid #32A5D9;*/
}
.login_title span{
    font-size: 23px;
    color: #5e83e0;
    line-height: 37px;
    vertical-align: middle;
    height: 100%;
}
.login_title img{
    margin-right: 20px;
    vertical-align: middle;

}
.login_text .input_wrap span{
    display: block;
}
.login_text .input_wrap{
    margin-top: 20px;
}
.login_text .input_wrap .innnerInput{
    border: 1px solid #ececec;
    position: relative;
}
.input_wrap .innnerInput img{
    display: flex;
    position: absolute;
    width: 15px;
    height: 15px;
    top: 50%;
    margin-left: 5px;
    /*-webkit-transition: all .3s;*/
    transform: translateY(-50%);
    /*height: 100%;*/
    color: #c0c4cc;
    text-align: center;
}
.input_wrap .innnerInput span{
    display: inline-block;
}
.errors{
    color: red;
}

/**轮播图样式*/
#container {
    width: 100%;
    height: 420px;
    overflow: hidden;
}
#container,.sections {
    position: relative;
}
.section {
    background-position: 50% 50%;
    text-align: center;
    color: white;
}

#sectionsWrap  { position: relative; overflow: hidden; width: 100%;height: 100%; margin: 0 auto;}
/*.section  { position: absolute; left: 0; top: 0; width: 100%; height: 100%;}*/
/*****登陆后个人中心*******/
.liSev{
    line-height: 56px;
    text-align: right;
}
.personCenter{
    width: 302px;
    height: 0;
    position: relative;
    z-index: 10;
    overflow: hidden;
    border: 1px solid #e1e1e1;
    float: right;
    /*margin-right: -20px;*/
    transition:width .5s ;
    -webkit-transition:height .5s;
    margin-bottom: -5000px;
}
.personCenter .personCenter_top{
    width: 100%;
    height: 120px;
    overflow: hidden;
    background: #F7F7F7;
    border-bottom: 1px solid #e1e1e1;
}
.personCenter_top ul{
    width: 100%;
    margin-left: 30px;
    margin-top: 14px;
    color: #616c7e;
}
 .personCenter_top ul li{
    margin-left: 0;
    line-height: 14px ;
    width: initial;
}
.personCenter_top ul li img{
    margin-bottom: -6px;
    margin-right: 10px;
}
 .personCenter_bot ul li{
    margin-left: 0;
    width: initial;
}
.personCenter_bot ul li input{
    width: 150px;
    height: 50px;
    background: #FFF;
    border: none;
    /*font-family: "微软雅黑";*/
    font-family: 'PingFangSC','helvetica neue','hiragino sans gb','arial','microsoft yahei ui','microsoft yahei','simsun','sans-serif'!important;
"Helvetica Neue", "Luxi Sans", "DejaVu Sans", Tahoma, "Hiragino Sans GB", STHeiti, "Microsoft YaHei";
    font-size: 14px;
    color: #31495C;
    cursor: pointer;
}
/********图形验证码********/
.right_bottom input{
	height: 38px;
    padding-left: 20px;
    font-size: 14px;
    color: #a9a9a9;
    border: 1px solid #dddddd;
    width: 120px;
    float: left !important;
    display: inline-block;
    margin-top: 10px;
}
.right_bottom .authCode{
	width: auto;
	height: 36px;
	float: right !important;
	display: inline-block;
	margin-bottom: 0;
	margin-top: 10px;
}
.authCode a{
	line-height: 36px;
	float: right;
	margin-left: 5px;
	margin-top: 10px;
}
.authCode img{
	/*width: 68px;
	height: 34px;*/
	border: 1px solid #dddddd;
}
.right_bottom .authCode a{
	font-size: 12px;
	line-height: 36px;
	color: #698ee6;
}

.clearfloat{
    zoom:1;
}
.clearfloat:after{
    content: "";
    clear: both;
    height: 0
}
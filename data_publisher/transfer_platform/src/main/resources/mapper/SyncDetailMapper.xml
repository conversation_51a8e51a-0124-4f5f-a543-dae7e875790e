<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jy.mapper.SyncDetailMapper">
	<select id="listSyncDetail" resultType="com.jy.bean.po.SyncDetail">
		SELECT
		p.clzlid
		FROM
		sync_detail p
		<where>
			1 = 1
			<if test="_parameter.containsKey('mainBatchNo') and mainBatchNo != null">
				AND p.main_batch_no = #{mainBatchNo}
			</if>
			<if test="_parameter.containsKey('status') and status != null">
				AND p.status = #{status}
			</if>
			<if test="_parameter.containsKey('tableName') and tableName != null">
				AND p.table_name =#{tableName}
			</if>
		</where>
	</select>

	<sql id="Base_Column_List">
		ID,
		CLZLID,
		TABLE_NAME,
		PPBM,
		MAIN_BATCH_NO,
		STATUS,
		C_TIME,
		U_TIME,
		DEL_FLAG,
		C_BY,
		U_BY,
		REMARK
	</sql>

	<insert id="save" parameterType="com.jy.bean.po.SyncDetail">
		insert into sync_detail (<include refid="Base_Column_List"/>)
		values (
			#{id,jdbcType=VARCHAR},
			#{clzlid,jdbcType=VARCHAR},
			#{tableName,jdbcType=VARCHAR},
			#{ppbm,jdbcType=VARCHAR},
			#{mainBatchNo,jdbcType=VARCHAR},
			#{status,jdbcType=VARCHAR},
			systimestamp,
			systimestamp,
			#{delFlag,jdbcType=VARCHAR},
			#{cBy,jdbcType=VARCHAR},
			#{uBy,jdbcType=VARCHAR},
			#{remark,jdbcType=VARCHAR}
		)
	</insert>

	<update id="update" parameterType="com.jy.bean.po.SyncDetail">
		update sync_detail
		<set>
			<if test="status != null">
				status = #{status,jdbcType=VARCHAR},
			</if>
			<if test="remark != null">
				remark = #{remark,jdbcType=VARCHAR},
			</if>
			u_time = systimestamp
		</set>
		where main_batch_no = #{mainBatchNo,jdbcType=VARCHAR}
	</update>

</mapper>
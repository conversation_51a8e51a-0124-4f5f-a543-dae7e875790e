<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jy.mapper.SendTableMapper">

	<select id="listBatchState" resultType="com.jy.bean.po.SendTable">
		SELECT
		a.MAIN_VERSION_CODE,
		a.STATE,
		b.VERSION_CODE ,
		b.HANDLE_TABLE,
		b.STATE as versionState
		FROM
		RECEIVE_GROUP_DATA a,
		SEND_TABLE b
		<where>
			a.MAIN_VERSION_CODE = b.MAIN_VERSION_CODE
			<if test="_parameter.containsKey('mainVersionCode') and mainVersionCode != null and mainVersionCode != ''">
				and a.MAIN_VERSION_CODE = #{mainVersionCode}
			</if>
		</where>
	</select>

	<sql id="Base_Column_List">
		ID,
		MAIN_VERSION_CODE,
		VERSION_CODE,
		SEND_TABLE,
		STATE,
		HANDLE_TABLE,
		CREATE_DATE,
		SEND_CODE
	</sql>

	<insert id="insert" parameterType="com.jy.bean.po.SendTable">
		INSERT INTO SEND_TABLE(<include refid="Base_Column_List"/>)
		VALUES (
		#{id,jdbcType=VARCHAR},
		#{mainVersionCode,jdbcType=VARCHAR},
		#{versionCode,jdbcType=VARCHAR},
		#{sendTable,jdbcType=VARCHAR},
		#{state,jdbcType=VARCHAR},
		#{handleTable,jdbcType=VARCHAR},
		SYSDATE,
		#{sendCode,jdbcType=VARCHAR}
		)
	</insert>

	<select id="listSendTable" resultType="com.jy.bean.po.SendTable">
		select
		ID,
		MAIN_VERSION_CODE,
		VERSION_CODE,
		SEND_TABLE,
		STATE,
		HANDLE_TABLE,
		CREATE_DATE,
		SEND_CODE
		from SEND_TABLE
		<where>
			<if test="_parameter.containsKey('mainVersionCodes') and mainVersionCodes != null and mainVersionCodes != ''">
				and MAIN_VERSION_CODE in ${mainVersionCodes}
			</if>
		</where>
	</select>


</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jy.mapper.DataStatisticsMapper">

	<insert id="transformationData" parameterType="map">
		INSERT INTO DATA_STATISTICS(ID,czid,zcjsl,zppsl,zcxxsl,zczsl,zcxsl,zlpcbsl,zppbsbsl,zcxxbsbsl,zczbsbsl,zcxbsbsl,
			cjsl,ppsl,cxxsl,czsl,cxsl,lpcbsl,cxljsl,cxljxssl,czhcmsl,cxbzjsl,cxfbzjsl,cxtpsl,cxtprdsl,czljsl,czbzjsl,
		    czfbzjsl,ppljsl,ppbzjsl,ppfbzj,ppbsbsl,cxxbsbsl,czbsbsl,cxbsbsl,data_type,add_time,receive_date,xytpsl,cztpzl,czljzl,group_part_num,brand_part_num)
		select
		      sys_guid(),#{groupId},
			sum(case when type = '1' then num else 0 end) zcjsl
			 ,sum(case when type = '2' then num else 0 end) zppsl
			 ,sum(case when type = '3' then num else 0 end) zcxxsl
			 ,sum(case when type = '4' then num else 0 end) zczsl
			 ,sum(case when type = '5' then num else 0 end) zcxsl
			 ,sum(case when type = '6' then num else 0 end) zlpcbsl
			 ,sum(case when type = '7' then num else 0 end) zppbsbsl
			 ,sum(case when type = '8' then num else 0 end) zcxxbsbsl
			 ,sum(case when type = '9' then num else 0 end) zczbsbsl
			 ,sum(case when type = '10' then num else 0 end) zcxbsbsl
			 ,sum(case when type = '11' then num else 0 end) cjsl
			 ,sum(case when type = '12' then num else 0 end) ppsl
			 ,sum(case when type = '13' then num else 0 end) cxxsl
			 ,sum(case when type = '14' then num else 0 end) czsl
			 ,sum(case when type = '15' then num else 0 end) cxsl
			 ,sum(case when type = '16' then num else 0 end) lpcbsl
			 ,sum(case when type = '17' then num else 0 end) cxljsl
			 ,sum(case when type = '18' then num else 0 end) cxljxssl
			 ,sum(case when type = '19' then num else 0 end) czhcmsl
			 ,sum(case when type = '20' then num else 0 end) cxbzjsl
			 ,sum(case when type = '21' then num else 0 end) cxfbzjsl
			 ,sum(case when type = '22' then num else 0 end) cxtpsl
			 ,sum(case when type = '23' then num else 0 end) cxtprdsl
			 ,sum(case when type = '24' then num else 0 end) czljsl
			 ,sum(case when type = '25' then num else 0 end) czbzjsl
			 ,sum(case when type = '26' then num else 0 end) czfbzjsl
			 ,sum(case when type = '27' then num else 0 end) ppljsl
			 ,sum(case when type = '28' then num else 0 end) ppbzjsl
			 ,sum(case when type = '29' then num else 0 end) ppfbzj
			 ,sum(case when type = '30' then num else 0 end) ppbsbsl
			 ,sum(case when type = '31' then num else 0 end) cxxbsbsl
			 ,sum(case when type = '32' then num else 0 end) czbsbsl
			 ,sum(case when type = '33' then num else 0 end) cxbsbsl,'2',sysdate,#{date,jdbcType=VARCHAR}
			 ,sum(case when type = '34' then num else 0 end) xytpsl
			 ,sum(case when type = '35' then num else 0 end) cztpzl
			 ,sum(case when type = '36' then num else 0 end) czljzl
			 ,sum(case when type = '37' then num else 0 end) groupPartNum
			 ,sum(case when type = '38' then num else 0 end) brandPartNum
		from (
			 SELECT count(1) num, '1' type
			 FROM f_zc_qccjxxb
			 UNION ALL
			 SELECT count(1), '2'
			 FROM f_zc_clppxxb
			 UNION ALL
			 SELECT count(1), '3'
			 FROM f_zc_cxxxb
			 UNION ALL
			 SELECT count(1), '4'
			 FROM f_zc_clfzxxb
			 UNION ALL
			 SELECT count(1), '5'
			 FROM f_zc_clzlb
			 UNION ALL
			 SELECT count(1), '6'
			 FROM f_pj_zc_cxdyb
			 UNION ALL
			 SELECT count(1), '7'
			 FROM f_zc_clppxxb_flag
			 UNION ALL
			 SELECT count(1), '8'
			 FROM f_zc_cxxxb_flag
			 union ALL
			 SELECT count(1), '9'
			 from f_zc_clfzxxb_flag
			 UNION ALL
			 SELECT count(1), '10'
			 FROM f_zc_clzlb_flag
			 UNION ALL
			 SELECT 1 num, '11' type
			 FROM dual
			 UNION ALL
			 SELECT 1, '12'
			 FROM dual
			 UNION ALL
			 SELECT 1, '13'
			 FROM dual
			 UNION ALL
			 SELECT 1, '14'
			 FROM dual
			 UNION ALL
			 SELECT count(1), '15'
			 FROM f_zc_clzlb where zbid = #{groupId}
			 UNION ALL
			 SELECT count(1), '16'
			 FROM f_pj_zc_cxdyb WHERE pjcxid in (SELECT id FROM f_zc_clzlb where zbid = #{groupId})
			 UNION ALL
			 SELECT count(1),'17' FROM (select * from (SELECT trim(CLZLID) CLZLID,trim(YCLJH) YCLJH
			 FROM f_pj_cllbjdyb_${brandCode} f1 left join F_ZC_CLZLB f2 on f1.clzlid = f2.id WHERE f2.zbid = #{groupId})
			 GROUP BY CLZLID,YCLJH)
			 UNION ALL
			 SELECT count(1),'18' FROM (select * from (SELECT trim(CLZLID) CLZLID,trim(YCLJH) YCLJH
			 FROM f_pj_cllbjdyb_${brandCode}  f1 left join F_ZC_CLZLB f2 on f1.clzlid = f2.id WHERE f2.zbid = #{groupId} AND sfyc = '0')
			 GROUP BY CLZLID,YCLJH)
			 UNION ALL
			 SELECT count(1),'19' FROM (select  * from (SELECT trim(CLZLID) CLZLID,trim(YCLJH) YCLJH
			 FROM f_pj_cllbjdyb_${brandCode} f1 left join F_ZC_CLZLB f2 on f1.clzlid = f2.id WHERE f2.zbid = #{groupId} AND inc_code is not null AND trim(inc_code) is not null)
			 GROUP BY CLZLID,YCLJH)
			 UNION ALL
			 SELECT count(1), '20'
								   FROM (select  * from (SELECT trim(CLZLID) CLZLID,trim(YCLJH) YCLJH, trim(ljbzbm) ljbzbm FROM f_pj_cllbjdyb_${brandCode} f1 left join F_ZC_CLZLB f2 on f1.clzlid = f2.id WHERE f2.zbid = #{groupId} AND ljbzbm != '999999')
				   GROUP BY CLZLID,YCLJH, ljbzbm)
			 UNION ALL
			 SELECT count(1), '21'
			 FROM (select * from (SELECT trim(CLZLID) CLZLID,trim(YCLJH) YCLJH FROM f_pj_cllbjdyb_${brandCode} f1 left join F_ZC_CLZLB f2 on f1.clzlid = f2.id WHERE f2.zbid = #{groupId} and ljbzbm = '999999')
				   GROUP BY CLZLID,YCLJH)
			 UNION ALL
			 SELECT count(1), '22' FROM
				 (select * from (SELECT trim(clzlid) clzlid,trim(ycth) ycth FROM f_pj_clljtxdyb_${brandCode} f1 left join F_ZC_CLZLB f2 on f1.clzlid = f2.id WHERE f2.zbid = #{groupId}) GROUP BY clzlid,ycth)
			 UNION ALL
			 SELECT count(1), '23' FROM
				 (select * from (SELECT trim(clzlid) clzlid,trim(ycth) ycth,trim(ycxh) ycxh FROM f_pj_clljtxdyb_${brandCode} f1 left join F_ZC_CLZLB f2 on f1.clzlid = f2.id WHERE f2.zbid = #{groupId}) GROUP BY clzlid,ycth,ycxh)
			 UNION ALL
			 SELECT count(distinct ycljh), '24'
			 FROM f_pj_czlbjdyb_${brandCode} where czid = #{groupId}
			 UNION ALL
			 SELECT count(1), '25'
			 FROM (select * from (SELECT trim(ycljh) ycljh, trim(ljbzbm) ljbzbm FROM f_pj_czlbjdyb_${brandCode} where ljbzbm != '999999' and czid = #{groupId}) GROUP BY ycljh, ljbzbm)
			 UNION ALL
			 SELECT count(DISTINCT ycljh), '26'
			 FROM f_pj_czlbjdyb_${brandCode}
			 where ljbzbm = '999999' AND czid = #{groupId}
			 UNION ALL
			 SELECT count(DISTINCT ycljh), '27'
			 FROM f_pj_pplbjdyb where ppid = #{brandId}
			 UNION ALL
			 SELECT count(1), '28'
			 FROM (select * from (SELECT trim(ycljh) ycljh, trim(ljbzbm) ljbzbm FROM f_pj_pplbjdyb where ljbzbm != '999999' AND ppid = #{brandId}) GROUP BY ycljh, ljbzbm)
			 UNION ALL
			 SELECT count(DISTINCT ycljh), '29'
			 FROM f_pj_pplbjdyb
			 where ljbzbm = '999999' AND ppid = #{brandId}
			 UNION ALL
			 SELECT count(1), '30'
			 FROM f_zc_clppxxb_flag where brand_id = #{brandId}
			 UNION ALL
			 SELECT count(1), '31'
			 FROM f_zc_cxxxb_flag WHERE SERIES_ID = #{seriesId}
			 union ALL
			 SELECT count(1), '32'
			 from f_zc_clfzxxb_flag WHERE group_id = #{groupId}
			 UNION ALL
			 SELECT count(1), '33'
			 FROM f_zc_clzlb_flag f LEFT JOIN f_zc_clzlb fz ON f.id = FZ.id WHERE FZ.zbid = #{groupId}
			 UNION ALL
			 SELECT count(1), '34' FROM
			 (select * from (SELECT trim(clzlid) clzlid,trim(ycth) ycth,trim(ycxh) ycxh,trim(XZB) x,trim(YZB) y  FROM f_pj_clljtxdyb_${brandCode} f1 left join F_ZC_CLZLB f2 on f1.clzlid = f2.id WHERE f2.zbid = #{groupId} AND xzb is not null and yzb is not null) GROUP BY clzlid,ycth,ycxh,x,y)
			 UNION ALL
			 SELECT count(1),'35'  FROM f_pj_clljtxdyb_${brandCode} f1 left join F_ZC_CLZLB f2 on f1.clzlid = f2.id WHERE f2.zbid = #{groupId}
			 UNION ALL
			 SELECT count(1),'36'  FROM f_pj_cllbjdyb_${brandCode} f1 left join F_ZC_CLZLB f2 on f1.clzlid = f2.id WHERE f2.zbid = #{groupId}
			 UNION ALL
			 SELECT count(1),'37'  FROM f_pj_czlbjdyb_${brandCode} f1 WHERE f1.czid = #{groupId}
			 UNION ALL
			 SELECT count(1),'38'  FROM f_pj_pplbjdyb f1 WHERE f1.PPID = #{brandId}
		 ) a
	</insert>

	<select id="listDataStatistics" resultType="com.jy.bean.dto.DataStatisticsDto" parameterType="map">
		select  ds.*,r.zbmc groupName from DATA_STATISTICS ds left join f_zc_clfzxxb r on ds.czid = r.id where 1 = 1
		<if test="date != null and date != ''">
			AND ds.receive_date = #{date}
		</if>
		<if test="groupId != null and groupId != ''">
			AND ds.czid = #{groupId}
		</if>
		order by data_type asc
	</select>

	<select id="getCTableDataStaticticsList" parameterType="map" resultType="map">
		SELECT count(1) num,state,'cjsl' type FROM c_zc_qccjxxb WHERE main_code in <foreach collection="list" item="item" separator="," open="(" close=")">#{item.mainVersionCode}</foreach>  GROUP BY state
		UNION ALL
		SELECT count(1) num,state,'ppsl'
		FROM c_zc_clppxxb WHERE main_code in <foreach collection="list" item="item" separator="," open="(" close=")">#{item.mainVersionCode}</foreach> GROUP BY state
		UNION ALL
		SELECT count(1) num,state,'cxxsl'
		FROM c_zc_cxxxb WHERE main_code in <foreach collection="list" item="item" separator="," open="(" close=")">#{item.mainVersionCode}</foreach> GROUP BY state
		UNION ALL
		SELECT count(1) num,state,'czsl'
		FROM c_zc_clfzxxb WHERE main_code in <foreach collection="list" item="item" separator="," open="(" close=")">#{item.mainVersionCode}</foreach> GROUP BY state
		UNION ALL
		SELECT count(1) num,state,'cxsl'
		FROM c_zc_clzlb where main_code in <foreach collection="list" item="item" separator="," open="(" close=")">#{item.mainVersionCode}</foreach> GROUP BY state
		UNION ALL
		SELECT count(1), state,'lpcbsl'
		FROM c_pj_zc_cxdyb WHERE main_code in <foreach collection="list" item="item" separator="," open="(" close=")">#{item.mainVersionCode}</foreach> GROUP BY state
		UNION ALL
		SELECT count(1),state,'cxljsl' FROM (SELECT CLZLID,YCLJH,state
		FROM c_pj_cllbjdyb_${brandCode} WHERE main_code in <foreach collection="list" item="item" separator="," open="(" close=")">#{item.mainVersionCode}</foreach>
		GROUP BY CLZLID,YCLJH,state) GROUP BY state
		UNION ALL
		SELECT count(1),state,'cxljxssl' FROM (SELECT CLZLID,YCLJH,state
		FROM c_pj_cllbjdyb_${brandCode} WHERE main_code in <foreach collection="list" item="item" separator="," open="(" close=")">#{item.mainVersionCode}</foreach> and sfyc_new = '0'
		GROUP BY CLZLID,YCLJH,state) GROUP BY state
		UNION ALL
		SELECT count(1),state,'czhcmsl' FROM (SELECT CLZLID,YCLJH,state
		FROM c_pj_cllbjdyb_${brandCode} WHERE main_code in <foreach collection="list" item="item" separator="," open="(" close=")">#{item.mainVersionCode}</foreach> and inc_code_new is not null
		GROUP BY CLZLID,YCLJH,state) GROUP BY state
		UNION ALL
		SELECT count(1),state,'cxbzjsl' FROM (SELECT CLZLID,YCLJH,state,ljbzbm
		FROM c_pj_cllbjdyb_${brandCode} WHERE main_code in <foreach collection="list" item="item" separator="," open="(" close=")">#{item.mainVersionCode}</foreach> and ljbzbm != '999999'
		GROUP BY CLZLID,YCLJH,state,ljbzbm) GROUP BY state
		UNION ALL
		SELECT count(1),state,'cxfbzjsl' FROM (SELECT CLZLID,YCLJH,state
		FROM c_pj_cllbjdyb_${brandCode} WHERE main_code in <foreach collection="list" item="item" separator="," open="(" close=")">#{item.mainVersionCode}</foreach> and ljbzbm = '999999'
		GROUP BY CLZLID,YCLJH,state) GROUP BY state
		UNION ALL
		SELECT count(1),state,'cxtpsl' FROM
			(SELECT clzlid,ycth,state FROM c_pj_clljtxdyb_${brandCode} WHERE main_code in <foreach collection="list" item="item" separator="," open="(" close=")">#{item.mainVersionCode}</foreach> GROUP BY clzlid,ycth,state) GROUP BY state
		UNION ALL
		SELECT count(1),state,'cxtprdsl' FROM
			(SELECT clzlid,ycth,ycxh,state FROM c_pj_clljtxdyb_${brandCode} WHERE main_code in <foreach collection="list" item="item" separator="," open="(" close=")">#{item.mainVersionCode}</foreach> GROUP BY clzlid,ycth,ycxh,state) GROUP BY state
		UNION ALL
		SELECT count(1), state,'czljsl' FROM (
		SELECT ycljh,state FROM c_pj_czlbjdyb_${brandCode} where main_code in <foreach collection="list" item="item" separator="," open="(" close=")">#{item.mainVersionCode}</foreach> GROUP BY ycljh,state)
		GROUP BY state
		UNION ALL
		SELECT count(1), state,'czbzjsl' FROM (
		SELECT ycljh,state,ljbzbm FROM c_pj_czlbjdyb_${brandCode} where main_code in <foreach collection="list" item="item" separator="," open="(" close=")">#{item.mainVersionCode}</foreach> and ljbzbm != '999999' GROUP BY ycljh,state,ljbzbm)
		GROUP BY state
		UNION ALL
		SELECT count(1), state,'czfbzjsl' FROM (
		SELECT ycljh,state FROM c_pj_czlbjdyb_${brandCode} where main_code in <foreach collection="list" item="item" separator="," open="(" close=")">#{item.mainVersionCode}</foreach> and ljbzbm = '999999' GROUP BY ycljh,state)
		GROUP BY state
		UNION ALL
		SELECT count(1), state,'ppljsl' FROM (
		select state,ycljh_new FROM c_pj_pplbjdyb where main_code in <foreach collection="list" item="item" separator="," open="(" close=")">#{item.mainVersionCode}</foreach> GROUP BY state,ycljh_new)
		GROUP BY state
		UNION ALL
		SELECT count(1), state,'ppbzjsl' FROM (
		select state,ycljh_new,ljbzbm_new FROM c_pj_pplbjdyb where main_code in <foreach collection="list" item="item" separator="," open="(" close=")">#{item.mainVersionCode}</foreach> AND ljbzbm_new != '999999' GROUP BY state,ycljh_new,ljbzbm_new)
		GROUP BY state
		UNION ALL
		SELECT count(1), state,'ppfbzj' FROM (
		select state,ycljh_new FROM c_pj_pplbjdyb where main_code in <foreach collection="list" item="item" separator="," open="(" close=")">#{item.mainVersionCode}</foreach> AND ljbzbm_new = '999999' GROUP BY state,ycljh_new)
		GROUP BY state
		UNION ALL
		SELECT count(1), state,'ppbsbsl' FROM c_zc_clppxxb_flag where main_code in <foreach collection="list" item="item" separator="," open="(" close=")">#{item.mainVersionCode}</foreach> GROUP BY state
		UNION ALL
		SELECT count(1), state,'cxxbsbsl'
		FROM c_zc_cxxxb_flag WHERE main_code in <foreach collection="list" item="item" separator="," open="(" close=")">#{item.mainVersionCode}</foreach> GROUP BY state
		union ALL
		SELECT count(1), state,'czbsbsl'
		from c_zc_clfzxxb_flag WHERE main_code in <foreach collection="list" item="item" separator="," open="(" close=")">#{item.mainVersionCode}</foreach> GROUP BY state
		UNION ALL
		SELECT count(1), state,'cxbsbsl'
		FROM c_zc_clzlb_flag WHERE main_code in <foreach collection="list" item="item" separator="," open="(" close=")">#{item.mainVersionCode}</foreach> GROUP BY state
	</select>

	<insert id="insertDataStatisticsBatch" parameterType="list">
		insert into DATA_STATISTICS
		<foreach collection="list" item="item" separator=" union all ">
			select sys_guid(),#{item.czid,jdbcType=VARCHAR},#{item.zcjsl,jdbcType=VARCHAR},#{item.zppsl,jdbcType=VARCHAR},#{item.zcxxsl,jdbcType=VARCHAR},#{item.zczsl,jdbcType=VARCHAR},#{item.zcxsl,jdbcType=VARCHAR},#{item.cjsl,jdbcType=VARCHAR},#{item.ppsl,jdbcType=VARCHAR},#{item.cxxsl,jdbcType=VARCHAR},#{item.czsl,jdbcType=VARCHAR},#{item.cxsl,jdbcType=VARCHAR},#{item.lpcbsl,jdbcType=VARCHAR},#{item.cxljsl,jdbcType=VARCHAR},#{item.cxljxssl,jdbcType=VARCHAR},#{item.czhcmsl,jdbcType=VARCHAR},#{item.cxbzjsl,jdbcType=VARCHAR},#{item.cxfbzjsl,jdbcType=VARCHAR},#{item.cxtpsl,jdbcType=VARCHAR},#{item.cxtprdsl,jdbcType=VARCHAR},#{item.czljsl,jdbcType=VARCHAR},#{item.czbzjsl,jdbcType=VARCHAR},#{item.czfbzjsl,jdbcType=VARCHAR},#{item.ppljsl,jdbcType=VARCHAR},#{item.ppbzjsl,jdbcType=VARCHAR},#{item.ppfbzj,jdbcType=VARCHAR},#{item.ppbsbsl,jdbcType=VARCHAR},#{item.cxxbsbsl,jdbcType=VARCHAR},#{item.czbsbsl,jdbcType=VARCHAR},#{item.cxbsbsl,jdbcType=VARCHAR},#{item.dataType,jdbcType=VARCHAR},sysdate,#{item.zczbsbsl,jdbcType=VARCHAR},#{item.zppbsbsl,jdbcType=VARCHAR},#{item.zcxxbsbsl,jdbcType=VARCHAR},#{item.zcxbsbsl,jdbcType=VARCHAR},#{item.zlpcbsl,jdbcType=VARCHAR},#{item.receiveDate,jdbcType=VARCHAR},#{item.xytpsl,jdbcType=VARCHAR},#{item.cztpzl,jdbcType=VARCHAR},#{item.czljzl,jdbcType=VARCHAR},#{item.group_part_num,jdbcType=VARCHAR},#{item.brand_part_num,jdbcType=VARCHAR} from dual
		</foreach>
	</insert>

	<insert id="insertCTableDataStatictics">
		INSERT INTO DATA_STATISTICS(ID,czid,cjsl,ppsl,cxxsl,czsl,cxsl,lpcbsl,cxljsl,cxljxssl,czhcmsl,cxbzjsl,cxfbzjsl,cxtpsl,cxtprdsl,czljsl,czbzjsl,
									czfbzjsl,ppljsl,ppbzjsl,ppfbzj,ppbsbsl,cxxbsbsl,czbsbsl,cxbsbsl,data_type,add_time,RECEIVE_DATE)
		<foreach collection="list" item="item" separator=" union all ">
			select  sys_guid(),#{item.czid,jdbcType=VARCHAR},#{item.cjsl,jdbcType=VARCHAR},#{item.ppsl,jdbcType=VARCHAR},#{item.cxxsl,jdbcType=VARCHAR},#{item.czsl,jdbcType=VARCHAR},#{item.cxsl,jdbcType=VARCHAR},#{item.lpcbsl,jdbcType=VARCHAR},#{item.cxljsl,jdbcType=VARCHAR},#{item.cxljxssl,jdbcType=VARCHAR},#{item.czhcmsl,jdbcType=VARCHAR},#{item.cxbzjsl,jdbcType=VARCHAR},#{item.cxfbzjsl,jdbcType=VARCHAR},#{item.cxtpsl,jdbcType=VARCHAR},#{item.cxtprdsl,jdbcType=VARCHAR},#{item.czljsl,jdbcType=VARCHAR},#{item.czbzjsl,jdbcType=VARCHAR},#{item.czfbzjsl,jdbcType=VARCHAR},#{item.ppljsl,jdbcType=VARCHAR},#{item.ppbzjsl,jdbcType=VARCHAR},#{item.ppfbzj,jdbcType=VARCHAR},#{item.ppbsbsl,jdbcType=VARCHAR},#{item.cxxbsbsl,jdbcType=VARCHAR},#{item.czbsbsl,jdbcType=VARCHAR},#{item.cxbsbsl,jdbcType=VARCHAR},#{item.dataType,jdbcType=VARCHAR},sysdate,#{item.receiveDate,jdbcType=VARCHAR} from dual
		</foreach>
	</insert>

	<select id="exportData" resultMap="groupData" parameterType="String">
		select  ds.*,r.zbmc groupName from DATA_STATISTICS ds left join f_zc_clfzxxb r on ds.czid = r.id where 1 = 1
		<if test="_parameter != null and _parameter != ''">
			AND ds.receive_date = #{_parameter}
		</if>
		order by data_type asc
	</select>
	<resultMap id="groupData" type="map">
		<result column="czid" property="czid"></result>
		<result column="groupName" property="groupName"></result>
		<collection property="dataStatisticsList" resultMap="dataStatistics" javaType="list"></collection>
	</resultMap>
	<resultMap id="dataStatistics" type="map">
		<result column="id" property="id"></result>
		<result column="zcjsl" property="zcjsl"></result>
		<result column="zppsl" property="zppsl"></result>
		<result column="zcxxsl" property="zcxxsl"></result>
		<result column="zczsl" property="zczsl"></result>
		<result column="zcxsl" property="zcxsl"></result>
		<result column="cjsl" property="cjsl"></result>
		<result column="ppsl" property="ppsl"></result>
		<result column="cxxsl" property="cxxsl"></result>
		<result column="czsl" property="czsl"></result>
		<result column="cxsl" property="cxsl"></result>
		<result column="lpcbsl" property="lpcbsl"></result>
		<result column="cxljsl" property="cxljsl"></result>
		<result column="cxljxssl" property="cxljxssl"></result>
		<result column="czhcmsl" property="czhcmsl"></result>
		<result column="cxbzjsl" property="cxbzjsl"></result>
		<result column="cxfbzjsl" property="cxfbzjsl"></result>
		<result column="cxtpsl" property="cxtpsl"></result>
		<result column="cxtprdsl" property="cxtprdsl"></result>
		<result column="czljsl" property="czljsl"></result>
		<result column="czbzjsl" property="czbzjsl"></result>
		<result column="czfbzjsl" property="czfbzjsl"></result>
		<result column="ppljsl" property="ppljsl"></result>
		<result column="ppbzjsl" property="ppbzjsl"></result>
		<result column="ppfbzj" property="ppfbzj"></result>
		<result column="ppbsbsl" property="ppbsbsl"></result>
		<result column="cxxbsbsl" property="cxxbsbsl"></result>
		<result column="czbsbsl" property="czbsbsl"></result>
		<result column="cxbsbsl" property="cxbsbsl"></result>
		<result column="data_type" property="dataType"></result>
		<result column="add_time" property="addType"></result>
		<result column="zczbsbsl" property="zczbsbsl"></result>
		<result column="zppbsbsl" property="zppbsbsl"></result>
		<result column="zcxxbsbsl" property="zcxxbsbsl"></result>
		<result column="zcxbsbsl" property="zcxbsbsl"></result>
		<result column="zlpcbsl" property="zlpcbsl"></result>
	</resultMap>


	<update id="updateStisticsTest">
		update DATA_STATISTICS set add_time = to_date('2019-04-16 23:00:00','YYYY-MM-DD HH24:MI:SS')
	</update>

	<select id="getStisticsDataContrast" resultMap="czList" parameterType="String">
		select * from DATA_STATISTICS where  receive_date = #{_parameter}
	</select>

	<resultMap id="czList" type="map">
		<result property="czid" column="czid"></result>
		<collection property="dataBeanList" resultMap="dataBean" javaType="arrayList"></collection>
	</resultMap>
	<resultMap id="dataBean" type="com.jy.bean.dto.DataStatisticsDto">
		<result property="zcjsl" column="zcjsl"></result>
		<result property="zppsl" column="zppsl"></result>
		<result property="zcxxsl" column="zcxxsl"></result>
		<result property="zczsl" column="zczsl"></result>
		<result property="zcxsl" column="zcxsl"></result>
		<result property="cjsl" column="cjsl"></result>
		<result property="ppsl" column="ppsl"></result>
		<result property="cxxsl" column="cxxsl"></result>
		<result property="czsl" column="czsl"></result>
		<result property="cxsl" column="cxsl"></result>
		<result property="lpcbsl" column="lpcbsl"></result>
		<result property="cxljsl" column="cxljsl"></result>
		<result property="cxljxssl" column="cxljxssl"></result>
		<result property="czhcmsl" column="czhcmsl"></result>
		<result property="cxbzjsl" column="cxbzjsl"></result>
		<result property="cxfbzjsl" column="cxfbzjsl"></result>
		<result property="cxtpsl" column="cxtpsl"></result>
		<result property="cxtprdsl" column="cxtprdsl"></result>
		<result property="czljsl" column="czljsl"></result>
		<result property="czbzjsl" column="czbzjsl"></result>
		<result property="czfbzjsl" column="czfbzjsl"></result>
		<result property="ppljsl" column="ppljsl"></result>
		<result property="ppbzjsl" column="ppbzjsl"></result>
		<result property="ppfbzj" column="ppfbzj"></result>
		<result property="ppbsbsl" column="ppbsbsl"></result>
		<result property="cxxbsbsl" column="cxxbsbsl"></result>
		<result property="czbsbsl" column="czbsbsl"></result>
		<result property="cxbsbsl" column="cxbsbsl"></result>
		<result property="dataType" column="data_type"></result>
		<result property="zppbsbsl" column="zppbsbsl"></result>
		<result property="zcxxbsbsl" column="zcxxbsbsl"></result>
		<result property="zcxbsbsl" column="zcxbsbsl"></result>
		<result property="zczbsbsl" column="zczbsbsl"></result>
		<result property="zlpcbsl" column="zlpcbsl"></result>
		<result property="xytpsl" column="xytpsl"></result>
		<result property="cztpzl" column="cztpzl"></result>
		<result property="czljzl" column="czljzl"></result>
		<result property="group_part_num" column="group_part_num"></result>
		<result property="brand_part_num" column="brand_part_num"></result>
	</resultMap>
</mapper>
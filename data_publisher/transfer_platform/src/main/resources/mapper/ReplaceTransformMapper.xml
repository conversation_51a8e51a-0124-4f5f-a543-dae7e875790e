<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jy.mapper.ReplaceTransformMapper">

	<select id="getMaxRelGroupId" parameterType="map" resultType="String">
		select max(to_number(rel_group_id))
		from f_rep_out_relation
		<where>
			1=1
			<if test="_parameter.containsKey('brandId') and brandId != null">
				and brand_id = #{brandId}
			</if>
		</where>
	</select>


	<select id="ListByNoRelGroupId" resultType="com.jy.bean.po.RepOutRelation">
		SELECT
		p.id,
		p.brand_id,
		p.tmp_part_number,
		p.rep_tmp_part_number,
		p.rel_group_id
		FROM
		p_rep_out_relation p
		<where>
			rel_group_id is null
			<if test="_parameter.containsKey('brandId') and brandId != null">
				and brand_id = #{brandId}
			</if>
		</where>
	</select>

	<update id="updateByRelReplace">
		update p_rep_out_part_number set rel_group_id =
		(select rel_group_id from p_rep_out_relation where p_rep_out_part_number.brand_id = p_rep_out_relation.brand_id
		and (p_rep_out_part_number.tmp_part_number = p_rep_out_relation.tmp_part_number or p_rep_out_part_number.tmp_part_number = p_rep_out_relation.rep_tmp_part_number )and rownum =1)
		where rel_group_id is null and exists (select 1 from p_rep_out_relation where p_rep_out_part_number.brand_id = p_rep_out_relation.brand_id
		and (p_rep_out_part_number.tmp_part_number = p_rep_out_relation.tmp_part_number or p_rep_out_part_number.tmp_part_number = p_rep_out_relation.rep_tmp_part_number )and rownum =1)
    </update>

</mapper>
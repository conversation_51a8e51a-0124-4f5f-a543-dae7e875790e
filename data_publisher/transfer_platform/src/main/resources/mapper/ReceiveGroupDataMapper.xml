<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jy.mapper.ReceiveGroupDataMapper">


	<sql id="Base_Column_List">
		ID,
		GROUP_ID,
		GROUP_CODE,
		GROUP_NAME,
		MAIN_VERSION_CODE,
		RECEIVE_DATE,
		GAIN_DATA_STATE,
		STATE,
		ERR_NUM,
		BRAND_ID,
		BRAND_NAME,
		BRAND_CODE,
		VEHICLE_ID,
		VEHICLE_NAME,
		VEH<PERSON><PERSON>_CODE,
		SERIES_ID,
		SERIES_NAME,
		SERIES_CODE,
		IS_MODIFY,
		END_DATE,
		P_MAIN_VERSION_CODE,
		TABLE_SUFFIX,
		ERR_MSG,
		DEL_FLAG,
		COMPARE_BATCH_NO,
		CLIENT_CODE,
		CLIENT_URL
</sql>

	<insert id="insert" parameterType="com.jy.bean.dto.ReceiveGroupDataDto">
		INSERT INTO RECEIVE_GROUP_DATA(<include refid="Base_Column_List"/>)
		VALUES (
		#{id,jdbcType=VARCHAR},
		#{groupId,jdbcType=VARCHAR},
		#{groupCode,jdbcType=VARCHAR},
		#{groupName,jdbcType=VARCHAR},
		#{mainVersionCode,jdbcType=VARCHAR},
		sysdate,
		#{gainDataState,jdbcType=VARCHAR},
		#{state,jdbcType=VARCHAR},
		#{errNum,jdbcType=VARCHAR},
		#{brandId,jdbcType=VARCHAR},
		#{brandName,jdbcType=VARCHAR},
		#{brandCode,jdbcType=VARCHAR},
		#{vehicleId,jdbcType=VARCHAR},
		#{vehicleName,jdbcType=VARCHAR},
		#{vehicleCode,jdbcType=VARCHAR},
		#{seriesId,jdbcType=VARCHAR},
		#{seriesName,jdbcType=VARCHAR},
		#{seriesCode,jdbcType=VARCHAR},
		#{isModify,jdbcType=VARCHAR},
		sysdate,
		#{pMainVersionCode,jdbcType=VARCHAR},
		#{tableSuffix,jdbcType=VARCHAR},
		'',
		#{delFlag,jdbcType=VARCHAR},
		#{compareBatchNo,jdbcType=VARCHAR},
		#{clientCode,jdbcType=VARCHAR},
		#{clientUrl,jdbcType=VARCHAR}
		)
	</insert>

	<select id="listReceiveGroupDataDto" resultType="com.jy.bean.dto.ReceiveGroupDataDto">
		select
		MAIN_VERSION_CODE,
		RECEIVE_DATE,
		GAIN_DATA_STATE,
		STATE,
		ERR_NUM,
		IS_MODIFY,
		END_DATE,
		P_MAIN_VERSION_CODE,
		TABLE_SUFFIX,
		ERR_MSG,
		COMPARE_BATCH_NO,
		CLIENT_CODE,
		CLIENT_URL,
		CLIENT_CODE || '_' || CLIENT_URL as clientKey
		from RECEIVE_GROUP_DATA
		<where>
			<if test="_parameter.containsKey('tableName') and tableName != null and tableName != ''">
				and TABLE_NAME = #{tableName}
			</if>
			<if test="_parameter.containsKey('versionCode') and versionCode != null and versionCode != ''">
				and version_code = #{versionCode}
			</if>
			<if test="_parameter.containsKey('compareBatchNo') and compareBatchNo != null and compareBatchNo != ''">
				and COMPARE_BATCH_NO = #{compareBatchNo}
			</if>
		</where>
	</select>

	<insert id="insertBatch" parameterType="list">
		INSERT INTO RECEIVE_GROUP_DATA(ID,GROUP_ID,GROUP_CODE,GROUP_NAME,MAIN_VERSION_CODE,RECEIVE_DATE,GAIN_DATA_STATE,STATE,ERR_NUM,BRAND_ID,BRAND_NAME,BRAND_CODE,VEHICLE_ID,VEHICLE_NAME,VEHICLE_CODE,SERIES_ID,SERIES_NAME,SERIES_CODE,IS_MODIFY,END_DATE,P_MAIN_VERSION_CODE,TABLE_SUFFIX,ERR_MSG,DEL_FLAG,data_type)
		<foreach collection="list" item="item" separator="union all">
			SELECT
			#{item.id,jdbcType=VARCHAR},#{item.groupId,jdbcType=VARCHAR},#{item.groupCode,jdbcType=VARCHAR},#{item.groupName,jdbcType=VARCHAR},#{item.mainVersionCode,jdbcType=VARCHAR}
			,sysdate,#{item.gainDataState,jdbcType=VARCHAR},#{item.state,jdbcType=VARCHAR},#{item.errNum,jdbcType=VARCHAR},#{item.brandId,jdbcType=VARCHAR},#{item.brandName,jdbcType=VARCHAR}
			,#{item.brandCode,jdbcType=VARCHAR},#{item.vehicleId,jdbcType=VARCHAR},#{item.vehicleName,jdbcType=VARCHAR},#{item.vehicleCode,jdbcType=VARCHAR},#{item.seriesId,jdbcType=VARCHAR},#{item.seriesName,jdbcType=VARCHAR},#{item.seriesCode,jdbcType=VARCHAR},#{item.isModify,jdbcType=VARCHAR},sysdate,#{item.pMainVersionCode,jdbcType=VARCHAR},#{item.tableSuffix,jdbcType=VARCHAR},'',#{item.delFlag,jdbcType=VARCHAR},#{item.dataType,jdbcType=VARCHAR}
			FROM dual
		</foreach>
	</insert>

	<insert id="insertTableBatch" parameterType="list">
		INSERT INTO GAIN_TABLE_DATA
		<foreach collection="list" item="item" separator="union all">
			SELECT
			LOWER(sys_guid()) ,#{item.tableName},'00',sysdate,null
			,#{item.versionCode},#{item.mainVersionCode},#{item.saveTable},#{item.type}
			FROM dual
		</foreach>
	</insert>

	<select id="getTableListMainVersionCode" parameterType="String" resultType="com.jy.bean.dto.ReceiveGroupDataDto">
		SELECT rgd.SERIES_ID,rgd.del_flag,rgd.IS_MODIFY,rgd.brand_code,rgd.group_id,rgd.brand_id,rgd.group_code,gtd.TABLE_NAME,gtd.VERSION_CODE,rgd.MAIN_VERSION_CODE,gtd.save_table,rgd.DATA_TYPE,rgd.client_code,rgd.client_url,rgd.TABLE_SUFFIX,rgd.BRAND_NAME FROM
		RECEIVE_GROUP_DATA rgd LEFT JOIN GAIN_TABLE_DATA gtd ON rgd.main_version_code = gtd.main_version_code
		WHERE rgd.MAIN_VERSION_CODE = #{_parameter} AND (gtd.state = '00' OR gtd.state = '02')
	</select>

	<select id="getByPMainVersionCode" parameterType="String" resultType="com.jy.bean.dto.ReceiveGroupDataDto">
		SELECT
			rgd.IS_MODIFY,
			rgd.brand_code,
			rgd. GROUP_ID,
			rgd.brand_id,
			rgd.group_code,
			rgd.SERIES_ID,
			rgd.SERIES_CODE,
			'' TABLE_NAME,
			st.VERSION_CODE,
			st.MAIN_VERSION_CODE,
			'' save_table,
			'1' DATA_TYPE
		FROM
			RECEIVE_GROUP_DATA rgd
		LEFT JOIN SEND_TABLE st ON RGD.MAIN_VERSION_CODE = ST.MAIN_VERSION_CODE
		WHERE rgd.MAIN_VERSION_CODE = #{_parameter}
	</select>

	<update id="updateErrorNum" parameterType="String">
		update RECEIVE_GROUP_DATA SET err_num = err_num + 1,END_DATE = sysdate WHERE main_version_code = #{_parameter} AND err_num &lt; 3
	</update>

	<insert id="insertTableDataBatch" parameterType="map">
		INSERT INTO ${tableName}(
		<foreach collection="list" item="item" index="index">
			<if test="index == 0">
				<!--循环表字段-->
				<foreach collection="item" index="key" item="value" separator=",">
					${key}
				</foreach>
				<if test="groupCode != null and groupCode != ''">
					,group_id,group_code
				</if>
			</if>
		</foreach>
		)
		<foreach collection="list" item="item" separator="union all">
			SELECT
			<!--循环值-->
			<foreach collection="item" index="key" item="value" separator=",">
				#{   value,jdbcType=VARCHAR}
			</foreach>
			<if test="groupCode != null and groupCode != ''">
				,#{groupId},#{groupCode}
			</if>
			FROM dual
		</foreach>
	</insert>

	<update id="updateTableStateByMianVersionCode" parameterType="map">
		UPDATE GAIN_TABLE_DATA SET state = #{state},GAIN_DATE = sysdate
		 <if test="errMsg != null and errMsg != ''">
			 ,err_msg = #{errMsg}
		 </if>
		 WHERE 1 = 1
		<choose>
			<when test="versionCode != null and versionCode != ''">
				AND version_code = #{versionCode}
			</when>
			<when test="mainVersionCode != null and mainVersionCode != ''">
				AND main_version_code = #{mainVersionCode}
			</when>
			<otherwise>
				AND 1 = 2
			</otherwise>
		</choose>
	</update>

	<update id="updateGroupStateByMainVersionCode" parameterType="map">
		UPDATE RECEIVE_GROUP_DATA SET STATE = #{state},end_date = sysdate
		<if test="errMsg != null and errMsg != ''">
			,ERR_MSG = #{errMsg}
		</if>
		WHERE main_version_code = #{mainVersionCode}
		<if test="state == '501'">
			AND (STATE = '500' OR STATE = '021')
		</if>
	</update>

	<select id="getTableDataList" resultType="map" parameterType="map">
		SELECT * FROM ${tableName} WHERE version_code = #{versionCode}
		<if test="vehicleId != null and vehicleId != ''">
			AND CLZLID = #{vehicleId}
		</if>
		<if test="oldMainVersionCode != null and oldMainVersionCode != ''">
			union all
			select * from ${tableName} where main_code = #{oldMainVersionCode} and state = 'delete'
			<if test="vehicleId != null and vehicleId != ''">
				AND CLZLID = #{vehicleId}
			</if>
		</if>
	</select>

	<select id="getTest" resultType="map">
		select * from  ${transfer}.BRAND_PART
	</select>

	<insert id="saveConversionTables" parameterType="list">
		INSERT INTO CONVERSION_TABLE(id,TABLE_NAME,FIELD_NAME,IS_MAIN)
		<foreach collection="list" item="item" separator="union all">
			<foreach collection="item.fieldList" item="item1" separator="union all">
				SELECT LOWER(sys_guid()),#{item.tableName},#{item1.field},'0' from dual
			</foreach>
		</foreach>
	</insert>

	<insert id="saveAssociated" parameterType="com.jy.bean.dto.GroupBrandAssociatedDto">
		INSERT INTO GROUP_BRAND_ASSOCIATED(id,brand_id,brand_code,table_name,CONVERSION_TABLE_NAME,PRODUCT_TABLE_NAME,CONTRAST_TABLE_NAME)
		<foreach collection="list" item="item" separator="union all">
			SELECT LOWER(sys_guid()),#{item.brandId},#{item.brandCode,jdbcType=VARCHAR},#{item.tableName,jdbcType=VARCHAR},#{item.conversionTableName,jdbcType=VARCHAR},#{item.productTableName,jdbcType=VARCHAR},#{item.contrastTableName,jdbcType=VARCHAR}
			from dual
		</foreach>
	</insert>

	<select id="getAssociatedTableNameList" parameterType="map" resultType="map">
		<!--SELECT table_name,CONTRAST_TABLE_NAME from GROUP_BRAND_ASSOCIATED WHERE brand_id = #{brandId}
		UNION ALL
		SELECT table_name,CONTRAST_TABLE_NAME from GROUP_BRAND_ASSOCIATED WHERE brand_id IS NULL-->
		SELECT table_name,CONTRAST_TABLE_NAME FROM GROUP_BRAND_ASSOCIATED WHERE table_name = #{tableName}
	</select>

	<select id="createTables" parameterType="map" statementType="CALLABLE" resultType="map">
		{call TEMP_EMP_CREATETAB(#{tableName,mode=IN,jdbcType=VARCHAR},#{brandCode,mode=IN,jdbcType=VARCHAR})}
	</select>

    <select id="delOldGroupData" parameterType="map" statementType="CALLABLE" resultType="map">
        {call ${mid}.TRUNCATE_TABLE(#{tableName,mode=IN,jdbcType=VARCHAR})}
    </select>

	<delete id="delOldGroupData1" parameterType="map">
		<!--<if test="dataType == 5">-->
			truncate table ${mid}.${tableName}
		<!--</if>-->
		<!--<if test="dataType != 5">
			DELETE FROM ${tableName} where 1 = 1
			<choose>
				<when test="groupId != null and groupId != '' and dataType == 1">
					AND group_id = #{groupId}
				</when>
				<when test="brandId != null and brandId != '' and dataType == 3">
					AND brand_id = #{brandId}
				</when>
				<when test="brandId != null and brandId != '' and dataType == 4">
					AND AFT_BRAND_ID = #{brandId}
				</when>
				<when test="dataType == 2">
					AND SOURCE = #{source}
				</when>
				<otherwise>
					AND 1 = 2
				</otherwise>
			</choose>
		</if>-->
	</delete>

	<select id="getClfzxxbByGroupCode" parameterType="String" resultType="map">
		SELECT * FROM f_zc_clfzxxb where id = #{_parameter}
	</select>

	<select id="getPartNum" parameterType="String" resultType="map">
		SELECT * FROM p_zc_clfzxxb where id = #{_parameter}
	</select>

	<update id="updateSuffix" parameterType="map">
		UPDATE p_zc_clfzxxb SET parts_suffix = #{partsSuffix},hours_suffix = #{hoursSuffix} where id = #{groupId}
	</update>

	<select id="listClfzxxbByNullSuffix" resultType="map">
		SELECT * FROM p_zc_clfzxxb where parts_suffix is null or hours_suffix is null
	</select>

	<select id="getTest1" parameterType="String" resultType="map">
		SELECT
		NVL(PB.ID,'') ID,
		'其它' CA_GROUP_NAME,
		'905' CA_GROUP_CODE,
		'999999' CA_GROUP_ID,
		'其它' CATEGORY_NAME,
		'-1' SUB_CATEGORY_NAME,
		'999999' CATEGORY_ID,
		'3BD42EB549CB0DF8E050A8C007508EB2' SUB_CATEGORY_ID,
		'905' CATEGORY_CODE,
		'-1' SUB_CATEGORY_CODE,
		'999999' STD_PART_ID,
		NVL(PB.ORI_CATAGROY,'')	ORI_CATAGROY,
		NVL(PB.ORI_SECTION,'')	ORI_SECTION,
		NVL(PB.ORI_GRAPHIC,'')	ORI_GRAPHIC,
		NVL(PB.ORI_SEQUENCE,'')	ORI_SEQUENCE,
		NVL(PB.ORI_PART_NAME,'')	ORI_PART_NAME,
		NVL(PB.PART_NUMBER,'')	PART_NUMBER,
		NVL(PB.PART_NAME,'')	PART_NAME,
		NVL(PB.RL_TAG,'')	RL_TAG,
		NVL(PB.PART_CODE,'')	PART_CODE,
		NVL(PB.PART_NOTE,'')	PART_NOTE,
		NVL(PB.QUANT_PERU,'')	QUANT_PERU,
		NVL(PB.ENGINE_DISPLACEMENT,'')	ENGINE_DISPLACEMENT,
		NVL(PB.ENGINE_NO,'')	ENGINE_NO,
		NVL(PB.TRANS_NO,'')	TRANS_NO,
		NVL(PB.TRANS_MODEL,'')	TRANS_MODEL,
		NVL(PB.PART_COLOR,'')	PART_COLOR,
		NVL(PB.PAINT_TAG,'')	PAINT_TAG,
		NVL(PB.CHASS_NO_FROM,'')	CHASS_NO_FROM,
		NVL(PB.CHASS_NO_TO,'')	CHASS_NO_TO,
		NVL(PB.PART_MATERIAL,'')	PART_MATERIAL,
		NVL(PB.START_YEAR,'')	START_YEAR,
		NVL(PB.END_YEAR,'')	END_YEAR,
		NVL(PB.VEH_LWIDS,'')	VEH_LWIDS,
		NVL(PB.TMP_PART_NUMBER,'')	TMP_PART_NUMBER,
		NVL(PB.PART_NUMBER_ID,'')	PART_NUMBER_ID,
		NVL(PB.REL_HOT_ID,'')	REL_HOT_ID,
		NVL(PB.PID,'')	PID
		FROM TMP_MSB0_DATA_20190109 PB
		where 1 = 1
		<if test="_parameter != null and _parameter != ''">
			AND PB.VEH_YWIDS = #{_parameter}
		</if>
	</select>

	<select id="getTableMainFields" parameterType="String" resultType="map">
		SELECT field_name FROM CONVERSION_TABLE WHERE is_main = 1 and LOWER(table_name) = #{_parameter}
	</select>

	<select id="getSendFailMainVersionCode" parameterType="String" resultType="com.jy.bean.dto.ReceiveGroupDataDto">
		SELECT r.main_version_code,r.group_code,r.brand_code,r.GROUP_ID,r.brand_id FROM RECEIVE_GROUP_DATA r
		WHERE r.state = '901'
		<if test="_parameter != null and _parameter != ''">
			AND r.MAIN_VERSION_CODE = #{_parameter}
		</if>
	</select>

	<select id="getSendTable" parameterType="String" resultType="map">
		SELECT CONTRAST_TABLE_NAME,IS_BRAND_CODE FROM GROUP_BRAND_ASSOCIATED WHERE TABLE_NAME = #{_parameter}
	</select>

	<insert id="saveSendTable" parameterType="list">
		INSERT INTO SEND_TABLE(MAIN_VERSION_CODE,VERSION_CODE,SEND_TABLE,STATE,HANDLE_TABLE,CREATE_DATE,SEND_CODE)
		<foreach collection="list" item="item" separator="union all">
			SELECT #{item.mainVersionCode},#{item.versionCode,jdbcType=VARCHAR},#{item.sendTable,jdbcType=VARCHAR},#{item.state,jdbcType=VARCHAR},#{item.handleTable},SYSDATE,#{item.sendCode,jdbcType=VARCHAR}
			from dual
		</foreach>
	</insert>

	<select id="getSendTableListByMainVersionCode" parameterType="map" resultType="map">
		SELECT * FROM  SEND_TABLE WHERE main_version_code = #{mainVersionCode} AND (state = '71' OR state = '3') and HANDLE_TABLE != 'ZC'
		ORDER  BY state DESC
	</select>

	<update id="updateSendTableState" parameterType="map">
		UPDATE SEND_TABLE SET state = #{state},END_DATE = sysdate
		 <if test="errMsg != null and errMsg != ''">
			 ,err_msg = #{errMsg}
		 </if>
		 WHERE
		<if test="id != null and id != ''">
			id = #{id}
		</if>
		<if test="versionCode != null and versionCode != ''">
			version_code = #{versionCode}
		</if>
	</update>
	
	<select id="getPartNumAndHotFlag" parameterType="String" resultType="map">
		SELECT zc.id,zc.BRAND_HOT_FLAG,zcf.PART_NUM FROM P_ZC_CLPPXXB zc LEFT JOIN p_ZC_CLPPXXB_FLAG zcf ON zc.id = zcf.brand_id
		WHERE zc.PPBM = #{_parameter}
	</select>

	<update id="updateBrandLevel" parameterType="map">
		UPDATE p_ZC_CLPPXXB_FLAG SET BRAND_LEVEL = #{brandLevel} WHERE BRAND_ID = #{brandId}
	</update>

	<select id="getReceiceGroupStateByMainVersionCode" parameterType="String" resultType="String">
		SELECT STATE FROM  RECEIVE_GROUP_DATA where main_version_code = #{mainVersionCode}
	</select>

	<select id="groupTransformation" parameterType="map" statementType="CALLABLE" resultType="map">
		{call pkg_convert_data.p_conver_part_vehicle(#{mainVersionCode,mode=IN,jdbcType=VARCHAR},#{groupId,mode=IN,jdbcType=VARCHAR},#{state,mode=OUT,jdbcType=VARCHAR})}
	</select>

	<select id="dataContrast" parameterType="map" statementType="CALLABLE" resultType="String">
		{call p_compare_data(#{mainVersionCode,mode=IN,jdbcType=VARCHAR},#{versionCode,mode=IN,jdbcType=VARCHAR},#{certain,mode=IN,jdbcType=VARCHAR},#{state,mode=OUT,jdbcType=VARCHAR})}
	</select>

	<select id="brandTransformation" parameterType="map" statementType="CALLABLE" resultType="map">
		{call pkg_convert_data.p_conver_brand_part(#{mainVersionCode,mode=IN,jdbcType=VARCHAR},#{state,mode=OUT,jdbcType=VARCHAR})}
	</select>

	<select id="repOutTransformation" parameterType="map" statementType="CALLABLE" resultType="map">
		{call pkg_convert_data.p_conver_rep_out_part(#{mainVersionCode,mode=IN,jdbcType=VARCHAR},#{state,mode=OUT,jdbcType=VARCHAR})}
	</select>

	<select id="pzshTransformation" parameterType="map" statementType="CALLABLE" resultType="map">
		{call pkg_convert_data.p_conver_pzsh(#{mainVersionCode,mode=IN,jdbcType=VARCHAR},#{state,mode=OUT,jdbcType=VARCHAR})}
	</select>

	<select id="stdPartTransformation" parameterType="map" statementType="CALLABLE" resultType="map">
		{call pkg_convert_data.p_conver_std_part(#{mainVersionCode,mode=IN,jdbcType=VARCHAR},#{state,mode=OUT,jdbcType=VARCHAR})}
	</select>

	<select id="clgroupTransformation" parameterType="map" statementType="CALLABLE" resultType="map">
		{call pkg_convert_data.p_conver_part_clgroup(#{mainVersionCode,mode=IN,jdbcType=VARCHAR},#{groupRelFlag,mode=IN,jdbcType=VARCHAR},#{state,mode=OUT,jdbcType=VARCHAR})}
	</select>

    <select id="oneGroup" parameterType="map" statementType="CALLABLE" resultType="map">
        {call pkg_convert_data.p_conver_one_group(#{mainVersionCode,mode=IN,jdbcType=VARCHAR},#{groupId,mode=IN,jdbcType=VARCHAR},#{state,mode=OUT,jdbcType=VARCHAR})}
    </select>

    <select id="oneTable" parameterType="map" statementType="CALLABLE" resultType="map">
        {call pkg_convert_data.p_conver_one_table(#{mainVersionCode,mode=IN,jdbcType=VARCHAR},#{tableName,mode=IN,jdbcType=VARCHAR},#{state,mode=OUT,jdbcType=VARCHAR})}
    </select>

	<select id="truckVehicle" parameterType="map" statementType="CALLABLE" resultType="map">
		{call pkg_convert_data.p_conver_truck_vehicle(#{mainVersionCode,mode=IN,jdbcType=VARCHAR},#{state,mode=OUT,jdbcType=VARCHAR})}
	</select>

	<select id="getSendData" parameterType="map" resultType="map">
		SELECT s.SEND_TABLE,s.VERSION_CODE,r.BRAND_CODE,s.SEND_CODE,f.parts_suffix,r.GROUP_CODE FROM RECEIVE_GROUP_DATA r
			LEFT JOIN SEND_TABLE s ON r.MAIN_VERSION_CODE = s.MAIN_VERSION_CODE
			LEFT JOIN f_zc_clfzxxb f ON r.GROUP_CODE = f.ZBBIANMA
			WHERE r.MAIN_VERSION_CODE = #{mainVersionCode}
			<if test="tableName != null and tableName != ''">
				AND HANDLE_TABLE = #{tableName}
			</if>
	</select>

	<select id="getModifyByMainVersionCode" parameterType="String" resultType="map">
		SELECT IS_MODIFY,DEL_FLAG FROM  RECEIVE_GROUP_DATA WHERE  MAIN_VERSION_CODE = #{_parameter}
	</select>

	<select id="getNullPartNameByGroupCode" parameterType="String" resultType="map">
		select part_name,id
			from f_part_name
			where group_id=#{_parameter}
			and hz_number is null
	</select>

	<select id="getMaxHzNumber" parameterType="String" resultType="map">
		select part_name,max(nvl(hz_number,0)) hz_number
		from f_part_name
		where group_id=#{_parameter}
		group by part_name
	</select>

	<update id="updateHzNumber" parameterType="map">
		<foreach collection="list" open="begin" separator=";" close=";end;" item="item">
			update f_part_name
			set hz_number = #{item.number}
			where id=#{item.id}
		</foreach>
	</update>

	<update id="updateCllbjdyb">
		update p_pj_cllbjdyb p
			set (ljbzmc,ycljmc, ljbzpy,ycljpy)=(select f.part_name||f.hz_number,f.part_name||f.hz_number,
			f_getpy(f.part_name||f.hz_number),f_getpy(f.part_name||f.hz_number)
			from f_part_name f
			where p.ycljh_replace=f.tmp_part_number
			and p.ljbzmc=f.part_name
			and f.group_id=p.czid)
			where exists(select 1
			from f_part_name k
			where p.ycljh_replace=k.tmp_part_number
			and p.ljbzmc=k.part_name
			and p.czid=k.group_id)
	</update>

	<update id="updateWLCllbjdyb">
		update p_wl_pj_cllbjdyb p
		set (ljbzmc,ljbzpy,ycljpy)=(select f.part_name||f.hz_number,
			   f_getpy(f.part_name||f.hz_number),f_getpy(f.part_name||f.hz_number)
		from f_part_name f
		where p.ycljh_replace=f.tmp_part_number
		  and p.ljbzmc=f.part_name
		  and f.group_id=p.czid)
		where exists(select 1
		 from f_part_name k
		 where p.ycljh_replace=k.tmp_part_number
		   and p.ljbzmc=k.part_name
		   and p.czid=k.group_id)
	</update>

	<select id="getBrandAndTableSuffixByGroupId" resultType="map" parameterType="String">
		select * FROM M_GROUP_BRAND_SUFFIX WHERE  group_id = #{_parameter}
	</select>

	<update id="updateBrandByMainVersionCode" parameterType="map">
		UPDATE RECEIVE_GROUP_DATA SET BRAND_ID = #{brandId},BRAND_CODE = #{brandCode},TABLE_SUFFIX = #{tableSuffix},GROUP_CODE = #{groupCode} WHERE MAIN_VERSION_CODE = #{mainVersionCode}
	</update>

	<select id="selUserTableNumByBrandCode" parameterType="String" resultType="int">
		select nvl(COUNT(*),0) from user_synonyms where (table_OWNER = '${final}' OR table_OWNER = '${compare}') and
			(table_name = 'PJ_CLLBJDYB_${_parameter}' OR table_name = 'PJ_CLLJTXDYB_${_parameter}' OR table_name = 'PJ_CZLBJDYB_${_parameter}')
	</select>

	<select id="createFTable" parameterType="map" statementType="CALLABLE" resultType="map">
		{call ${final}.CREATE_F_TABLE(#{brandCode,mode=IN,jdbcType=VARCHAR},#{errMsg,mode=OUT,jdbcType=VARCHAR},#{createNum,mode=OUT,jdbcType=DOUBLE})}
	</select>

	<select id="createCTable" parameterType="map" statementType="CALLABLE" resultType="map">
		{call ${compare}.CREATE_C_TABLE(#{brandCode,mode=IN,jdbcType=VARCHAR},#{errMsg,mode=OUT,jdbcType=VARCHAR},#{createNum,mode=OUT,jdbcType=DOUBLE})}
	</select>

	<update id="createTYC" parameterType="list">
		<foreach collection="list" item="item" open="begin" close=";end;" separator=";">
			execute immediate 'CREATE OR REPLACE SYNONYM ${transfer}.F_${item} FOR "${final}".${item}';
			execute immediate 'CREATE OR REPLACE SYNONYM ${transfer}.C_${item} FOR "${compare}".${item}'
		</foreach>
	</update>

	<update id="updateTableNameByMainVersionCode" parameterType="map">
		UPDATE SEND_TABLE SET SEND_TABLE = replace(SEND_TABLE,'_${oldBrandCode}','_${newBrandCode}'),HANDLE_TABLE = replace(HANDLE_TABLE,'_${oldBrandCode}','_${newBrandCode}')
		WHERE MAIN_VERSION_CODE = #{mainVersionCode}
	</update>

	<select id="pUpdateData" parameterType="map" statementType="CALLABLE" resultType="map">
		{call p_update_data(#{mainVersionCode,mode=IN,jdbcType=VARCHAR},#{versionCode,mode=IN,jdbcType=VARCHAR},#{certain,mode=IN,jdbcType=VARCHAR},#{state,mode=OUT,jdbcType=VARCHAR})}
	</select>

	<select id="selDataByTableName" resultType="map" parameterType="map">
		SELECT * FROM (SELECT s.*,ROWNUM rowNums FROM (
			SELECT  * FROM  ${tableName} WHERE  1 = 1
			<if test="mainVersionCode != null and mainVersionCode != ''">
				AND main_version_code = #{mainVersionCode}
			</if>
			<if test="versionCode != null and versionCode != ''">
				and version_code = #{versionCode}
			</if>
		) s WHERE ROWNUM &lt;= #{limit}) where rowNums >= #{row}
	</select>

	<select id="selDataByTableNameCount" resultType="long" parameterType="map">

		SELECT  COUNT(1) FROM  ${tableName} WHERE  1 = 1
		<if test="mainVersionCode != null and mainVersionCode != ''">
			AND main_version_code = #{mainVersionCode}
		</if>
		<if test="versionCode != null and versionCode != ''">
			and version_code = #{versionCode}
		</if>

	</select>

	<select id="getWLTableByBrandCode" parameterType="String" resultType="map">
		SELECT * FROM WL_BRAND_CODE WHERE brand_code LIKE '%'||#{_parameter}||'%'
	</select>

	<select id="getWLTableCount" parameterType="map" resultType="int">
		select nvl(COUNT(*),0) from user_synonyms where table_OWNER = '${final}' and
			table_name like '%'||#{_parameter}||'%'
	</select>

	<update id="createWLTYC" parameterType="list">
		<foreach collection="list" item="item" open="begin" close=";end;" separator=";">
			execute immediate 'CREATE OR REPLACE SYNONYM ${transfer}.P_${item.pTable} FOR ${product}.${item.pTable}';
			execute immediate 'CREATE OR REPLACE SYNONYM ${transfer}.F_${item.fTable} FOR ${final}.${item.fTable}';
			execute immediate 'CREATE OR REPLACE SYNONYM ${transfer}.C_${item.fTable} FOR "${compare}".${item.fTable}'
		</foreach>
	</update>

	<select id="createWLPTable" parameterType="map" statementType="CALLABLE" resultType="map">
		{call ${product}.CREATE_WL_P_TABLE(#{tableName,mode=IN,jdbcType=VARCHAR},#{errMsg,mode=OUT,jdbcType=VARCHAR})}
	</select>

	<select id="createWLFTable" parameterType="map" statementType="CALLABLE" resultType="map">
		{call ${final}.CREATE_WL_F_TABLE(#{tableName,mode=IN,jdbcType=VARCHAR},#{tableName1,mode=IN,jdbcType=VARCHAR},#{brandCode,mode=IN,jdbcType=VARCHAR},#{errMsg,mode=OUT,jdbcType=VARCHAR})}
	</select>

	<select id="createWLCTable" parameterType="map" statementType="CALLABLE" resultType="map">
		{call ${compare}.CREATE_WL_C_TABLE(#{tableName,mode=IN,jdbcType=VARCHAR},#{tableName1,mode=IN,jdbcType=VARCHAR},#{brandCode,mode=IN,jdbcType=VARCHAR},#{errMsg,mode=OUT,jdbcType=VARCHAR})}
	</select>

	<select id="getReceiveListByPage" parameterType="map" resultType="com.jy.bean.vo.ReceiveGroupDataVo">
		SELECT * FROM (SELECT w.*,rownum rowNums from (
			SELECT r.MAIN_VERSION_CODE,s.VERSION_CODE,g.TABLE_NAME,g.STATE
			dataState,s.STATE sendState,s.SEND_TABLE,r.brand_id brandId,g.data_type dataType,
			r.GROUP_CODE,r.BRAND_CODE,s.err_msg,ROWNUM,r.GROUP_ID groupId,to_char(r.RECEIVE_DATE,'YYYY-MM-DD HH24:MI:SS') receiveDate,to_char(s.end_date,'YYYY-MM-DD HH24:MI:SS') endDate,
			r.TABLE_SUFFIX tableSuffix
			FROM RECEIVE_GROUP_DATA r
			left JOIN GAIN_TABLE_DATA g ON r.MAIN_VERSION_CODE = g.MAIN_VERSION_CODE
			left JOIN SEND_TABLE s ON g.version_code = s.version_code WHERE r.is_modify = 0
			<if test="mainVersionCode != null and mainVersionCode != ''">
				AND r.main_version_code = #{mainVersionCode}
			</if>
			<if test="versionCode != null and versionCode != ''">
				AND g.version_code = #{versionCode}
			</if>
			<if test="groupCode != null and groupCode != ''">
				AND r.group_code = #{groupCode}
			</if>
			<if test="state != null and state != ''">
				<choose>
					<when test="state == '02'">
						AND g.state = #{state}
					</when>
					<otherwise>
						AND s.state = #{state}
					</otherwise>
				</choose>
			</if>
			UNION ALL
			SELECT r.MAIN_VERSION_CODE,s.VERSION_CODE,'' TABLE_NAME,'01'
			dataState,s.STATE sendState,s.SEND_TABLE,r.brand_id brandId,'1' dataType,
			r.GROUP_CODE,r.BRAND_CODE,s.err_msg,ROWNUM,r.GROUP_ID groupId,to_char(r.RECEIVE_DATE,'YYYY-MM-DD HH24:MI:SS') receiveDate,to_char(s.end_date,'YYYY-MM-DD HH24:MI:SS') endDate
			,r.TABLE_SUFFIX tableSuffix
			FROM RECEIVE_GROUP_DATA r
			left JOIN SEND_TABLE s ON r.main_version_code = s.main_version_code WHERE r.is_modify = 1
			<if test="mainVersionCode != null and mainVersionCode != ''">
				AND r.main_version_code = #{mainVersionCode}
			</if>
			<if test="versionCode != null and versionCode != ''">
				AND g.version_code = #{versionCode}
			</if>
			<if test="groupCode != null and groupCode != ''">
				AND r.group_code = #{groupCode}
			</if>
			<if test="state != null and state != ''">
				<choose>
					<when test="state == '02'">
						AND 1 = 2
					</when>
					<when test="state == '2'">
						AND (r.state = 4 or r.state = 2)
					</when>
					<otherwise>
						AND s.state = #{state}
					</otherwise>
				</choose>
			</if>
			ORDER BY receiveDate DESC nulls LAST
		) w WHERE rownum &lt;= #{limit}) WHERE rowNums >= #{row}
	</select>

	<select id="getListCount" parameterType="map" resultType="long">
		SELECT COUNT(1)
		FROM RECEIVE_GROUP_DATA r
		left JOIN GAIN_TABLE_DATA g ON r.MAIN_VERSION_CODE = g.MAIN_VERSION_CODE
		left JOIN SEND_TABLE s ON g.version_code = s.version_code
		WHERE 1 = 1
		<if test="mainVersionCode != null and mainVersionCode != ''">
			AND r.main_version_code = #{mainVersionCode}
		</if>
		<if test="versionCode != null and versionCode != ''">
			AND g.version_code = #{versionCode}
		</if>
		<if test="state != null and state != ''">
			<choose>
				<when test="state == '02'">
					AND g.state = #{state}
				</when>
				<otherwise>
					AND s.state = #{state}
				</otherwise>
			</choose>
		</if>
	</select>
	
	<select id="judgeExistsGroupCodeByDate" parameterType="map" resultType="int">
		SELECT COUNT(1) FROM RECEIVE_GROUP_DATA WHERE RECEIVE_DATE > to_date(#{date},'yyyy-mm-dd hh24:mi:ss') AND group_code = #{groupCode}
	</select>
	
	<update id="updateGainTableByMainVersionCode" parameterType="String">
		update GAIN_TABLE_DATA SET state = '00' WHERE main_version_code = #{_parameter}
	</update>

	<select id="groupTransformationByWL" parameterType="map" statementType="CALLABLE" resultType="map">
		{call pkg_convert_data.p_conver_part_other_brand(#{mainVersionCode,mode=IN,jdbcType=VARCHAR},#{groupId,mode=IN,jdbcType=VARCHAR},#{state,mode=OUT,jdbcType=VARCHAR})}
	</select>

	<select id="getSendTableByWlTable" parameterType="map" resultType="map">
		select HANDLE_TABLE,VERSION_CODE from SEND_TABLE where MAIN_VERSION_CODE = #{mainVersionCode}
	</select>

	<select id="getCompareDataByTableName" parameterType="map" resultType="map">
		select * from ${tableName} where version_code = #{versionCode}
		<if test="vehicleId != null and vehicleId != ''">
			AND CLZLID = #{vehicleId}
		</if>
		<choose>
			<when test="tableName == 'c_zc_clppxxb'">
				AND id = #{brandId}
			</when>
			<when test="tableName == 'c_zc_cxxxb' or tableName == 'c_zc_clzlb'">
				AND ppid_new = #{brandId}
			</when>
			<when test="tableName == 'c_zc_clfzxxb'">
				AND brand_id_new = #{brandId}
			</when>
		    <when test="tableName == 'c_zc_qccjxxb'">
				AND id in(select cjid_new from c_zc_clppxxb where id = #{brandId})
			</when>
			<otherwise>
				AND 1 = 1
			</otherwise>
		</choose>
		<if test="oldMainVersionCode != null and oldMainVersionCode != ''">
			union all
			select * from ${tableName} where main_code = #{oldMainVersionCode} and state = 'delete'
			<if test="vehicleId != null and vehicleId != ''">
				AND CLZLID = #{vehicleId}
			</if>
		</if>
	</select>

	<select id="getSendTableHXErrDataByMainVersionCode" parameterType="String" resultType="String">
		select distinct VERSION_CODE from (select VERSION_CODE,STATE from SEND_TABLE where MAIN_VERSION_CODE = #{_parameter} and (STATE = '70' or STATE = '61') order by STATE desc)
	</select>

	<select id="getStatisticsData" resultMap="getMainData" parameterType="map">
		select GROUP_ID,BRAND_CODE,SERIES_ID,MAIN_VERSION_CODE,BRAND_ID,GROUP_NAME,IS_STATISTICS from RECEIVE_GROUP_DATA where to_char(RECEIVE_DATE,'yyyy-mm-dd') = #{date} and STATE = '000' and group_id is not null and IS_MODIFY = '0'
		<if test="isStatistics != null and isStatistics != ''">
			and IS_STATISTICS = '0'
		</if>
		<if test="groupId != null and groupId !=''">
			AND GROUP_ID = #{groupId}
		</if>
	</select>
	<resultMap id="getMainData" type="map">
		<result column="GROUP_ID" property="groupId"></result>
		<result column="BRAND_CODE" property="brandCode"></result>
		<result column="SERIES_ID" property="seriesId"></result>
		<result column="BRAND_ID" property="brandId"></result>
		<result column="GROUP_NAME" property="groupName"></result>
		<result column="IS_STATISTICS" property="isStatistics"></result>
		<collection property="mainCodeList" resultMap="mainCode" javaType="list"></collection>
	</resultMap>
	<resultMap id="mainCode" type="map">
		<result column="main_version_code" property="mainVersionCode"></result>
	</resultMap>

	<update id="updateReStatistics" parameterType="list">
		<foreach collection="list" open="begin" separator=";" close=";end;" item="item">
			update RECEIVE_GROUP_DATA
			set IS_STATISTICS = '1'
			where MAIN_VERSION_CODE=#{item.mainVersionCode,jdbcType=VARCHAR}
		</foreach>
	</update>

	<select id="getSubVehicleIdByGroupId" parameterType="String" resultType="map">
		select distinct m.zccxid SUB_VEHICLE_ID,p.cxid from p_pj_zc_cxdyb m,p_zc_clzlb p
		<where>
			m.pjcxid = p.id and m.zccxid is not null and  m.exists_flag ='1'
			<if test="_parameter != null and _parameter != ''">
				and p.zbid = #{_parameter}
			</if>
		</where>
	</select>

	<insert id="saveSubVehicleTypeBatch" parameterType="com.jy.bean.dto.SubVehicleTypeDto">
		insert into SUB_VEHICLE_TYPE(SUB_VEHICLE_ID,VEHICLE_TYPE_NAME,VEHICLE_TYPE_CODE,VEH_CATE_NAMES,VEH_CATE_CODES,VEH_CATE_ONE_NAMES,VEH_CATE_ONE_CODES,VEH_CATE_TWO_CODES,ID,MAIN_VERSION_CODE,ADD_TIME,GRADE_ID,GRADE_NAME,GRADE_CODE)
		<foreach collection="list" item="item" separator=" union all ">
			select #{item.subVehicleId,jdbcType=VARCHAR}, #{item.vehicleTypeName,jdbcType=VARCHAR}, #{item.vehicleTypeCode,jdbcType=VARCHAR},
			       #{item.vehCateNames,jdbcType=VARCHAR}, #{item.vehCateCodes,jdbcType=VARCHAR}, #{item.vehCateOneNames,jdbcType=VARCHAR},
			       #{item.vehCateOneCodes,jdbcType=VARCHAR},#{item.vehCateTwoCodes,jdbcType=VARCHAR},sys_guid(),#{mainVersionCode},sysdate, #{item.gradeId,jdbcType=VARCHAR}, #{item.gradeCode,jdbcType=VARCHAR}, #{item.gradeName,jdbcType=VARCHAR}
			from dual
		</foreach>
	</insert>

	<select id="judgeVehicleType" resultType="map">
		SELECT listagg(sub_vehicle_id,',') WITHIN GROUP(ORDER BY sub_vehicle_id) sub_vehicle_id,part_vehicle_id FROM m_vehicle_info GROUP BY part_vehicle_id
	</select>

	<update id="testTransUpdate">
		update SEND_TABLE set state = '50' where main_version_code = 'GroupData2019041711502450246860'
	</update>

	<select id="getVehicleIdByC" parameterType="map" resultType="map">
		select distinct clzlid from ${tableName} where version_code = #{versionCode}
	</select>

	<update id="deleteZhPartTemp" parameterType="map">
		truncate table ZH_PART_TEMP_001
	</update>
	<insert id="insertZhPartTemp" parameterType="map">
		insert into ZH_PART_TEMP_001(BZ,YCLJH,CXLJFZID,LJBZBM,ID,CLZLID)
		SELECT
		pjcxid, wm_concat( distinct VEHICLE_TYPE_CODE)  VEHICLE_TYPE_CODE,
		wm_concat( distinct grade_id)  grade_id,
		wm_concat( distinct grade_name)  grade_name,
		wm_concat( distinct grade_code)  grade_code,
		wm_concat( distinct VEHICLE_TYPE_NAME)  VEHICLE_TYPE_NAME
		FROM
		(
		SELECT DISTINCT
		VEHICLE_TYPE_CODE,grade_id,grade_name,grade_code,pz.pjcxid,VEHICLE_TYPE_NAME
		FROM
		SUB_VEHICLE_TYPE s
		LEFT JOIN p_pj_zc_cxdyb pz ON s.SUB_VEHICLE_ID = pz.zccxid
		WHERE
		s.main_version_code = #{mainVersionCode}
		) group by pjcxid
	</insert>
	<update id="updateClzVehicleType" parameterType="map">
		UPDATE P_ZC_CLZLB P
		SET (VEHICLE_TYPE_CODE,grade_id,grade_name,grade_code
		) = (
		  select YCLJH,CXLJFZID,LJBZBM,id from ZH_PART_TEMP_001 z where p.id = z.bz
		)
		WHERE
		EXISTS (
		  SELECT 1 FROM ZH_PART_TEMP_001 z1 WHERE p.id = z1.bz
		)
	</update>

    <update id="updateClzVehicleTypeId" parameterType="map">
        UPDATE P_ZC_CLZLB z SET (VEHICLE_TYPE_ID,VEHICLE_TYPE_NAME) = (
            SELECT id,CLZLMC FROM F_PJ_CXZDB p WHERE z.VEHICLE_TYPE_CODE = p.CXBM
        )
		<if test="groupId != null and groupId != ''">
			<where>
				z.zbid = #{groupId}
			</where>
		</if>
    </update>

	<update id="updateClzFlagVehicleType" parameterType="map">
        UPDATE P_ZC_CLZLB_FLAG P
        SET (VEHICLE_TYPE_CODE, VEHICLE_TYPE_NAME) = (
            select YCLJH,CLZLID from ZH_PART_TEMP_001 z where p.id = z.bz
        )
        WHERE
            EXISTS (
                SELECT 1 FROM ZH_PART_TEMP_001 z1
                WHERE p.id = z1.bz
            )
	</update>

	<select id="getVehicleTypeData" parameterType="String" resultType="map">
		select listagg(VEHICLE_TYPE_NAME,',') within GROUP(ORDER BY VEHICLE_TYPE_NAME) VEHICLE_TYPE_NAME,
			   listagg(VEHICLE_TYPE_CODE,',') within group(ORDER BY VEHICLE_TYPE_CODE) VEHICLE_TYPE_CODE,zbid from P_ZC_CLZLB
		WHERE zbid = #{_parameter}
		group by zbid
	</select>

	<select id="getVehicleTypeDataByGroup" parameterType="String" resultType="map">
		select VEHICLE_TYPE_NAME,VEHICLE_TYPE_CODE from P_ZC_CLFZXXB_FLAG where id = #{_parameter}
	</select>

	<update id="updateClfFlagVehicleType" parameterType="map">
		update p_zc_clfzxxb_flag set VEHICLE_TYPE_NAME = #{vehicleTypeName},VEHICLE_TYPE_CODE = #{vehicleTypeCode} where id = #{id}
	</update>

	<select id="getCxxVehicleTypeData" parameterType="map" resultType="map">
		select listagg(VEHICLE_TYPE_NAME,',') within GROUP(ORDER BY VEHICLE_TYPE_NAME) VEHICLE_TYPE_NAME,
			   listagg(VEHICLE_TYPE_CODE,',') within group(ORDER BY VEHICLE_TYPE_CODE) VEHICLE_TYPE_CODE from P_ZC_CLFZXXB_FLAG
		WHERE GROUP_ID in(SELECT id FROM P_ZC_CLFZXXB WHERE cxid =  #{_parameter})
	</select>

	<update id="updateCxxFlagVehicleType" parameterType="map">
		update P_ZC_CXXXB_FLAG set VEHICLE_TYPE_NAME = #{vehicleTypeName},VEHICLE_TYPE_CODE = #{vehicleTypeCode} where SERIES_ID = #{id}
	</update>

	<select id="getClpFlagVehicleTypeData" parameterType="map" resultType="map">
		select listagg(VEHICLE_TYPE_NAME,',') within GROUP(ORDER BY VEHICLE_TYPE_NAME) VEHICLE_TYPE_NAME,
			   listagg(VEHICLE_TYPE_CODE,',') within group(ORDER BY VEHICLE_TYPE_CODE) VEHICLE_TYPE_CODE from P_ZC_CXXXB_FLAG
		WHERE SERIES_ID in(SELECT id FROM P_ZC_CXXXB WHERE ppid = #{_parameter})
	</select>

	<update id="updateClpFlagVehicleType" parameterType="map">
		update P_ZC_CLPPXXB_FLAG set VEHICLE_TYPE_NAME = #{vehicleTypeName},VEHICLE_TYPE_CODE = #{vehicleTypeCode} where BRAND_ID = #{id}
	</update>

	<select id="getMainVersionCodeByDelFlag" parameterType="map" resultType="map">
		SELECT main_version_code FROM (SELECT * FROM RECEIVE_GROUP_DATA WHERE MAIN_VERSION_CODE != #{mainVersionCode} and group_code = #{groupCode} and state = '904'
					   ORDER BY RECEIVE_DATE DESC)  WHERE ROWNUM = 1
	</select>

	<insert id="insertBatchByWorkHours" parameterType="list">
		INSERT INTO RECEIVE_GROUP_DATA(ID,GROUP_ID,GROUP_CODE,GROUP_NAME,MAIN_VERSION_CODE,RECEIVE_DATE,STATE,ERR_NUM,BRAND_ID,BRAND_NAME,BRAND_CODE,SERIES_ID,SERIES_NAME,SERIES_CODE,IS_MODIFY,END_DATE,TABLE_SUFFIX,ERR_MSG,DEL_FLAG)
		<foreach collection="list" item="item" separator="union all">
			SELECT
			sys_guid(),#{item.groupId,jdbcType=VARCHAR},#{item.groupCode,jdbcType=VARCHAR},#{item.groupName,jdbcType=VARCHAR},#{item.mainVersionCode,jdbcType=VARCHAR}
			,sysdate,'002','0',#{item.brandId,jdbcType=VARCHAR},#{item.brandName,jdbcType=VARCHAR}
			,#{item.brandCode,jdbcType=VARCHAR},#{item.seriesId,jdbcType=VARCHAR},#{item.seriesName,jdbcType=VARCHAR},#{item.seriesCode,jdbcType=VARCHAR},'0',sysdate,#{item.tableSuffix,jdbcType=VARCHAR},'','0'
			FROM dual
		</foreach>
	</insert>

	<select id="getConverMainField" resultType="map">
		select FIELD_NAME from CONVERSION_TABLE where TABLE_NAME = 'PJ_CLLBJDYB' and IS_MAIN = '1' and  DEL_FLAG = '0'
	</select>

	<select id="getFCLLByLjbzid" parameterType="map" resultType="map">
		select
		<foreach collection="list" item="item" separator=",">
			${item.fieldName}
		</foreach>
		,REPAIR_RELATION,id,ljbzbid,clzlid
		from F_PJ_CLLBJDYB_${brandCode} where clzlid = #{vehicleId} and ljbzbid in(select STD_PART_ID from work_hours where main_version_code = #{mainVersionCode})
	</select>

	<select id="getReceiveGroupDataModel" resultType="com.jy.bean.dto.ReceiveGroupDataDto" parameterType="String">
		select r.*,s.VERSION_CODE from  RECEIVE_GROUP_DATA r left join  SEND_TABLE s on r.MAIN_VERSION_CODE = s.MAIN_VERSION_CODE where r.MAIN_VERSION_CODE = #{_parameter}
	</select>

	<select id="getAllGroup" resultType="map">
		select id,cxid,BRAND_ID from P_ZC_CLFZXXB
	</select>

	<select id="selCxdybByVersionCode" parameterType="String" resultType="map">
		select * from C_PJ_ZC_CXDYB where version_code = #{_parameter}
	</select>

	<insert id="insertSendTable" parameterType="map">
		INSERT INTO SEND_TABLE(MAIN_VERSION_CODE,VERSION_CODE,SEND_TABLE,STATE,HANDLE_TABLE,CREATE_DATE,SEND_CODE)
			values (#{mainVersionCode},#{versionCode,jdbcType=VARCHAR},#{sendTable,jdbcType=VARCHAR},#{state,jdbcType=VARCHAR},#{handleTable},SYSDATE,#{sendCode,jdbcType=VARCHAR})
	</insert>

	<update id="updateSendTable" parameterType="map">
		update SEND_TABLE set state = #{state} where version_code = #{versionCode} and HANDLE_TABLE = 'ZC'
	</update>

	<select id="getConductCount" resultType="int">
		select count(1) from RECEIVE_GROUP_DATA where STATE not in('021','903','904','905','000','2000')
	</select>

    <select id="commercialVehicleList" resultType="map">
		SELECT id,part_num FROM p_zc_clfzxxb WHERE id in(
			SELECT id FROM p_truck_zc_clfzxxb
		) AND parts_suffix is null
    </select>

	<select id="getTruckVehicleId" resultType="map">
		SELECT pz.cxid,pp.zccxid sub_vehicle_id FROM p_truck_zc_clzlb pz
		 , p_pj_zc_cxdyb pp WHERE pz.id = pp.pjcxid and pp.exists_flag ='1'
	</select>

	<update id="updateVehicleTypeId" parameterType="map">
		UPDATE p_zc_clzlb p SET vehicle_type_id = (SELECT ID from f_pj_cxzdb f WHERE p.vehicle_type_code = f.CXBM)
		<if test="groupId != null and groupId != ''">
			<where>
				P.zbid = #{groupId}
			</where>
		</if>
	</update>

	<select id="getTableDataCount" parameterType="map" resultType="int">
		select count(1) from ${tableName} where version_code= #{versionCode}
	</select>

	<select id="getOneGroupDelCount" parameterType="String" resultType="int">
		SELECT count(1) FROM p_zc_clzlb_flag p WHERE not EXISTS
			(SELECT 1 FROM m_vehicle_info m WHERE p.id = m.part_vehicle_id)
		    and p.czid = #{_parameter} AND p.part_num > 0
	</select>

	<insert id="saveBrandRelation" parameterType="map">
		insert into p_pj_brand_relation(id,ppid,ppbm,ppmc,table_name,belong_flag,scbz,jlrq,jlrid,xgrq,xgrid,table_name_cz)
		values (sys_guid(),#{brandId},#{brandCode},#{brandName},#{tableName},'1','0',sysdate,'jy_system',sysdate,'jy_system',#{groupTableName})
	</insert>

	<update id="updateTruckClzVehicleType" parameterType="map">

		begin
			execute immediate 'truncate table ZH_PART_TEMP_001';
			insert into ZH_PART_TEMP_001(BZ,YCLJH,CXLJFZID,LJBZBM,ID,CLZLID)
			SELECT
				pjcxid,
				wm_concat( distinct VEHICLE_TYPE_CODE)  VEHICLE_TYPE_CODE,
				wm_concat( distinct grade_id)  grade_id,
				wm_concat( distinct grade_name)  grade_name,
				wm_concat( distinct grade_code)  grade_code,
				wm_concat( distinct VEHICLE_TYPE_NAME)  VEHICLE_TYPE_NAME
				<!--listagg (VEHICLE_TYPE_CODE, ',') WITHIN GROUP (ORDER BY VEHICLE_TYPE_CODE) VEHICLE_TYPE_CODE,
				listagg (grade_id, ',') WITHIN GROUP (ORDER BY grade_id) grade_id,
				listagg (grade_name, ',') WITHIN GROUP (ORDER BY grade_name) grade_name,
				listagg (grade_code, ',') WITHIN GROUP (ORDER BY grade_code) grade_code,
				listagg (VEHICLE_TYPE_NAME, ',') WITHIN GROUP (ORDER BY VEHICLE_TYPE_NAME) VEHICLE_TYPE_NAME-->
			FROM
				(
					SELECT DISTINCT
						VEHICLE_TYPE_CODE,grade_id,grade_name,grade_code,pz.pjcxid,VEHICLE_TYPE_NAME
					FROM
						SUB_VEHICLE_TYPE s
							LEFT JOIN P_PJ_ZC_CXDYB pz ON s.SUB_VEHICLE_ID = pz.zccxid and pz.exists_flag ='1'
					WHERE
						s.main_version_code = #{mainVersionCode}
				) group by pjcxid;
			UPDATE P_ZC_CLZLB P
			SET (VEHICLE_TYPE_CODE,grade_id,grade_name,grade_code
					) = (
				select YCLJH,CXLJFZID,LJBZBM,id from ZH_PART_TEMP_001 z where p.id = z.bz
			)
			WHERE
				EXISTS (
						SELECT 1 FROM ZH_PART_TEMP_001 z1 WHERE p.id = z1.bz
					);
		end;
	</update>
</mapper>
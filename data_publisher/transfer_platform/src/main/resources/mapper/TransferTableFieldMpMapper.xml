<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jy.mapper.TransferTableFieldMpMapper">

	<select id="listTransferTableFieldMp" resultType="com.jy.bean.po.TransferTableFieldMp">
		SELECT
		p.id,
		p.table_name,
		p.base_table_field,
		p.table_field
		FROM
		transfer_table_field_mp p
		<where>
			1 = 1
			<if test="_parameter.containsKey('tableName') and tableName != null">
				AND p.table_name = #{tableName}
			</if>
		</where>
	</select>

	<sql id="Base_Column_List">
		ID,
		TABLE_NAME,
		BASE_TABLE_FIELD,
		TABLE_FIELD,
		C_BY,
		C_TIME,
		U_BY,
		U_TIME,
		DEL_FLAG,
		REMARK
	</sql>

	<insert id="save" parameterType="com.jy.bean.po.TransferTableFieldMp">
		insert into transfer_table_field_mp (<include refid="Base_Column_List"/>)
		values (
			#{id,jdbcType=VARCHAR},
			#{tableName,jdbcType=VARCHAR},
			#{baseTableField,jdbcType=VARCHAR},
			#{tableField,jdbcType=VARCHAR},
			#{cBy,jdbcType=VARCHAR},
			sysdate,
			#{uBy,jdbcType=VARCHAR},
			sysdate,
			#{delFlag,jdbcType=VARCHAR},
			#{remark,jdbcType=VARCHAR}
		)
	</insert>

	<update id="update" parameterType="com.jy.bean.po.TransferTableFieldMp">
		update transfer_table_field_mp
		<set>
			<if test="baseTableField != null">
				base_table_field = #{baseTableField,jdbcType=VARCHAR},
			</if>
			<if test="tableName != null">
				table_name = #{tableName,jdbcType=VARCHAR},
			</if>
			<if test="tableField != null">
				table_field = #{tableField,jdbcType=VARCHAR},
			</if>
			<if test="cBy != null">
				c_by = #{cBy,jdbcType=VARCHAR},
			</if>
			<if test="uBy != null">
				u_by = #{uBy,jdbcType=VARCHAR},
			</if>
			<if test="remark != null">
				remark = #{remark,jdbcType=VARCHAR},
			</if>
			u_time = sysdate
		</set>
		where id = #{id,jdbcType=VARCHAR}
	</update>

	<delete id="delete">
		delete from transfer_table_field_mp
		where id = #{id,jdbcType=VARCHAR}
	</delete>

</mapper>
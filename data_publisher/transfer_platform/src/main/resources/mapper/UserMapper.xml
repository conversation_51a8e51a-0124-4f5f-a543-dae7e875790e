<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jy.mapper.UserMapper">

	<resultMap id="powerMap"   type="com.jy.bean.po.SysPowerPo">
		<result column="ID" property="id"></result>
		<result column="POWER_ORDER" property="powerOrder" jdbcType="VARCHAR"></result>
		<result column="POWER_LEVEL" property="powerLevel" jdbcType="VARCHAR"></result>
		<result column="POWER_NAME" property="powerName" jdbcType="VARCHAR"></result>
		<result column="URL" property="url" jdbcType="VARCHAR"></result>
		<result column="CTIME" property="ctime" jdbcType="DATE"></result>
		<result column="UTIME" property="utime" jdbcType="DATE"></result>
		<result column="ICON" property="icon" jdbcType="VARCHAR"></result>
		<result column="PARENTID" property="parentId" jdbcType="VARCHAR"></result>
	</resultMap>

	<!-- 根据用户id得到用户对应的所有菜单权限-->
	<select id="selectPowerByUserId" parameterType="java.lang.String" resultMap="powerMap">
	  select *
        from sys_power
       where id in (select srp.power_id
                from sys_role_power srp, sys_user u
               where u.role_code = srp.role_code
                 and u.DEL_FLAG = '0'
                 and u.id = #{userId}) and del_flag = '0' order by power_order asc
	</select>

	<!-- 根据用户名密码查询用户 -->
	 <select id="getUserPoByUp" resultType="com.jy.bean.po.UserPo" >
		select * from SYS_USER where  USER_NAME=#{userName} and PASS_WORD=#{passWord}
	 </select>


	<update id="updateUser" parameterType="com.jy.bean.po.UserPo">
		update SYS_USER u
		SET
		u.pass_word=#{passWord,jdbcType=VARCHAR},
		u.update_by=#{id,jdbcType=VARCHAR},
		u.update_time=#{updateTime,jdbcType=TIMESTAMP},
		u.user_name=#{userName,jdbcType=VARCHAR},
		u.role_code=#{roleCode,jdbcType=VARCHAR},
		u.phone=#{phone,jdbcType=VARCHAR},
		u.del_flag=#{delFlag,jdbcType=VARCHAR},
		u.email=#{email,jdbcType=VARCHAR}
		WHERE
		u.id=#{id}
	</update>

	<!-- 添加用户         BEGIN   -->
	<insert id="insertUserPo" parameterType="com.jy.bean.po.UserPo">
		INSERT INTO SYS_USER(
		pass_word,
		user_name,
		role_code,
		email,
		phone,
		CREATE_TIME,
		CREATE_BY,
		UPDATE_TIME,
		UPDATE_BY,
		DEL_FLAG)
		VALUES(
		#{passWord,jdbcType=VARCHAR},
		#{userName,jdbcType=VARCHAR},
		#{roleCode,jdbcType=VARCHAR},
		#{email,jdbcType=VARCHAR},
		#{phone,jdbcType=VARCHAR},
		#{createTime,jdbcType=TIMESTAMP},
		#{createBy,jdbcType=VARCHAR},
		#{updateTime,jdbcType=TIMESTAMP},
		#{updateBy,jdbcType=VARCHAR},
		#{delFlag,jdbcType=VARCHAR}
		)
	</insert>
	<!-- 添加用户         END   -->

	<!-- 查询用户名  -->
	<select id="selectUserPoByName" parameterType="java.lang.String" resultType="java.lang.Integer">
		select count(1) from SYS_USER WHERE USER_NAME =#{userName}
	</select>

	<!-- 根据用户id查询用户信息  -->
	<select id="getUserPoById" parameterType="java.lang.String" resultType="com.jy.bean.po.UserPo">
		select * from SYS_USER WHERE del_flag='0' and id =#{id}
	</select>

	<select id="getUserDtoById" parameterType="java.lang.String"  resultType="com.jy.bean.dto.UserDTO">
		select t.id,
				t.user_name,
				t.pass_word,
				t.email,
				t.phone,
				t.role_code,
				t.del_flag,
				t.create_by,
				t.create_time,
				t.update_by,
				t.update_time
		  from sys_user t
		  WHERE t.del_flag = '0' and t.id =#{id}
	</select>

    <sql id="publicQueryCriteria">
        <where>
			u.del_flag='0'
            <if test="dto.userName !=null and dto.userName !='' ">
                and u.user_Name = #{dto.userName}
            </if>
        </where>
    </sql>

	<select id="getUserList" parameterType="com.jy.bean.dto.UserDTO" resultType="com.jy.bean.dto.UserDTO">
		SELECT * FROM
		(
			SELECT A.*, ROWNUM RN
				FROM(
					select u.*,dic.name as roleName from sys_user u
					left join dict_info dic
					on dic.code = u.role_code
					<include refid="publicQueryCriteria"/>
					order by u.create_time DESC

				)A
			WHERE ROWNUM &lt;= #{limit}
		)
		WHERE RN &gt;= #{row}


	</select>

    <select id="selectCount" resultType="Long" parameterType="com.jy.bean.dto.UserDTO">
        select count(1) from sys_user u
        <include refid="publicQueryCriteria"/>
    </select>




</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jy.mapper.ProcedureMapper">

	<select id="tAnalyzeData" parameterType="map" statementType="CALLABLE" resultType="map">
		{call T_ANALYZE_DATA(#{status,mode=OUT,jdbcType=VARCHAR},#{message,mode=OUT,jdbcType=VARCHAR})}
	</select>

	<select id="partProcedure" parameterType="map" statementType="CALLABLE" resultType="map">
		{call pkg_convert_data.p_conver_part_procedure(#{mainBatchNo,mode=IN,jdbcType=VARCHAR},#{groupId,mode=IN,jdbcType=VARCHAR},#{status,mode=OUT,jdbcType=VARCHAR},#{message,mode=OUT,jdbcType=VARCHAR})}
	</select>

	<select id="wlPartProcedure" parameterType="map" statementType="CALLABLE" resultType="map">
		{call pkg_convert_data.p_conver_wl_part_procedure(#{mainBatchNo,mode=IN,jdbcType=VARCHAR},#{groupId,mode=IN,jdbcType=VARCHAR},#{status,mode=OUT,jdbcType=VARCHAR},#{message,mode=OUT,jdbcType=VARCHAR})}
	</select>

	<select id="compareFlagProcedure" parameterType="map" statementType="CALLABLE" resultType="map">
		{call p_veh_compare_data(#{mainBatchNo,mode=IN,jdbcType=VARCHAR},#{status,mode=OUT,jdbcType=VARCHAR},#{message,mode=OUT,jdbcType=VARCHAR})}
	</select>

	<select id="compareProcedure" parameterType="map" statementType="CALLABLE" resultType="map">
		{call p_compare_data(#{mainBatchNo,mode=IN,jdbcType=VARCHAR},#{tableName,mode=IN,jdbcType=VARCHAR},#{suffixFlag,mode=IN,jdbcType=VARCHAR},#{endFlag,mode=IN,jdbcType=VARCHAR},#{status,mode=OUT,jdbcType=VARCHAR},#{message,mode=OUT,jdbcType=VARCHAR})}
	</select>

	<select id="compareSyncProcedure" parameterType="map" statementType="CALLABLE" resultType="map">
		{call p_sync_compare_data(#{mainBatchNo,mode=IN,jdbcType=VARCHAR},#{tableName,mode=IN,jdbcType=VARCHAR},#{ppbm,mode=IN,jdbcType=VARCHAR},#{certainId,mode=IN,jdbcType=VARCHAR},#{status,mode=OUT,jdbcType=VARCHAR},#{message,mode=OUT,jdbcType=VARCHAR})}
	</select>

	<select id="updateProcedure" parameterType="map" statementType="CALLABLE" resultType="map">
		{call p_update_data(#{mainBatchNo,mode=IN,jdbcType=VARCHAR},#{tableName,mode=IN,jdbcType=VARCHAR},#{suffixFlag,mode=IN,jdbcType=VARCHAR},#{endFlag,mode=IN,jdbcType=VARCHAR},#{status,mode=OUT,jdbcType=VARCHAR},#{message,mode=OUT,jdbcType=VARCHAR})}
	</select>

	<select id="createFCTable" parameterType="map" statementType="CALLABLE" resultType="map">
		{call create_f_c_table(#{groupId,mode=IN,jdbcType=VARCHAR},#{tableNum,mode=OUT,jdbcType=INTEGER},#{partSuffix,mode=OUT,jdbcType=VARCHAR},#{status,mode=OUT,jdbcType=VARCHAR},#{message,mode=OUT,jdbcType=VARCHAR})}
	</select>

	<select id="replaceProcedure" parameterType="map" statementType="CALLABLE" resultType="map">
		{call pkg_convert_data.p_conver_replace_procedure(#{mainBatchNo,mode=IN,jdbcType=VARCHAR},#{status,mode=OUT,jdbcType=VARCHAR},#{message,mode=OUT,jdbcType=VARCHAR})}
	</select>

	<select id="stdPartProcedure" parameterType="map" statementType="CALLABLE" resultType="map">
		{call pkg_convert_data.p_conver_std_part_procedure(#{mainBatchNo,mode=IN,jdbcType=VARCHAR},#{status,mode=OUT,jdbcType=VARCHAR},#{message,mode=OUT,jdbcType=VARCHAR})}
	</select>

	<select id="truncateMData" parameterType="map" statementType="CALLABLE" resultType="map">
		{call ${mid}.TRUNCATE_TABLE(#{tableName,mode=IN,jdbcType=VARCHAR})}
	</select>

	<select id="truncatePData" parameterType="map" statementType="CALLABLE" resultType="map">
		{call ${product}.TRUNCATE_TABLE(#{tableName,mode=IN,jdbcType=VARCHAR})}
	</select>

</mapper>
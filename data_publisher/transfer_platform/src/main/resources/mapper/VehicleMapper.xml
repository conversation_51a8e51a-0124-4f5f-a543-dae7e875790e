<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jy.mapper.VehicleMapper">

	<select id="vehicleConversion" resultType="map" parameterType="map">
		select pjcxid claim_vehicle_id,zccxbm_rb vehicle_code from F_PJ_ZC_CXDYB fc where exists_flag = 1 and zccxbm_rb in
		<foreach collection="list" separator="," item="item" open="(" close=")">
			#{item}
		</foreach>
	</select>

	<delete id="deleteZcClzlb" parameterType="map" >
		delete from p_zc_clzlb
		where zbid in ${czids}
		and type_flag = '1'
	</delete>

	<delete id="deleteZcClzlbFlag" parameterType="map" >
		delete from p_zc_clzlb_flag a where
		exists (
		select 1
		from p_zc_clzlb
		where a.id = id and type_flag = '1' and zbid in ${czids}
		)
	</delete>


	<delete id="deletePjZcCxdyb" parameterType="map" >
		delete from p_pj_zc_cxdyb a where
		exists (
		select 1
		from p_zc_clzlb
		where a.pjcxid = id and type_flag = '1' and zbid in ${czids}
		)
	</delete>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jy.mapper.GroupBrandAssocitedMapper">

	<select id="listGroupBrandAssocited" resultType="com.jy.bean.po.GroupBrandAssocited">
		select * from GROUP_BRAND_ASSOCIATED
		<where>
			<if test="_parameter.containsKey('tableName') and tableName != null and tableName != ''">
				and TABLE_NAME = #{tableName}
			</if>
			<if test="_parameter.containsKey('isBrandCode') and isBrandCode != null and isBrandCode != ''">
				and is_brand_code = #{isBrandCode}
			</if>
		</where>
	</select>

</mapper>
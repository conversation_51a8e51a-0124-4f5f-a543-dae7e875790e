<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jy.mapper.WorkHoursMapper">

	<insert id="saveWorkHours" parameterType="com.jy.bean.dto.WorkHoursDto">
		insert into work_hours (ID,MAIN_VERSION_CODE,GROUP_ID,STD_PART_ID,OPERATE_CODE,CHANGE_CODE,BRAND_ID)
		<foreach collection="list" item="item" separator="union all">
			select sys_guid(),#{mainVersionCode},#{groupId},#{item.stdPartId},#{item.operateCode},#{item.changeCode},#{brandId} from dual
		</foreach>
	</insert>

	<select id="getPJMainCloum" resultType="map">
		select FIELD_NAME from CONVERSION_TABLE where TABLE_NAME = 'PJ_CLLBJDYB' and IS_MAIN = '1' and DEL_FLAG = '0'
	</select>

	<select id="getWorkHoursListByMainVersionCode" parameterType="String" resultType="com.jy.bean.dto.WorkHoursDto">
		select STD_PART_ID,OPERATE_CODE,CHANGE_CODE,VEHICLE_ID from work_hours where main_version_code = #{_parameter}
	</select>
</mapper>
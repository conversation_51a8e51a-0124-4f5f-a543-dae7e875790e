<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jy.mapper.ReceiveBatchMapper">

	<select id="listReceiveBatch" resultType="com.jy.bean.po.ReceiveBatch">
		SELECT
		p.certain_id,
		p.certain_code,
		p.certain_name,
		p.main_batch_no,
		p.status,
		p.c_time,
		p.client_code,
		p.transfer_order
		FROM
		receive_batch p
		<where>
			1 = 1
			<if test="_parameter.containsKey('mainBatchNo') and mainBatchNo != null">
				AND p.main_batch_no = #{mainBatchNo}
			</if>
			<if test="_parameter.containsKey('status') and status != null">
				AND p.status = #{status}
			</if>
			<if test="_parameter.containsKey('errorStatuses') and errorStatuses != null">
				AND p.status not in ${errorStatuses}
			</if>
		</where>
		order by transfer_order asc, c_time asc
	</select>

	<select id="listBatchByApCl" resultType="com.jy.bean.po.ReceiveBatch">
		select b.main_batch_no
		from RECEIVE_BATCH  a, SEND_DETAIL b
		where a.MAIN_BATCH_NO = b.MAIN_BATCH_NO
		and a.MAIN_BATCH_NO like 'ClVeh_%'
		and b.TABLE_NAME ='pj_zc_cxdyb'
		and a.notice_status is null
		and b.status ='200'
		group by b.main_batch_no order by b.main_batch_no asc
	</select>


	<sql id="Base_Column_List">
		ID,
		CERTAIN_ID,
		CERTAIN_CODE,
		CERTAIN_NAME,
		MAIN_BATCH_NO,
		STATUS,
		C_TIME,
		U_TIME,
		DEL_FLAG,
		C_BY,
		U_BY,
		REMARK,
		TRANSFER_ORDER,
		CLIENT_CODE
	</sql>

	<insert id="save" parameterType="com.jy.bean.po.ReceiveBatch">
		insert into receive_batch (<include refid="Base_Column_List"/>)
		values (
			#{id,jdbcType=VARCHAR},
			#{certainId,jdbcType=VARCHAR},
			#{certainCode,jdbcType=VARCHAR},
			#{certainName,jdbcType=VARCHAR},
			#{mainBatchNo,jdbcType=VARCHAR},
			#{status,jdbcType=VARCHAR},
			systimestamp,
			systimestamp,
			#{delFlag,jdbcType=VARCHAR},
			#{cBy,jdbcType=VARCHAR},
			#{uBy,jdbcType=VARCHAR},
			#{remark,jdbcType=VARCHAR},
			#{transferOrder,jdbcType=INTEGER},
			#{clientCode,jdbcType=VARCHAR}
		)
	</insert>

	<update id="update" parameterType="com.jy.bean.po.ReceiveBatch">
		update receive_batch
		<set>
			<if test="status != null">
				status = #{status,jdbcType=VARCHAR},
			</if>
			<if test="noticeStatus != null">
				notice_status = #{noticeStatus,jdbcType=VARCHAR},
			</if>
			<if test="remark != null">
				remark = #{remark,jdbcType=VARCHAR},
			</if>
			<if test="transferOrder != null">
				transfer_order = #{transferOrder,jdbcType=INTEGER},
			</if>
			<if test="clientCode != null">
				client_code = #{clientCode,jdbcType=VARCHAR},
			</if>
			u_time = systimestamp
		</set>
		where main_batch_no = #{mainBatchNo,jdbcType=VARCHAR}
	</update>


	<update id="updateWaitStatus" >
		update receive_batch
		set status = #{receiveStatus,jdbcType=VARCHAR} where status = #{waitStatus,jdbcType=VARCHAR}
	</update>
</mapper>

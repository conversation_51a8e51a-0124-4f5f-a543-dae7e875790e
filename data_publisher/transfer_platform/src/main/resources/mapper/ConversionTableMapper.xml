<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jy.mapper.ConversionTableMapper">


	<select id="listConversionTable" resultType="com.jy.bean.po.ConversionTable">
		select *
		from CONVERSION_TABLE
		<where>
			<if test="_parameter.containsKey('tableName') and tableName != null and tableName != ''">
				and table_name = upper(#{tableName})
			</if>
		</where>
	</select>


</mapper>
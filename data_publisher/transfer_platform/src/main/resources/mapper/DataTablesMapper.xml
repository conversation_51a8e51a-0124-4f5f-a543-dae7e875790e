<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jy.mapper.DataTablesMapper">

	<select id="getListByPage" parameterType="map" resultType="com.jy.bean.dto.DataTablesDTO">
		SELECT * FROM
		(
		SELECT A.*, ROWNUM RN
		FROM(
		SELECT id,TABLE_NAME tableName,IS_ENABLE isEnable,CREATE_USER createUser,CREATE_TIME createTime,TABLE_STRUCTURE,IS_SEPARATE,TYPE,merge_table FROM data_tables
		<include refid="where"></include>
		ORDER  BY IS_ENABLE DESC,UPDATE_TIME DESC
		)A
		WHERE ROWNUM &lt;= #{limit}
		)
		WHERE RN &gt;= #{row}
	</select>

	<select id="getListCount" parameterType="map" resultType="long">
		SELECT NVL(count(*),0) FROM data_tables
		<include refid="where"></include>
	</select>

	<insert id="insert" parameterType="com.jy.bean.dto.DataTablesDTO">
		INSERT INTO data_tables(ID,TABLE_NAME,IS_ENABLE,CREATE_USER,CREATE_TIME,UPDATE_USER,UPDATE_TIME,TYPE,TABLE_STRUCTURE,IS_SEPARATE,merge_table) VALUES (lower(sys_guid()),#{tableName},#{isEnable},#{createUser},sysdate,#{createUser},sysdate,#{type},#{tableStructure},#{isSeparate},#{mergeTable,jdbcType=VARCHAR})
	</insert>

	<update id="update" parameterType="com.jy.bean.dto.DataTablesDTO">
		UPDATE data_tables
		<trim prefix="set" suffixOverrides=",">
			UPDATE_USER = #{updateUser},UPDATE_TIME = sysdate,
			<if test="tableName != null and tableName != ''">
				TABLE_NAME = #{tableName},
			</if>
			<if test="isEnable != null and isEnable != ''">
				IS_ENABLE = #{isEnable},
			</if>
			<if test="type != null and type != ''">
				TYPE = #{type},
			</if>
			<if test="tableStructure != null and tableStructure != ''">
				TABLE_STRUCTURE = #{tableStructure},
			</if>
			<if test="isSeparate != null and isSeparate != ''">
				IS_SEPARATE = #{isSeparate},
			</if>
			<if test="mergeTable != null and mergeTable != ''">
				merge_table = #{mergeTable}
			</if>
		</trim>
		WHERE id = #{id}
	</update>

	<delete id="deleteById" parameterType="String">
		DELETE FROM data_tables WHERE id = #{_parameter}
	</delete>

	<sql id="where">
		<where>
			1 = 1
			<if test="id != null and id != ''">
				AND id = #{id}
			</if>
			<if test="tableName != null and tableName != ''">
				AND TABLE_NAME = #{tableName}
			</if>
			<if test="isEnable != null and isEnable != ''">
				AND IS_ENABLE = #{isEnable}
			</if>
			<if test="createUser != null and createUser != ''">
				AND CREATE_USER = #{createUser}
			</if>
		</where>
	</sql>

	<select id="getListByEnable" parameterType="map" resultType="map">
		SELECT table_name,TABLE_STRUCTURE,IS_SEPARATE,merge_table FROM data_tables WHERE is_enable = '1'
		<if test="type != null and type != ''">
			AND TYPE = #{type}
		</if>
		<if test="tableName != null and tableName != ''">
			AND TABLE_NAME = #{tableName}
		</if>
	</select>

	<select id="isExistsTable" resultType="int">
		select nvl(count(*),0) from dba_tables where owner = 'PART_MID' and table_name = #{tableName}
	</select>
</mapper>
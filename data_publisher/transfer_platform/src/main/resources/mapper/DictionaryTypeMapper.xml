<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jy.mapper.DictionaryTypeMapper">

	<!-- 字典列表 -->
	<select id="getDictionaryTypeList" parameterType="java.lang.String" resultType="com.jy.bean.po.DictInfo" >
		select t.name, t.code ,t.id
		from dict_info t
		where t.del_Flag='0' and  t.type = #{type}
	</select>

	<!-- 根据字典编码得到名称 -->
	<select id="getDictNameByCode" parameterType="java.lang.String" resultType="java.lang.String" >
		select t.name
		from dict_info t
		where t.del_Flag='0' and  t.code = #{code}
	</select>

</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jy.mapper.SendStateMapper">

	<insert id="SaveSendState" parameterType="com.jy.bean.dto.SendStateDto">
		insert into send_state(id,SEND_TABLE,send_state,send_time,send_code,VEHICLE_ID,send_table_id)
		values(#{id},#{sendTable},#{sendState},sysdate,#{sendCode},#{vehicleId},#{sendTableId})
	</insert>

	<update id="updateSendState" parameterType="map">
		update send_state set send_state = #{state}
		<if test="sendNum != null and sendNum != ''">
			,send_num = #{sendNum}
		</if>
		where id = #{stateId}
	</update>

	<select id="getSendListByTableId" parameterType="map" resultType="map">
		select id state_id,send_state from send_state where send_table_id = #{id}
	  	<if test="vehicleId != null and vehicleId != ''">
			and VEHICLE_ID = #{vehicleId}
		</if>
	</select>

	<select id="listSendState" resultType="com.jy.bean.dto.SendStateDto">
		select
		SEND_TABLE,
		send_state,
		send_time,
		send_code,
		VEHICLE_ID,
		send_table_id
		from send_state
		<where>
			<if test="_parameter.containsKey('sendTableIds') and sendTableIds != null and sendTableIds != ''">
				and send_table_id in ${sendTableIds}
			</if>
		</where>
	</select>

	<select id="listCompareData" resultType="com.jy.bean.dto.SendStateDto">
		SELECT
		aa.*, D .TABLE_NAME AS deleteTable
		FROM
		(
		SELECT
		A .CLIENT_CODE || '_' || A .CLIENT_URL AS clientKey,
		A .MAIN_VERSION_CODE,
		c.SEND_CODE,
		c.SEND_TIME,
		c.SEND_TABLE
		FROM
		RECEIVE_GROUP_DATA A,
		SEND_TABLE b,
		SEND_STATE c
		<where>
			a.MAIN_VERSION_CODE = b.MAIN_VERSION_CODE AND b. ID = c.SEND_TABLE_ID
			<if test="_parameter.containsKey('compareBatchNo') and compareBatchNo != null and compareBatchNo != ''">
				and a.COMPARE_BATCH_NO = #{compareBatchNo}
			</if>
		</where>
		) aa
		LEFT JOIN COMPARE_ERROR_DATA D ON aa.main_version_code = D .main_version_code
	</select>


</mapper>
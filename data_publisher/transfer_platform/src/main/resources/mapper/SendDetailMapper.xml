<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jy.mapper.SendDetailMapper">

	<select id="listSendDetail" resultType="com.jy.bean.po.SendDetail">
		SELECT
		p.id,
		p.table_name,
		p.total,
		p.batch_no,
		p.main_batch_no,
		p.status,
		p.c_table_name,
		p.message
		FROM
		send_detail p
		<where>
			1 = 1
			<if test="_parameter.containsKey('mainBatchNo') and mainBatchNo != null">
				AND p.main_batch_no = #{mainBatchNo}
			</if>
			<if test="_parameter.containsKey('batchNo') and batchNo != null">
				AND p.batch_no = #{batchNo}
			</if>
		</where>
		order by status desc, c_time asc
	</select>

	<sql id="Base_Column_List">
		ID,
		TABLE_NAME,
		C_TABLE_NAME,
		STATUS,
		MESSAGE,
		BATCH_NO,
		MAIN_BATCH_NO,
		TOTAL,
		C_BY,
		C_TIME,
		U_BY,
		U_TIME,
		DEL_FLAG,
		REMARK
	</sql>

	<insert id="save" parameterType="com.jy.bean.po.SendDetail">
		insert into send_detail (<include refid="Base_Column_List"/>)
		values (
			#{id,jdbcType=VARCHAR},
			#{tableName,jdbcType=VARCHAR},
			#{cTableName,jdbcType=VARCHAR},
			#{status,jdbcType=VARCHAR},
			#{message,jdbcType=VARCHAR},
			#{batchNo,jdbcType=VARCHAR},
			#{mainBatchNo,jdbcType=VARCHAR},
			#{total,jdbcType=INTEGER},
			#{cBy,jdbcType=VARCHAR},
			#{cTime,jdbcType=TIMESTAMP},
			#{uBy,jdbcType=VARCHAR},
			#{uTime,jdbcType=TIMESTAMP},
			#{delFlag,jdbcType=VARCHAR},
			#{remark,jdbcType=VARCHAR}
		)
	</insert>

	<update id="update" parameterType="com.jy.bean.po.SendDetail">
		update send_detail
		<set>
			<if test="status != null">
				status = #{status,jdbcType=VARCHAR},
			</if>
			<if test="message != null">
				message = #{message,jdbcType=VARCHAR},
			</if>
			<if test="remark != null">
				remark = #{remark,jdbcType=VARCHAR},
			</if>
			u_time = #{uTime,jdbcType=TIMESTAMP}
		</set>
		where batch_no = #{batchNo,jdbcType=VARCHAR}
	</update>

</mapper>
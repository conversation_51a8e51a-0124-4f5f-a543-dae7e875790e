<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jy.mapper.CommonMapper">

	<select id="getTableCount" parameterType="map" resultType="int">
		select count(1) from ${tableName}
		<where>
			1=1
			<if test="_parameter.containsKey('mainBatchNo') and mainBatchNo != null">
				and main_code = #{mainBatchNo}
			</if>
		</where>
	</select>

	<select id="getTableData" parameterType="map" resultType="map">
		select * from ${tableName}
		<where>
			1=1
			<if test="_parameter.contains<PERSON>ey('mainBatchNo') and mainBatchNo != null">
				and main_code = #{mainBatchNo}
			</if>
			and data_order &gt; #{startNum} and data_order &lt;= #{endNum}
		</where>
	</select>


	<select id="existPartTable" parameterType="map" resultType="int">
		select nvl(COUNT(*),0) from user_synonyms where (table_OWNER = '${final}' OR table_OWNER = '${compare}') and
		(table_name = 'PJ_CLLBJDYB_${partSuffix}'
		 OR table_name = 'PJ_CLLJTXDYB_${partSuffix}'
		 OR table_name = 'PJ_CZLBJDYB_${partSuffix}')
	</select>

	<select id="getPartSuffix" parameterType="map" resultType="String">
		select brand_code as table_suffix from m_group_brand_suffix b where b.group_id = #{groupId}
	</select>

	<select id="getNullPartNameByGroupCode" parameterType="String" resultType="map">
		select part_name,id
		from f_part_name
		where group_id=#{_parameter}
		and hz_number is null
	</select>

	<select id="getMaxHzNumber" parameterType="String" resultType="map">
		select part_name,max(nvl(hz_number,0)) hz_number
		from f_part_name
		where group_id=#{_parameter}
		group by part_name
	</select>

	<update id="updateHzNumber" parameterType="map">
		<foreach collection="list" open="begin" separator=";" close=";end;" item="item">
			update f_part_name
			set hz_number = #{item.number}
			where id=#{item.id}
		</foreach>
	</update>

	<update id="updateCllbjdyb">
		update p_pj_cllbjdyb p
		set (ljbzmc,ycljmc)=(select f.part_name||f.hz_number,f.part_name||f.hz_number
		from f_part_name f
		where p.ycljh_replace=f.tmp_part_number
		and p.ljbzmc=f.part_name
		and f.group_id=p.czid)
		where p.ljbzbm ='999999' and exists(select 1
		from f_part_name k
		where p.ycljh_replace=k.tmp_part_number
		and p.ljbzmc=k.part_name
		and p.czid=k.group_id)
	</update>

	<update id="updateWLCllbjdyb">
		update p_wl_pj_cllbjdyb p
		set (ljbzmc)=(select f.part_name||f.hz_number
		from f_part_name f
		where p.ycljh_replace=f.tmp_part_number
		and p.ljbzmc=f.part_name
		and f.group_id=p.czid)
		where exists(select 1
		from f_part_name k
		where p.ycljh_replace=k.tmp_part_number
		and p.ljbzmc=k.part_name
		and p.czid=k.group_id)
	</update>

	<update id="updateIncGraphGroup">
		update p_ori_repair_detail p set p.inc_group_id = (select inc_group_id from p_ori_repair_inc where p.cl_vehicle_id = cl_vehicle_id
		and (p.ori_repair_id = ori_repair_id or p.ori_repair_id = f_ori_repair_id) and rownum=1)
		where p.cl_vehicle_id = #{clVehicleId} and exists (select 1 from p_ori_repair_inc where p.cl_vehicle_id = cl_vehicle_id
		and (p.ori_repair_id = ori_repair_id or p.ori_repair_id = f_ori_repair_id) and rownum=1)
	</update>

	<select id="listCertain" parameterType="map" resultType="String">
		select distinct ${certain} from ${tableName}
	</select>

	<select id="listCertainByWhere" parameterType="map" resultType="map">
		select distinct ${certain} from ${tableName}
		where ${whereKey} = #{whereValue}
	</select>

</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jy.mapper.BatchDetailMapper">

	<select id="listBatchDetail" resultType="com.jy.bean.po.BatchDetail">
		SELECT
		p.table_name,
		p.file_path,
		p.main_batch_no,
		p.batch_no,
		p.status
		FROM
		batch_detail p
		<where>
			1 = 1
			<if test="_parameter.containsKey('mainBatchNo') and mainBatchNo != null">
				AND p.main_batch_no = #{mainBatchNo}
			</if>
		</where>
	</select>

	<sql id="Base_Column_List">
		ID,
		MAIN_BATCH_NO,
		BATCH_NO,
		TABLE_NAME,
		STATUS,
		FILE_PATH,
		C_BY,
		C_TIME,
		U_BY,
		U_TIME,
		DEL_FLAG,
		REMARK
	</sql>

	<insert id="save" parameterType="com.jy.bean.po.BatchDetail">
		insert into batch_detail (<include refid="Base_Column_List"/>)
		values (
		#{id,jdbcType=VARCHAR},
		#{mainBatchNo,jdbcType=VARCHAR},
		#{batchNo,jdbcType=VARCHAR},
		#{tableName,jdbcType=VARCHAR},
		#{status,jdbcType=VARCHAR},
		#{filePath,jdbcType=VARCHAR},
		#{cBy,jdbcType=VARCHAR},
		sysdate,
		#{uBy,jdbcType=VARCHAR},
		sysdate,
		#{delFlag,jdbcType=VARCHAR},
		#{remark,jdbcType=VARCHAR}
		)
	</insert>

	<update id="update" parameterType="com.jy.bean.po.BatchDetail">
		update batch_detail
		<set>
			<if test="state != null">
				status = #{state,jdbcType=VARCHAR},
			</if>
			<if test="remark != null">
				remark = #{remark,jdbcType=VARCHAR},
			</if>
			u_time = sysdate
		</set>
		where main_batch_no = #{mainBatchNo,jdbcType=VARCHAR}
		and table_name = #{tableName,jdbcType=VARCHAR}
	</update>

</mapper>
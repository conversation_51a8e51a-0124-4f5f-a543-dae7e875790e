<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jy.mapper.PowerMapper">

	<!-- 删除角色对应的权限-->
	<delete id="delPowers"  parameterType="java.lang.String">
		delete from sys_role_power
		<where>
			role_code = #{roleCode}
			<if test="powerIds!=null and powerIds.length!=0">
				AND power_id in
				<foreach collection="powerIds" item="powerId"
						 index="index" open="(" close=")" separator=",">
					#{powerId}
				</foreach>
			</if>
		</where>
	</delete>

	<!-- 角色添加对应的菜单 -->
	<insert id="addRolePower" parameterType="com.jy.bean.po.UserPo">
		insert into sys_role_power(ID,ROLE_CODE,POWER_ID,CREATE_TIME,CREATE_BY,UPDATE_TIME,UPDATE_BY,DEL_FLAG) VALUES (
				sys_guid(),
				#{userPo.roleCode},
				#{userPo.powerId},
				sysdate,
				#{userPo.userName},
				sysdate,
				#{userPo.userName},
				'0'
		)

	</insert>

	<!-- 查询角色已经拥有的菜单 -->
	<select id="getHavePowerList" parameterType="java.lang.String" resultType="com.jy.bean.po.SysPowerPo">
		select s.*,s.PARENTID as parentId
  		from sys_power s
 		where id in (select power_id from sys_role_power where role_code = #{roleCode}) order by power_order
	</select>


	<select id="getNonePowerList" parameterType="java.lang.String" resultType="com.jy.bean.po.SysPowerPo">
		 select *
		 from sys_power
		 where id not in (select power_id from sys_role_power where role_code = #{roleCode}) order by power_order
	</select>


	<insert id="addMenu" parameterType="com.jy.bean.po.SysPowerPo" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO sys_power (
		    id,
		    power_order,
		    power_level,
		    power_name,
		    url,
		    ctime,
		    utime,
		    icon,
		    parentid,
		    del_flag
		  )
		VALUES
		  (
			lower(sys_guid()),
			#{powerOrder},
			#{powerLevel},
			#{powerName},
			#{url,jdbcType=VARCHAR},
			sysdate,
			sysdate,
			'ico_nav_backstage',
			#{parentId,jdbcType=VARCHAR},
			'0'
			)
	</insert>

	<select id="findMenuCount" parameterType="com.jy.bean.dto.UserDTO" resultType="java.lang.Long">
		select count(1) from sys_power
		where del_flag = '0'
	</select>

	<select id="findMenuByList" parameterType="com.jy.bean.dto.UserDTO" resultType="com.jy.bean.po.SysPowerPo">
		select * from sys_power
		where del_flag = '0'
	</select>

	<select id="findMenuList" parameterType="java.lang.String" resultType="com.jy.bean.po.SysPowerPo">
		select * from sys_power
		where del_flag = '0' and power_level = '1'
		<if test="id != '' and id != null">
			and parentid = #{id}
		</if>


	</select>

	<update id="delMenu" parameterType="java.lang.String">

		update sys_power set del_flag = '1'
		where id = #{id}

	</update>

	<update id="updateMenu" parameterType="com.jy.bean.po.SysPowerPo" >
		update sys_power
		   set
			   power_order = #{powerOrder},
			   power_level = #{powerLevel},
			   power_name = #{powerName},
			   url = #{url},
			   sysdate,
			   sysdate,
			   parentid = #{parentId}
		where id = #{id}



	</update>



</mapper>
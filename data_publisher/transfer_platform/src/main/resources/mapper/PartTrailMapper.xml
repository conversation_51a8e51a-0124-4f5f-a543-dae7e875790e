<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jy.mapper.PartTrailMapper">
	<sql id="Base_Column_List">
		MAIN_BATCH_NO,
		PPID,
		PPBM,
		PPMC,
		CXID,
		CXBM,
		CXMC,
		STATUS,
		C_TIME,
		U_TIME,
		DEL_FLAG,
		C_BY,
		U_BY,
		REMARK
	</sql>

	<insert id="save" parameterType="com.jy.bean.po.PartTrail">
		insert into f_part_trail (<include refid="Base_Column_List"/>)
		values (
			#{mainBatchNo,jdbcType=VARCHAR},
			#{ppid,jdbcType=VARCHAR},
			#{ppbm,jdbcType=VARCHAR},
			#{ppmc,jdbcType=VARCHAR},
			#{cxid,jdbcType=VARCHAR},
			#{cxbm,jdbcType=VARCHAR},
			#{cxmc,jdbcType=VARCHAR},
			#{status,jdbcType=VARCHAR},
			systimestamp,
			systimestamp,
			#{delFlag,jdbcType=VARCHAR},
			#{cBy,jdbcType=VARCHAR},
			#{uBy,jdbcType=VARCHAR},
			#{remark,jdbcType=VARCHAR}
		)
	</insert>

	<update id="update" parameterType="com.jy.bean.po.PartTrail">
		update f_part_trail
		<set>
			<if test="status != null">
				status = #{status,jdbcType=VARCHAR},
			</if>
			<if test="remark != null">
				remark = #{remark,jdbcType=VARCHAR},
			</if>
			u_time = systimestamp
		</set>
		where main_batch_no = #{mainBatchNo,jdbcType=VARCHAR}
	</update>


</mapper>
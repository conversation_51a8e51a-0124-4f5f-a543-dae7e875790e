<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jy.mapper.CompareErrorDataMapper">

	<select id="listCompareErrorData" resultType="com.jy.bean.po.CompareErrorData">
		select * from COMPARE_ERROR_DATA
		<where>
			<if test="_parameter.containsKey('tableName') and tableName != null and tableName != ''">
				and TABLE_NAME = #{tableName}
			</if>
			<if test="_parameter.containsKey('versionCode') and versionCode != null and versionCode != ''">
				and version_code = #{versionCode}
			</if>
			<if test="_parameter.containsKey('compareBatchNo') and compareBatchNo != null and compareBatchNo != ''">
				and COMPARE_BATCH_NO = #{compareBatchNo}
			</if>
		</where>
	</select>

	<sql id="Base_Column_List">
		ID,
		TABLE_NAME,
		RECEIVE_DATE,
		MAIN_VERSION_CODE,
		VERSION_CODE,
		MESSAGE
	</sql>

	<insert id="insert" parameterType="com.jy.bean.po.CompareErrorData">
		INSERT INTO COMPARE_ERROR_DATA(<include refid="Base_Column_List"/>)
		VALUES (
		#{id,jdbcType=VARCHAR},
		#{tableName,jdbcType=VARCHAR},
		SYSDATE,
		#{mainVersionCode,jdbcType=VARCHAR},
		#{versionCode,jdbcType=VARCHAR},
		#{message,jdbcType=VARCHAR}
		)
	</insert>


</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jy.mapper.AutoTestMapper">

	<delete id="deleteDataQuery" >
		delete from ${tableName}
		<where>
			<if test="_parameter.containsKey('id') and id != null and id != ''">
				and id like 'AUTO_TEST%'
			</if>
			<if test="_parameter.containsKey('czid') and czid != null and czid != ''">
				and czid like 'AUTO_TEST%'
			</if>
			<if test="_parameter.containsKey('ppid') and ppid != null and ppid != ''">
				and ppid like 'AUTO_TEST%'
			</if>
			<if test="_parameter.containsKey('clzlid') and clzlid != null and clzlid != ''">
				and clzlid like 'AUTO_TEST%'
			</if>
		</where>
	</delete>


</mapper>
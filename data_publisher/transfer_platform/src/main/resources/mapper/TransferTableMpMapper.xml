<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jy.mapper.TransferTableMpMapper">

	<select id="listTransferTableMp" resultType="com.jy.bean.po.TransferTableMp">
		SELECT
		p.id,
		p.base_table_name,
		p.table_name,
		p.suffix_flag,
		p.transfer_flag
		FROM
		transfer_table_mp p
		<where>
			1 = 1
			<if test="_parameter.containsKey('baseTableName') and baseTableName != null">
				AND p.base_table_name = #{baseTableName}
			</if>
			<if test="_parameter.containsKey('baseTableNames') and baseTableNames != null">
				and p.base_table_name in ${baseTableNames}
			</if>
			<if test="_parameter.containsKey('partSuffix') and partSuffix != null and partSuffix != 'WLM0'">
				and p.table_name != 'wl_pj_cllbjdyb' and table_name != 'WL_PJ_CLLBJDYB'
			</if>
			<if test="_parameter.containsKey('transferFlag') and transferFlag != null">
				AND p.transfer_flag = #{transferFlag}
			</if>
		</where>
	</select>

	<sql id="Base_Column_List">
		ID,
		BASE_TABLE_NAME,
		TABLE_NAME,
		SUFFIX_FLAG,
		C_BY,
		C_TIME,
		U_BY,
		U_TIME,
		DEL_FLAG,
		REMARK,
		TRANSFER_FLAG
	</sql>

	<insert id="save" parameterType="com.jy.bean.po.TransferTableMp">
		insert into transfer_table_mp (<include refid="Base_Column_List"/>)
		values (
			#{id,jdbcType=VARCHAR},
			#{baseTableName,jdbcType=VARCHAR},
			#{tableName,jdbcType=VARCHAR},
			#{suffixFlag,jdbcType=VARCHAR},
			#{cBy,jdbcType=VARCHAR},
			sysdate,
			#{uBy,jdbcType=VARCHAR},
			sysdate,
			#{delFlag,jdbcType=VARCHAR},
			#{remark,jdbcType=VARCHAR},
			#{transferFlag,jdbcType=VARCHAR}
		)
	</insert>

	<update id="update" parameterType="com.jy.bean.po.TransferTableMp">
		update transfer_table_mp
		<set>
			<if test="baseTableName != null">
				base_table_name = #{baseTableName,jdbcType=VARCHAR},
			</if>
			<if test="tableName != null">
				table_name = #{tableName,jdbcType=VARCHAR},
			</if>
			<if test="suffixFlag != null">
				suffix_flag = #{suffixFlag,jdbcType=VARCHAR},
			</if>
			<if test="transferFlag != null">
				transfer_flag = #{transferFlag,jdbcType=VARCHAR},
			</if>
			<if test="cBy != null">
				c_by = #{cBy,jdbcType=VARCHAR},
			</if>
			<if test="uBy != null">
				u_by = #{uBy,jdbcType=VARCHAR},
			</if>
			<if test="remark != null">
				remark = #{remark,jdbcType=VARCHAR},
			</if>
			u_time = sysdate
		</set>
		where id = #{id,jdbcType=VARCHAR}
	</update>

	<delete id="delete">
		delete from transfer_table_mp
		where id = #{id,jdbcType=VARCHAR}
	</delete>

</mapper>
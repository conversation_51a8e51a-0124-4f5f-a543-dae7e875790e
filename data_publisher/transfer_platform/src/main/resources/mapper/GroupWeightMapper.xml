<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jy.mapper.GroupWeightMapper">

   <delete id="deleteAll" >
        delete from group_weight;
    </delete>

    <insert id="init">
        INSERT INTO group_weight SELECT
            g.part_table_suffix,
            sum(gf.part_num) AS part_num,
            count(g.part_table_suffix) AS group_num
        FROM
            cl_veh_group g,
            cl_veh_group_flag gf
        WHERE
            gf.group_id = g.id
        GROUP BY
            g.part_table_suffix
    </insert>

    <update id="update" parameterType="com.jy.bean.dto.GroupWeight" >
        update group_weight
        <set>
            <if test="partNum != null">
                part_num = #{partNum},
            </if>
            <if test="groupNum != null">
                group_num = #{groupNum},
            </if>
        </set>
        where part_table_suffix = #{partTableSuffix, jdbcType=VARCHAR}
    </update>

    <select id="getMinPartTableSuffix" resultType="com.jy.bean.dto.GroupWeight">
        select * from (SELECT
            part_num + group_num * 1000 AS weight,
            part_num,
            group_num,
            part_table_suffix
        FROM
            group_weight
        ORDER BY
            weight
        ) where rownum=1
    </select>
</mapper>
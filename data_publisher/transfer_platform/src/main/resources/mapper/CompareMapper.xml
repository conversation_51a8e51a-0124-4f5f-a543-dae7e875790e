<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jy.mapper.CompareMapper">


	<select id="getCountByTableName" resultType="java.lang.Integer">
		select
		count(1) as count
		from f_${tableName}
		<where>
			<if test="_parameter.containsKey('maxVersionId') and maxVersionId != null and maxVersionId != ''">
				and version_id &lt;= #{maxVersionId}
			</if>
			<if test="_parameter.containsKey('minVersionId') and minVersionId != null and minVersionId != ''">
				and version_id &gt; #{minVersionId}
			</if>
			<if test="_parameter.containsKey('layerValue') and layerValue != null and layerValue != ''">
				and ${layerKey} = #{layerValue}
			</if>
		</where>
	</select>

	<select id="listRangeByTableName" resultType="java.math.BigInteger">

		SELECT * FROM (SELECT maxVersionId from (
		select version_id as maxVersionId,rownum from f_${tableName}
		WHERE ROWNUM &lt;= ${count} AND VERSION_ID &gt; ${minVersionId}
		<if test="_parameter.containsKey('layerValue') and layerValue != null and layerValue != ''">
			and ${layerKey} = #{layerValue}
		</if>
		ORDER BY VERSION_ID ASC
		) ORDER BY maxVersionId desc) WHERE rownum =1
	</select>

	<!--


		SELECT
		version_id as maxVersionId
		FROM
		f_${tableName}
		WHERE
		ROWNUM &lt;= ${count} AND
		version_id > ${minVersionId}
		ORDER BY
		version_id asc-->

	<select id="query" resultType="java.util.HashMap">
		select *  from ${tableName}
		<where>
			<if test="_parameter.containsKey('maxVersionId') and maxVersionId != null and maxVersionId != ''">
				and version_id &lt; #{maxVersionId}
			</if>
			<if test="_parameter.containsKey('minVersionId') and minVersionId != null and minVersionId != ''">
				and version_id &gt;= #{minVersionId}
			</if>
			<if test="_parameter.containsKey('layerValue') and layerValue != null and layerValue != ''">
				and ${layerKey} = #{layerValue}
			</if>
		</where>
	</select>

	<select id="listTableName" resultType="java.lang.String">
		select lower(table_name)
		from user_synonyms
		<where> table_OWNER = '${final}'
			<if test="_parameter.containsKey('tableName') and tableName != null and tableName != ''">
				and table_name like upper('${tableName}%')
				and table_name not like upper('%bak')
				and table_name not like '%bak'
			</if>
		</where>
	</select>

	<select id="listCompareLayer" resultType="java.lang.String">
		select distinct ${compareLayerKey}
		from f_${tableName}
	</select>

	<insert id="resendInsert" >
		INSERT INTO c_${tableName}(
		${cKey},
		main_code,
		version_code,
		state,
		created_time,
		version_id)
		SELECT
		${fKey},
		'${mainVersionCode}',
		'${versionCode}',
		'${state}',
		SYSDATE,
		version_id
		from f_${tableName}
		<where>
			<if test="_parameter.containsKey('maxVersionId') and maxVersionId != null and maxVersionId != ''">
				and version_id &lt;= #{maxVersionId}
			</if>
			<if test="_parameter.containsKey('minVersionId') and minVersionId != null and minVersionId != ''">
				and version_id &gt; #{minVersionId}
			</if>
			<if test="_parameter.containsKey('layerValue') and layerValue != null and layerValue != ''">
				and ${layerKey} = #{layerValue}
			</if>
		</where>
	</insert>

</mapper>
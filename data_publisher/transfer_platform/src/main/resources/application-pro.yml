#端口
server:
  port: 8080

spring:
  datasource:
    url: ********************************************
    username: data_transfer
    password: DDoPVmUE1A
    #A5EaTYR09F  final
    #ZndVNh2NWV   product
    driver-class-name: oracle.jdbc.OracleDriver

  rabbitmq:
    #    host: *********
    #    port: 5672
    #    username: admin
    #    password: admin
    host: *********
    port: 5672
    username: admin
    password: MJT5Np4n7A
    dataTraceInfo: data-trace-exchange,data-trace-routing-key,data-trace-queue,direct
    requested-heartbeat: 15 # 心跳间隔，单位秒。
    connection-timeout: 30000 # 连接超时时间，单位毫秒
  thymeleaf:
    mode: LEGACYHTML5
    encoding: UTF-8
    #开发时关闭缓存,不然没法看到实时页面
    cache: false

mybatis:
  type-aliases-package: com.jy.bean
  mapper-locations: classpath:mapper/*.xml
  configuration:
    #开启驼峰命名转换
    mapUnderscoreToCamelCase: true
    jdbc-type-for-null: 'null'
    call-setters-on-nulls: true
  configuration-properties:
    transfer: DATA_TRANSFER
    product: DATA_PRODUCT
    mid: PART_MID
    final: DATA_FINAL
    compare: DATA_COMPARE

#---------------------------日志配置--------------------------
logging:
  level:
    org.springframework.security: info
    org.springframework: info
    com.jy.mapper: debug
  config: classpath:log4j2-pro.yml

#自定义配置
gainData:
  accessoriesUrl: http://192.168.80.104:60002/receiveGroupData/receive
  accessoriesResultUrl: http://192.168.80.104:60002/receiveGroupData/result
  saveFilePath: /jydata/transfer_platfrom
  vehicleUrl: http://192.168.80.203:8080/publish-platform/receiveData/receivePartClaimVeh
  statisticsUrl: /jydata/transfer_platfrom/execl
  vehicleTypeUrl: http://192.168.80.203:8080/publish-platform/vehicleType/getVehicleTypefacadeTest
  jgPath: /jydata/transfer_platfrom/tj
  pageSize: 20000
#数据统计地址
statistics:
  productUrl: http://192.168.80.104:60002/StatisticsGroupData
  facadeTokenUrl:
  #http://10.1.1.28/auth-service/oauth/token?grant_type=password&username=operate&password=0e710e7e0836c8c7
  facadeStatisticsUrl:
  #http://10.1.1.28/part-service/facadeTest?partTableTotal&vehGroupIds=
  testFacadeTokenUrl:
  #http://*********:8765/auth-service/oauth/token?grant_type=password&username=operate&password=0e710e7e0836c8c7
  testFacadeStatisticsUrl:
  #http://*********:8765/part-service/facadeTest?partTableTotal&vehGroupIds=
  dataBaseExistsMqData: http://192.168.80.125:8080/dict/existsMqData
  localUrl: http://192.168.80.80:8080/transfer_platfrom/statistics/dataStatistics
  clientUrl:
compare:
  layerSize: 1000
  batchSize: 50

publishConfig:

  ##推送日志测试环境
  ## 预生产
  #sentFlag: http://192.168.80.131:6601/receiveLog/receiveLogJson
  ## 生产
  sentFlag: http://192.168.80.125:8080/receiveLog/receiveLogJson

  ##发送json 测试环境
  ##预生产地址
  #sentJson: http://192.168.80.131:6601/receive/allData
  ##生产地址
  sentJson: http://192.168.80.125:8080/receive/allData
  sendStateJson: http://192.168.80.125:8080/dealDataLog/dataPublishLogs


serviceName: 产品数据转换平台

srcData:
  filePath: /jydata/data/transfer_platform/src_data/
httpUtils:
  authorization: YW5kcm9pZDphbmRyb2lk
  dataPublish:
    url: http://10.2.4.12:8091/
    username: SRC_PART
    password: src_part
    sendDataPath: baseDatas
    listClientTablePath: clientTable
    listClientUrlPath: clientUrlMp
    listClientFieldPath: clientTableFieldMp
    comparePath: compares
  facade:
    authorization: YW5kcm9pZDphbmRyb2lk
    env1:
      url: http://*********:8765/
      username: admin
      password: 76b47ec5b90bac30
    env2:
      url: http://10.1.1.28/
      username: admin
      password: 4a902d43c3fbc0a3
    emailPath: common-service/email
    defaultEmailTo: <EMAIL>,<EMAIL>,<EMAIL>
    newPartEmailTo: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
    newPartEmailCc: <EMAIL>
    workWechatPath: common-service/workWeChats
    defaultWechatName: 谢瑞诺,耿悦,张良
  part:
    partUrl: http://192.168.80.104:8098/
    partPicPath: api/v1/pic/publish
    svgPicPath: api/v1/svg/publish
transform:
  batchLimit: 3000
  pushLimit: 3000
  receivePartLimit: 10

thread:
  corePoolSize: 10
  maxPoolSize: 15
  queueCapacity: 5
  keepAliveSeconds: 3000
  threadName: transferasync-service-

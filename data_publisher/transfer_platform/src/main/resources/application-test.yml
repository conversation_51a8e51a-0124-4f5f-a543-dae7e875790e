#端口
server:
  port: 8099

spring:
  datasource:
    url: *******************************************
    username: data_transfer
    password: 123456
    driver-class-name: oracle.jdbc.OracleDriver

  rabbitmq:
    host: **************
    port: 5672
    username: guest
    password: guest
    requested-heartbeat: 15 # 心跳间隔，单位秒。
    connection-timeout: 30000 # 连接超时时间，单位毫秒
    dataTraceInfo: data-trace-exchange,data-trace-routing-key,data-trace-queue,direct
  thymeleaf:
    mode: LEGACYHTML5
    encoding: UTF-8
    #开发时关闭缓存,不然没法看到实时页面
    cache: false

mybatis:
  type-aliases-package: com.jy.bean
  mapper-locations: classpath:mapper/*.xml
  configuration:
    #开启驼峰命名转换
    mapUnderscoreToCamelCase: true
    jdbc-type-for-null: 'null'
    call-setters-on-nulls: true
  #设置mybatis全局变量
  configuration-properties:
    transfer: DATA_TRANSFER
    product: DATA_PRODUCT
    mid: PART_MID
    final: DATA_FINAL
    compare: DATA_COMPARE

#---------------------------日志配置--------------------------
logging:
  level:
    org.springframework.security: info
    org.springframework: info
    com.jy.mapper: info
  config: classpath:log4j2-test.yml

#自定义配置
gainData:
  #测试环境
  #  accessoriesUrl: http://192.168.110.48:60002/receiveGroupData/receive
  #  accessoriesResultUrl: http://192.168.110.48:60002/receiveGroupData/result
  #  accessoriesUrl: http://192.168.136.63:60002/receiveGroupData/receive
  #  accessoriesResultUrl: http://192.168.136.63:60002/receiveGroupData/result
  #  saveFilePath: D:/groupTable
  #  vehicleUrl: http://172.16.10.34:8080/receiveData/receivePartClaimVeh
  #  statisticsUrl: D:/statistics
  accessoriesUrl: http://172.16.10.36:60002/receiveGroupData/receive
  accessoriesResultUrl: http://172.16.10.36:60002/receiveGroupData/result
  saveFilePath: /jydata/data/transfer
  #  saveFilePath: D:/groupTable
  vehicleUrl: http://172.16.10.34:8080/publish-platform/receiveData/receivePartClaimVeh
  statisticsUrl: /jydata/data/transfer
  vehicleTypeUrl: http://172.16.10.34:8080/publish-platform/vehicleType/getVehicleType
  jgPath: /jydata/data/transfer
  pageSize: 20000

#数据统计地址
statistics:
  productUrl: http://172.16.10.36:60002/StatisticsGroupData
  testFacadeTokenUrl: http://testfacade.jingyougroup.com:8765/auth-service/oauth/token?grant_type=password&username=operate&password=0e710e7e0836c8c7
  testFacadeStatisticsUrl: http://testfacade.jingyougroup.com:8765/part-service/facadeTest?partTableTotal&vehGroupIds=
  facadeTokenUrl: http://facade.jingyougroup.com/auth-service/oauth/token?grant_type=password&username=test&password=test424bef3761
  facadeStatisticsUrl: http://facade.jingyougroup.com/part-service/facadeTest?partTableTotal&vehGroupIds=
  localUrl: http://192.168.80.102:8080/transfer_platfrom/statistics/dataStatistics
  dataBaseExistsMqData: http://192.168.80.102:8090/dict/existsMqData
  clientUrl: http://192.168.80.102:8086
compare:
  layerSize: 100
  batchSize: 50

publishConfig:
  #  ##推送日志测试环境
  sentFlag: http://192.168.80.102:8090/receiveLog/receiveLogJson
  ##发送json 测试环境
  sentJson: http://192.168.80.102:8090/receive/allData
  sendStateJson: http://192.168.80.102:8090/dealDataLog/dataPublishLogs


serviceName: 产品数据转换平台
srcData:
  filePath: /home/<USER>/data/transfer_platform/src_data/
httpUtils:
  authorization: YW5kcm9pZDphbmRyb2lk
  dataPublish:
    url: http://***************:8091/
    username: SRC_PART
    password: src_part
    sendDataPath: baseDatas
    listClientTablePath: clientTable
    listClientUrlPath: clientUrlMp
    listClientFieldPath: clientTableFieldMp
    comparePath: compares
  facade:
    authorization: YW5kcm9pZDphbmRyb2lk
    env1:
      url: http://192.168.100.141:30089/
      username: admin
      password: admin2018
    env2:
      url: http://testfacade.jingyougroup.com:8765/
      username: admin
      password: 76b47ec5b90bac30
    emailPath: common-service/email
    defaultEmailTo: <EMAIL>
    newPartEmailTo: <EMAIL>
    newPartEmailCc:
    workWechatPath: common-service/workWeChats
    defaultWechatName: 曹立婷
  part:
    partUrl: http://192.168.80.102:8099/
    partPicPath: api/v1/pic/publish
    svgPicPath: api/v1/svg/publish
transform:
  batchLimit: 3000
  pushLimit: 5000
  receivePartLimit: 10
thread:
  corePoolSize: 10
  maxPoolSize: 15
  queueCapacity: 5
  keepAliveSeconds: 3000
  threadName: transferasync-service-



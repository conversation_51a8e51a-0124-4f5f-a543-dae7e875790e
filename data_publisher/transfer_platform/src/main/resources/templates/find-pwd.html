<!DOCTYPE html>
<html  xmlns:th="http://www.thymeleaf.org">
	<head>
		<base href="./" th:href="@{../}"/>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<meta name="renderer" content="webkit" />
		<meta http-equiv = "X-UA-Compatible" content = "IE=edge,chrome=1" />
		<!--标签页小logo-->
		<title>找回密码</title>
		<link rel="stylesheet" type="text/css" href="../static/css/common/reset.css" th:href="@{css/common/reset.css}" />
		<link rel="stylesheet" type="text/css" href="../static/css/main/find-pwd.css" th:href="@{css/main/find-pwd.css}" />

	</head>
<body>
	<div id="loginWrap">
			<div class="login_title">
				<img src="../static/icons/ico_title.png" th:src="@{icons/ico_title.png}"/>
				<span>找回密码</span>
			</div>
				<div id="loginDiv" class="login_text clearfloat">
						<input  type="text" class="login_item" id="phoneNum" placeholder="请输入手机号" maxlength="11">
				      	<p id="phoneNumMsg" class="errorPhone"></p>
				      	<input type="text" class="login_item phoneCode" name="phone_code" id="phone_code" placeholder="请输入手机验证码" maxlength="6"
				      		onblur="checkPhoneCode(this.value)" onkeyup="checkPhoneCode(this.value)">
				        <input class="login_item phoneCode recCode" id="sendMsg" type="button" value="获 取 验 证 码" onclick="sendMsg();">
						<p id="tipsMsg" class="errorPhone"></p>
						<input type="button" value="确      定" class="login_btn" id="confirmFindPwd" onclick="confirm()"  style="cursor:pointer">
				</div>
				
				<div id="changePwd" class="login_text clearfloat"  style="display: none;">
						<input  type="text" class="login_item" id="phone" placeholder="请输入手机号"  maxlength="11" readonly onblur="getPhoneNum(this.value)" onkeyup="getPhoneNum(this.value)">
						<p id="phoneTips" class="errorPhone"></p>
				      	<input type="password" class="login_item" id="newpass" placeholder="请输入新密码" onblur="checkPwdEmpty(this.value)"  onkeyup="checkPwdEmpty(this.value)" maxlength="18">
						<p id="passwordMsg" class="errorPhone"></p>
						<input  type="password" class="login_item" id="newpassAgain" placeholder="确认新密码" onblur="checkConfirmEmpty()" onkeyup="checkConfirmEmpty()"  maxlength="18"> 
						<p id="confirmMsg" class="errorPhone"></p>
						<input type="button" value="确 认 修 改" class="login_btn" id="updatePwd" onclick="confirmFindPwd()"  style="cursor:pointer">
				</div>
		</div>
    <script src="../static/plugin/jQuery/jquery-2.1.4.min.js" th:src="@{plugin/jQuery/jquery-2.1.4.min.js}"></script>
    <script src="../static/plugin/layer/layer.js" th:src="@{plugin/layer/layer.js}"></script>
    <script src="../static/js/find-pwd.js" th:src="@{js/find-pwd.js}"></script>
</body>
</html>
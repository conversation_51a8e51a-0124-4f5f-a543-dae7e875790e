<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <base href="./" th:href="@{../}"/>
    <meta charset="UTF-8">
    <link rel="stylesheet" type="text/css" href="../../static/css/task/reset.css" th:href="@{css/task/reset.css}">
    <link rel="stylesheet" type="text/css" href="../static/css/task/task-list.css" th:href="@{css/task/task-list.css}">
    <link rel="stylesheet" type="text/css" href="../static/css/task/task.css" th:href="@{css/task/task.css}">
    <link rel="stylesheet" type="text/css" href="../static/css/pages.css" th:href="@{css/pages.css}">
    <title>index</title>
</head>
<body style="min-width: 1145px;padding: 0 10px">
<div v-cloak id="statisticsList">
    <div class="title-wrap">
        <ul class="title">
            <li>
                <h3>数据统计管理</h3>
            </li>
        </ul>
        <ul class="userMsg">
            <!--<li>
                <span>日期 :</span><input type="date" name="" @change="test()"  v-model.trim="searchTime" placeholder="输入日期" style="height: 28px;" >
            </li>
            <li>
                <span>车组 :</span>
                <select v-model.trim="groupId" style="height: 28px">
                    <option value="">&#45;&#45;请选择&#45;&#45;</option>
                    <option v-for="group in groupList" v-bind:value="group.groupId">
                        {{group.groupName}}
                    </option>
                </select>
            </li>-->
            <li>
                <i></i>
                <input type="button" name="" value="返 回" class="searchBtn" @click="returnOne()">
                <input type="button" name="" value="导 出" class="searchBtn" @click="exportStatisticsData()">
            </li>
        </ul>
    </div>
    <div class="insurance_table">
        <table style="white-space:nowrap; ">
            <thead>
            <tr>
                <th>车组名称</th>
                <th>加工平台总数</th>
                <th>加工平台本期处理数量</th>
                <th>转换平台总数</th>
                <th>本期处理数量</th>
                <th>新增条数</th>
                <th>更新条数</th>
                <th>删除条数</th>
                <th>facade总数</th>
                <th>facade本期处理数量</th>
            </tr>
            </thead>
            <tbody>
            <thead>
            <tr v-for="(idx,item) in indexes">
                <template v-if="idx == 0"><th>厂家数量</th></template>
                <template v-if="idx == 1"><th>品牌数量</th></template>
                <template v-if="idx == 2"><th>车系数量</th></template>
                <template v-if="idx == 3"><th>车组数量</th></template>
                <template v-if="idx == 4"><th>车型数量</th></template>
                <template v-if="idx == 5"><th>理赔及承保关系数量</th></template>
                <template v-if="idx == 6"><th>车型零件数量</th></template>
                <template v-if="idx == 7"><th>车型零件显示零件数量</th></template>
                <template v-if="idx == 8"><th>车型下零件存在互斥码数量</th></template>
                <template v-if="idx == 9"><th>车型标准件数量</th></template>
                <template v-if="idx == 10"><th>车型非标准件数量</th></template>
                <template v-if="idx == 11"><th>车型图片数量</th></template>
                <template v-if="idx == 12"><th>车型图形热点数量</th></template>
                <template v-if="idx == 13"><th>车组零件数量</th></template>
                <template v-if="idx == 14"><th>车组标准件数量</th></template>
                <template v-if="idx == 15"><th>车组非标准件数量</th></template>
                <template v-if="idx == 16"><th>品牌零件数量</th></template>
                <template v-if="idx == 17"><th>品牌标准件数量</th></template>
                <template v-if="idx == 18"><th>品牌非标准件数量</th></template>
                <template v-if="idx == 19"><th>品牌标识表数量</th></template>
                <template v-if="idx == 20"><th>车系标识表数量</th></template>
                <template v-if="idx == 21"><th>车组标识表数量</th></template>
                <template v-if="idx == 22"><th>车型标识表数量</th></template>
                <template v-for="(data,index) in statisticsList">
                    <template v-if="idx == 0">

                        <template v-if="data.dataType == '1' || data.dataType == '2' || data.dataType == '6'">
                            <th>{{data.zcjsl}}</th>
                            <th>{{data.cjsl}}</th>
                        </template>
                        <template v-if="data.dataType == '3' || data.dataType == '4' || data.dataType == '5'">
                            <th>{{data.cjsl}}</th>
                        </template>
                    </template>
                    <template v-if="idx == 1">
                        <template v-if="data.dataType == '1' || data.dataType == '2' || data.dataType == '6'">
                            <th>{{data.zppsl}}</th>
                            <th>{{data.ppsl}}</th>
                        </template>
                        <template v-if="data.dataType == '3' || data.dataType == '4' || data.dataType == '5'">
                            <th>{{data.ppsl}}</th>
                        </template>
                    </template>
                    <template v-if="idx == 2">
                        <template v-if="data.dataType == '1' || data.dataType == '2' || data.dataType == '6'">
                            <th >{{data.zcxxsl}}</th>
                            <th>{{data.cxxsl}}</th>
                        </template>
                        <template v-if="data.dataType == '3' || data.dataType == '4' || data.dataType == '5'">
                            <th>{{data.cxxsl}}</th>
                        </template>
                    </template>
                    <template v-if="idx == 3">
                        <template v-if="data.dataType == '1' || data.dataType == '2' || data.dataType == '6'">
                            <th >{{data.zczsl}}</th>
                            <th>{{data.czsl}}</th>
                        </template>
                        <template v-if="data.dataType == '3' || data.dataType == '4' || data.dataType == '5'">
                            <th>{{data.czsl}}</th>
                        </template>
                    </template>
                    <template v-if="idx == 4">
                        <template v-if="data.dataType == '1' || data.dataType == '2' || data.dataType == '6'">
                            <th >{{data.zcxsl}}</th>
                            <th>{{data.cxsl}}</th>
                        </template>
                        <template v-if="data.dataType == '3' || data.dataType == '4' || data.dataType == '5'">
                            <th>{{data.cxsl}}</th>
                        </template>
                    </template>
                    <template v-if="idx == 5">
                        <template v-if="index == 0">
                            <th></th>
                        </template>
                        <template v-if="data.dataType == '2' || data.dataType == '6'">
                            <th >{{data.zlpcbsl}}</th>
                            <th>{{data.lpcbsl}}</th>
                        </template>
                        <template v-if="data.dataType == '1' || data.dataType == '3' || data.dataType == '4' || data.dataType == '5'">
                            <th>{{data.lpcbsl}}</th>
                        </template>
                    </template>
                    <template v-if="idx == 6">
                        <template v-if="data.dataType == '1' || data.dataType == '2' || data.dataType == '6'">
                            <th></th>
                            <th>{{data.cxljsl}}</th>
                        </template>
                        <template v-if="data.dataType == '3' || data.dataType == '4' || data.dataType == '5'">
                            <th>{{data.cxljsl}}</th>
                        </template>
                    </template>
                    <template v-if="idx == 7">
                        <template v-if="data.dataType == '1' || data.dataType == '2' || data.dataType == '6'">
                            <th></th>
                            <th>{{data.cxljxssl}}</th>
                        </template>
                        <template v-if="data.dataType == '3' || data.dataType == '4' || data.dataType == '5'">
                            <th>{{data.cxljxssl}}</th>
                        </template>
                    </template>
                    <template v-if="idx == 8">
                        <template v-if="data.dataType == '1' || data.dataType == '2' || data.dataType == '6'">
                            <th></th>
                            <th>{{data.czhcmsl}}</th>
                        </template>
                        <template v-if="data.dataType == '3' || data.dataType == '4' || data.dataType == '5'">
                            <th>{{data.czhcmsl}}</th>
                        </template>
                    </template>
                    <template v-if="idx == 9">
                        <template v-if="data.dataType == '1' || data.dataType == '2' || data.dataType == '6'">
                            <th ></th>
                            <th>{{data.cxbzjsl}}</th>
                        </template>
                        <template v-if="data.dataType == '3' || data.dataType == '4' || data.dataType == '5'">
                            <th>{{data.cxbzjsl}}</th>
                        </template>
                    </template>
                    <template v-if="idx == 10">
                        <template v-if="data.dataType == '1' || data.dataType == '2' || data.dataType == '6'">
                            <th ></th>
                            <th>{{data.cxfbzjsl}}</th>
                        </template>
                        <template v-if="data.dataType == '3' || data.dataType == '4' || data.dataType == '5'">
                            <th>{{data.cxfbzjsl}}</th>
                        </template>
                    </template>
                    <template v-if="idx == 11">
                        <template v-if="data.dataType == '1' || data.dataType == '2' || data.dataType == '6'">
                            <th ></th>
                            <th>{{data.cxtpsl}}</th>
                        </template>
                        <template v-if="data.dataType == '3' || data.dataType == '4' || data.dataType == '5'">
                            <th>{{data.cxtpsl}}</th>
                        </template>
                    </template>
                    <template v-if="idx == 12">
                        <template v-if="data.dataType == '1' || data.dataType == '2' || data.dataType == '6'">
                            <th ></th>
                            <th>{{data.cxtprdsl}}</th>
                        </template>
                        <template v-if="data.dataType == '3' || data.dataType == '4' || data.dataType == '5'">
                            <th>{{data.cxtprdsl}}</th>
                        </template>
                    </template>
                    <template v-if="idx == 13">
                        <template v-if="data.dataType == '1' || data.dataType == '2' || data.dataType == '6'">
                            <th ></th>
                            <th>{{data.czljsl}}</th>
                        </template>
                        <template v-if="data.dataType == '3' || data.dataType == '4' || data.dataType == '5'">
                            <th>{{data.czljsl}}</th>
                        </template>
                    </template>
                    <template v-if="idx == 14">
                        <template v-if="data.dataType == '1' || data.dataType == '2' || data.dataType == '6'">
                            <th ></th>
                            <th>{{data.czbzjsl}}</th>
                        </template>
                        <template v-if="data.dataType == '3' || data.dataType == '4' || data.dataType == '5'">
                            <th>{{data.czbzjsl}}</th>
                        </template>
                    </template>
                    <template v-if="idx == 15">
                        <template v-if="data.dataType == '1' || data.dataType == '2' || data.dataType == '6'">
                            <th ></th>
                            <th>{{data.czfbzjsl}}</th>
                        </template>
                        <template v-if="data.dataType == '3' || data.dataType == '4' || data.dataType == '5'">
                            <th>{{data.czfbzjsl}}</th>
                        </template>
                    </template>
                    <template v-if="idx == 16">
                        <template v-if="data.dataType == '1' || data.dataType == '2' || data.dataType == '6'">
                            <th ></th>
                            <th>{{data.ppljsl}}</th>
                        </template>
                        <template v-if="data.dataType == '3' || data.dataType == '4' || data.dataType == '5'">
                            <th>{{data.ppljsl}}</th>
                        </template>
                    </template>
                    <template v-if="idx == 17">
                        <template v-if="data.dataType == '1' || data.dataType == '2' || data.dataType == '6'">
                            <th ></th>
                            <th>{{data.ppbzjsl}}</th>
                        </template>
                        <template v-if="data.dataType == '3' || data.dataType == '4' || data.dataType == '5'">
                            <th>{{data.ppbzjsl}}</th>
                        </template>
                    </template>
                    <template v-if="idx == 18">
                        <template v-if="data.dataType == '1' || data.dataType == '2' || data.dataType == '6'">
                            <th ></th>
                            <th>{{data.ppfbzj}}</th>
                        </template>
                        <template v-if="data.dataType == '3' || data.dataType == '4' || data.dataType == '5'">
                            <th>{{data.ppfbzj}}</th>
                        </template>
                    </template>
                    <template v-if="idx == 19">
                        <template v-if="data.dataType == '1'">
                            <th ></th>
                            <th>{{data.ppbsbsl}}</th>
                        </template>
                        <template v-if="data.dataType == '3' || data.dataType == '4' || data.dataType == '5'">
                            <th>{{data.ppbsbsl}}</th>
                        </template>
                        <template v-if="data.dataType == '2' || data.dataType == '6'">
                            <th>{{data.zppbsbsl}}</th>
                            <th>{{data.ppbsbsl}}</th>
                        </template>
                    </template>
                    <template v-if="idx == 20">
                        <template v-if="data.dataType == '1'">
                            <th ></th>
                            <th>{{data.cxxbsbsl}}</th>
                        </template>
                        <template v-if="data.dataType == '3' || data.dataType == '4' || data.dataType == '5'">
                            <th>{{data.cxxbsbsl}}</th>
                        </template>
                        <template v-if="data.dataType == '2' || data.dataType == '6'">
                            <th>{{data.zcxxbsbsl}}</th>
                            <th>{{data.cxxbsbsl}}</th>
                        </template>
                    </template>
                    <template v-if="idx == 21">
                        <template v-if="data.dataType == '1'">
                            <th ></th>
                            <th>{{data.czbsbsl}}</th>
                        </template>
                        <template v-if="data.dataType == '3' || data.dataType == '4' || data.dataType == '5'">
                            <th>{{data.czbsbsl}}</th>
                        </template>
                        <template v-if="data.dataType == '2' || data.dataType == '6'">
                            <th>{{data.zczbsbsl}}</th>
                            <th>{{data.czbsbsl}}</th>
                        </template>
                    </template>
                    <template v-if="idx == 22">
                        <template v-if="data.dataType == '1'">
                            <th ></th>
                            <th>{{data.cxbsbsl}}</th>
                        </template>
                        <template v-if="data.dataType == '3' || data.dataType == '4' || data.dataType == '5'">
                            <th>{{data.cxbsbsl}}</th>
                        </template>
                        <template v-if="data.dataType == '2' || data.dataType == '6'">
                            <th>{{data.zcxbsbsl}}</th>
                            <th>{{data.cxbsbsl}}</th>
                        </template>
                    </template>
                </template>
            </tr>
            </thead>
            </tbody>
        </table>
    </div>
    <input type="hidden" th:value="${groupId}" ref="groupId">
    <input type="hidden" th:value="${date}" ref="searchTime">
    <!--<div th:include="footer-pages :: footer-pages"></div>-->
</div>
</body>
<script type="text/javascript" src="../static/plugin/jQuery/jquery-2.1.4.min.js"
        th:src="@{plugin/jQuery/jquery-2.1.4.min.js}"></script>
<script type="text/javascript" src="../static/plugin/vue/vue.min.js" th:src="@{plugin/vue/vue.min.js}"></script>
<script src="../static/plugin/vue/vue-resource.js" th:src="@{plugin/vue/vue-resource.js}"></script>
<script src="../static/plugin/vue/vue-validator.js" th:src="@{plugin/vue/vue-validator.js}"></script>
<script src="../static/plugin/layer/layer.js" th:src="@{plugin/layer/layer.js}"></script>
<script src="../static/js/dataStatistics/statistics_list.js" th:src="@{js/dataStatistics/statistics_list.js}"></script>
</html>
<html xmlns:th="http://www.thymeleaf.org" th:fragment="footer-pages">
<footer>
    <!--  分页脚步页码 -->
    <div class="page-bar">
        <ul class="part1">
            <li><a>总<i>{{totalCount}}</i>条数</a></li>
        </ul>
        <div class="part2">
            <ul>
                <li ><a @click="btnClick(1)">首页</a></li>
                <li ><a @click="btnClick(cur-1)">上一页</a></li>
                <li v-for="index in indexs"  v-bind:class="{ 'active': cur == index}">
                    <a v-on:click="btnClick(index)">{{ index }}</a>
                </li>
                <li ><a @click="btnClick(cur+1)">下一页</a></li>
                <li ><a @click="btnClick(all)">尾页</a></li>
                <li><a>共<i>{{all}}</i>页</a></li>
            </ul>
        </div>
    </div>
</footer>

<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" >
<head>
    <base href="./" th:href="@{../}"/>
    <meta charset="UTF-8">
    <link  href="../static/css/task/reset.css" th:href="@{css/task/reset.css}" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="../static/css/task/task-config.css" th:href="@{css/task/task-config.css}"/>
    <link rel="stylesheet" type="text/css" href="../../static/css/task/task.css" th:href="@{css/task/task.css}">
    <script type="text/javascript" th:src="@{plugin/jQuery/jquery-2.1.4.min.js}" src="../static/plugin/jQuery/jquery-2.1.4.min.js"></script>
    <script th:src="@{plugin/vue/vue.min.js}" src="../static/plugin/vue/vue.min.js"></script>
    <script th:src="@{plugin/vue/vue-resource.js}" src="../static/plugin/vue/vue-resource.js"></script>
    <script src="../static/plugin/layer/layer.js" th:src="@{plugin/layer/layer.js}"></script>
    <script src="../static/js/common.js" th:src="@{js/common.js}"></script>
</head>
<body>
<div class="taskdeal" v-cloak  id="table_insert" style="display:block;padding-top:0px;margin-top:0px;" >
    <div class="sidebarTable" >
        <table border="1" cellspcing="0" id="vcBase" style="margin-left: 0px">
            <!--基本参数-->
            <tbody>

                <tr>
                    <td>表名：</td>
                    <td v-cloak>
                        <input type="text" v-model="dataTable.tableName">
                        <input type="hidden" v-model="dataTable.id">
                        <span style="color: red;">&nbsp;&nbsp;*</span>
                    </td>
                </tr>
                <tr>
                    <td>表类型：</td>
                    <td v-cloak >
                        <select v-model="dataTable.type">
                            <option :value="1">车组</option>
                            <option :value="2">总成字典</option>
                            <option :value="3">替换号</option>
                            <option :value="4">品牌件</option>
                            <option :value="5">其他</option>
                            <option :value="6">单车组</option>
                            <option :value="7">整表推送</option>
                            <option :value="8">精细化商用车</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td>查询结构：</td>
                    <td v-cloak >
                        <select v-model="dataTable.tableStructure">
                            <option :value="0">原表查询</option>
                            <option :value="1">拼接车组编号查询</option>
                            <option :value="2">拼接品牌编号查询</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td>保存时是否分表：</td>
                    <td v-cloak >
                        <select v-model="dataTable.isSeparate">
                            <option :value="0">不分表</option>
                            <option :value="1">分表</option>
                            <option :value="2">合表</option>
                        </select>
                    </td>
                </tr>
                <tr v-show="dataTable.isSeparate == 2">
                    <td>合并保存的表名：</td>
                    <td v-cloak>
                        <input type="text" v-model="dataTable.mergeTable">
                        <span style="color: red;">&nbsp;&nbsp;*</span>
                    </td>
                </tr>
                <tr>
                    <td>是否启用：</td>
                    <td v-cloak >
                        <select v-model="dataTable.isEnable">
                            <option :value="1">启用</option>
                            <option :value="0">禁用</option>
                        </select>
                    </td>
                </tr>
            </tbody>
        </table>
        <table border="1" cellspcing="0" id="vcActive" style="margin-left: 0px">
            <tbody>
                <tr>
                    <td colspan="2" style="text-align: center;">
                        <div>
                            <input type="button"  value="提 交" class="veh-config-button" @click="saveTable()">
                            <input type="button"  value="关 闭" class="veh-config-button" @click="pageClose()">
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
<script src="../static/js/dataTables/dataTables_edit.js" th:src="@{js/dataTables/dataTables_edit.js}"></script>
</body>
</html>
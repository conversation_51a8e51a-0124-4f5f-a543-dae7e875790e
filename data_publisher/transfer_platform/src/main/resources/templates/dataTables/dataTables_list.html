<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
	<base href="./" th:href="@{../}"/>
	<meta charset="UTF-8">
	<link rel="stylesheet" type="text/css" href="../../static/css/task/reset.css" th:href="@{css/task/reset.css}">
	<link rel="stylesheet" type="text/css" href="../static/css/task/task-list.css" th:href="@{css/task/task-list.css}">
	<link rel="stylesheet" type="text/css" href="../static/css/task/task.css" th:href="@{css/task/task.css}">
	<link rel="stylesheet" type="text/css" href="../static/css/pages.css" th:href="@{css/pages.css}">
	<title>index</title>
</head>
<body style="min-width: 1145px;padding: 0 10px">
<div v-cloak id="tableList">
	<div class="title-wrap">
		<ul class="title">
			<li>
				<h3>数据表管理</h3>
				<input type="button" name="" value="✚  添加" class="searchBtn" @click="tableAdd()">
			</li>
		</ul>
		<ul class="userMsg">
			<li>
				<span>表名 :</span><input type="text" name="" v-model="tableName">
			</li>
			<li>
			</li>
			<li>
				<i></i>
				<input type="button" name="" value="查 询" class="searchBtn" @click="getTableList(1)">
				<input type="button" name="" value="清 空" class="checkGreyBtn btnLeft" @click="location.reload()">
			</li>
		</ul>
	</div>
	<div class="insurance_table">
		<table>
			<thead>
			<tr>
				<th>NO.</th>
				<th>表名</th>
				<th>表类型</th>
				<th>表结构</th>
				<th>是否分表</th>
				<th>合并后的表名</th>
				<th>是否启用</th>
				<th>操作</th>
			</tr>
			</thead>
			<tbody>
			<tr class="table_trdown" v-for="(table,index) in tableList">
				<td v-text="index+1">
				</td>
				<td v-text="table.tableName">
				</td>
				<td>
					<template v-if="table.type == 1">车组</template>
					<template v-if="table.type == 2">总成字典</template>
					<template v-if="table.type == 3">替换号</template>
					<template v-if="table.type == 4">品牌件</template>
					<template v-if="table.type == 5">其他</template>
					<template v-if="table.type == 6">单车组</template>
					<template v-if="table.type == 7">整表</template>
					<template v-if="table.type == 8">精细化商用车</template>
				</td>
				<td>
					<template v-if="table.tableStructure == 0">原样查询</template>
					<template v-if="table.tableStructure == 1">查询时拼接车组编号</template>
					<template v-if="table.tableStructure == 2">查询时拼接品牌编号</template>
				</td>
				<td>
					<template v-if="table.isSeparate == 0">不分表</template>
					<template v-if="table.isSeparate == 1">分表</template>
					<template v-if="table.isSeparate == 2">合表</template>
				</td>
				<td v-text="table.mergeTable">
				</td>
				<td>
					<template v-if="table.isEnable == 0">禁用</template>
					<template v-if="table.isEnable == 1">启用</template>
				</td>
				<td class="ditailBtn">
					<input type="button" name="" value="编 辑"  v-on:click="tableAdd(table)">
					<!--<input type="button" name="" value="客 户"  v-on:click="interfaceSettings(user)">-->
					<input type="button" name="" value="删 除"  v-on:click="deleteTable(table)">
				</td>
			</tr>

			<tr v-if="tableList.length == 0" class="table_trdown">
				<td th:colspan="6">无结果</td>
			</tr>

			</tbody>
		</table>
	</div>
    <div th:include="footer-pages :: footer-pages"></div>
</div>
</body>
<script type="text/javascript" src="../static/plugin/jQuery/jquery-2.1.4.min.js" th:src="@{plugin/jQuery/jquery-2.1.4.min.js}"></script>
<script type="text/javascript" src="../static/plugin/vue/vue.min.js" th:src="@{plugin/vue/vue.min.js}"></script>
<script src="../static/plugin/vue/vue-resource.js" th:src="@{plugin/vue/vue-resource.js}"></script>
<script src="../static/plugin/vue/vue-validator.js" th:src="@{plugin/vue/vue-validator.js}"></script>
<script src="../static/plugin/layer/layer.js" th:src="@{plugin/layer/layer.js}"></script>
<script src="../static/js/dataTables/dataTables_list.js" th:src="@{js/dataTables/dataTables_list.js}"></script>
</html>
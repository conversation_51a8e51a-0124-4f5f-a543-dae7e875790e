<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <base href="./" th:href="@{../}"/>
    <meta charset="UTF-8">
    <meta name="renderer" content="webkit" />
    <meta http-equiv = "X-UA-Compatible" content = "IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />

    <link rel="stylesheet" type="text/css" href="../static/css/common/reset.css" th:href="@{css/common/reset.css}" />
    <link rel="stylesheet" type="text/css" href="../static/css/main/api-and-function.css" th:href="@{css/main/api-and-function.css}" />
    <link rel="stylesheet" type="text/css" href="../static/plugin/layer/skin/default/layer.css" th:href="@{plugin/layer/skin/default/layer.css}" />

    <script type="text/javascript" src="../static/plugin/jQuery/jquery-2.1.4.min.js" th:src="@{plugin/jQuery/jquery-2.1.4.min.js}"></script>
    <script type="text/javascript" src="../static/plugin/layer/layer.js" th:src="@{plugin/layer/layer.js}"></script>
    <script src="../static/plugin/vue/vue.min.js" th:src="@{plugin/vue/vue.min.js}"></script>
    <script src="../static/plugin/vue/vue-resource.js" th:src="@{plugin/vue/vue-resource.js}"></script>

    <title>发布平台</title>
</head>
<body>

<div  class="main-content" id="apiAndFunction" v-cloak>
    <div id="navigation_left" class="left-nav" style=" overflow: auto;width: 170px" >
        <dl v-cloak v-if="menuParent.powerLevel==1" v-for="(menuParent, index) in menuList" >
            <dt @Click="toggle(menuParent)" :class="{active:menuParent==nowMenu}" @mouseenter="menuEnter(menuParent, index)" @mouseleave="menuLeave(menuParent, index)">

                <img v-if="menuParent==nowMenu || menuParent.isHover" v-bind:src = "'./icons/' + menuParent.icon + '.png' " class="dt_imgL"/>
                <img v-else v-bind:src = "'./icons/' + menuParent.icon + '.png' " class="dt_imgL"/>
                {{menuParent.powerName}}
                <img v-if="menuParent.url==null && menuParent.isClose" src="./icons/ico_arrow.png" th:src="@{icons/ico_arrow.png}" class="dt_imgR"/>
                <img v-if="menuParent.url==null && !menuParent.isClose" src="./icons/ico_arrow01.png" th:src="@{icons/ico_arrow01.png}" class="dt_imgR"/>
            </dt>

            <dd class="first_dd" :class="{active:menuChild==nowMenu}" @Click="toggle(menuChild)" v-if="menuChild.parentId==menuParent.id" v-show="!menuParent.isClose" v-for="(menuChild, index) in menuList">
                {{menuChild.powerName}}
            </dd>
        </dl>
    </div>
    <div class="main-div">
        <iframe id="main" src="page/initPage" width="100%"  class="main-iframe">
        </iframe>
    </div>

    <input type="hidden" th:value="${userId}" ref="userId"/>
    <input type="hidden" th:value="${roleCode}" ref="roleCode"/>
</div>

</body>


<script  th:inline="javascript">
    var apiAndFunction = new Vue({
        el:'#apiAndFunction',
        mounted: function () {
            this.$nextTick(function () {
                // 代码保证 this.$el 在 document 中
                this.toggleMenuHid();
            })
        },
        data:{
            userPoDetail:'',
            nameSpace: "page",
            nowUrl: "/initPage",
            nowMenu: {},
            menuList: []
        },
        methods:{
            toggle: function(menu){
                this.nowMenu = menu;
                if(menu.url){
                    this.go(menu.url);
                } else {
                    console.log("menu.isClose点击前："+menu.isClose)
                    menu.isClose = !menu.isClose;
                    console.log("menu.isClose点击后："+menu.isClose)
                    this.closeThisLevelOtherNodes(menu);
                }
            },
            menuEnter: function(menu, index){
                menu.isHover = true;
                Vue.set(this.menuList, index, menu);
            },
            menuLeave: function(menu, index){
                menu.isHover = false;
                Vue.set(this.menuList, index, menu);
            },
            getMenuList: function(){
                this.$http.get('./showMenu').then(function (response) {
                    response = response.data;
                    if (response.status == "200") {
                        this.menuList = response.result;
                        this.closeAllNodes()
                    }else{
                        layer.msg(response.message);
                    }
                });
            },
            closeAllNodes: function(){
                this.menuList.forEach(function(item, index, _list){
                    item.isClose = true;
                    Vue.set(_list, index, item);
                });
            },
            closeThisLevelOtherNodes: function(menuParent){
                this.menuList =  this.menuList.filter(function (item) {
                    if(item.powerLevel == menuParent.powerLevel && !item.isClose && item != menuParent){
                        item.isClose = true;
                    }
                    return true;
                });

//                this.menuList.forEach(function(item, index, _list){
//                    if(item.powerLevel == menuParent.powerLevel && !item.isClose && item != menuParent){
//                        item.isClose = true;
//                        Vue.set(_list, index, item);
////                        console.log("_list"+_list);
////                        console.log("index"+index);
////                        console.log("item"+item);
//                    }
//                });
            },
            go: function (url) {
                //this.nowUrl = url;
                document.getElementById('main').src="page" + url;
            },
            toggleMenuHid:function () {
                this.getMenuList();
            },
            getUserDetail:function () {
                var UserDTO ={
                    id : this.$refs.userId.value,
                };
                this.$http.get('./user/getUserMessage',UserDTO).then(function (response) {
                    response = response.data;
                    if(response.status == "200"){
                        this.userPoDetail =  response.result.user;
                    } else {
                        layer.msg(response.message);
                    }
                })
            }
        }
    });
</script>
</html>

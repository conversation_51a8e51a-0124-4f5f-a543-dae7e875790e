<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
	<head>
		<base href="./" th:href="@{../}"/>
		<meta charset="UTF-8">
		<title></title>
		<base href="../"/>
		<link href="../../static/css/common/reset.css" th:href="@{css/common/reset.css}" rel="stylesheet" type="text/css" />
		<link rel="stylesheet" type="text/css" href="../../static/css/common/index-public.css" th:href="@{css/common/index-public.css}"/>
        <link rel="stylesheet" type="text/css" href="../../static/plugin/layer/skin/default/layer.css" th:href="@{plugin/layer/skin/default/layer.css}" />
        <link rel="stylesheet" type="text/css" href="../../static/css/part/part.css" th:href="@{css/part/part.css}"/>

        <script type="text/javascript" src="../../static/plugin/jQuery/jquery-2.1.4.min.js" th:src="@{plugin/jQuery/jquery-2.1.4.min.js}"></script>
        <script src="../../static/plugin/vue/vue.min.js" th:src="@{plugin/vue/vue.min.js}"></script>
        <script src="../../static/plugin/layer/layer.js" th:src="@{plugin/layer/layer.js}"></script>
        <script src="../../static/plugin/vue/vue-resource.js" th:src="@{plugin/vue/vue-resource.js}"></script>
        <script type="text/javascript" src="../../static/js/common.js" th:src="@{js/common.js}"></script>
	</head>
	<body>
		<div  class="function-tip">
			<input type="button" name="" id="funtionButton" onclick="changeFunction()" value="已拥有菜单" class="active_btn"/><!-- 防止中间有间隙
            --><input type="button" name="" id="apiButton" onclick="changeAPI()" value="未拥有菜单" class="negative_btn"/>
			 <div class="function-content">
				 <input id="roleCodeId"  type="hidden" th:value="${roleCode}"/>
			 	<div id="swicDiv" v-cloak class="part-table">
					<div class="function-row" v-if="swichTable=='1'">
						
							<div class="part-table">
								<table>
									<thead>
									<tr>
										<th width="10%"><span>全选</span><input v-bind:checked="isAllChecked" @click="checkAll()" type="checkbox"  style="display: inline-block;"></th>
										<th width="17%">菜单编号</th>
										<th width="17%">菜单等级</th>
										<th width="17%">菜单名称</th>
										<th width="17%">连接地址</th>
										<th width="17%">图标地址</th>
									</tr>
									</thead>
									<tbody>
									<tr  v-cloak v-for="power in nonePowerList" :class="{'deepbg':power.powerLevel==1}">
										<td v-if="power.powerLevel==1"><label ></label>
                                            <input  @click="check(power)" v-bind:checked="power.isChecked" type="checkbox">
                                        </td>
										<td v-if="power.powerLevel==2"><label ></label><input  @click="check(power)" v-bind:checked="power.isChecked" type="checkbox"></td>
										<td class="td-blue">
											<label v-if="power.powerLevel==2"></label>
											{{ power.powerOrder}}
										</td>
										<td class="td-blue">{{ power.powerLevel}}</td>
										<td class="td-blue">{{ power.powerName}}</td>
										<td class="td-blue">{{ power.url}}</td>
										<td class="td-blue">{{ power.icon }}</td>
									</tr>
									<tr>
										<td colspan="2"></td>
										<td colspan="2" >
											<input type="button"  @click="addPower"  value="增 加" class="searchBtn">
											<input type="button"  @click="backRoleList" value="关 闭" class="checkGreyBtn btnLeft">
										</td>
										<td colspan="2"></td>
									</tr>
									</tbody>
								</table>
							</div>
						
					</div>
				</div>
                 <div id="part-table">
                    <div  class="function-row" style="display: block">
                        <div class="part-table">
                            <table>
                                <thead>
                                <tr>
									<th width="10%"><span>全选</span><input v-bind:checked="isAllChecked" @click="checkAll()" type="checkbox"  style="display: inline-block;"></th>
									<th width="17%">菜单编号</th>
									<th width="17%">菜单等级</th>
									<th width="17%">菜单名称</th>
									<th width="17%">连接地址</th>
									<th width="17%">图标地址</th>
                                </tr>
                                </thead>
                                <tbody>
								<tr  v-cloak v-for="power in havaPowerList" :class="{'deepbg':power.powerLevel==1}">
									<td v-if="power.powerLevel==1"><label ></label><input  @click="check(power)" v-bind:checked="power.isChecked" type="checkbox"></td>
									<td v-if="power.powerLevel==2"><label ></label><input  @click="check(power)" v-bind:checked="power.isChecked" type="checkbox"></td>
									<td >
										<label v-if="power.powerLevel==2"></label>
										{{ power.powerOrder}}
									</td>
									<td class="td-blue">{{ power.powerLevel}}</td>
									<td class="td-blue">{{ power.powerName}}</td>
									<td class="td-blue">{{ power.url}}</td>
									<td class="td-blue">{{ power.icon }}</td>
								</tr>
								<tr>
									<td colspan="2"></td>
									<td colspan="2" >
										<input type="button"  @click="delPower"  value="删 除" class="searchBtn">
										<input type="button"  @click="backRoleList" value="关 闭" class="checkGreyBtn btnLeft">
									</td>
									<td colspan="2"></td>
								</tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                 </div>
			</div>
		</div>


	</body>
	
	<script type="text/javascript">
        var havaPowerVue = new Vue({
            el: '#part-table',
            data:{
                powerName: '',
                havaPowerList: [],
				flag:'0',
                isAllChecked:false,
                powerOrder:''
            },
            mounted: function () {
                this.$nextTick(function () {
                    // 代码保证 this.$el 在 document 中
					if(this.flag == '0'){
                        this.getPowerbyRoleCode();
					}else{
					    alert("havaPowerVue");
					}
                })
            },
            methods:{
                getPowerbyRoleCode:function () {
                    loading();
                    var roleCode = $("#roleCodeId").val();
                    this.$http.get('./power/getHavePowerList?roleCode='+roleCode,{powerName:this.powerName}).then(function (response) {
                        response = response.data;
                        if (response.status == "200") {
                            this.havaPowerList = response.result;
                            this.havaPowerList.forEach(function (power, index) {
                                power.index = index;
                            });
                        } else {
                            exceptionHandler(response);
                        }
                        closeLoading();
                    });
                },
                backRoleList:function () {
                    window.location.href="./page/toRoleList"
                },
				delPower:function () {
                    var toDelArray = havaPowerVue.getCheckIdList();

					if(toDelArray.length === 0){
                        alert("请选择要删除的权限");
                        return false;
					}
                    var roleCode   = $("#roleCodeId").val();

                    $.ajax({
                        type: "POST",
                        url: "./power/delPower?roleCode="+roleCode,
                        dataType: "json",
                        contentType: "application/json",
                        data: JSON.stringify(toDelArray),
                        success: function (data) {
                            if(data.code = '000000'){
                                changeFunction();
                            }else{
                                alert("权限删除失败");
                            }
                        }
                    });
                },
                checkAll: function () {
                    this.isAllChecked = !this.isAllChecked;
                    this.havaPowerList.forEach(function (power) {
                        power.isChecked = havaPowerVue.isAllChecked;
					});
                },
                check: function(power){
                    power.isChecked = !power.isChecked;
                    Vue.set(this.havaPowerList, power.index, power);    //power当中定义index
                    var nextLevel = this.getNextLevel(power);
                    if(nextLevel.length > 0){
                        nextLevel.forEach(function (nextLevelPower) {
                            havaPowerVue.checkNextLevel(nextLevelPower, power.isChecked);
                        });
					}
				},
				checkNextLevel: function(power, isChecked){
                    power.isChecked = isChecked;
                    Vue.set(this.havaPowerList, power.index, power);
				},
				getCheckList: function(){
                    var returnList = [];
                    this.havaPowerList.forEach(function (power) {
						if(power.isChecked){
                            returnList.push(power);
						}
                    });
                    return returnList;
				},
				getCheckIdList: function(){
				    var checkList = this.getCheckList();
				    var returnList = [];
                    checkList.forEach(function (power) {
                        returnList.push(power.id);
					});
                    return returnList;
				},
                getNextLevel: function (power) {
                    var returnList = [];
                    this.havaPowerList.forEach(function (nextLevelPower) {
                        if(nextLevelPower.parentId == power.id){
                            returnList.push(nextLevelPower);
                        }
                    });
                    return returnList;
                },
                refreshHavePowerPage: function (roleCode) {
                    window.location.href = "./page/powerList?roleCode="+roleCode;
                }
            }
        });
        var nonePowerVue = new Vue({
            el:"#swicDiv",
            data:{
                swichTable:"2",
                currenApi:"222021",
                isAllChecked:false,
                flag: '0',
                nonePowerList:[]
            },
            methods:{
                getApiDoc:function (apiCode) {
                    this.currenApi=apiCode;
                    changeAPI();
                },
				toggleCodeNav: function(index){
					this.nowIndex = index;
				},
                getNonePowerbyRoleCode:function () {
					loading();
					var roleCode = $("#roleCodeId").val();
					this.$http.get('./power/getNonePowerList?roleCode='+roleCode,{powerName:this.powerName}).then(function (response) {
						response = response.data;
						if (response.status == "200") {
							this.nonePowerList = response.result;
                            this.nonePowerList.forEach(function (power, index) {
                                power.index = index;
                            });
						} else {
							exceptionHandler(response);
							//layer.msg(response.message);
						}
						closeLoading();
					});
                },
                backRoleList:function () {
                    window.location.href="./page/toRoleList"
                },
                addPower:function () {
                    var roleCode  = $("#roleCodeId").val();
                    var toAddArray = nonePowerVue.getCheckIdList();
                    if(toAddArray.length === 0){
                        alert("请选择要添加的权限");
                        return false;
                    }

                    $.ajax({
                        type: "POST",
                        url: "./power/addRolePower?roleCode="+roleCode,
                        dataType: "json",
                        contentType: "application/json",
                        data: JSON.stringify(toAddArray),
                        success: function (data) {
                            if(data.code = '000000'){
                                changeAPI();
                            }else{
                                alert("权限添加失败");
                            }
                        }
                    });
                },
                checkAll: function () {
                    this.isAllChecked = !this.isAllChecked;
                    this.nonePowerList.forEach(function (power) {
                        power.isChecked = nonePowerVue.isAllChecked;
                    });
                },
                check: function(power){
                    power.isChecked = !power.isChecked;
                    Vue.set(this.nonePowerList, power.index, power);    //power当中定义index
                    var nextLevel = this.getNextLevel(power);
                    if(nextLevel.length > 0){
                        nextLevel.forEach(function (nextLevelPower) {
                            nonePowerVue.checkNextLevel(nextLevelPower, power.isChecked);
                        });
                    }
                },
                getNextLevel:function (power) {
                    var returnList = [];
					this.nonePowerList.forEach(function (nextLevelPower) {
						if(nextLevelPower.parentId == power.id){
						    returnList.push(nextLevelPower);
						}
                    });
					return returnList;
                },
                checkNextLevel: function(power, isChecked){
                    power.isChecked = isChecked;
                    Vue.set(this.nonePowerList, power.index, power);
                },
                getCheckIdList: function(){
                    var checkList = this.getCheckList();
                    var returnList = [];
                    checkList.forEach(function (power) {
                        returnList.push(power.id);
                    });
                    return returnList;
                },
                getCheckList:function () {
					var returenList =[];
					this.nonePowerList.forEach(function (power,index) {
						if(power.isChecked){
                            returenList.push(power);
						}
                    });
					return returenList;
                }
            }
        });

        function changeAPI() {
            nonePowerVue.swichTable='1';
            havaPowerVue.flag = '1';
            nonePowerVue.getNonePowerbyRoleCode();
            document.getElementById("part-table").style.display="none";
            document.getElementById("funtionButton").className="negative_btn";
            document.getElementById("apiButton").className="active_btn";
        }

        function changeFunction() {
            nonePowerVue.swichTable='2';
            havaPowerVue.getPowerbyRoleCode();
            document.getElementById("part-table").style.display="block";
            document.getElementById("funtionButton").className="active_btn";
            document.getElementById("apiButton").className="negative_btn";
        }

	</script>
</html>

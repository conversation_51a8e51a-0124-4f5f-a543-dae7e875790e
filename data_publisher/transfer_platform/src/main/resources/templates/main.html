<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <base href="./" th:href="@{../}"/>
    <meta charset="UTF-8">
    <!--浏览器默认使用webkit内核-->
    <meta name="renderer" content="webkit" />
    <meta http-equiv = "X-UA-Compatible" content = "IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />

    <link rel="stylesheet" type="text/css" href="../static/css/main/main.css" th:href="@{css/main/main.css}" />
    <link rel="stylesheet" type="text/css" href="../static/css/common/reset.css" th:href="@{css/common/reset.css}" />
    <link rel="stylesheet" type="text/css" href="../static/plugin/layer/skin/default/layer.css" th:href="@{plugin/layer/skin/default/layer.css}" />
    <title>发布平台</title>
</head>
<body >
<style>
    .nav-li:hover{
        background:#00A2EF;
        color: white;
    }
    .decration{
        width: 70px;
        height: 4px;
        background:#00A2EF;
        position: absolute;
        left: 0;
        bottom:0;
    }
    .nav-li{
        position: relative;
    }
    .pageFlag{
        background: #00A2EF;
        position: absolute;
        bottom: 0;
        left: 10%;
        width: 80%;
        height: 4px;
    }

</style>
<div id="main" style="border-bottom: 1px solid #e1e1e1;">
    <div class="up-nav clearfloat" style="position: relative" v-show="showtop">
        <div class="main-logo">
            <span><img src="../static/icons/sma_logo.png" th:src="@{icons/sma_logo.png}" style="width: 100%;height: 100%"/></span><span>转换平台</span>
        </div>
        <ul v-cloak style="float: right; margin-right: 2px;">
            <div class="personInfo" @mouseenter="showPersonCenter()" @mouseleave="hidePersonCenter()">
                <span v-cloak >{{user.userName}}</span>
                <img src="../static/icons/ico_topbar_arrow.png" th:src="@{icons/ico_topbar_arrow.png}" />
            </div>
            <div  v-cloak class="personCenter" v-show="showFlag" th:style="'height:110px;'" @mouseenter="showPersonCenter()" @mouseleave="hidePersonCenter()">
                <div class="personCenter_top">
                    <ul class="">
                        <li>
                            <img src="../static/icons/ico_user_user.png" th:src="@{icons/ico_user_user.png}" />
                        </li>
                        <li>
                            <p v-cloak >{{user.userName}}</p>
                        </li>
                    </ul>
                </div>
                <div  v-cloak class="personCenter_bot">
                    <ul>
                        <li style="border-right: 1px solid #E1E1E1;">
                            <input type="button" name="" value="修改密码" onclick="window.open('./page/updatePwd')"/>

                        </li><li style="border-left: 1px solid #E1E1E1;">
                        <input type="button" name="" value="退出登录"
                               onclick="loginOut()" />
                    </li>
                    </ul>
                </div>
            </div>
        </ul>
    </div>
    <div class="content">
        <iframe name="apiAndFunction"  src="./page/apiAndFunction" width="100%" height="100%" class="main-iframe">
        </iframe>
    </div>
</div>

</body>
<script type="text/javascript" src="../static/plugin/jQuery/jquery-2.1.4.min.js" th:src="@{plugin/jQuery/jquery-2.1.4.min.js}"></script>
<script src="../static/plugin/vue/vue.min.js" th:src="@{plugin/vue/vue.min.js}"></script>
<script src="../static/plugin/vue/vue-resource.js" th:src="@{plugin/vue/vue-resource.js}"></script>
<script type="text/javascript" src="../static/plugin/layer/layer.js" th:src="@{plugin/layer/layer.js}"></script>

<script th:inline="javascript">
    var mainVue = new Vue({
        el:'#main',
        mounted: function () {
            this.$nextTick(function () {
                this.personCenter();
                //this.init();
                this.setTitle();
            });
        },
        data:{
            searchText: '',//文本框内容
            index: 0, //选项标识
            page: '',
            user: {},
            showFlag: false,
            nowIndex: '',
            showtop:true
        },
        methods: {
            personCenter: function () {
                this.$http.get('./user/getUser').then(function (response) {
                    response = response.data;
                    if (response.status == "200") {
                        this.user = response.result;
                    }
                });
            },
            setTitle: function(){
                document.title = '转换平台';
            },
            hidePersonCenter: function () {
                this.showFlag = false;
            },
            showPersonCenter: function(){
                this.showFlag = true;
            },

        }
    });

    /**
     * 登出
     */
    function loginOut(){
        mainVue.showtop = false;
        parent.location.href="./loginOut";
        localStorage.removeItem("loginState");//清除loginState的值
        localStorage.removeItem("token");//清除token的值
        window.sessionStorage.removeItem("sessionToken");//清除session中token的值
        window.sessionStorage.removeItem("curTime");//清除session中curTime的值 
        //个人中心的DIV隐藏，显示登录、注册div
    }
</script>
</html>

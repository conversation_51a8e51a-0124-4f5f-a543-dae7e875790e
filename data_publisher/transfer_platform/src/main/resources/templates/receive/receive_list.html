<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <base href="./" th:href="@{../}"/>
    <meta charset="UTF-8">
    <link rel="stylesheet" type="text/css" href="../../static/css/task/reset.css" th:href="@{css/task/reset.css}">
    <link rel="stylesheet" type="text/css" href="../static/css/task/task-list.css" th:href="@{css/task/task-list.css}">
    <link rel="stylesheet" type="text/css" href="../static/css/task/task.css" th:href="@{css/task/task.css}">
    <link rel="stylesheet" type="text/css" href="../static/css/pages.css" th:href="@{css/pages.css}">
    <title>index</title>
</head>
<body style="min-width: 1145px;padding: 0 10px">
<div v-cloak id="receiveList">
    <div class="title-wrap">
        <ul class="title">
            <li>
                <h3>数据日志管理</h3>
            </li>
        </ul>
        <ul class="userMsg">
            <li>
                <span>主批次号 :</span><input type="text" name="mainVersionCode" v-model="mainVersionCode">
                <span>车组编号 :</span><input type="text" name="groupCode" v-model="groupCode">
            </li>
            <li>
                <span>数据状态 :</span>
                <select v-model.trim="state" style="height: 28px">
                    <option value="">--请选择--</option>
                    <option value="02">获取失败</option>
                    <option value="51">转换成功</option>
                    <option value="50">转换失败</option>
                    <option value="61">对比成功</option>
                    <option value="60">对比失败</option>
                    <option value="2">发送成功</option>
                    <option value="3">发送失败</option>
                </select>
            </li>
            <li>
                <i></i>
                <input type="button" name="" value="查 询" class="searchBtn" @click="getReceiveList('search')">
                <input type="button" name="" value="清 空" class="checkGreyBtn btnLeft" @click="location.reload()">
            </li>
        </ul>
    </div>
    <div class="insurance_table">
        <table style="white-space:nowrap; ">
            <thead>
            <tr>
                <th>序号</th>
                <th>主批次号</th>
                <th>车组编号</th>
                <th>品牌编号</th>
                <th>接收时间</th>
                <th>小批次号</th>
                <th>表名</th>
                <th>获取数据状态</th>
                <th>发送表名</th>
                <th>处理状态</th>
                <th>错误信息</th>
                <th>处理时间</th>
                <th>操作</th>
            </tr>
            </thead>
            <tbody>
            <tr class="table_trdown" v-for="(receive,index) in receiveList">
                <td v-text="index+1">
                </td>
                <td v-text="receive.mainVersionCode">
                </td>
                <td v-text="receive.groupCode">
                </td>
                <td v-text="receive.brandCode">
                </td>
                <td v-text="receive.receiveDate">
                </td>
                <td v-text="receive.versionCode">
                </td>
                <td v-text="receive.tableName">
                </td>
                <td>
                    <template v-if="receive.dataState == '00'">
                        等待获取数据
                    </template>
                    <template v-if="receive.dataState == '01' || receive.dataState == '03' || receive.dataState == '04'">
                        数据获取成功
                    </template>
                    <template v-if="receive.dataState == '02'">
                        数据获取失败
                    </template>
                </td>
                <td v-text="receive.sendTable">
                </td>
                <td>
                    <template v-if="receive.sendState == '40'">
                        等待转换
                    </template>
                    <template v-if="receive.sendState == '51'">
                        转换成功
                    </template>
                    <template v-if="receive.sendState == '50'">
                        转换失败
                    </template>
                    <template v-if="receive.sendState == '60'">
                        对比失败
                    </template>
                    <template v-if="receive.sendState == '61'">
                        对比成功
                    </template>
                    <template v-if="receive.sendState == '70'">
                        回写失败
                    </template>
                    <template v-if="receive.sendState == '71'">
                        回写成功
                    </template>
                    <template v-if="receive.sendState == '3'">
                        发送失败
                    </template>
                    <template v-if="receive.sendState == '2'">
                        发送成功
                    </template>
                    <template v-if="receive.sendState == '4'">
                        数据为空
                    </template>
                </td>
                <td v-text="receive.errMsg"></td>
                <td v-text="receive.endDate">
                </td>
                <td>
                    <template v-if="receive.dataState == '02'">
                        <input type="button" name="" value="重新获取" class="searchBtn" @click="operationTips(receive,1)">
                    </template>
                    <template v-if="receive.sendState == '50'">
                        <input type="button" name="" value="重新转换" class="searchBtn" @click="operationTips(receive,1)">
                    </template>
                    <template v-if="receive.sendState == '60'">
                        <input type="button" name="" value="重新对比" class="searchBtn" @click="operationTips(receive,1)">
                    </template>
                    <!--<template v-if="receive.sendState == '70'">
                        <input type="button" name="" value="重新回写" class="searchBtn" @click="operationTips(receive,2)">
                    </template>-->
                    <template v-if="receive.sendState == '3'">
                        <input type="button" name="" value="重新发送" class="searchBtn" @click="operationTips(receive,3)">
                    </template>
                </td>
            </tr>
            <tr v-if="receiveList.length == 0" class="table_trdown">
                <td th:colspan="12">无结果</td>
            </tr>

            </tbody>
        </table>
    </div>
    <div th:include="footer-pages :: footer-pages"></div>
</div>
</body>
<script type="text/javascript" src="../static/plugin/jQuery/jquery-2.1.4.min.js"
        th:src="@{plugin/jQuery/jquery-2.1.4.min.js}"></script>
<script type="text/javascript" src="../static/plugin/vue/vue.min.js" th:src="@{plugin/vue/vue.min.js}"></script>
<script src="../static/plugin/vue/vue-resource.js" th:src="@{plugin/vue/vue-resource.js}"></script>
<script src="../static/plugin/vue/vue-validator.js" th:src="@{plugin/vue/vue-validator.js}"></script>
<script src="../static/plugin/layer/layer.js" th:src="@{plugin/layer/layer.js}"></script>
<script src="../static/js/receive/receive_list.js" th:src="@{js/receive/receive_list.js}"></script>
</html>
<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <base href="./" th:href="@{../}"/>
    <meta charset="UTF-8">
    <link rel="stylesheet" type="text/css" href="../../static/css/task/reset.css" th:href="@{css/task/reset.css}">
    <link rel="stylesheet" type="text/css" href="../static/css/task/task-list.css" th:href="@{css/task/task-list.css}">
    <link rel="stylesheet" type="text/css" href="../static/css/task/task.css" th:href="@{css/task/task.css}">
    <link rel="stylesheet" type="text/css" href="../static/css/pages.css" th:href="@{css/pages.css}">
    <title>index</title>
</head>
<body style="min-width: 1145px;padding: 0 10px">
<div v-cloak id="tableDataList">
    <div class="title-wrap">
        <ul class="title">
            <li>
                <h3>表数据查询</h3>
            </li>
        </ul>
        <ul class="userMsg">
            <li>
                <span>表名 :</span><input type="text" name="tableName" v-model="tableName">
                <span>主批次号 :</span><input type="text" name="mainVersionCode" v-model="mainVersionCode">
                <span>小批次号 :</span><input type="text" name="versionCode" v-model="versionCode">
            </li>
            <li>
                <i></i>
                <input type="button" name="" value="查 询" class="searchBtn" @click="tableDataList()">
                <input type="button" name="" value="清 空" class="checkGreyBtn btnLeft" @click="location.reload()">
            </li>
        </ul>
    </div>
    <div class="insurance_table">
        <table style="white-space:nowrap; ">
            <thead>
            <tr>
                <th>序号</th>
                <template v-for="(data,index) in dataList">
                    <template v-if="index == 0">
                        <template v-for="(field,key) in data" :key="key">
                            <th v-show="key != 'rownums'">{{key}}</th>
                        </template>
                    </template>
                </template>
            </tr>
            </thead>
            <tbody>
            <tr class="table_trdown" v-for="(data,index) in dataList">
                <td v-text="index+1"></td>
                <template v-for="(field,key) in data" :key="key">
                    <td v-show="key != 'rownums'">{{field}}</td>
                </template>
            </tr>
            <tr v-if="dataList.length == 0" class="table_trdown">
                <td th:colspan="9">无结果</td>
            </tr>

            </tbody>
        </table>
    </div>
    <div th:include="footer-pages :: footer-pages"></div>


</div>
</body>
<script type="text/javascript" src="../static/plugin/jQuery/jquery-2.1.4.min.js"
        th:src="@{plugin/jQuery/jquery-2.1.4.min.js}"></script>
<script type="text/javascript" src="../static/plugin/vue/vue.min.js" th:src="@{plugin/vue/vue.min.js}"></script>
<script src="../static/plugin/vue/vue-resource.js" th:src="@{plugin/vue/vue-resource.js}"></script>
<script src="../static/plugin/vue/vue-validator.js" th:src="@{plugin/vue/vue-validator.js}"></script>
<script src="../static/plugin/layer/layer.js" th:src="@{plugin/layer/layer.js}"></script>
<script src="../static/js/receive/receive_data.js" th:src="@{js/receive/receive_data.js}"></script>
</html>
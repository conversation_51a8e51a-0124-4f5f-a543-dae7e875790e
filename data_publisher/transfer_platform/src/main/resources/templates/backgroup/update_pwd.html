<!DOCTYPE html>
<html  xmlns:th="http://www.thymeleaf.org">
	<head>
		<base href="./" th:href="@{../}"/>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>修改密码</title>
		<style type="text/css">
			.form-group{
				font-family: 'PingFangSC','helvetica neue','hiragino sans gb','arial','microsoft yahei ui','microsoft yahei','simsun','sans-serif'!important;"Helvetica Neue", "Luxi Sans", "DejaVu Sans", <PERSON><PERSON><PERSON>, "Hiragino Sans GB", STHeiti, "Microsoft YaHei";
				font-size: 14px;
			}
			input,button{
				font-family: 'PingFangSC','helvetica neue','hiragino sans gb','arial','microsoft yahei ui','microsoft yahei','simsun','sans-serif'!important;"Helvetica Neue", "Luxi Sans", "DejaVu Sans", <PERSON><PERSON><PERSON>, "Hiragino Sans GB", STHeiti, "Microsoft YaHei";
				outline:none;
			}

			.container{
				margin-top: 80px;
				margin-left: 60px;
			}
			.container .inputWrap{
				display: inline-block;
			}
			.container input{
				width: 250px;
				height: 34px;
				margin-top: 24px;
				padding: 6px 12px;
				font-size: 14px;
				display: inline-block;
				line-height: 1.42857143;
				color: #555;
				background-color: #F7F7F7;
				background-image: none;
				border: 1px solid #ccc;
				border-radius: 4px;
			}
			.container input:focus {
				border: 1px solid rgba(50,165,217,0.4);
			}
			.container label{
				margin-left: 24px;
				margin-right: 30px;
			}
			.container .control-label{
				margin-left: 0;
				margin-right: 26px;
			}
			.container .btn{
				width: 130px;
				height: 40px;
				border: none;
				margin-left: 100px;
				background: #00A0E9;
				color: #ffffff;
				border-radius: 5px;
			}
			.container .btn:hover{
				background: #32a5d9;
				cursor: pointer;
			}
			.form-group p{
				margin-top: -30px;
			}
		</style>
		<link href="../../static/css/common/reset.css" th:href="@{css/common/reset.css}" rel="stylesheet" type="text/css" />

	</head>
<body>
	<div class="container">
		<form class="form-horizontal">
		  <div class="form-group">

		    <div class="inputWrap clearfloat">
				<label for="oldpass">旧密码</label>
		      <input type="password" class="form-control" id="oldpass"
		      		placeholder="请输入原密码" >
		      <p id="oldpassTip" style="display:none;color:red;"></p>
		    </div>
		  </div>
		  <div class="form-group">
		    <div class="inputWrap clearfloat">
				<label for="newpass">新密码</label>
				<input type="password" class="form-control" id="newpass"
		      			placeholder="请输入新密码">
		      <p id="newpassTip" style="display:none;color:red;"></p>
		    </div>
		  </div>
		  <div class="form-group">
		    <div class="inputWrap clearfloat901228">
				<label for="newpassAgain" class=" control-label" >确认新密码</label>
				<input type="password" class="form-control" id="newpassAgain"
		      			placeholder="确认新密码">
		      <p id="newpassAgainTip" style="display:none;color:red;"></p>
		    </div>
		  </div>
		  <div class="form-group">
		 	 <input type="button" class="btn" value="确认修改" id="submit" onclick="sumbit();">
		  </div>
		</form>
	</div>
    <script src="../../static/plugin/jQuery/jquery-2.1.4.min.js" th:src="@{plugin/jQuery/jquery-2.1.4.min.js}"></script>
    <script src="../../static/plugin/layer/layer.js" th:src="@{plugin/layer/layer.js}"></script>
    <script type="text/javascript">

        /**
         确认修改密码按钮触发的事件
         */
        function sumbit(){
            $("#oldpassTip").empty();
            $("#newpassTip").empty();
            $("#newpassAgainTip").empty();

            var oldpass = $.trim($("#oldpass").val());
            if(oldpass =='' && oldpass.length == 0) {
                showError('oldpass', '原密码不能为空');
                return;
            }

            var newpass = $.trim($("#newpass").val());
            var newpassAgain = $.trim($("#newpassAgain").val());

            if(newpass == ''  && newpass.length == 0 || newpass.length < 5 || newpass.length > 18) {
                showError('newpass', '密码5~18位');
                error = true;
                return;
            }

            if(newpassAgain != newpass) {
                showError('newpassAgain', '与输入的新密码不一致');
            } else {
                $.post('./user/updatePwd', {"oldpass":oldpass,"new_password":newpass}, function(response) {
                    if(response.status == "200"){
                        layer.msg(data.message);
                        setTimeout(function(){
                            layer.closeAll();
                            parent.location.href="./loginOut";
                        }, 2000);
                    } else {
                        layer.msg(data.message);
                    }
                });
            }
        }

        function showError(formSpan, errorText) {
            $("#" + formSpan).css({"border-color":"red"});
            $("#" + formSpan + "Tip").empty();
            $("#" + formSpan + "Tip").append(errorText);
            $("#" + formSpan + "Tip").css({"display":"inline"});
        }
    </script>
</body>
</html>
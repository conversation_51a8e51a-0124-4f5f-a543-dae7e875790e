<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
	<base href="./" th:href="@{../}"/>
	<meta charset="UTF-8">
	<link rel="stylesheet" type="text/css" href="../../static/css/task/reset.css" th:href="@{css/task/reset.css}">
	<link rel="stylesheet" type="text/css" href="../static/css/task/task-list.css" th:href="@{css/task/task-list.css}">
	<link rel="stylesheet" type="text/css" href="../static/css/task/task.css" th:href="@{css/task/task.css}">
	<link rel="stylesheet" type="text/css" href="../static/css/pages.css" th:href="@{css/pages.css}">
	<title>index</title>
</head>
<body style="min-width: 1145px;padding: 0 10px">
<div v-cloak id="userList">
	<div class="title-wrap">
		<ul class="title">
			<li>
				<h3>用户管理</h3>
				<input type="button" name="" value="✚  添加用户" class="searchBtn" @click="userAdd()">
			</li>
		</ul>
		<ul class="userMsg">
			<li>
				<span>登录名 :</span><input type="text" name="" v-model="userName">
			</li>
			<li>
			</li>
			<li>
				<i></i>
				<input type="button" name="" value="查 询" class="searchBtn" @click="getUserPoList(1)">
				<input type="button" name="" value="清 空" class="checkGreyBtn btnLeft" @click="location.reload()">
			</li>
		</ul>
	</div>
	<div class="insurance_table">
		<table>
			<thead>
			<tr>
				<th>NO.</th>
				<th>登录名</th>
				<th>邮箱</th>
				<th>操作</th>
			</tr>
			</thead>
			<tbody>
			<tr class="table_trdown" v-for="(user,index) in getUserList">
				<td v-text="index+1">
				</td>
				<td v-text="user.userName">
				</td>
				<td v-text="user.email">
				</td>
				<td class="ditailBtn">
					<input type="button" name="" value="编 辑"  v-on:click="userDetailEdit(user)">
					<!--<input type="button" name="" value="客 户"  v-on:click="interfaceSettings(user)">-->
					<input type="button" name="" value="删 除"  v-on:click="userDel(user)">
				</td>
			</tr>

			<tr v-if="getUserList.length == 0" class="table_trdown">
				<td th:colspan="6">无结果</td>
			</tr>

			</tbody>
		</table>
	</div>
    <div th:include="footer-pages :: footer-pages"></div>
</div>
</body>
<script type="text/javascript" src="../static/plugin/jQuery/jquery-2.1.4.min.js" th:src="@{plugin/jQuery/jquery-2.1.4.min.js}"></script>
<script type="text/javascript" src="../static/plugin/vue/vue.min.js" th:src="@{plugin/vue/vue.min.js}"></script>
<script src="../static/plugin/vue/vue-resource.js" th:src="@{plugin/vue/vue-resource.js}"></script>
<script src="../static/plugin/vue/vue-validator.js" th:src="@{plugin/vue/vue-validator.js}"></script>
<script src="../static/plugin/layer/layer.js" th:src="@{plugin/layer/layer.js}"></script>
<script src="../static/js/user/user_list.js" th:src="@{js/user/user_list.js}"></script>
</html>
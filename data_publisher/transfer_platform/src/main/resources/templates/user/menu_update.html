<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:v-on="http://www.w3.org/1999/xhtml">
<head>
    <base href="./" th:href="@{../}"/>
    <meta charset="UTF-8">
    <link  href="../static/css/task/reset.css" th:href="@{css/task/reset.css}" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="../static/css/task/task-config.css" th:href="@{css/task/task-config.css}"/>
    <link rel="stylesheet" type="text/css" href="../../static/css/task/task.css" th:href="@{css/task/task.css}">
    <script type="text/javascript" th:src="@{plugin/jQuery/jquery-2.1.4.min.js}" src="../static/plugin/jQuery/jquery-2.1.4.min.js"></script>
    <script th:src="@{plugin/vue/vue.min.js}" src="../static/plugin/vue/vue.min.js"></script>
    <script th:src="@{plugin/vue/vue-resource.js}" src="../static/plugin/vue/vue-resource.js"></script>
    <script src="../static/plugin/layer/layer.js" th:src="@{plugin/layer/layer.js}"></script>
    <script src="../static/js/common.js" th:src="@{js/common.js}"></script>
    <style>
        .topul{
            overflow:hidden;
            margin-bottom:10px;
        }
        .topul li{
            width:33%;
            text-align:center;
            float:left;
            margin-bottom:10px;
        }
    </style>
    <title>菜单修改</title>
</head>
<body style="min-width: 1145px;">
<div class="taskdeal" id="homemadeProcessAdd" v-cloak style="display:bloc;padding-top:0px;margin-top:0px;">
    <div class="sidebarTable" id="taskList" v-cloak>
        <table>
            <tr style="color: #868686;">
                <td colspan="8" align="center" >修改菜单信息<td>
            </tr>
            <tr>
                <td><font color="red">*</font>菜单名称:</td>
                <td v-cloak ><input type="text" v-model="menuList.powerName"></td>
                <td><font color="red">*</font>菜单排序编码:</td>
                <td v-cloak ><input type="text" v-model="menuList.powerOrder"></td>
            </tr>
            <tr>
                <td>菜单路径:</td>
                <td v-cloak ><input type="text" v-model="menuList.url"></td>
                <td>菜单等级:</td>
                <td v-cloak >
                    <select v-model="menuList.powerLevel" class="selectStyle" @change="menuChange()">
                        <option value="" selected="selected" class="selectStyle">==请选择==</option>
                        <option value="1" selected="selected" class="selectStyle">一级菜单</option>
                        <option value="2" selected="selected" class="selectStyle">二级菜单</option>
                    </select>
                </td>
            </tr>
            <tr v-if="modelFlag">
                <td>父模块:</td>
                <td v-cloak >
                    <select v-model="menuList.parentId" class="selectStyle">
                        <option value="" selected="selected" class="selectStyle">==请选择==</option>
                        <option v-for="com in menu"  v-bind:value="com.id">
                            {{ com.powerName }}
                        </option>
                    </select>
                </td>

            </tr>

        </table>
        <div class="finish">
            <input type="button" name="" value="保 存" class="veh-config-button" @click="submit()">
            <input type="button"  value="关 闭" class="veh-config-button" @click="close()">
        </div>
    </div>
</div>
<script>
    var homemadeProcessAdd = new Vue({
        el: "#homemadeProcessAdd",
        data:{
            menu: [],
            menuList:JSON.parse(JSON.stringify(window.parent.importHalfCodeProcess.menu)),
            modelFlag: false
        },
        mounted: function () {
            this.getMenuList();
            this.changeMenuFlag();
        },
        methods:{
            submit: function () {
                this.$http.post("./power/updateMenu",this.menuList).then(function (response) {
                    response = response.data;
                    if(response.status == "200"){
                        layer.msg(response.message);
                    }else{
                        layer.msg(response.message);
                    }
                })
            },
            getMenuList:function () {
                this.$http.get("./power/findMenuList").then(function (response) {
                    response = response.data;
                    if(response.status == "200"){
                        if(response.result != null){
                            this.menu = response.result;
                            console.log("查询出来的menu："+JSON.stringify(this.menu));
                        }
                    }

                })
            },
            menuChange: function () {
                if(this.menuList.powerLevel == '2'){
                    this.modelFlag = true;
                }
                if(this.menuList.powerLevel == '1'){
                    this.modelFlag = false;
                }
            },
            changeMenuFlag: function () {
                if(this.menuList.powerLevel == '2'){
                    this.modelFlag = true;

                }
            },
            close:function () {
                parent.layer.closeAll();
                window.parent.importHalfCodeProcess.findByList("1");
            }
        }

    });


</script>
</body>

</html>
<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:v-on="http://www.w3.org/1999/xhtml">
<head>
    <base href="./" th:href="@{../}"/>
    <meta charset="UTF-8">
    <meta name="renderer" content="webkit" />
    <meta http-equiv = "X-UA-Compatible" content = "IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
    <link rel="stylesheet" type="text/css" href="../static/css/task/reset.css" th:href="@{css/task/reset.css}">
    <link rel="stylesheet" type="text/css" href="../static/css/task/task.css" th:href="@{css/task/task.css}">
    <link rel="stylesheet" type="text/css" href="../static/css/pages.css" th:href="@{css/pages.css}">
    <link rel="stylesheet" type="text/css" href="../static/css/task/reset.css" th:href="@{css/task/reset.css}">
    <link rel="stylesheet" type="text/css" href="../static/css/task/veh-list.css" th:href="@{css/task/veh-list.css}">
    <link rel="stylesheet" type="text/css" href="../static/css/task/vin_search.css" th:href="@{css/task/vin_search.css}">
    <link rel="stylesheet" type="text/css" href="../static/css/task/task-list.css" th:href="@{css/task/task-list.css}">


    <script src="../static/plugin/jQuery/jquery-2.1.4.min.js" th:src="@{plugin/jQuery/jquery-2.1.4.min.js}"></script>
    <script src="../static/plugin/vue/vue.min.js" th:src="@{plugin/vue/vue.min.js}"></script>
    <script src="../static/plugin/vue/vue-resource.js" th:src="@{plugin/vue/vue-resource.js}"></script>
    <script src="../static/plugin/vue/vue-validator.js" th:src="@{plugin/vue/vue-validator.js}"></script>
    <script src="../static/plugin/laydate/laydate.js" th:src="@{plugin/laydate/laydate.js}"></script>
    <script src="../static/plugin/layer/layer.js" th:src="@{plugin/layer/layer.js}"></script>
    <script src="../../static/js/common.js" th:src="@{js/common.js}"></script>
    <style>
        .topul{
            overflow:hidden;
            margin-bottom:10px;
        }
        .topul li{
            width:33%;
            text-align:center;
            float:left;
            margin-bottom:10px;
        }
    </style>
    <title>客户信息</title>
</head>
<body style="min-width: 1145px;">
<div class="function-row" id="importHalfCodeProcess" v-cloak>
    <div class="function-row" id="taskList" v-cloak>
        <div class="title-wrap">
            <ul class="title">
                <li>
                    <h3>菜单管理</h3>
                    <input type="button" name="" value="✚  添加菜单" class="searchBtn" @click="menuAdd()">
                </li>
            </ul>
        </div>
        <div class="insurance_table">
            <table>
                <thead>
                <tr>
                    <th>NO.</th>
                    <th>菜单名</th>
                    <th>菜单等级</th>
                    <th>菜单路径</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                <tr class="table_trdown" v-for="(menu,index) in menuList">
                    <td v-text="index+1">
                    </td>
                    <td v-text="menu.powerName">
                    </td>
                    <td>
                        <p v-if = "menu.powerLevel == '1' ">
                            一级菜单
                        </p>
                        <p v-if = "menu.powerLevel == '2' ">
                            二级菜单
                        </p>
                    </td>
                    <td v-text="menu.url">
                    </td>
                    <td class="ditailBtn">
                        <input type="button" name="" value="编 辑"  v-on:click="updateMenu(menu)">
                        <input type="button" name="" value="删 除"  v-on:click="delMenu(menu)">
                    </td>
                </tr>
                <tr v-if="menuList == null" class="table_trdown">
                    <td th:colspan="5">无结果</td>
                </tr>
                </tbody>
            </table>
        </div>
        <div th:include="footer-pages :: footer-pages"></div>
    </div>
</div>
<script>
    var importHalfCodeProcess = new Vue({
        el: "#importHalfCodeProcess",
        data:{
            totalCount: 0, //总数
            all: 0, //总页数
            cur: 1, //当前页码
            pageFlag:'',
            menuList: [],
            menu:[]

        },

        mounted:function () {
            this.findByList();
        },
        methods:{
            findByList: function (type) {
                //需要把page置为1
                if("1" == type){
                    this.clearPage();
                }
                var TaskDTO = {
                    page: this.cur
                };
                this.menuList = '';
//				this.checkData = [];
                this.$http.get('./power/findMenuPower',TaskDTO).then(function (response) {
                    console.log(response);
                    response = response.data;
                    if(response.status == "200"){
                        this.menuList = response.result.data;
                        this.totalCount = response.result.total;
                        this.all = Math.ceil(response.result.total/10);
                    }else{
                        layer.msg(response.message);
                    }
                })
            },
            delMenu: function (data) {
                this.$http.get('./power/findMenuPower',data).then(function (response) {
                    response = response.data;
                    if(response.status == "200"){
                        layer.msg(response.message);
                    }else{
                        layer.msg(response.message);
                    }
                })
            },
            menuAdd: function () {
                layer.open({
                    type:2,
                    title:"添加菜单信息",
                    maxmin: false,
                    closeBtn: 1,
                    id: "LAY_layuipro", //设定一个id，防止重复弹出
                    area: ['90%', '90%'],
                    content: './page/addMenu'
                });
            },
            updateMenu: function (data) {
                this.menu = data;
                layer.open({
                    type:2,
                    title:"修改菜单信息",
                    maxmin: false,
                    closeBtn: 1,
                    id: "LAY_layuipro", //设定一个id，防止重复弹出
                    area: ['90%', '90%'],
                    content: './page/updateMenu'
                });
            },
            clearPage: function(){
                this.totalCount = 0, //当前页的页码
                this.cur = 1
            },
            btnClick: function(data){//页码点击事件
                if(data != this.cur && this.all !=0 && data <= this.all && data > 0){
                    this.cur = data;
                    importHalfCodeProcess.findByList(2);
                }
            },
            delmenu: function (data) {
                this.$http.post('./power/delMenu',data).then(function (response) {
                    response = response.data;
                    if(response.status == "200"){
                        layer.msg(response.message);
                    }else{
                        layer.msg(response.message);
                    }
                    this.findByList(1);
                })
            },

            del: function (data) {
                layer.msg('确定删除数据么？', {
                    time: 0 //不自动关闭
                    , btn: ['确定', '取消']
                    , yes: function(index){
                        importHalfCodeProcess.delVinInf(data)
                    }
                });
            },



        },
        computed:{
            indexs: function () {
                var left = 1
                var right = this.all
                var ar = []
                if (this.all >= 11) {
                    if (this.cur > 5 && this.cur < this.all - 4) {
                        left = this.cur - 5
                        right = this.cur + 4
                    } else {
                        if (this.cur <= 5) {
                            left = 1
                            right = 10
                        } else {
                            right = this.all
                            left = this.all - 9
                        }
                    }
                }
                while (left <= right) {
                    ar.push(left)
                    left++
                }
                return ar
            },
            showLast: function () {
                if (this.cur == this.all) {
                    return false
                }
                return true
            },
            showFirst: function () {
                if (this.cur == 1) {
                    return false
                }
                return true
            }
        },
    });


</script>
</body>

</html>
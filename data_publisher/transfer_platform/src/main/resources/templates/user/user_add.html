<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" >
<head>
    <base href="./" th:href="@{../}"/>
    <meta charset="UTF-8">
    <link  href="../static/css/task/reset.css" th:href="@{css/task/reset.css}" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="../static/css/task/task-config.css" th:href="@{css/task/task-config.css}"/>
    <link rel="stylesheet" type="text/css" href="../../static/css/task/task.css" th:href="@{css/task/task.css}">
    <script type="text/javascript" th:src="@{plugin/jQuery/jquery-2.1.4.min.js}" src="../static/plugin/jQuery/jquery-2.1.4.min.js"></script>
    <script th:src="@{plugin/vue/vue.min.js}" src="../static/plugin/vue/vue.min.js"></script>
    <script th:src="@{plugin/vue/vue-resource.js}" src="../static/plugin/vue/vue-resource.js"></script>
    <script src="../static/plugin/layer/layer.js" th:src="@{plugin/layer/layer.js}"></script>
    <script src="../static/js/common.js" th:src="@{js/common.js}"></script>
</head>
<body>
<!-- 添加用户信息 -->
<div class="taskdeal" v-cloak  id="user-insert" style="display:block;padding-top:0px;margin-top:0px;" >
    <div class="sidebarTable" >
        <table border="1" cellspcing="0" id="vcBase" style="margin-left: 0px">
            <!--基本参数-->
            <tbody>

                <tr>
                    <td>登录名：</td>
                    <td v-cloak>
                        <input type="text" v-model="userName" @blur="selectUserName()">
                        <span style="color: red;">&nbsp;&nbsp;*</span>
                    </td>
                </tr>
                <tr>
                    <td>密码：</td>
                    <td v-cloak >
                        <input type="password" size="20" id="newPassWord" v-model="passWord" placeholder="请输入密码">
                        <span style="color: red;">&nbsp;&nbsp;*</span>
                    </td>
                    <td>确认密码：</td>
                    <td v-cloak >
                        <input type="password" size="20" id="relPassWord" v-model="relPassWord" placeholder="请确认密码">
                        <span style="color: red;">&nbsp;&nbsp;*</span>
                    </td>

                </tr>
                <tr>
                    <td>角色：</td>
                    <td>
                        <select v-model="roleCode" class="selectStyle">
                            <option value ="" selected="selected" class="selectStyle">==请选择==</option>
                            <option v-for="role in userRoleList"  v-bind:value="role.code">
                                {{ role.name }}
                            </option>
                        </select>
                        <span style="color: red;">&nbsp;&nbsp;*</span>
                    </td>
                </tr>
                <tr>
                    <td>联系电话：</td>
                    <td v-cloak >
                        <input type="text" v-model="phone">
                        <span style="color: red;">&nbsp;&nbsp;*</span>
                    </td>
                    <td >邮箱：</td>
                    <td colspan="3" v-cloak >
                        <input v-model="email" type="text">
                        <span style="color: red;">&nbsp;&nbsp;*</span>
                    </td>
                </tr>

            </tbody>
        </table>
        <table border="1" cellspcing="0" id="vcActive" style="margin-left: 0px">
            <tbody>
                <tr>
                    <td colspan="2" style="text-align: center;">
                        <div>
                            <input type="button"  value="提 交" class="veh-config-button" @click="saveUser()">
                            <input type="button"  value="关 闭" class="veh-config-button" @click="pageClose()">
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
<script src="../static/js/user/user_add.js" th:src="@{js/user/user_add.js}"></script>
</body>
</html>
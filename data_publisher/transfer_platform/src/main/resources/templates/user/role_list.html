<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
	<base href="./" th:href="@{../}"/>
	<meta charset="UTF-8">
	<link rel="stylesheet" type="text/css" href="../../static/css/task/reset.css" th:href="@{css/task/reset.css}">
	<link rel="stylesheet" type="text/css" href="../../static/css/role/role.css" th:href="@{css/role/role.css}">
	

	<script type="text/javascript" src="../../static/plugin/vue/vue.min.js" th:src="@{plugin/vue/vue.min.js}"></script>
	<script src="../../static/plugin/vue/vue-resource.js" th:src="@{plugin/vue/vue-resource.js}"></script>
	<script src="../../static/plugin/vue/vue-validator.js" th:src="@{plugin/vue/vue-validator.js}"></script>
	<title>index</title>
</head>
<body>
	<div id="function-row" class="function-row" v-cloak>
		<div class="insurance_table">
			<table style="width: 95%">
				<thead>
					<tr>
						<th width="23%">序号</th>
						<th width="23%">角色名称</th>
						<th width="23%">角色编码</th>
						<th width="23%">权限查看</th>
					</tr>
				</thead>
				<tbody>
					<tr class="table_trdown" v-for="(role,index) in roleList">
						<td v-text="index + 1" width="25%">

						</td>
						<td v-text="role.name" width="25%">

						</td>
						<td v-text="role.code" width="25%">

						</td>
						<td class="ditailBtn" width="25%">
							<input type="button" name="" @click="powerPage(role.code);" value="权 限">
						</td>
					 </tr>
				</tbody>
			</table>
		</div>
	</div>
</body>

<script th:inline="javascript">
    var main = new Vue({
            el:'#function-row',
            mounted: function () {
                this.$nextTick(function () {
                    this.init();
                });
            },
            data:{
                roleList:[],
				roleCode:''
			},
            methods: {
                init: function(){
                    this.$http.get('./role/getRoleList').then(function (response) {
                        response = response.data;
                        if (response.status == "200") {
                            this.roleList = response.result;
                        }else{
                            exceptionHandler(response);
                        }
                    });
                },
				powerPage:function (roleCode) {
                   window.location.href = "./page/powerList?roleCode="+roleCode;
                }
            }
        })
		</script>
</html>
<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <base href="./" th:href="@{/}"/>
    <meta charset="UTF-8">
    <meta name="renderer" content="webkit"/>
    <meta name="baidu-site-verification" content="qBoq19BHL3"/>
    <meta name="keywords" content="精友"/>
    <meta name="description" content="精友、精友、精友"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no"/>
    <link rel="stylesheet" type="text/css" href="../static/css/common/reset.css" th:href="@{css/common/reset.css}"/>
    <link rel="stylesheet" type="text/css" href="../static/css/index/index.css" th:href="@{css/index/index.css}"/>
    <link type="text/css" rel="stylesheet" href="../static/css/common/business.css" th:href="@{css/common/business.css}"/>
    <title>首页</title>

    <script type="text/javascript" th:inline="javascript">
        function checkParent() {
            if (window != top){ top.location.href = location.href;}
        }
    </script>

</head>
<body onload="checkParent();">
<div id="navigation" class="navWrap">
    <div class="loginWrap clearfloat">
        <div id="imgPic">
            <!--<img src="../static/icons/pic-login-new.jpg"  th:src="@{icons/pic_login_repair.jpg}" style="margin-left: -20px;margin-top: -80px;height: 450px;width: 500px;">-->
            <img src="../static/icons/pic-login-new.jpg"  th:src="@{icons/transfer.jpg}" style="margin-left: -20px;margin-top: -80px;height: 450px;width: 500px;">
        </div>
        <div id="loginWrap" onkeydown="keyLogin();">
            <div class="login_title">
                <img src="../static/icons/JY-LOGO.png" th:src="@{icons/JY-LOGO.png}">
                <span>转换平台</span>
            </div>
            <div id="loginDiv" class="login_text">
                <!--<span id="tipsMsg" style="color: red; font-size: 18px;padding-left: 120px"></span>-->
                <div class="input_wrap">
                    <span>帐号</span>
                    <span class="innnerInput">
                        <img src="../static/icons/account-icon.png" th:src="@{icons/account-icon.png}" style="">
                        <input type="text" class="login_item" id="userName" v-model ="userName"  placeholder="请输入用户名" onkeyup="clearTips()">
                    </span>                     
                </div>
                <div class="clearfloat input_wrap">
                    <span>密码</span>
                    <span class="innnerInput">
                        <img src="../static/icons/password-icon.png" th:src="@{icons/password-icon.png}">
                        <input type="password" class="login_item" v-model ="passWord" id="passWord" placeholder="请输入登录密码" onkeyup="clearTips()">
                    </span>
                </div>
                <div class="right_bottom clearfloat">
                    <input type="text" name="vcode" id="vcode" v-model ="vcode" placeholder="验证码" maxlength="4"/>
                    <div class="authCode">
                        <img id="codePic" :src="codeImgUrl" @click="getVcode();" alt="无法显示"/>
                    </div>
                </div>
                <input type="button" value="登      录" class="login_btn" @click="login()" style="cursor:pointer">
                <span class="noId">还没有账号?</span><span class="register" @click="register()">立即注册</span>
            </div>
        </div>
    </div>
</div>

</body>

<script type="text/javascript" src="../static/plugin/jQuery/jquery-2.1.4.min.js" th:src="@{plugin/jQuery/jquery-2.1.4.min.js}"></script>
<script type="text/javascript" src="../static/plugin/vue/vue.min.js" th:src="@{plugin/vue/vue.min.js}"></script>
<script src="../static/plugin/vue/vue-resource.js" th:src="@{plugin/vue/vue-resource.js}"></script>
<script src="../static/plugin/vue/vue-validator.js" th:src="@{plugin/vue/vue-validator.js}"></script>
<script type="text/javascript" src="../static/plugin/layer/layer.js" th:src="@{plugin/layer/layer.js}"></script>
<script type="text/javascript" th:inline="javascript">

    var vm = new Vue({
        el: '#navigation',
        data: {
            codeImgUrl:'',
            userName:'',
            passWord:'',
            vcode:''
        },
        mounted: function() {
            this.$nextTick(function () {
                this.getVcode()
            })
        },
        methods: {
            goFunction: function () {
                window.location.href = "./page/indexMain";
            },
            register:function () {
                layer.msg("注册功能未开通，敬请期待！");
                return;
            },
            getVcode:function () {
                this.codeImgUrl = "./getGifCode?flag=" + Math.random();
            },
            login:function () {
                var checkBox = false;
                var rememberMe = $("input:checkbox:checked").val();
                if ("rememberMe" == rememberMe) {
                    checkBox = true;
                }
                $.ajax({
                    type: "POST",
                    url: "./login",
                    data: {"loginName": this.userName, "password": this.passWord, "rememberMe": checkBox, "vcode":this.vcode},
                    dataType: "text",
                    async: true,
                    success: function (data) {
                        data = eval("(" + data + ")");
                        if ("0" == data.code) {
                            layer.msg(data.message);
//                            $("#tipsMsg").text(data.message);
                        } else {
                            vm.goFunction();
                        }
                    }
                });
            }
        }
    });

    //清除错误提示
    function clearTips() {
        $("#tipsMsg").text("");
    }
    // 回车键
    function keyLogin() {
        if (event.keyCode == 13)
            $(".login_btn").click();
    }

</script>


</html>


Configuration:
  status: info

  Properties:
    Property:
      -  name: logFormat
         value: '%d{yyyy-MM-dd HH:mm:ss.SSS}{GMT+8}  %-5level  %logger{35}  %msg  %n'

  Appenders:
    Console:  #输出到控制台
      name: Console
      target: SYSTEM_OUT
      ThresholdFilter:
        level: info
        onMatch: ACCEPT
        onMismatch: DENY
#      JsonLayout:
#        complete: true
#        locationInfo: true
      PatternLayout:
        pattern: ${logFormat}


    RollingFile:
     - name: RollingFile
       fileName: '/home/<USER>/test/logs/transfer-platform.log'
       filePattern: '/home/<USER>/test/logs/transfer-platform.%d{yyyy-MM-dd}.log'
       PatternLayout:
         Pattern: ${logFormat}
       Policies:
         SizeBasedTriggeringPolicy:
           size: 512 MB

    Async:
      - name: AsyncRollingFile
        AppenderRef:
          ref: RollingFile

  Loggers:
    Root:
       AppenderRef:
         -  ref: Console
         -  ref: AsyncRollingFile
       level: info

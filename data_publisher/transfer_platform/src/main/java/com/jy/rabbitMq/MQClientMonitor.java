package com.jy.rabbitMq;

import com.rabbitmq.client.Channel;
import com.rabbitmq.client.GetResponse;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.rabbit.core.ChannelCallback;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.RabbitListenerEndpointRegistry;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by jdd on 2019/1/14.
 */
@Component
public class MQClientMonitor {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private AmqpAdmin rabbitAdmin;

    private static final Log log = LogFactory.getLog(MQClientMonitor.class);

    private static final String CONTAINER_NOT_EXISTS = "消息队列%s对应的监听容器不存在！";

    private RabbitListenerEndpointRegistry registry;

    public MQClientMonitor(RabbitListenerEndpointRegistry registry){
        this.registry = registry;
    }

    /**
     * queue2ContainerAllMap初始化标识
     */
    private volatile boolean hasInit = false;

    /**
     * 所有的队列监听容器MAP
     */
    private final Map<String, SimpleMessageListenerContainer> allQueue2ContainerMap = new ConcurrentHashMap<>();

    /**
     * 重置消息队列并发消费者数量
     * @param queueName
     * @param concurrentConsumers must greater than zero
     * @return
     */
    public boolean resetQueueConcurrentConsumers(String queueName, int concurrentConsumers) {
        Assert.state(concurrentConsumers > 0, "参数 'concurrentConsumers' 必须大于0.");
        SimpleMessageListenerContainer container = findContainerByQueueName(queueName);
        if (container.isActive() && container.isRunning()) {
            container.setConcurrentConsumers(concurrentConsumers);
            return true;
        }
        return false;
    }


    /**
     * 重启对消息队列的监听
     * @param queueName
     * @return
     */
    public boolean restartMessageListener(String queueName) {
        SimpleMessageListenerContainer container = findContainerByQueueName(queueName);
        Assert.state(!container.isRunning(), String.format("消息队列%s对应的监听容器正在运行！", queueName));
        container.start();
        return true;
    }

    /**
     * 停止对消息队列的监听
     * @param queueName
     * @return
     */
    public boolean stopMessageListener(String queueName) {
        SimpleMessageListenerContainer container = findContainerByQueueName(queueName);
        Assert.state(container.isRunning(), String.format("消息队列%s对应的监听容器未运行！", queueName));
        container.stop();
        return true;
    }

    /**
     * 统计所有消息队列详情
     * @return
     */
    public List<MessageQueueDatail> statAllMessageQueueDetail() {
        List<MessageQueueDatail> queueDetailList = new ArrayList<>();
        getQueue2ContainerAllMap().entrySet().forEach(entry -> {
            String queueName = entry.getKey();
            SimpleMessageListenerContainer container = entry.getValue();
            MessageQueueDatail deatil = new MessageQueueDatail(queueName, container);
            queueDetailList.add(deatil);
        });

        return queueDetailList;
    }

    public SimpleMessageListenerContainer findByQueueName(String queueName){
        return findContainerByQueueName(queueName);
    }

    /**
     * 根据队列名查找消息监听容器
     * @param queueName
     * @return
     */
    public SimpleMessageListenerContainer findContainerByQueueName(String queueName) {
        String key = queueName;
        SimpleMessageListenerContainer container = getQueue2ContainerAllMap().get(key);
        Assert.notNull(container, String.format(CONTAINER_NOT_EXISTS, key));
        return container;
    }

    private Map<String, SimpleMessageListenerContainer> getQueue2ContainerAllMap() {
        if (!hasInit) {
            synchronized (allQueue2ContainerMap) {
                if (!hasInit) {
                    registry.getListenerContainers().forEach(container -> {
                        SimpleMessageListenerContainer simpleContainer = (SimpleMessageListenerContainer) container;
                        Arrays.stream(simpleContainer.getQueueNames()).forEach(queueName ->
                                allQueue2ContainerMap.putIfAbsent(queueName, simpleContainer));
                    });
                    hasInit = true;
                }
            }
        }
        return allQueue2ContainerMap;
    }


    /**
     * 消息队列详情
     * <AUTHOR>
     * @date 2019/1/14
     */
    public static final class MessageQueueDatail {

        /**
         * 队列名称
         */
        private String queueName;

        /**
         * 监听容器标识
         */
        private String containerIdentity;

        /**
         * 监听是否有效
         */
        private boolean activeContainer;

        /**
         * 是否正在监听
         */
        private boolean running;

        /**
         * 活动消费者数量
         */
        private int activeConsumerCount;

        public MessageQueueDatail(String queueName, SimpleMessageListenerContainer container) {
            this.queueName = queueName;
            this.running = container.isRunning();
            this.activeContainer = container.isActive();
            this.activeConsumerCount = container.getActiveConsumerCount();
            this.containerIdentity = "Container@" + ObjectUtils.getIdentityHexString(container);
        }

        public String getQueueName() {
            return queueName;
        }

        public String getContainerIdentity() {
            return containerIdentity;
        }

        public boolean isActiveContainer() {
            return activeContainer;
        }

        public boolean isRunning() {
            return running;
        }

        public int getActiveConsumerCount() {
            return activeConsumerCount;
        }
    }

    public static class ChannelCallbackImpl implements ChannelCallback<String> {

        private String queueName;
        public ChannelCallbackImpl(String queueName){
            this.queueName = queueName;
        }
        @Override
        public String doInRabbit(Channel channel) {
            GetResponse response = null;
            try {
                response = channel.basicGet(queueName, false);
                channel.basicAck(response.getEnvelope().getDeliveryTag(), false);
                return new String(response.getBody(), "UTF-8");
            }
            catch(Exception e) {
                try {
                    channel.basicNack(response.getEnvelope().getDeliveryTag(), false, true);
                } catch (Exception e1) {
                    // TODO Auto-generated catch block
                    log.error("queueName: "+queueName+" ; message: "+e1.getMessage()+"");
                    return null;
                }
            }
            return null;
        }
    }

    public String processQueue(String queueName) {
        return rabbitTemplate.execute(new ChannelCallbackImpl(queueName));
    }

    public int getCount(String queueName) {
        Properties properties = rabbitAdmin.getQueueProperties(queueName);
        return (Integer)properties.get(RabbitAdmin.QUEUE_MESSAGE_COUNT);
    }
}

package com.jy.rabbitMq;

import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Created by jdd on 2018/11/28.
 */

@Configuration
public class RabbitConfig {

    /**
     * 设置mq名称
     * testMainVersionCode开发环境
     * testMainVersionCode1测试环境
     * receiveMainVersionCode生产环境
     */
//    public static final String MQ_NAME = "testMainVersionCode";
//    public static final String MQ_NAME = "testMainVersionCode1";
    public static final String MQ_NAME = "receiveMainVersionCode";

    public static final String WORK_MQ_NAME = "workHoursMainVersionCode";

    @Bean
    public Queue queue() {
        /*Map<String, Object> args= new HashMap<>(1);
        args.put("x-max-priority", 10);*/
        return new Queue(MQ_NAME);
        //return new Queue(MQ_NAME,true,false,false,args);
    }

    @Bean
    public Queue workQueue() {
        /*Map<String, Object> args= new HashMap<>(1);
        args.put("x-max-priority", 10);*/
        return new Queue(WORK_MQ_NAME);
        //return new Queue(MQ_NAME,true,false,false,args);
    }
}

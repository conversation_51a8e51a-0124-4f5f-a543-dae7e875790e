package com.jy.config;

import com.jy.shiro.PublishPlatformFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Created by ljx on 2017/7/13.
 * 拦截器配置
 */
@Configuration
public class FilterConfig {

    @Bean
    public FilterRegistrationBean indexFilterRegistration() {
        FilterRegistrationBean registration = new FilterRegistrationBean(new PublishPlatformFilter());
        registration.addUrlPatterns("/**");
        return registration;
    }
}

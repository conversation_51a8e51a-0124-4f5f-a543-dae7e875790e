package com.jy.config;

import com.jy.shiro.PublishPlatformRealm;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.DelegatingFilterProxy;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Shiro 权限控制配置类
 *
 * <AUTHOR>
 * @create 2017-06-01 12:48
 **/
@Configuration
public class ShiroConfig {

    /**
     * 自定义Realm
     * @return
     */
    @Bean(name = "shiroRealm")
    public PublishPlatformRealm getShiroRealm() {
        return new PublishPlatformRealm();
    }

    /**
     * 获取Shiro 权限管理对象securityManager
     * @param shiroRealm
     * @return
     */
    @Bean(name = "securityManager")
    public DefaultWebSecurityManager getDefaultWebSecurityManager(PublishPlatformRealm shiroRealm) {
        DefaultWebSecurityManager manager = new DefaultWebSecurityManager();
        manager.setRealm(shiroRealm);
        return manager;
    }

    @Bean
    public FilterRegistrationBean delegatingFilterProxy(){
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
        DelegatingFilterProxy proxy = new DelegatingFilterProxy();
        proxy.setTargetFilterLifecycle(true);
        proxy.setTargetBeanName("shiroFilter");
        filterRegistrationBean.setFilter(proxy);
        return filterRegistrationBean;
    }
    /**
     * Shiro 拦截器  anon:标识为白名单，跳过权限认证，authc：标识为必须经权限认证才能访问
     * @param securityManager
     * @return
     */
    @Bean(name = "shiroFilter")
    public ShiroFilterFactoryBean shirFilter(DefaultWebSecurityManager securityManager){
        ShiroFilterFactoryBean shiroFilterFactoryBean  = new ShiroFilterFactoryBean();
        shiroFilterFactoryBean.setSecurityManager(securityManager);
        Map<String,String> filterChainDefinitionMap = new LinkedHashMap<String,String>();
        filterChainDefinitionMap.put("/logout", "logout");

        filterChainDefinitionMap.put("/facade/**", "anon");
        filterChainDefinitionMap.put("/publishCon/publishData", "anon");
        //通知车组更新信息
        filterChainDefinitionMap.put("/receiveGroupData/*", "anon");
        /*filterChainDefinitionMap.put("/receiveGroupData/getList", "anon");
        filterChainDefinitionMap.put("/receiveGroupData/test1", "anon");
        filterChainDefinitionMap.put("/receiveGroupData/againSend", "anon");
        filterChainDefinitionMap.put("/receiveGroupData/getOriginalData", "anon");
        filterChainDefinitionMap.put("/receiveGroupData/selDataByTableName", "anon");
        filterChainDefinitionMap.put("/receiveGroupData/mqState", "anon");*/
        filterChainDefinitionMap.put("/statistics/dataStatistics", "anon");
        filterChainDefinitionMap.put("/autoTests", "anon");
        filterChainDefinitionMap.put("/admin/dataStatistics/dataStatistics", "anon");
        filterChainDefinitionMap.put("/admin/dataStatistics/tj", "anon");
        filterChainDefinitionMap.put("/workHours/saveWorkHours", "anon");
        filterChainDefinitionMap.put("/vehicle/vehicleConversion", "anon");
        filterChainDefinitionMap.put("/vehicle/resend", "anon");
        filterChainDefinitionMap.put("/compares/**", "anon");
        filterChainDefinitionMap.put("/sendTables/**", "anon");
        filterChainDefinitionMap.put("/receiveBatchNos/**", "anon");
        //将静态资添加到白名单
        filterChainDefinitionMap.put("/icons/**", "anon");

        filterChainDefinitionMap.put("/getGifCode", "anon");
        filterChainDefinitionMap.put("/login", "anon");
        filterChainDefinitionMap.put("/plugin/**", "anon");
        filterChainDefinitionMap.put("/css/**", "anon");
        filterChainDefinitionMap.put("/js/**", "anon");
        filterChainDefinitionMap.put("/**", "authc");

        //登录地址
        shiroFilterFactoryBean.setLoginUrl("/index");
        //登录成功后要跳转的链接
        shiroFilterFactoryBean.setSuccessUrl("/page/indexMain");
        //未授权界面;
        shiroFilterFactoryBean.setUnauthorizedUrl("/403");
        shiroFilterFactoryBean.setFilterChainDefinitionMap(filterChainDefinitionMap);
        return shiroFilterFactoryBean;
    }

}
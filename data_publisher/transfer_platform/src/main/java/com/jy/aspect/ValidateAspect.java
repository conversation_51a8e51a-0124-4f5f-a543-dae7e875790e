package com.jy.aspect;

import com.jy.ann.Validate;
import com.jy.ann.ValidateFiled;
import com.jy.bean.result.JsonResult;
import com.jy.bean.result.ResultStatus;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.lang.annotation.Annotation;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * Created by zy on 2017/11/14.
 * 参数统一校验
 */
@Component
@Aspect
public class ValidateAspect {

    /**
     * 使用AOP对使用了Validate的方法进行代理校验
     * @throws Throwable
     */
    @Around("@annotation(com.jy.ann.Validate)")
    public Object validateAround(ProceedingJoinPoint joinPoint) throws Throwable  {
        boolean flag = false ;
        Validate an = null;
        Object[] args =  null ;
        Method method = null;
        Object target = null ;
        String methodName = null;
        JsonResult<String> jsonResult = new JsonResult<String>();
        try{
            methodName = joinPoint.getSignature().getName();
            target = joinPoint.getTarget();
            method = getMethodByClassAndName(target.getClass(), methodName);    //得到拦截的方法
            args = joinPoint.getArgs();     //方法的参数
            an = (Validate)getAnnotationByMethod(method ,Validate.class );
            flag = validateFiled(an.fileds() , args, jsonResult);
        } catch(Exception e){
            e.printStackTrace();
            flag = false;
            jsonResult.setResultStatus(ResultStatus.BAD_PARAM);
        } finally{
            if(flag){
                return joinPoint.proceed();
            }else{  //这里使用了Spring MVC ，所有返回值应该为Strng或ModelAndView ，如果是用Struts2，直接返回一个String的resutl就行了
                jsonResult.setStatus(ResultStatus.BAD_PARAM.getStatus());
                return jsonResult;
            }
        }
    }

    /**
     * 验证参数是否合法
     */
    public boolean validateFiled(ValidateFiled[] valiedatefiles , Object[] args, JsonResult<String> jsonResult) throws Exception {
        for (ValidateFiled validateFiled : valiedatefiles) {
            Object arg = null;
            if("".equals(validateFiled.filedName()) ){
                arg = args[validateFiled.index()];
            }else{
                arg = getFieldByObjectAndFileName(args[validateFiled.index()] ,
                        validateFiled.filedName() );
            }

            if(validateFiled.notNull()){        //判断参数是否为空
                if(arg == null ) {
                    jsonResult.setMessage("参数" + validateFiled.filedName() + "为空; ");
                    return false;
                } else {
                    if(arg.toString().length() == 0){
                        jsonResult.setMessage("参数" + validateFiled.filedName() + "为空; ");
                        return false;
                    }
                }
            }else{      //如果该参数能够为空，并且当参数为空时，就不用判断后面的了 ，直接返回true
                if(arg == null )
                    return true;
            }

            if(!"".equals(validateFiled.filterRegStr())){      //判断去掉特殊字符
                arg = arg.toString().replaceAll(validateFiled.filterRegStr(), "");
            }

            if(validateFiled.maxLen() > 0){      //判断字符串最大长度
                if((arg.toString()).trim().length() > validateFiled.maxLen()) {
                    jsonResult.setMessage("参数" + validateFiled.filedName() + "超出最大长度，最大长度为" + validateFiled.maxLen() + "; ");
                    return false;
                }
            }

            if(validateFiled.minLen() > 0){      //判断字符串最小长度
                if((arg.toString()).trim().length() < validateFiled.minLen()) {
                    jsonResult.setMessage("参数" + validateFiled.filedName() + "少于最小长度，最小长度为" + validateFiled.minLen() + "; ");
                    return false;
                }
            }

            if(validateFiled.maxVal() != -1){   //判断数值最大值
                if( (Integer)arg > validateFiled.maxVal()) {
                    jsonResult.setMessage("参数" + validateFiled.filedName() + "大于最大值，最大值为" + validateFiled.maxVal() + "; ");
                    return false;
                }

            }

            if(validateFiled.minVal() != -1){   //判断数值最小值
                if((Integer)arg < validateFiled.minVal()) {
                    jsonResult.setMessage("参数" + validateFiled.filedName() + "小于最大值，最小值为" + validateFiled.minVal() + "; ");
                    return false;
                }
            }

            if(!"".equals(validateFiled.regStr())){ //判断正则
                //  if(arg instanceof String || arg.getClass().isPrimitive()){
                if(!arg.toString().matches(validateFiled.regStr())){
                    jsonResult.setMessage("参数" + validateFiled.filedName() + "不满足规则，" + validateFiled.regStrName() + "; ");
                    return false;
                }
               /* }  else {
                    return false;
                }*/
            }
            if(!"".equals(validateFiled.fileFormat())){      //判断文件格式
                MultipartFile file = (MultipartFile) arg;
                String format = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
                if(!validateFiled.fileFormat().contains(format.toLowerCase())){
                    jsonResult.setMessage("文件" + validateFiled.filedName() + "格式不满足规则，应为" + validateFiled.fileFormat() + "; ");
                    return false;
                }
            }

            if(!"".equals(validateFiled.imageSize())){      //判断文件尺寸 宽* 高
                MultipartFile file = (MultipartFile) arg;
                BufferedImage bi = ImageIO.read(file.getInputStream());
                String[] imageSize = validateFiled.imageSize().split("\\*");
                if(Integer.parseInt(imageSize[0]) < bi.getWidth() || Integer.parseInt(imageSize[1]) < bi.getHeight()){
                    jsonResult.setMessage("文件" + validateFiled.filedName() + "尺寸不满足规则，最大为" + validateFiled.imageSize() + "; ");
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 根据对象和属性名得到 属性
     */
    public Object getFieldByObjectAndFileName(Object targetObj , String fileName) throws SecurityException, NoSuchMethodException, IllegalArgumentException, IllegalAccessException, InvocationTargetException{
        String tmp[] = fileName.split("\\.");
        Object arg = targetObj ;
        for (int i = 0; i < tmp.length; i++) {
            if(arg instanceof String){

            } else if(arg instanceof MultipartFile && fileName.equals(((MultipartFile) arg).getName())) {

            } else {
                Method methdo = arg.getClass().
                        getMethod(getGetterNameByFiledName(tmp[i]));
                arg = methdo.invoke(arg);
            }
        }
        return arg ;
    }

    /**
     * 根据属性名 得到该属性的getter方法名
     */
    public String getGetterNameByFiledName(String fieldName){
        return "get" + fieldName.substring(0 ,1).toUpperCase() + fieldName.substring(1) ;
    }

    /**
     * 根据目标方法和注解类型  得到该目标方法的指定注解
     */
    public Annotation getAnnotationByMethod(Method method , Class annoClass){
        Annotation all[] = method.getAnnotations();
        for (Annotation annotation : all) {
            if (annotation.annotationType() == annoClass) {
                return annotation;
            }
        }
        return null;
    }

    /**
     * 根据类和方法名得到方法
     */
    public Method getMethodByClassAndName(Class c , String methodName){
        Method[] methods = c.getDeclaredMethods();
        for (Method method : methods) {
            if(method.getName().equals(methodName)){
                return method ;
            }
        }
        return null;
    }
}

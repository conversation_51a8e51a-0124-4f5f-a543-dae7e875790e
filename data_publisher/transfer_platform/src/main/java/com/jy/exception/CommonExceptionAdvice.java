package com.jy.exception;

import com.jy.bean.result.JsonResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.xml.bind.ValidationException;
import java.util.Set;

/**
 * 作用：全局异常处理
 */
//@ControllerAdvice
//@ResponseBody
public class CommonExceptionAdvice {

    private static Logger logger = LoggerFactory.getLogger(CommonExceptionAdvice.class);

    /**
     * 400 - Bad Request
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public JsonResult handleMissingServletRequestParameterException(MissingServletRequestParameterException e) {
        logger.error("缺少请求参数", e);
        return new JsonResult(String.valueOf(HttpStatus.BAD_REQUEST.value()), "required_parameter_is_not_present");
    }

    /**
     * 400 - Bad Request
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public JsonResult handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
        logger.error("参数解析失败", e);
        return new JsonResult(String.valueOf(HttpStatus.BAD_REQUEST.value()), "could_not_read_json");
    }

    /**
     * 400 - Bad Request
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public JsonResult handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        logger.error("参数验证失败", e);
        BindingResult result = e.getBindingResult();
        FieldError error = result.getFieldError();
        String field = error.getField();
        String code = error.getDefaultMessage();
        String message = String.format("%s:%s", field, code);
        return new JsonResult(String.valueOf(HttpStatus.BAD_REQUEST.value()), message);
    }

    /**
     * 400 - Bad Request
     */
    @ExceptionHandler(BindException.class)
    public JsonResult handleBindException(BindException e) {
        logger.error("参数绑定失败", e);
        BindingResult result = e.getBindingResult();
        FieldError error = result.getFieldError();
        String field = error.getField();
        String code = error.getDefaultMessage();
        String message = String.format("%s:%s", field, code);
        return new JsonResult(String.valueOf(HttpStatus.BAD_REQUEST.value()), message);
    }

    /**
     * 400 - Bad Request
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public JsonResult handleServiceException(ConstraintViolationException e) {
        logger.error("参数验证失败", e);
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        ConstraintViolation<?> violation = violations.iterator().next();
        String message = violation.getMessage();
        return new JsonResult(String.valueOf(HttpStatus.BAD_REQUEST.value()), "parameter:" + message);
    }

    /**
     * 400 - Bad Request
     */
    @ExceptionHandler(ValidationException.class)
    public JsonResult handleValidationException(ValidationException e) {
        logger.error("参数验证失败", e);
        return new JsonResult(String.valueOf(HttpStatus.BAD_REQUEST.value()), "validation_exception");
    }

    /**
     * 405 - Method Not Allowed
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public JsonResult handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e) {
        logger.error("不支持当前请求方法", e);
        return new JsonResult(String.valueOf(HttpStatus.METHOD_NOT_ALLOWED.value()), "request_method_not_supported");
    }

    /**
     * 415 - Unsupported Media Type
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public JsonResult handleHttpMediaTypeNotSupportedException(Exception e) {
        logger.error("不支持当前媒体类型", e);
        return new JsonResult(String.valueOf(HttpStatus.UNSUPPORTED_MEDIA_TYPE.value()), "content_type_not_supported");
    }

    /**
     * 500 - Internal Server Error
     */
    @ExceptionHandler(CommonException.class)
    public JsonResult handleServiceException(CommonException e) {
        logger.error("业务逻辑异常", e);
        return new JsonResult(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()), "业务逻辑异常：" + e.getMessage());
    }

    /**
     * 500 - Internal Server Error
     */
    @ExceptionHandler(Exception.class)
    public JsonResult handleException(Exception e) {
        logger.error("通用异常", e);
        return new JsonResult(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()), "通用异常：" + e.getMessage());
    }

    /**
     * 操作数据库出现异常:名称重复，外键关联
     */
    @ExceptionHandler(DataIntegrityViolationException.class)
    public JsonResult handleException(DataIntegrityViolationException e) {
        logger.error("操作数据库出现异常:", e);
        return new JsonResult(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()), "操作数据库出现异常：字段重复、有外键关联等");
    }
}


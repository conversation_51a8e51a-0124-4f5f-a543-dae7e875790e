package com.jy.security;

public class GlobalKeys {

	//登录验证
	public static final String SYN_SECURITY_LOGIN_CHECKCODE_ERROR="验证码错误!";//验证码错误
	public static final String SYN_SECURITY_LOGIN_CHECKCODE_EMPTY="请填写验证码";//验证码为空
	public static final String SYN_SECURITY_LOGIN_NAME_ERROR="帐号错误";//帐号错误
	public static final String SYN_SECURITY_LOGIN_PASSWORD_ERROR="密码错误";//密码错误
	public static final String SYN_SECURITY_LOGIN_ACCOUNT_LOCKED="账号被锁定";//密码错误
	public static final String SYN_SECURITY_LOGIN_NUMBER_ERROR="错误次数过多";//错误次数过多
	public static final String SYN_SECURITY_LOGIN_NAME_OR_PASSWORD_ERROR="账号或密码错误";//账号或密码错误
	public static final String SYN_SECURITY_LOGIN_USER_EXPIRED="用户到期";//用户到期
	public static final String SYN_SECURITY_LOGIN_USER_NOT_ACTIVATE="用户未激活";//用户未激活

	
	public static final String SYN_SECURITY_LOGIN_SUCCESS="成功";
	public static final String SYN_SECURITY_UPDATE_SUCCESS="修改成功";
	public static final String SYN_SECURITY_REGIST_SUCCESS="注册成功";
	public static final String SYN_SECURITY_LOGIN_FAIL="失败";
	//登录错误 （帐号密码错误统一显示帐号或密码错误）
	public static final String SYN_SECURITY_LOGIN_ERROR="登录错误";
	public static final String SYN_SECURITY_REGIST_ERROR="注册失败";
	//session 超时
	public static final String SYN_SESSION_TIMEOUT="登录超时";

	// 注册验证
	public static final String SYN_SECURITY_REGISTER_USER_EXPIRED="已有相同邮箱存在!";//已有相同邮箱存在
	public static final String NO_SECUTRITY_ROLE="没有旗下子账号注册权限，请联系精友管理员!";
	
	//页面展示标签
	public static final String SYN_SECURITY_LOGIN_MSG="synSecurityLoginMsg";
	
	
	// 用户类型
	public static final String NQ_USER_TYPE = "用户类型";	
	// 认证类型
	public static final String NQ_ATTESTATION_TYPE = "认证类型";
	// 用户状态
	public static final String NQ_USER_STATE = "用户状态";
	// 测试类型
	public static final String NQ_CHANNEL_TYPE = "测试类型";
	
	
	//登录成功 入库密码
	public static final String SUCCES_PASSCODE="***********";
	
}


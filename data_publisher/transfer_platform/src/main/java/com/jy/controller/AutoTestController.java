package com.jy.controller;

import com.github.pagehelper.PageHelper;
import com.jy.bean.po.ReceiveBatch;
import com.jy.bean.result.JsonResult;
import com.jy.rabbitMq.RabbitConfig;
import com.jy.service.AutoTestService;
import com.jy.service.CommonService;
import com.jy.service.ReceiveBatchService;
import com.jy.transform.PartDataTransform;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/autoTests")
public class AutoTestController {

    @Autowired
    private AmqpTemplate template;
    @Autowired
    private CommonService commonService;
    @Autowired
    private ReceiveBatchService receiveBatchService;
    @Autowired
    private PartDataTransform partDataTransform;

    private static final Map<String, String> deleteQueryMap = new HashMap<>();
    static {
        deleteQueryMap.put("f_zc_clzlb","id");
        deleteQueryMap.put ("f_zc_clzlb_flag","id");
        deleteQueryMap.put ("f_zc_clfzxxb","id");
        deleteQueryMap.put ("f_zc_clfzxxb_flag","id");
        deleteQueryMap.put ("f_zc_cxxxb","id");
        deleteQueryMap.put ("f_zc_cxxxb_flag","id");
        deleteQueryMap.put ("f_zc_clppxxb","id");
        deleteQueryMap.put ("f_zc_clppxxb_flag","id");
        deleteQueryMap.put("f_zc_qccjxxb","id");
        deleteQueryMap.put ("f_pj_zc_cxdyb","pjcxid");
        deleteQueryMap.put ("f_pj_cllbjdyb","clzlid");
        deleteQueryMap.put ("f_pj_clljtxdyb","clzlid");
        deleteQueryMap.put ("f_pj_czlbjdyb","clzlid");
        deleteQueryMap.put ("f_pj_pplbjdyb","ppid");
        deleteQueryMap.put ("f_pj_cxljfzdyb","clzlid");
        deleteQueryMap.put ("f_pj_czljfzdyb","czid");
        deleteQueryMap.put("p_zc_clzlb","id");
        deleteQueryMap.put ("p_zc_clzlb_flag","id");
        deleteQueryMap.put ("p_zc_clfzxxb","id");
        deleteQueryMap.put ("p_zc_clfzxxb_flag","id");
        deleteQueryMap.put ("p_zc_cxxxb","id");
        deleteQueryMap.put ("p_zc_cxxxb_flag","id");
        deleteQueryMap.put ("p_zc_clppxxb","id");
        deleteQueryMap.put ("p_zc_clppxxb_flag","id");
        deleteQueryMap.put("p_zc_qccjxxb","id");
    }

    @Autowired
    private AutoTestService autoTestService;

    @RequestMapping(params="deleteQuery", method = RequestMethod.POST)
    public JsonResult<String> customDeleteQuery(String brandCodes) throws Exception{
        for (Map.Entry<String, String> entry : deleteQueryMap.entrySet()) {
            Map<String, String> map = new HashMap<>();
            map.put(entry.getValue(), entry.getValue());
            autoTestService.deleteDataQuery(entry.getKey(), map);
        }

        JsonResult jsonResult = new JsonResult();
        jsonResult.setResult("成功");
        return jsonResult;
    }

    @RequestMapping(params="insertMq", method = RequestMethod.GET)
    public JsonResult<String> insertMq(String mqName, String mainVersionCode) throws Exception{
        template.convertAndSend(RabbitConfig.WORK_MQ_NAME, mainVersionCode);
        JsonResult jsonResult = new JsonResult();
        jsonResult.setResult("成功");
        return jsonResult;
    }

    @RequestMapping(params="test", method = RequestMethod.GET)
    public JsonResult<List<Map<String,Object>>> test(String mainVersionCode) throws Exception{
        ReceiveBatch receiveBatch = receiveBatchService.listByMainBatchNo(mainVersionCode);
        partDataTransform.update(receiveBatch);
        JsonResult jsonResult = new JsonResult();
        return jsonResult;
    }
}

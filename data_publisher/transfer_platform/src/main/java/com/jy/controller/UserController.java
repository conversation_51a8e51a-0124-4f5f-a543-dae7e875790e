package com.jy.controller;

import com.jy.bean.dto.PageCustom;
import com.jy.bean.dto.UserDTO;
import com.jy.bean.po.DictInfo;
import com.jy.bean.po.UserPo;
import com.jy.bean.result.JsonResult;
import com.jy.bean.result.ResultStatus;
import com.jy.exception.CommonException;
import com.jy.service.DictionaryTypeService;
import com.jy.service.UserService;
import com.jy.util.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * create by ljx
 */
@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private UserService userServiceImpl;

	@Autowired
	private DictionaryTypeService dictionaryTypeService;

	/**
	 * 获取用户列表
	 * @param dto
	 * @return
	 * @throws CommonException
	 */
	@RequestMapping("/userList")
	public JsonResult<PageCustom<List<UserDTO>>> userList(@Param("dto") UserDTO dto) throws CommonException {
		JsonResult<PageCustom<List<UserDTO>>> jsonResult = new JsonResult<PageCustom<List<UserDTO>>>();
		PageCustom<List<UserDTO>> PageCustoms = new PageCustom<>();
		PageCustoms.setPage(dto.getPage());
		PageCustom<List<UserDTO>> list = userServiceImpl.getUserList(dto,PageCustoms);
		if(list != null){
			jsonResult.setMessage("查询成功");
			jsonResult.setResult(list);
		} else {
			jsonResult.setResultStatus(ResultStatus.NO_DATA);
		}
		return jsonResult;
	}



	/**
	 * 用户保存
	 * @param
	 * @return
	 * @throws CommonException
	 */
	@RequestMapping("/saveUserDetail")
	public JsonResult saveUserDetail(@RequestBody UserDTO dto)throws CommonException {
		JsonResult jsonResult = new JsonResult();
		int res = userServiceImpl.saveUser(dto);
		if(res > 0){
			jsonResult.setStatus(ResultStatus.SUCCESS.getStatus());
			jsonResult.setMessage("用户信息更新成功");
		} else {
            jsonResult.setResultStatus(ResultStatus.NO_DATA);
        }
		return jsonResult;
	}

	/**
	 * 验证登录名是否注册
	 * @param
	 * @return
	 * @throws CommonException
	 */
	@RequestMapping("/searchUserName")
	public JsonResult searchUserName(@Param("dto")UserDTO dto)throws CommonException {
		JsonResult jsonResult = new JsonResult();
		int i = userServiceImpl.selectUserPoByName(dto.getUserName());
		if(i > 0){
			jsonResult.setStatus(ResultStatus.SUCCESS.getStatus());
			jsonResult.setMessage("用户名已经注册");
		} else {
			jsonResult.setResultStatus(ResultStatus.NO_DATA);
		}
		return jsonResult;
	}

	/**
	 * 获取当前登录用户信息
	 * @param request
	 * @return
	 */
	@RequestMapping("/getUser")
	public JsonResult getUser(HttpServletRequest request) {
		JsonResult jsonResult = new JsonResult();
		UserPo nqUser = (UserPo) request.getSession().getAttribute("currentUser");
		jsonResult.setResult(nqUser);
		return jsonResult;
	}

    /**
     * 修改密码
     * @param
     * @return
     */
    @RequestMapping(value="updatePwd", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<String> updatePwd(HttpSession session,
                                        @RequestParam(value = "oldpass", defaultValue = "") String oldpass,
                                        @RequestParam(value = "new_password", defaultValue = "") String new_password) throws CommonException {
        JsonResult<String> jsonResult = new JsonResult<>();
        UserPo user = (UserPo) session.getAttribute("currentUser");
		String res = userServiceImpl.updatePwd(user,oldpass,new_password);
		if(StringUtils.notEmpty(res)){
			if(res.equals("success")){
				jsonResult.setResultStatus(ResultStatus.SUCCESS);
			} else if (res.equals("pasError")){
				jsonResult.setStatus("1");
				jsonResult.setMessage("原密码错误");
			} else {
				jsonResult.setStatus("2");
				jsonResult.setMessage("修改失败");
			}
		} else {
			jsonResult.setStatus("2");
			jsonResult.setMessage("修改失败");
		}

        return jsonResult;
    }



	/**
	 * 获取用户详情 需要的字典
	 * @param
	 * @return
	 * @throws CommonException
	 */
	@RequestMapping("/getUserDetail")
	public JsonResult getUserDetail()throws CommonException {
		JsonResult jsonResult = new JsonResult();

		List<DictInfo> roleList = dictionaryTypeService.getDictionaryTypeList("003");

		Map<String,Object> map = new HashMap();
		map.put("roleList",roleList);

		if(map != null){
			jsonResult.setMessage("跳转详情页面");
			jsonResult.setResult(map);
		} else {
			jsonResult.setResultStatus(ResultStatus.NO_DATA);
		}
		return jsonResult;
	}


}
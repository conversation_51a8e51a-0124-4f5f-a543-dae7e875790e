package com.jy.controller;

import com.jy.bean.po.UserPo;
import com.jy.exception.CommonException;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpSession;
import javax.websocket.server.PathParam;

@Controller
@RequestMapping(value = "/page")
public class IndexController {


    /**
     * 登录成功后跳转到主页面
     * @return
     */
    @RequestMapping(value = "/toIndex")
    public String toIndex(Model model, @RequestParam(value = "index")String index) throws CommonException {
        model.addAttribute("index", index);
        return "index";
    }

    /**
     * 登录成功后跳转到主页面
     * @return
     */
    @RequestMapping(value = "/indexMain")
    public String toMainPage() throws CommonException {
        return "main";
    }

    /**
     * 登录成功后初始的页面
     * @return
     */
    @RequestMapping(value = "/initPage")
    public String initPage() throws CommonException {
        return "welcome";
    }

    /**
     * 用户列表页面
     * @return
     */
    @RequestMapping(value = "/userList")
    public String userList() throws CommonException {
        return "user/user_list";
    }

    /**
     * 用户编辑页面
     * @return
     */
    @RequestMapping(value = "/userDetailEdit")
    public String userDetailEdit() throws CommonException {
        return "user/user_detail";
    }

    /**
     * 接口设置页面
     * @param userId
     * @param model
     * @return
     * @throws CommonException
     */
    @RequestMapping(value = "/interfaceSettings")
    public String interfaceSettings(@RequestParam("userId")String userId, Model model) throws CommonException {
        model.addAttribute("userId",userId);
        return "user/interface_settings";
    }

    @RequestMapping(value = "/cusInterfaceY")
    public String cusInterfaceY() throws CommonException {
        return "user/cus_interface_y";
    }

    @RequestMapping(value = "/cusInterfaceW")
    public String cusInterfaceW() throws CommonException {
        return "user/cus_interface_w";
    }

    /**
     * 用户添加页面
     * @return
     */
    @RequestMapping(value = "/userAdd")
    public String userAdd() throws CommonException {
        return "user/user_add";
    }

    /**
     * 跳转修改密码页面
     * @return
     */
    @RequestMapping(value = "/updatePwd")
    public String toUpdatePwd() throws CommonException {
        return "backgroup/update_pwd";
    }

    /**
     * 跳转个人信息页面
     * @return
     */
    @RequestMapping(value = "/personMessage")
    public String personMessage(Model model, HttpSession session) throws CommonException {
        UserPo user = (UserPo) session.getAttribute("currentUser");
        model.addAttribute("userId",user.getId());
        return "user/person_message";
    }

    /**
     * 跳转权限页签展示页面
     * @return
     * @throws CommonException
     */
    @RequestMapping(value = "/powerList")
    public String powerList(Model model, String roleCode) throws CommonException {
        model.addAttribute("roleCode",roleCode);
        return "power/power_list";
    }

    /**
     * 跳转角色列表页面
     * @return
     */
    @RequestMapping(value = "/toRoleList")
    public String toPowerManager() throws CommonException {
        return "user/role_list";
    }

    /**
     *菜单页面
     * @return
     */
    @RequestMapping(value = "/apiAndFunction")
    public String apiAndFunction(Model model, HttpSession session) throws CommonException {
        UserPo user = (UserPo) session.getAttribute("currentUser");
        model.addAttribute("roleCode",user.getRoleCode());
        model.addAttribute("userId",user.getId());
        return "api-and-function";
    }

    /**
     * 错误页面跳转
     * @param code
     * @param message
     * @return
     * @throws CommonException
     */
    @RequestMapping(value = "/error")
    public ModelAndView defaultErrorHandler(@PathParam("code") String code, @PathParam("message") String message) throws CommonException {
        ModelAndView mav = new ModelAndView();
        mav.addObject("code", code);
        mav.addObject("message", message);
        mav.setViewName("error");
        return mav;
    }



    /**
     * 未选字段列表
     * @return
     * @throws CommonException
     */
    @RequestMapping(value = "/fieldUnSelect")
    public String fieldUnSelect(@Param("tableId") String tableId, @Param("customerId")String customerId ,Model model) throws CommonException {
        model.addAttribute("tableId", tableId);
        model.addAttribute("customerId", customerId);
        return "customerInf/customer_field_unselect";
    }

    /**
     * 修理厂数据导入页面
     * @return
     */
    @RequestMapping(value = "/repairImp")
    public String repairImp() throws CommonException {
        return "repairImp/repair_imp";
    }

    @RequestMapping("dataTables")
    public String dataTables(){
        return "dataTables/dataTables_list";
    }

    /**
     * 菜单管理页面
     * @return
     */
    @RequestMapping(value = "/menuList")
    public String menuList() throws CommonException {
        return "user/menu_list";
    }

    /**
     * 菜单添加页面
     * @return
     */
    @RequestMapping(value = "/addMenu")
    public String addMenu() throws CommonException {
        return "user/menu_add";
    }

    /**
     * 菜单修改页面
     * @return
     */
    @RequestMapping(value = "/updateMenu")
    public String updateMenu() throws CommonException {
        return "user/menu_update";
    }

    /**
     * 转换数据列表
     * @return
     */
    @RequestMapping("receive")
    public String receive(){
        return "receive/receive_list";
    }

    /**
     * 查询表数据
     * @return
     */
    @RequestMapping("tableData")
    public String tableData(){
        return "receive/receive_data";
    }

    /**
     * 统计数据
     * @return
     */
    @RequestMapping("statistics")
    public String dataStatistics(String date,Model view){
        view.addAttribute("date",date);
        return "dataStatistics/statistics_group_list";
    }

    @RequestMapping("staGroup")
    public String staGroup(String groupId, String date, Model view){
        view.addAttribute("groupId",groupId);
        view.addAttribute("date",date);
        return "dataStatistics/statistics_list";
    }
}



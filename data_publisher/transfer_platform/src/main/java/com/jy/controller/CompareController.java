package com.jy.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jy.bean.common.Constant;
import com.jy.bean.dto.*;
import com.jy.bean.po.CompareDataLayer;
import com.jy.bean.po.CompareErrorData;
import com.jy.bean.po.SendTable;
import com.jy.bean.result.JsonResult;
import com.jy.exception.CompareException;
import com.jy.service.*;
import com.jy.util.Dictionary;
import com.jy.util.*;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/8/20
 */
@RestController
@RequestMapping("/compares")
public class CompareController {
    private final static Log log = LogFactory.getLog(CompareController.class);

    private LinkedBlockingQueue<CompareDataLayer> reSendQueue = new LinkedBlockingQueue(10000000);
    private boolean reSendFlag = true;

    @Value("${compare.layerSize}")
    private Integer layerSize;
    @Value("${compare.batchSize}")
    private Integer batchSize;
    @Value("${httpUtils.dataPublish.comparePath}")
    private String comparePath;

    @Autowired
    private DataPublishUtils dataPublishUtils;
    @Autowired
    private CompareService compareService;
    @Autowired
    private SendTableService sendTableService;
    @Autowired
    private SendStateService sendStateService;
    @Autowired
    private ClientService clientService;
    @Autowired
    private CompareErrorDataService compareErrorDataService;
    @Autowired
    private ReceiveGroupDataService receiveGroupDataService;
    @Autowired
    private ConversionTableService conversionTableService;
    @Autowired
    private GroupBrandAssocitedService groupBrandAssocitedService;

    private static Map<String, Boolean> partClientLock = new HashMap<>();

    private static Map<String, List<CompareDataDTO>> clientResult = new HashMap<>();

    /**
     * 1、传递客户端则指定与此客户端对账
     * 2、不传递则对账所有客户端
     * @param clientCode
     * @return
     * @throws Exception
     */
    @RequestMapping(params={"compare","baseClientCode"}, method = RequestMethod.GET)
    public JsonResult<JSONObject> compare(String baseClientCode, String clientCode, String tableNames) throws Exception {
        clientResult = new HashMap<>();
        if(!Dictionary.getIsCompare()){
            throw new CompareException("正在对账中，不能重复点击!");
        }

        List<ClientUrlDTO> clientList = clientService.listCompareClient(baseClientCode, clientCode);
        if(EmptyUtils.isEmpty(clientList)){
            throw new CompareException("无对账客户端，请检查！");
        }
        boolean isCompare = receiveGroupDataService.isVerification(this.fitClientCode(clientList));
        if(!isCompare){
            throw new CompareException("正在存在发送中数据，不能对账!");
        }
        //暂停处理转换平台mq数据
        Dictionary.setIsCompare(false);
        String compareBatchNo = StringUtils.getGUID();

        JSONObject object = new JSONObject();
        Map<String, List<String>> splitTables = this.getSplitTables();
        List<String> noSplitTables = groupBrandAssocitedService.listByIsBrandCode("0");
        for(ClientUrlDTO client : clientList){
            //组装数据
            List<CompareDataDTO> data = this.fitClientData(tableNames, client.getClientCode(), splitTables, noSplitTables);
            if(EmptyUtils.isNotEmpty(data)){
                //发送数据
                JSONObject response = this.sendClientData(compareBatchNo, client.getClientCode(), client.getUrl(), data);
                object.put(client.getClientCode(), response);
                if(response.containsKey("status") && "200".equals(response.get("status")) && !Dictionary.getIsCompare()){
                    partClientLock.put(client.getClientCode() + "_" + client.getUrl(), false);
                }
            }
        }
        if(EmptyUtils.isEmpty(object)){
            Dictionary.setIsCompare(true);
            throw new CompareException("客户端无此对账表，请检查！");
        }
        object.put("compareBatchNo", compareBatchNo);
        object.put("compareStatus", Dictionary.getIsCompare());
        JsonResult<JSONObject> jsonResult = new JsonResult<>();
        jsonResult.setResult(object);
        return jsonResult;
    }

    @RequestMapping(params={"compare","baseTableName"}, method = RequestMethod.GET)
    public JsonResult<List<CompareDataLayer>> listBranchCountByTableName(String baseTableName, String layerKey, String layerValue) throws Exception {
        baseTableName = baseTableName.toLowerCase();
        Integer total = compareService.getCountByTableName(baseTableName, layerKey, layerValue);
        List<CompareDataLayer> compareDataLayers = null;
        if(EmptyUtils.isNotEmpty(total) && total > 0){
            compareDataLayers = this.fitTableRange(baseTableName, total, layerKey, layerValue);
        }

        JsonResult<List<CompareDataLayer>> jsonResult = new JsonResult<>();
        jsonResult.setResult(compareDataLayers);
        return jsonResult;
    }

    @RequestMapping(params={"resend"}, method = RequestMethod.POST)
    public JsonResult<String> listBranchCountByTableName(@RequestBody CompareDataLayer compareDataLayer) throws Exception {
        reSendQueue.add(compareDataLayer);

        JsonResult<String> jsonResult = new JsonResult<>();
        jsonResult.setResult("成功");
        return jsonResult;
    }

    @Scheduled(fixedDelay=5000)
    public void saveBatchEndTrail() {
        try {
            if(reSendFlag){
                while(reSendQueue.size() > 0){
                    this.pullReSendQueue();
                }
                reSendFlag = true;
            }
        } catch (Exception e) {
            reSendFlag = true;
            e.printStackTrace();
            log.error(e.getMessage());
        }

      //  log.warn("reSendQueue used size: {}" +  reSendQueue.size());
    }

    public void pullReSendQueue() {
        CompareDataLayer compareDataLayer = reSendQueue.poll();
        log.error("数据重发：" + compareDataLayer);
        String simpleTableName = groupBrandAssocitedService.fitTableName(compareDataLayer.getBaseTableName());

        //取出待发送数据 生成两个批次 1）删除 2)更新
        this.deleteHandle(compareDataLayer);
        this.insertHandle(compareDataLayer, simpleTableName);
    }

    @RequestMapping(params={"unlock","clientCode","clientUrl"}, method = RequestMethod.POST)
    public JsonResult<String> unlock(@RequestBody List<CompareDataDTO> compareDataDTOs, String clientCode, String clientUrl) throws Exception {
        //根据一个客户端返回结果直接开锁（暂时只有人保，后续修改）
        partClientLock.put(clientCode + "_" + clientUrl, true);
        boolean lockFlag = true;
        for (Map.Entry<String, Boolean> entry : partClientLock.entrySet()) {
            if(!entry.getValue()){
                lockFlag = false;
            }
        }
        if(lockFlag){
            Dictionary.setIsCompare(true);
        }
        clientResult.put(clientCode, compareDataDTOs);
        JsonResult<String> jsonResult = new JsonResult<>();
        jsonResult.setResult("成功");
        return jsonResult;
    }

    @RequestMapping(params={"listLock"}, method = RequestMethod.GET)
    public JsonResult<Map<String, Object>> listLock() throws Exception {
        JsonResult<Map<String, Object>> jsonResult = new JsonResult<>();
        Map<String, Object> result = new HashMap<>();
        result.putAll(partClientLock);
        result.put("base", Dictionary.getIsCompare());
        result.put("errorTable", clientResult);
        jsonResult.setResult(result);
        return jsonResult;
    }

    @RequestMapping(params={"compareBatchNo"}, method = RequestMethod.GET)
    public JsonResult<Map<String, List<SendStateDto>>> listCompareBatch(String compareBatchNo) throws Exception {
        List<SendStateDto> sendStateDtos = sendStateService.listCompareData(compareBatchNo);
        Map<String, List<SendStateDto>> result = sendStateDtos.stream().collect(Collectors.groupingBy(SendStateDto::getClientKey));

        JsonResult<Map<String, List<SendStateDto>>> jsonResult = new JsonResult<>();
        jsonResult.setResult(result);
        return jsonResult;
    }

    private void insertHandle(CompareDataLayer compareDataLayer, String simpleTableName){
        String mainVersionCode = "CompareData"+ TimestampTool.yyyymmddhhmmss()+ StringUtils.randomStr(5);
        String versionCode = "CompareData"+ TimestampTool.yyyymmddhhmmss()+ StringUtils.randomStr(7);
        //重发数据插入代发布表
        Map<String, String> fieldMap = conversionTableService.mapByTableName(simpleTableName);
        StringBuffer fKeyBuffer = new StringBuffer();
        StringBuffer cKeyBuffer = new StringBuffer();
        fieldMap.forEach((k, v)->{
            fKeyBuffer.append(k + ",");
            cKeyBuffer.append(v + ",");
        });

        String fKey = fKeyBuffer.substring(0, fKeyBuffer.length() -1).toString();
        String cKey = cKeyBuffer.substring(0, cKeyBuffer.length() -1).toString();
        compareService.resendInsert(mainVersionCode, versionCode, "insert", compareDataLayer.getBaseTableName(), cKey, fKey, compareDataLayer.getMaxVersionId(), compareDataLayer.getMinVersionId());
        String suffix = compareDataLayer.getPartTableSuffix();
        if(!StringUtils.isEmpty(suffix) && compareDataLayer.getBaseTableName().indexOf("pj_czlbjdyb") > -1){
            suffix = (Integer.parseInt(suffix) * 10)+"";
        }
        this.receiveVersion(compareDataLayer.getCompareBatchNo(), compareDataLayer.getClientUrl(), compareDataLayer.getClientCode(), "c_" + compareDataLayer.getBaseTableName(), compareDataLayer.getBaseTableName(),mainVersionCode, versionCode, suffix);
    }

    private void deleteHandle(CompareDataLayer compareDataLayer){
        String mainVersionCode = "CompareData"+ TimestampTool.yyyymmddhhmmss()+ StringUtils.randomStr(5);
        String versionCode = mainVersionCode + StringUtils.randomStr(5);
        //待发送数据
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("maxVersionId", compareDataLayer.getMaxVersionId());
        map.put("minVersionId", compareDataLayer.getMinVersionId());
        if(EmptyUtils.isNotEmpty(compareDataLayer.getPartTableSuffix())){
            map.put("partTableSuffix", compareDataLayer.getPartTableSuffix());
        }
        CompareErrorData compareErrorData = new CompareErrorData(mainVersionCode, versionCode, compareDataLayer.getBaseTableName(), JSON.toJSONString(map));
        compareErrorDataService.insert(compareErrorData);
        this.receiveVersion(compareDataLayer.getCompareBatchNo(), compareDataLayer.getClientUrl(), compareDataLayer.getClientCode(), Constant.ERROR_RECEIVE_TABLE, Constant.ERROR_RECEIVE_TABLE, mainVersionCode, versionCode, "");
    }

    private void receiveVersion(String compareBatchNo, String clientUrl, String clientCode, String sendTableName, String handleTableName, String mainVersionCode, String versionCode, String partTableSuffix){
        //主批次
        ReceiveGroupDataDto receiveGroupDataDto = new ReceiveGroupDataDto(compareBatchNo, mainVersionCode, clientUrl, clientCode);
        //子批次
        SendTable sendTable = new SendTable(sendTableName, handleTableName, mainVersionCode, versionCode, versionCode, "71");
        receiveGroupDataService.insert(receiveGroupDataDto);
        sendTableService.insert(sendTable);
        receiveGroupDataService.dataAssembly(mainVersionCode, "", "", partTableSuffix, true, "","0",receiveGroupDataDto.getClientCode(),receiveGroupDataDto.getClientUrl());
    }

    private JSONObject sendClientData(String compareBatchNo, String clientCode, String url, List<CompareDataDTO> compareDataDTOs) throws Exception {
        Map<String, String> querys = new HashMap<>();
        querys.put("compare", "compare");
        querys.put("clientCode", clientCode);
        querys.put("url", url);
        querys.put("compareBatchNo", compareBatchNo);
        log.warn("本次对账：" + "客户端：" + clientCode + "---对账表:" + JSON.toJSONString(compareDataDTOs));

        return dataPublishUtils.doPost(comparePath, querys, JSON.toJSONString(compareDataDTOs));
    }

    private List<CompareDataDTO> fitClientData(String tableNames, String clientCode, Map<String, List<String>> splitTables, List<String> noSplitTables) throws Exception {
        //1 组装path（针对facade）
        Map<String, ClientUrlDTO> pathMap = clientService.mapClientUrlByClientCode(clientCode);
        //客户端接收的表名
        Map<String, ClientTableDTO> tableMp = clientService.mapClientTableByClientCode(clientCode);

        List<CompareDataDTO> compareDataDTOs = new ArrayList<>();

        List<String> tableList = EmptyUtils.isEmpty(tableNames) ? new ArrayList<>() : Arrays.asList(tableNames.toLowerCase().split(","));

        //1、不分表数据组装
        compareDataDTOs.addAll(this.fitNoSplitTables(tableList, noSplitTables, tableMp, pathMap));
        //2、分表数据组装
        compareDataDTOs.addAll(this.fitSplitTables(tableList, splitTables, tableMp, pathMap));

        return compareDataDTOs;
    }

    private String fitClientCode(List<ClientUrlDTO> clientUrlDTOs){
        if(EmptyUtils.isEmpty(clientUrlDTOs)){
            return "";
        }
        String clientCodes = clientUrlDTOs.stream().map(ClientUrlDTO::getClientCode).collect(Collectors.joining(","));
        clientCodes = clientCodes + "," + clientUrlDTOs.get(0).getBaseClientCode();
        return clientCodes;
    }

    private Map<String, List<String>> getSplitTables(){
        Map<String, List<String>> map = new HashMap<>();
        List<String> tableArray = groupBrandAssocitedService.listByIsBrandCode("1");
        for(String tableName : tableArray){
            List<String> fTableNames = compareService.listTableName(tableName);
            map.put(tableName, fTableNames);
        }
        return map;
    }

    private List<CompareDataDTO> fitNoSplitTables(List<String> tableArray, List<String> noSplitTables, Map<String, ClientTableDTO> tableMp, Map<String, ClientUrlDTO> pathMap){
        List<CompareDataDTO> compareDataDTOs = new ArrayList<>();
        for(String str : noSplitTables){
            if(EmptyUtils.isNotEmpty(tableArray) && !tableArray.contains(str)){
                continue;
            }
            if(tableMp.containsKey(str) ){
                String path = pathMap.containsKey(str) ? pathMap.get(str).getPath() : null;
                CompareDataDTO compareDataDTO = new CompareDataDTO(str, tableMp.get(str).getTableName(), path);
                compareDataDTOs.add(compareDataDTO);
            }
        }
        return compareDataDTOs;
    }

    private List<CompareDataDTO> fitSplitTables(List<String> tableArray, Map<String, List<String>> splitTables, Map<String, ClientTableDTO> tableMp, Map<String, ClientUrlDTO> pathMap) throws Exception {
        List<CompareDataDTO> compareDataDTOs = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : splitTables.entrySet()) {
            if(tableMp.containsKey(entry.getKey())){
                for(String fTableName : entry.getValue()){
                    if(EmptyUtils.isNotEmpty(tableArray) && !tableArray.contains(fTableName)){
                        continue;
                    }

                    String clientTableName = fTableName.contains(tableMp.get(entry.getKey()).getTableName()) ? fTableName : tableMp.get(entry.getKey()).getTableName();
                    String path = pathMap.containsKey(entry.getKey()) ? pathMap.get(entry.getKey()).getPath() : null;
                    CompareDataDTO compareDataDTO = new CompareDataDTO(fTableName, clientTableName, path);
                    if(EmptyUtils.isNotEmpty(pathMap)){
                        //组装分表中维度数据
                        compareDataDTO = this.fitCmpareLayer(fTableName, compareDataDTO, tableMp.get(entry.getKey()));
                    }
                    compareDataDTOs.add(compareDataDTO);
                }
            }
        }
        return compareDataDTOs;
    }

    private CompareDataDTO fitCmpareLayer(String fTableName, CompareDataDTO compareDataDTO, ClientTableDTO clientTableDTO) throws Exception {
        String baseCompareLayerKey = "czid";
        List<String> compareLayerValues = compareService.listCompareLayer(fTableName, baseCompareLayerKey);
        Map<String, ClientTableFieldMpDTO> map = clientService.mapClientTableFieldByTableName(clientTableDTO.getClientCode(), clientTableDTO.getTableName());
        String compareLayerKey = map.containsKey(baseCompareLayerKey) ? map.get(baseCompareLayerKey).getTableField() : "groupId";
        compareDataDTO.setCompareLayerKey(compareLayerKey);
        compareDataDTO.setBaseCompareLayerKey(baseCompareLayerKey);
        compareDataDTO.setCompareLayerValue(compareLayerValues);
        return compareDataDTO;
    }

    private List<CompareDataLayer> fitTableRange(String baseTableName, Integer total, String layerKey, String layerValue){
        List<CompareDataLayer> result = new ArrayList<>();
        int pageSize = (int)Math.ceil(total / (layerSize * 1.0));
        BigInteger minVersionId = new BigInteger("0");
        for(int i=0; i<pageSize; i++){
            BigInteger maxVersionId = compareService.listPageRangeByTableName(baseTableName, layerKey, layerValue, minVersionId, layerSize);
            Integer size = layerSize;
            if( i == pageSize -1){
                size = compareService.getCountByTableNameAndVersionId(baseTableName, layerKey, layerValue, maxVersionId, minVersionId);
            }

            CompareDataLayer compareDataLayer = new CompareDataLayer(baseTableName, maxVersionId, minVersionId, size);
            result.add(compareDataLayer);
            minVersionId = maxVersionId;
        }
        return result;
    }

}

package com.jy.controller;

import com.jy.bean.dto.PageCustom;
import com.jy.bean.dto.UserDTO;
import com.jy.bean.po.SysPowerPo;
import com.jy.bean.po.UserPo;
import com.jy.bean.result.JsonResult;
import com.jy.bean.result.ResultStatus;
import com.jy.service.PowerService;
import com.jy.service.impl.CommonServiceImpl;
import com.jy.util.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/power")
public class PowerController {

    @Autowired
    private PowerService powerServiceImpl;

	@Autowired
	private CommonServiceImpl commonService;


	/**
	 * 角色拥有的权限查询
	 */
	@RequestMapping(value="getHavePowerList",method = RequestMethod.GET)
	public JsonResult<List<SysPowerPo>> getHavePowerList(String roleCode){
		JsonResult<List<SysPowerPo>> jsonResult = new JsonResult<>();
		List<SysPowerPo> list  = powerServiceImpl.getHavePowerList(roleCode);
		if(list!=null){
			jsonResult.setMessage("查询角色拥有权限成功");
			jsonResult.setResult(list);
		} else {
			jsonResult.setResultStatus(ResultStatus.NO_DATA);
		}
		return jsonResult;
	}


	/**
	 * 查询角色未拥有的权限查询
	 */
	@RequestMapping(value="getNonePowerList",method = RequestMethod.GET)
	public JsonResult<List<SysPowerPo>> getNonePowerList(String roleCode){
		JsonResult<List<SysPowerPo>> jsonResult = new JsonResult<>();
		List<SysPowerPo> list = powerServiceImpl.getNonePowerList(roleCode);
		if(list != null){
			jsonResult.setMessage("查询角色拥有权限成功");
			jsonResult.setResult(list);
		} else {
			jsonResult.setResultStatus(ResultStatus.NO_DATA);
		}
		return jsonResult;
	}

	/**
	 * 删除角色对应的权限
	 */
	@RequestMapping("/delPower")
	public JsonResult<String> delPower(@RequestBody String[] powerIds, String roleCode){

		JsonResult<String> jsonResult = new JsonResult<String>();
		//删除角色
		if(roleCode == null || roleCode.trim().equals("") || powerIds.length <= 0){
			jsonResult.setResultStatus(ResultStatus.INTERNAL_SERVER_ERROR);
			jsonResult.setMessage("删除角色失败");
			return  jsonResult;
		} else {
			boolean result = powerServiceImpl.delPowers(powerIds, roleCode);
			if (result) {
				jsonResult.setResultStatus(ResultStatus.SUCCESS);
				jsonResult.setMessage("删除角色成功");
			} else {
				jsonResult.setResultStatus(ResultStatus.INTERNAL_SERVER_ERROR);
				jsonResult.setMessage("删除角色失败");
			}
			return jsonResult;
		}
	}

	/**
	 * 为角色添加对应的权限
	 */
	@RequestMapping("/addRolePower")
	public JsonResult<String> addRolePower(@RequestBody String[] powerIds, String roleCode, HttpSession session){
		JsonResult<String> jsonResult = new JsonResult<>();
		UserPo user = (UserPo) session.getAttribute("currentUser");
		UserPo userPo = null;
		boolean result = false;
		if(StringUtils.notEmpty(powerIds)){
			for(int i = 0; i < powerIds.length; i++){
				userPo = new UserPo();
				userPo.setRoleCode(roleCode);
				userPo.setPowerId(powerIds[i]);
				userPo.setUserName(user.getUserName());
				result = powerServiceImpl.addRolePower(userPo);
			}
		}
		if(result){
			jsonResult.setResultStatus(ResultStatus.SUCCESS);
			jsonResult.setMessage("为角色添加权限成功");
		} else {
			jsonResult.setResultStatus(ResultStatus.INTERNAL_SERVER_ERROR);
			jsonResult.setMessage("为角色添加权限失败");
		}
		return jsonResult;
	}



	/**
	 * 添加菜单
	 */
	@RequestMapping("/addMnue")
	public JsonResult addMnue(@RequestBody SysPowerPo power){
		JsonResult jsonResult = new JsonResult();
		String s = powerServiceImpl.addMenuPower(power);
		jsonResult.setMessage(s);
		return jsonResult;
	}

	/**
	 * 添加菜单
	 */
	@RequestMapping("/updateMenu")
	public JsonResult updateMenu(@RequestBody SysPowerPo power){
		JsonResult jsonResult = new JsonResult();
		String s = powerServiceImpl.updateMenuPower(power);
		jsonResult.setMessage(s);
		return jsonResult;
	}

	/**
	 * 删除菜单
	 */
	@RequestMapping("/delMenu")
	public JsonResult delMenu(@RequestBody SysPowerPo power){
		JsonResult jsonResult = new JsonResult();
		String s = powerServiceImpl.delMenuPower(power);
		jsonResult.setMessage(s);
		return jsonResult;
	}


	/**
	 * 分页查询菜单
	 */
	@RequestMapping("/findMenuPower")
	public JsonResult<PageCustom<List<SysPowerPo>>> findMenuPower(@Param("dto") UserDTO dto){
		JsonResult<PageCustom<List<SysPowerPo>>> jsonResult = new JsonResult<PageCustom<List<SysPowerPo>>>();
		PageCustom<List<UserDTO>> PageCustoms = new PageCustom<>();
		PageCustoms.setPage(dto.getPage());
		PageCustom<List<SysPowerPo>> list = powerServiceImpl.findMenuByList(dto,PageCustoms);
		jsonResult.setResult(list);
		return jsonResult;
	}

	@RequestMapping("/findMenuList")
	public JsonResult findMenuList(String id){
		JsonResult jsonResult = new JsonResult();
		List list = new ArrayList();
		if(id != null){
			list = commonService.getOnlyJson(id);
		}
		List<SysPowerPo> menuList = powerServiceImpl.findMenuList(list);
		jsonResult.setResult(menuList);
		return jsonResult;
	}


}
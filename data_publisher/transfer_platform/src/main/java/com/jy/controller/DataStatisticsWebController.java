package com.jy.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jy.bean.dto.DataStatisticsDto;
import com.jy.bean.result.JsonResult;
import com.jy.service.DataStatisticsService;
import com.jy.service.ReceiveGroupDataService;
import com.jy.util.HttpClientUtil;
import com.jy.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2019/4/1.
 */
@Controller
@RequestMapping("admin/dataStatistics")
public class DataStatisticsWebController {

    @Autowired
    private DataStatisticsService dataStatisticsService;

    @Autowired
    private ReceiveGroupDataService receiveGroupDataService;

    @Value("${gainData.statisticsUrl}")
    private String statisticsUrl;

    @Value("${statistics.productUrl}")
    private String productUrl;

    @Value("${statistics.facadeTokenUrl}")
    private String facadeTokenUrl;

    @Value("${statistics.facadeStatisticsUrl}")
    private String facadeStatisticsUrl;

    @Value("${statistics.localUrl}")
    private String localUrl;

    @Value("${gainData.jgPath}")
    private String jgPath;

    @ResponseBody
    @RequestMapping("listDataStatistics")
    public JsonResult listDataStatistics(String groupId,String date){

        JsonResult jsonResult = new JsonResult();
        List<DataStatisticsDto> data = dataStatisticsService.listDataStatistics(groupId,date);

        jsonResult.setResult(handleData(data));
        return  jsonResult;
    }

    @ResponseBody
    @RequestMapping("getGroupId")
    public JsonResult getGroupId(String date){
        JsonResult jsonResult = new JsonResult();
        jsonResult.setResult(receiveGroupDataService.getStatisticsData(date,"",""));
        return jsonResult;
    }

    /**
     * 导出数据
     * @param date 导出哪个日期的数据
     * @return
     */
    @ResponseBody
    @RequestMapping("exportStatisticsData")
    public JsonResult exportStatisticsData(String date,HttpServletResponse response){

        JsonResult jsonResult = new JsonResult();
        if(StringUtils.isBlank(date)){
            jsonResult.setStatus("20000");
            jsonResult.setMessage("日期不能为空");
            return  jsonResult;
        }
        List<Map<String,Object>> list = dataStatisticsService.exportData(date);
        String fileName = "数据统计"+StringUtils.randomStr(5)+"_"+date+".csv";
        String filePath = statisticsUrl;
        String name = filePath + "/" + fileName;
        File file = null;
        String tab = "\",";
        String cc = "\"";
        String hh = "\"\r\n";
        StringBuilder write;
        FileOutputStream outSTr = null;
        BufferedOutputStream Buff = null;
        try {
            //判断下载文件夹是否存在
            File fileDir = new File(filePath);
            if(!fileDir.exists()){
                fileDir.mkdirs();
            }
            file = new File(name);
            outSTr = new FileOutputStream(file);
            Buff = new BufferedOutputStream(outSTr);
            String orgPartNoDeal=null;
            int[] indexes = new int[]{0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22};
            if(list != null && !list.isEmpty()){
                Buff.write((cc+hh).getBytes("GB2312"));
                for (Map<String,Object> map:list) {
                    //文件头
                    String head=cc+"车组名称:"+map.get("groupName")+"\",\"加工平台总数\",\"加工平台本期处理数量\",\"转换平台总数\",\"本期处理数量\",\"新增条数\",\"更新条数\",\"删除条数\",\"facade总数\",\"facade本期处理数量"+hh;
                    Buff.write(head.getBytes("GB2312"));
                    //List<DataStatisticsDto> dataList = (List<DataStatisticsDto>)map.get("dataStatisticsList");
                    List<DataStatisticsDto> dataList = JSONArray.parseArray(JSON.toJSONString(map.get("dataStatisticsList")),DataStatisticsDto.class);
                    dataList = handleData(dataList);
                    for (int i:indexes){
                        write = new StringBuilder();
                        for (int j = 0;j<dataList.size();j++){
                            DataStatisticsDto dto = dataList.get(j);
                            if(i == 0){
                                if(j == 0){
                                    write.append(cc+"厂家数量"+tab);
                                }
                                if("1".equals(dto.getDataType()) || "2".equals(dto.getDataType()) || "6".equals(dto.getDataType())){
                                    write.append(cc+dto.getZcjsl()+tab);
                                    write.append(cc+dto.getCjsl()+tab);
                                }else{
                                    write.append(cc+dto.getCjsl()+tab);
                                }
                            }
                            if(i == 1){
                                if(j == 0){
                                    write.append(cc+"品牌数量"+tab);
                                }
                                if("1".equals(dto.getDataType()) || "2".equals(dto.getDataType()) || "6".equals(dto.getDataType())){
                                    write.append(cc+dto.getZppsl()+tab);
                                    write.append(cc+dto.getPpsl()+tab);
                                }else{
                                    write.append(cc+dto.getPpsl()+tab);
                                }
                            }
                            if(i == 2){
                                if(j == 0){
                                    write.append(cc+"车系数量"+tab);
                                }
                                if("1".equals(dto.getDataType()) || "2".equals(dto.getDataType()) || "6".equals(dto.getDataType())){
                                    write.append(cc+dto.getZcxxsl()+tab);
                                    write.append(cc+dto.getCxxsl()+tab);
                                }else{
                                    write.append(cc+dto.getCxxsl()+tab);
                                }
                            }
                            if(i == 3){
                                if(j == 0){
                                    write.append(cc+"车组数量"+tab);
                                }
                                if("1".equals(dto.getDataType()) || "2".equals(dto.getDataType()) || "6".equals(dto.getDataType())){
                                    write.append(cc+dto.getZczsl()+tab);
                                    write.append(cc+dto.getCzsl()+tab);
                                }else{
                                    write.append(cc+dto.getCzsl()+tab);
                                }
                            }
                            if(i == 4){
                                if(j == 0){
                                    write.append(cc+"车型数量"+tab);
                                }
                                if("1".equals(dto.getDataType()) || "2".equals(dto.getDataType()) || "6".equals(dto.getDataType())){
                                    write.append(cc+dto.getZcxsl()+tab);
                                    write.append(cc+dto.getCxsl()+tab);
                                }else{
                                    write.append(cc+dto.getCxsl()+tab);
                                }
                            }
                            if(i == 5) {
                               if(j == 0) {
                                   write.append(cc+"理赔及承保关系数量"+tab);
                                   write.append(cc+" "+tab);
                               }
                               if("2".equals(dto.getDataType()) || "6".equals(dto.getDataType())){
                                   write.append(cc+dto.getZlpcbsl()+tab);
                                   write.append(cc+dto.getLpcbsl()+tab);
                                }else{
                                   write.append(cc+dto.getLpcbsl()+tab);
                               }
                            }
                            if(i == 6){
                                if(j == 0){
                                    write.append(cc+"车型零件数量"+tab);
                                }
                                if("1".equals(dto.getDataType()) || "2".equals(dto.getDataType()) || "6".equals(dto.getDataType())){
                                    write.append(cc+" "+tab);
                                    write.append(cc+dto.getCxljsl()+tab);
                                }else{
                                    write.append(cc+dto.getCxljsl()+tab);
                                }
                            }
                            if(i == 7){
                                if(j == 0){
                                    write.append(cc+"车型零件显示零件数量"+tab);
                                }
                                if("1".equals(dto.getDataType()) || "2".equals(dto.getDataType()) || "6".equals(dto.getDataType())){
                                    write.append(cc+" "+tab);
                                    write.append(cc+dto.getCxljxssl()+tab);
                                }else{
                                    write.append(cc+dto.getCxljxssl()+tab);
                                }
                            }
                            if(i == 8){
                                if(j == 0){
                                    write.append(cc+"车型下零件存在互斥码数量"+tab);
                                }
                                if("1".equals(dto.getDataType()) || "2".equals(dto.getDataType()) || "6".equals(dto.getDataType())){
                                    write.append(cc+" "+tab);
                                    write.append(cc+dto.getCzhcmsl()+tab);
                                }else{
                                    write.append(cc+dto.getCzhcmsl()+tab);
                                }
                            }
                            if(i == 9){
                                if(j == 0){
                                    write.append(cc+"车型标准件数量"+tab);
                                }
                                if("1".equals(dto.getDataType()) || "2".equals(dto.getDataType()) || "6".equals(dto.getDataType())){
                                    write.append(cc+" "+tab);
                                    write.append(cc+dto.getCxbzjsl()+tab);
                                }else{
                                    write.append(cc+dto.getCxbzjsl()+tab);
                                }
                            }
                            if(i == 10){
                                if(j == 0){
                                    write.append(cc+"车型非标准件数量"+tab);
                                }
                                if("1".equals(dto.getDataType()) || "2".equals(dto.getDataType()) || "6".equals(dto.getDataType())){
                                    write.append(cc+" "+tab);
                                    write.append(cc+dto.getCxfbzjsl()+tab);
                                }else{
                                    write.append(cc+dto.getCxfbzjsl()+tab);
                                }
                            }
                            if(i == 11){
                                if(j == 0){
                                    write.append(cc+"车型图片数量"+tab);
                                }
                                if("1".equals(dto.getDataType()) || "2".equals(dto.getDataType()) || "6".equals(dto.getDataType())){
                                    write.append(cc+" "+tab);
                                    write.append(cc+dto.getCxtpsl()+tab);
                                }else{
                                    write.append(cc+dto.getCxtpsl()+tab);
                                }
                            }
                            if(i == 12){
                                if(j == 0){
                                    write.append(cc+"车型图片热点数量"+tab);
                                }
                                if("1".equals(dto.getDataType()) || "2".equals(dto.getDataType()) || "6".equals(dto.getDataType())){
                                    write.append(cc+" "+tab);
                                    write.append(cc+dto.getCxtprdsl()+tab);
                                }else{
                                    write.append(cc+dto.getCxtprdsl()+tab);
                                }
                            }
                            if(i == 13){
                                if(j == 0){
                                    write.append(cc+"车组零件数量"+tab);
                                }
                                if("1".equals(dto.getDataType()) || "2".equals(dto.getDataType()) || "6".equals(dto.getDataType())){
                                    write.append(cc+" "+tab);
                                    write.append(cc+dto.getCzljsl()+tab);
                                }else{
                                    write.append(cc+dto.getCzljsl()+tab);
                                }
                            }
                            if(i == 14){
                                if(j == 0){
                                    write.append(cc+"车组标准件数量"+tab);
                                }
                                if("1".equals(dto.getDataType()) || "2".equals(dto.getDataType()) || "6".equals(dto.getDataType())){
                                    write.append(cc+" "+tab);
                                    write.append(cc+dto.getCzbzjsl()+tab);
                                }else{
                                    write.append(cc+dto.getCzbzjsl()+tab);
                                }
                            }
                            if(i == 15){
                                if(j == 0){
                                    write.append(cc+"车组非标准件数量"+tab);
                                }
                                if("1".equals(dto.getDataType()) || "2".equals(dto.getDataType()) || "6".equals(dto.getDataType())){
                                    write.append(cc+" "+tab);
                                    write.append(cc+dto.getCzfbzjsl()+tab);
                                }else{
                                    write.append(cc+dto.getCzfbzjsl()+tab);
                                }
                            }
                            if(i == 16){
                                if(j == 0){
                                    write.append(cc+"品牌零件数量"+tab);
                                }
                                if("1".equals(dto.getDataType()) || "2".equals(dto.getDataType()) || "6".equals(dto.getDataType())){
                                    write.append(cc+" "+tab);
                                    write.append(cc+dto.getPpljsl()+tab);
                                }else{
                                    write.append(cc+dto.getPpljsl()+tab);
                                }
                            }
                            if(i == 17){
                                if(j == 0){
                                    write.append(cc+"品牌标准件数量"+tab);
                                }
                                if("1".equals(dto.getDataType()) || "2".equals(dto.getDataType()) || "6".equals(dto.getDataType())){
                                    write.append(cc+" "+tab);
                                    write.append(cc+dto.getPpbzjsl()+tab);
                                }else{
                                    write.append(cc+dto.getPpbzjsl()+tab);
                                }
                            }
                            if(i == 18){
                                if(j == 0){
                                    write.append(cc+"品牌非标准件数量"+tab);
                                }
                                if("1".equals(dto.getDataType()) || "2".equals(dto.getDataType()) || "6".equals(dto.getDataType())){
                                    write.append(cc+" "+tab);
                                    write.append(cc+dto.getPpfbzj()+tab);
                                }else{
                                    write.append(cc+dto.getPpfbzj()+tab);
                                }
                            }
                            if(i == 19){
                                if(j == 0){
                                    write.append(cc+"品牌标识表数量"+tab);
                                }
                                if("1".equals(dto.getDataType())){
                                    write.append(cc+" "+tab);
                                    write.append(cc+" "+tab);
                                }else if("3".equals(dto.getDataType()) || "4".equals(dto.getDataType()) || "5".equals(dto.getDataType())){
                                    write.append(cc+dto.getPpbsbsl()+tab);
                                }else{
                                    write.append(cc+dto.getZppbsbsl()+tab);
                                    write.append(cc+dto.getPpbsbsl()+tab);
                                }
                            }
                            if(i == 20){
                                if(j == 0){
                                    write.append(cc+"车系标识表数量"+tab);
                                }
                                if("1".equals(dto.getDataType())){
                                    write.append(cc+""+tab);
                                    write.append(cc+dto.getCxxbsbsl()+tab);
                                }else if("3".equals(dto.getDataType()) || "4".equals(dto.getDataType()) || "5".equals(dto.getDataType())){
                                    write.append(cc+dto.getCxxbsbsl()+tab);
                                }else{
                                    write.append(cc+dto.getZcxxbsbsl()+tab);
                                    write.append(cc+dto.getCxxbsbsl()+tab);
                                }
                            }
                            if(i == 21){
                                if(j == 0){
                                    write.append(cc+"车组标识表数量"+tab);
                                }
                                if("1".equals(dto.getDataType())){
                                    write.append(cc+" "+tab);
                                    write.append(cc+dto.getCzbsbsl()+tab);
                                }else if("3".equals(dto.getDataType()) || "4".equals(dto.getDataType()) || "5".equals(dto.getDataType())){
                                    write.append(cc+dto.getCzbsbsl()+tab);
                                }else{
                                    write.append(cc+dto.getZczbsbsl()+tab);
                                    write.append(cc+dto.getCzbsbsl()+tab);
                                }
                            }
                            if(i == 22){
                                if(j == 0){
                                    write.append(cc+"车型标识表数量"+tab);
                                }
                                if("1".equals(dto.getDataType())){
                                    write.append(cc+" "+tab);
                                    write.append(cc+dto.getCxbsbsl()+tab);
                                }else if("3".equals(dto.getDataType()) || "4".equals(dto.getDataType()) || "5".equals(dto.getDataType())){
                                    write.append(cc+dto.getCxbsbsl()+tab);
                                }else{
                                    write.append(cc+dto.getZcxbsbsl()+tab);
                                    write.append(cc+dto.getCxbsbsl()+tab);
                                }
                            }
                        }
                        String data = write.substring(0,write.length()-2)+hh;
                        Buff.write(data.getBytes("GB2312"));
                    }

                }
            }else{
                jsonResult.setStatus("404");
                jsonResult.setMessage("数据为空");
                return jsonResult;
            }
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            try {
                Buff.flush();
                Buff.close();
                outSTr.flush();
                outSTr.close();
            }catch (IOException e){
                e.printStackTrace();
            }
        }
        jsonResult.setResult(fileName);
        return jsonResult;
    }

    public List<DataStatisticsDto> handleData(List<DataStatisticsDto> data){
        List<String> list = new ArrayList<>();
        list.add("1");
        list.add("2");
        list.add("3");
        list.add("4");
        list.add("5");
        list.add("6");

        data.forEach((DataStatisticsDto dto) -> {
            list.remove(dto.getDataType());
        });
        DataStatisticsDto dto = null;
        for (String str : list) {
            dto = new DataStatisticsDto();
            dto.setDataType(str);
            data.add(dto);
        }
        Collections.sort(data,new Comparator<DataStatisticsDto>(){

            @Override
            public int compare(DataStatisticsDto o1, DataStatisticsDto o2) {
                int dataType1 = Integer.parseInt(o1.getDataType());
                int dataType2 = Integer.parseInt(o2.getDataType());
                if(dataType1 > dataType2){
                    return 1;
                }
                if(dataType1 == dataType2){
                    return 0;
                }
                return -1;
            }
        });
        return  data;
    }

    /**
     * 文件下载
     * @param response
     * @param fileName
     * @throws FileNotFoundException
     */
    @RequestMapping("download")
    public void download(HttpServletResponse response,String fileName) throws FileNotFoundException{

        //下载本地区域
        String ss=fileName.replaceAll("/","");
        ss=ss.replaceAll("\\/", "");
        String fileNames=ss.replace("onL\\(\\)","");
        BufferedInputStream bis = null;
        BufferedOutputStream bos = null;
        OutputStream fos = null;
        InputStream fis = null;
        String uploadFilepath = statisticsUrl + File.separator + fileName;
        File downFile=new File(uploadFilepath);

        try {
            response.setContentType("text/csv");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;fileName="
                    + URLEncoder.encode(fileNames,"utf-8") );
            //得到文件全路径及文件
            fis = new FileInputStream(downFile);

            bis = new BufferedInputStream(fis);
            fos = response.getOutputStream();
            bos = new BufferedOutputStream(fos);

            int bytesRead = 0;
            byte[] buffer = new byte[fis.available()];
            while ((bytesRead = bis.read(buffer)) != -1) {
                //将文件发送到客户端
                bos.write(buffer, 0, bytesRead);
            }
        }
        catch (Exception e) {
            response.reset();
            e.printStackTrace();
        }
        finally {
            try {

                bos.flush();
                if (fos != null) {
                    fos.close();
                }
                if (bos != null) {
                    bos.close();
                }
                if (fis != null) {
                    fis.close();
                }
                if (bis != null) {
                    bis.close();
                }
            }
            catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @ResponseBody
    @RequestMapping("statisticsByGroup")
    public JsonResult statisticsByGroup(String groupId,String date,String groupName,String brandCode,String seriesId,String brandId,String mainCodeList){

        JsonResult jsonResult = new JsonResult();
        List<Map<String,Object>> isStatstics = receiveGroupDataService.getStatisticsData(date,"0",groupId);
        if(isStatstics == null || isStatstics.isEmpty()){
            jsonResult.setStatus("4004");
            jsonResult.setMessage("该车组已经统计过数据");
            return jsonResult;
        }
        List<Object> list = JSONArray.parseArray(mainCodeList);
        List<Map<String,Object>> listMap = new ArrayList<>();
        for(Object obj:list){
            listMap.add((Map<String, Object>) obj);
        }
        //统计转换平台数据
        dataStatisticsService.transformationData(brandCode,groupId,seriesId,brandId,listMap,date);

        //获取加工平台数据
        String jgData = HttpClientUtil.doPost(productUrl+"?groupId="+groupId,"");
        dataStatisticsService.insertDataStatisticsBatch(jgData,"1",date);

        //获取facade数据
        String data = HttpClientUtil.doPost(facadeTokenUrl,"","");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("vehGroupIds",new String[]{groupId});
        jsonObject.put("url",localUrl);
        jsonObject.put("date",date);
        if(!StringUtils.isBlank(data)){
            JSONObject json = JSONObject.parseObject(data);
            HttpClientUtil.doPost(facadeStatisticsUrl,jsonObject.toJSONString(),json.getString("access_token"));
        }
        return jsonResult;
    }

    @ResponseBody
    @RequestMapping("dataStatistics")
    public String dataStatistics(){
        /*String[] str = new String[]{
                "SKA1_I0000000000000000200000000000229_4028b2b65b7f5feb015b8410629d0770_4028d06d66a56b88016705e1702c001b",
                "KDA1_I0000000000000000200000000000300_4028b2b65282a8e7015285d1c0cc001d_4028d06d6759c9dd016771c578a70794",
                "ADA0_402880ef0cd29b61010cd7e6dade004d_40288088312d40190131468d89aa0edb_4028d06d6759c9dd0167781ecdca14cd",
                "FTA1_402880ef0cd29b61010d0b1574db021b_4028d06d691e81e00169b28824b44985_4028d06d691e81e00169b28970114986",
                "BYA0_5ea38e3a0dc1c163010dceeed48555b4_4028b2b6504fed3801505fd73ff90b85_4028d06d6545b19a01658dbcdcce762c",
                "CCA2_4028b2b6495650c701495a65cb380909_4028d06d6545b19a0165c2be52ed1a83_4028d06d6545b19a0165c2beeb831a84"
        };
        String brandCode;
        String brandId;
        String seriesId;
        String groupId;
        String date = "2018-05-29";
        int i = 0;
        String gooidf = "";
        StringBuffer groupIds = new StringBuffer(10);
        for (String str1 : str) {
            i++;
            System.out.println("-----------------这是第"+i+"条数据--------------");
            String[] strs = str1.split("_");
            brandCode = strs[0];
            brandId = strs[1];
            seriesId = strs[2];
            groupId = strs[3];

            //统计转换平台数据
            dataStatisticsService.transformationData(brandCode,groupId,seriesId,brandId,null,date);

            //获取加工平台数据
            //String jgData = HttpClientUtil.doPost(productUrl+"?groupId="+groupId,"");
            //dataStatisticsService.insertDataStatisticsBatch(jgData,"1",date);
            groupIds.append(groupId+",");
            if(i%10 == 0){
                System.out.println("--------------ggroupIds前"+groupIds.length()+"--------------");
                gooidf = groupIds.toString().substring(0,groupIds.length()-1);
                test(gooidf,date);
                gooidf = "";
                groupIds = new StringBuffer(10);
                System.out.println("--------------ggroupIds后"+groupIds.length()+"--------------");
            }
        }
        if(groupIds.length() > 0){
            System.out.println("--------------ggroupIds前"+groupIds.length()+"--------------");
            gooidf = groupIds.toString().substring(0,groupIds.length()-1);
            test(gooidf,date);
            groupIds = new StringBuffer(10);
            System.out.println("--------------ggroupIds后"+groupIds.length()+"--------------");
        }*/
        return "SUCCESS";
    }

    public void test(String groupIds,String date){
        //获取facade数据
        String data = HttpClientUtil.doPost(facadeTokenUrl,"","");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("vehGroupIds",groupIds.split(","));
        jsonObject.put("url",localUrl);
        jsonObject.put("date",date);
        if(!StringUtils.isBlank(data)){
            JSONObject json = JSONObject.parseObject(data);
            HttpClientUtil.doPost(facadeStatisticsUrl,jsonObject.toJSONString(),json.getString("access_token"));
        }
    }

    @ResponseBody
    @RequestMapping("tj")
    public String tj(String date){
        dataStatisticsService.getStisticsDataContrast(date,jgPath);
        return "SUCCESS";
    }
}

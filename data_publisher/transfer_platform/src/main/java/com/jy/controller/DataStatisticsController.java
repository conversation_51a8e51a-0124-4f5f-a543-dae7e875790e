package com.jy.controller;

import com.alibaba.fastjson.JSONObject;
import com.jy.bean.result.JsonResult;
import com.jy.bean.result.ResultStatus;
import com.jy.service.DataStatisticsService;
import com.jy.service.ReceiveGroupDataService;
import com.jy.util.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 * @date 2019/4/1.
 */
@Controller
@RequestMapping("statistics")
public class DataStatisticsController {

    private final static Log log = LogFactory.getLog(DataStatisticsController.class);

    @Autowired
    private DataStatisticsService dataStatisticsService;

    @Autowired
    private ReceiveGroupDataService receiveGroupDataService;

    @ResponseBody
    @RequestMapping(value = "dataStatistics",method = RequestMethod.POST)
    public JsonResult dataStatistics(@RequestBody String json){
        log.info("------------------统计数据返回:"+json+"---------------------");
        JsonResult jsonResult = new JsonResult();
        JSONObject jsonObject = JSONObject.parseObject(json);
        String dataJson = jsonObject.getString("dataJson");
        if(StringUtils.isBlank(dataJson)){
            jsonResult.setResultStatus(ResultStatus.BAD_PARAM);
            return  jsonResult;
        }
        dataStatisticsService.insertDataStatisticsBatch(dataJson,jsonObject.getString("dataType"),jsonObject.get("date") == null ? "" : jsonObject.getString("date"));
        return jsonResult;
    }
}

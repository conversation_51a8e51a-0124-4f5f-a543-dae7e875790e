package com.jy.controller;

import com.jy.bean.po.SysPowerPo;
import com.jy.bean.po.UserPo;
import com.jy.bean.result.JsonResult;
import com.jy.bean.result.ResultStatus;
import com.jy.exception.CommonException;
import com.jy.security.GlobalKeys;
import com.jy.service.PowerService;
import com.jy.util.MD5Util;
import com.jy.util.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.apache.shiro.authc.UnknownAccountException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.util.ThreadContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Random;

@Controller
public class LoginController {

    private Logger log = LoggerFactory.getLogger(LoginController.class);

    @Autowired
    private PowerService powerServiceImpl;

    @RequestMapping(value={"/index","/"})
    public String index(@RequestParam(value = "name", required = false, defaultValue = "Sam") String name, Model model) throws CommonException {
        model.addAttribute("name", name);
        return "index";
    }

    /**
     * 登录
     * @param loginName
     * @param password
     * @return
     */
    @RequestMapping(value="/login", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<String> login(HttpServletRequest request,
                                    @RequestParam(value="loginName",defaultValue="") String loginName,
                                    @RequestParam(value="password",defaultValue="") String password,
                                    @RequestParam(value="rememberMe",defaultValue="") Boolean rememberMe,
                                    @RequestParam(value="vcode",defaultValue="") String vcode) {
        JsonResult<String> jsonResult = new JsonResult<>();

        //验证码为空
        if(StringUtils.isEmpty(vcode)){
            jsonResult.setStatus("0");
            jsonResult.setMessage(GlobalKeys.SYN_SECURITY_LOGIN_CHECKCODE_EMPTY);
            return jsonResult;
        }

        Session session = SecurityUtils.getSubject().getSession();
        //转化成小写字母
        vcode = vcode.toLowerCase();
        String v = (String) session.getAttribute("_code");
        //还可以读取一次后把验证码清空，这样每次登录都必须获取验证码
        if(!vcode.equals(v)){
            jsonResult.setStatus("0");
            jsonResult.setMessage(GlobalKeys.SYN_SECURITY_LOGIN_CHECKCODE_ERROR);
            return jsonResult;
        }
        /**登录部分*/
        Subject subject = SecurityUtils.getSubject();
        UserPo nqUser = (UserPo) request.getSession().getAttribute("currentUser");
        if (StringUtils.notEmpty(nqUser)) {
            //判断认证是否通过
            if (subject.isAuthenticated()) {
                if (!subject.getPrincipal().toString().equals(loginName.trim())) {
                    subject.logout();
                }
            }
        }
        UsernamePasswordToken token = new UsernamePasswordToken(loginName.trim().toLowerCase(), MD5Util.getMD5StringWithSalt(password.trim()),rememberMe);
//        UsernamePasswordToken token = new UsernamePasswordToken(loginName.trim(), MD5.encrypt(password + loginName),rememberMe);

        try {
            subject.login(token);
            if (subject.isAuthenticated()) {
                return jsonResult;
            } else {
                subject.logout();
            }
        } catch (UnknownAccountException e) {
            request.setAttribute(GlobalKeys.SYN_SECURITY_LOGIN_MSG, GlobalKeys.SYN_SECURITY_LOGIN_NAME_ERROR);
            jsonResult.setStatus("0");
            jsonResult.setMessage(GlobalKeys.SYN_SECURITY_LOGIN_NAME_ERROR);
        } catch (IncorrectCredentialsException e) {
            request.setAttribute(GlobalKeys.SYN_SECURITY_LOGIN_MSG, GlobalKeys.SYN_SECURITY_LOGIN_PASSWORD_ERROR);
            jsonResult.setStatus("0");
            jsonResult.setMessage(GlobalKeys.SYN_SECURITY_LOGIN_PASSWORD_ERROR);
        } catch (AuthenticationException e) {
            request.setAttribute(GlobalKeys.SYN_SECURITY_LOGIN_MSG, GlobalKeys.SYN_SECURITY_LOGIN_NAME_OR_PASSWORD_ERROR);
            jsonResult.setStatus("0");
            jsonResult.setMessage(GlobalKeys.SYN_SECURITY_LOGIN_NAME_OR_PASSWORD_ERROR);
        }
        return jsonResult;
    }

    /**
	 * 查找用户对应的菜单权限
	 * @param session
	 * @return
	 */
    @RequestMapping(value="/showMenu")
    @ResponseBody
	public JsonResult<List<SysPowerPo>> showMenu(HttpSession session) throws CommonException {
        UserPo user = (UserPo) SecurityUtils.getSubject().getSession().getAttribute("currentUser");
    	JsonResult<List<SysPowerPo>> jsonResult = new JsonResult<List<SysPowerPo>>();
        try {
            List<SysPowerPo> list = powerServiceImpl.showMenu(user.getId());
            if(null != list && list.size() > 0){
                jsonResult.setResult(list);
            } else {
                jsonResult.setResultStatus(ResultStatus.NO_DATA);
            }
            return jsonResult;
        } catch (Exception e) {
            e.printStackTrace();
            jsonResult.setResultStatus(ResultStatus.INTERNAL_SERVER_ERROR);
        }
    	return jsonResult;
	}


    @RequestMapping(value="/loginOut" )
	public String loginOut(HttpServletRequest request, HttpSession session) throws CommonException {
		Subject subject = SecurityUtils.getSubject();
		if (subject.isAuthenticated()) {
			subject.logout();
           /* HttpSession getSession = request.getSession();
            Enumeration em = getSession.getAttributeNames();
            while(em.hasMoreElements()){
                System.out.println(em.nextElement().toString()+"       ==============");
                getSession.removeAttribute(em.nextElement().toString());
            }*/
		}
		return "redirect:index";
	}


    /**
	 * 获取验证码（Gif版本）
	 * @param response
	 */
	@RequestMapping(value="getGifCode",method= RequestMethod.GET)
	public void getGifCode(HttpServletResponse response, HttpServletRequest request) throws CommonException {
		try {
			final int WIDTH = 130;
			final int HEIGHT = 40;

			response.setContentType("image/jpeg");

			// 防止浏览器缓冲
			response.setHeader("Pragma", "No-cache");
			response.setHeader("Cache-Control", "no-cache");
			response.setDateHeader("Expires", 0);
			HttpSession session = request.getSession();
			BufferedImage image = new BufferedImage(WIDTH, HEIGHT, BufferedImage.TYPE_INT_RGB);
			Graphics g = image.getGraphics();

			char[] rands = getCode(4);


			drawBackground(g);
			drawRands(g, rands);
			g.dispose();
			ServletOutputStream out = response.getOutputStream();
			ByteArrayOutputStream bos = new ByteArrayOutputStream();
			ImageIO.write(image, "PNG", bos);
			byte[] buf = bos.toByteArray();
			response.setContentLength(buf.length);
			out.write(buf);
			bos.close();
			out.close();
			session.setAttribute("_code", new String(rands).toLowerCase());
		} catch (IOException e) {
			e.printStackTrace();
			System.out.println("验证码错误");
		}
	}


	 /**
     * 产生随机数
     * @return
     */
    private char[] getCode(int length) {
    	final String chars = "23456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz";
        char[] rands = new char[length];
        for (int i = 0; i < length; i++) {
            int rand = (int) (Math.random() * chars.length());
            rands[i] = chars.charAt(rand);
        }
        return rands;
    }

    /**
     * 绘制背景
     * @param g
     */
    private void drawBackground(Graphics g) {

    	final int WIDTH = 130;
	    final int HEIGHT = 40;

        g.setColor(new Color(0xDCeeee));
        g.fillRect(0, 0, WIDTH, HEIGHT);
        Random random=new Random();
        int len=0;
        while(len<=5){
            len=random.nextInt(15);
        }
        for (int i = 0; i < len; i++) {
            int x = random.nextInt(WIDTH);
            int y = random.nextInt(HEIGHT);
            int red = 255-random.nextInt(200);
            int green = 255-random.nextInt(200);
            int blue = 255-random.nextInt(200);
            g.setColor(new Color(red, green, blue));
//          g.drawLine(x, y, random.nextInt(WIDTH)-x, random.nextInt(HEIGHT)-y);
            g.drawOval(x, y, 2, 2);
        }
    }
    /**
     * 绘制验证码
     * @param g
     * @param rands
     */
    private void drawRands(Graphics g, char[] rands) {
        Random random=new Random();

        g.setFont(new Font("Verdana", Font.ITALIC | Font.BOLD, 28));
        for(int i=0;i<rands.length;i++){
            int red = random.nextInt(255);
            int green = random.nextInt(255);
            int blue = random.nextInt(255);
            g.setColor(new Color(red, green, blue));
            g.drawString("" + rands[i], i*30, 30);
        }
    }



    /**
     * 记住我之后的登录
     * @return
     */
    @RequestMapping(value="/loginByToken", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<String> loginByToken(HttpServletRequest request,
                                           @RequestParam(value="token",defaultValue="") String token) throws CommonException {
            JsonResult<String> jsonResult = new JsonResult<String>();

            if(!StringUtils.isEmpty(token) && !"null".equals(token)){
            	String loginName = null;
                String password = null;
                Boolean rememberMe = false;
                try {
    	            String myToken = new String(new sun.misc.BASE64Decoder().decodeBuffer(token));

    	            String[] split = myToken.split("~~");
    	            if(split.length == 3){
    	            	for (int i = 0; i < split.length; i++) {
    	            		loginName = split[0];
    	            		password = split[1];
    	            		rememberMe = Boolean.valueOf(split[2]);
    					}
    	            }

    	            /**登录部分*/

    	            Subject subject = SecurityUtils.getSubject();
    	            UserPo nqUser = (UserPo) request.getSession().getAttribute("currentUser");
    	            if (StringUtils.notEmpty(nqUser)) {
    	                //判断认证是否通过
    	                if (subject.isAuthenticated()) {
    	                    if (!subject.getPrincipal().toString().equals(loginName.trim())) {
    	                        subject.logout();
    	                    }
    	                }
    	            }
    	            UsernamePasswordToken sysToken = new UsernamePasswordToken(loginName.trim().toLowerCase(), password,rememberMe);

                    subject.login(sysToken);
                    //验证是否登录成功
                    if (subject.isAuthenticated()) {
                    	log.info("用户：[" + loginName.trim() + "]进行登录验证通过");
                        ThreadContext.bind(SecurityUtils.getSubject());
                        jsonResult.setStatus("1");
                		jsonResult.setMessage("登录成功");
                        return jsonResult;
                    } else {
                        sysToken.clear();
                        request.setAttribute(GlobalKeys.SYN_SECURITY_LOGIN_MSG, GlobalKeys.SYN_SECURITY_LOGIN_NAME_OR_PASSWORD_ERROR);
                        jsonResult.setStatus("0");
                		jsonResult.setMessage("账号或密码错误");
                    }
                } catch (UnknownAccountException e) {
                    request.setAttribute(GlobalKeys.SYN_SECURITY_LOGIN_MSG, GlobalKeys.SYN_SECURITY_LOGIN_NAME_ERROR);
                    jsonResult.setStatus("0");
            		jsonResult.setMessage(GlobalKeys.SYN_SECURITY_LOGIN_NAME_ERROR);
                } catch (IncorrectCredentialsException e) {
                    request.setAttribute(GlobalKeys.SYN_SECURITY_LOGIN_MSG, GlobalKeys.SYN_SECURITY_LOGIN_PASSWORD_ERROR);
                    jsonResult.setStatus("0");
            		jsonResult.setMessage(GlobalKeys.SYN_SECURITY_LOGIN_PASSWORD_ERROR);
                } catch (AuthenticationException e) {
                    request.setAttribute(GlobalKeys.SYN_SECURITY_LOGIN_MSG, GlobalKeys.SYN_SECURITY_LOGIN_NAME_OR_PASSWORD_ERROR);
                    jsonResult.setStatus("0");
                    jsonResult.setMessage(GlobalKeys.SYN_SECURITY_LOGIN_NAME_OR_PASSWORD_ERROR);
                } catch (IOException e) {
                	request.setAttribute(GlobalKeys.SYN_SECURITY_LOGIN_ERROR, GlobalKeys.SYN_SECURITY_LOGIN_ERROR);
                    jsonResult.setStatus("0");
                    jsonResult.setMessage(GlobalKeys.SYN_SECURITY_LOGIN_ERROR);
    			}
            }
        return jsonResult;
    }
}
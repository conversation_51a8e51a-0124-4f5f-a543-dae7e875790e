package com.jy.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jy.ann.MethodMonitor;
import com.jy.bean.common.*;
import com.jy.bean.dto.BaseDataDTOs;
import com.jy.bean.po.*;
import com.jy.bean.result.JsonResult;
import com.jy.bean.result.ResultStatus;
import com.jy.exception.CompareException;
import com.jy.service.*;
import com.jy.transform.PartDataTransform;
import com.jy.util.*;
import com.jy.util.rabbitmq.DataTraceUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.util.*;

/**
 * 主批次接收数据接口
 */
@RestController
@RequestMapping("receiveBatchNos")
public class ReceiveBatchController {
    private static final Logger logger = LogManager.getLogger(ReceiveBatchController.class);


    @Value("${srcData.filePath}")
    private String filePath;
    @Value("${httpUtils.dataPublish.username}")
    private String clientCode;

    @Autowired
    private CommonService commonService;
    @Autowired
    private BatchDetailService batchDetailService;
    @Autowired
    private ReceiveBatchService receiveBatchService;
    @Autowired
    private PartDataTransform partDataTransform;
    @Autowired
    private ProcedureService procedureService;
    @Autowired
    private FacadeUtils facadeUtils;
    @Autowired
    private SyncDetailService syncDetailService;
    @Autowired
    RabbitTemplate rabbitTemplate;

    @RequestMapping(params={"vehicle"}, method = RequestMethod.POST)
    @MethodMonitor
    public JsonResult<String> receiveVeh(@RequestBody BaseDataDTOs baseDataDTOs) {
        String tableNameNoSuffix = "m_" + baseDataDTOs.getTableName().substring(2).toLowerCase();
        baseDataDTOs.setTableName(tableNameNoSuffix);
        JsonResult<String> jsonResult = this.fitData(baseDataDTOs, PublishTypeEnum.CL_VEH.getCode());
        return jsonResult;
    }


    @RequestMapping(params={"amCl"}, method = RequestMethod.POST)
    @MethodMonitor
    public JsonResult<String> receiveAmCl(@RequestBody BaseDataDTOs baseDataDTOs) {
        String tableNameNoSuffix = "m_" + baseDataDTOs.getTableName().substring(2).toLowerCase();
        baseDataDTOs.setTableName(tableNameNoSuffix);
        JsonResult<String> jsonResult = this.fitData(baseDataDTOs, PublishTypeEnum.AM_CL.getCode());
        return jsonResult;
    }

    @RequestMapping(params={"part"}, method = RequestMethod.POST)
    @MethodMonitor
    public JsonResult<String> receivePart(@RequestBody BaseDataDTOs baseDataDTOs) {
        String tableNameNoSuffix = "m_" + baseDataDTOs.getTableName().substring(2).toLowerCase();
        baseDataDTOs.setTableName(tableNameNoSuffix);
        JsonResult<String> jsonResult = this.fitData(baseDataDTOs, PublishTypeEnum.PART.getCode());
        return jsonResult;
    }

    @RequestMapping(params={"replacer"}, method = RequestMethod.POST)
    @MethodMonitor
    public JsonResult<String> receiveReplace(@RequestBody BaseDataDTOs baseDataDTOs) {
        String tableNameNoSuffix = "m_" + baseDataDTOs.getTableName().substring(2).toLowerCase();
        baseDataDTOs.setTableName(tableNameNoSuffix);
        JsonResult<String> jsonResult = this.fitData(baseDataDTOs, PublishTypeEnum.REPLACER.getCode());
        return jsonResult;
    }

    @RequestMapping(params={"stdPart"}, method = RequestMethod.POST)
    @MethodMonitor
    public JsonResult<String> receiveStdPart(@RequestBody BaseDataDTOs baseDataDTOs) {
        String tableNameNoSuffix = "m_" + baseDataDTOs.getTableName().substring(2).toLowerCase();
        baseDataDTOs.setTableName(tableNameNoSuffix);
        JsonResult<String> jsonResult = this.fitData(baseDataDTOs, PublishTypeEnum.STD_PART.getCode());
        return jsonResult;
    }

    @RequestMapping(params={"oriRepair"}, method = RequestMethod.POST)
    @MethodMonitor
    public JsonResult<String> receiveOriRepair(@RequestBody BaseDataDTOs baseDataDTOs) {
        String tableNameNoSuffix = baseDataDTOs.getTableName().substring(2).toLowerCase();
        baseDataDTOs.setTableName(tableNameNoSuffix);
        JsonResult<String> jsonResult = this.fitData(baseDataDTOs, PublishTypeEnum.ORI_REPAIR.getCode());
        return jsonResult;
    }

    @RequestMapping(params={"state"}, method = RequestMethod.GET)
    @MethodMonitor
    public JsonResult<String> updateContinueFlag(String state) {
        JsonResult<String> jsonResult = new JsonResult<String>();
        if("start".equals(state)){
            Constant.IS_CONTINUE = true;
        }else if("stop".equals(state)){
            Constant.IS_CONTINUE = false;
        }
        return jsonResult;
    }

    /**
     * 返回定时内可接收的车组数量
     * @return
     * @throws Exception
     */
    @RequestMapping(params={"groupNum"}, method = RequestMethod.GET)
    @MethodMonitor
    public JsonResult<Integer> getPartNum() throws Exception {
        JsonResult<Integer> jsonResult = new JsonResult<>();
        List<ReceiveBatch> receiveBatch = receiveBatchService.listByStatus(BatchNoStatus.SUCCESS_RECEIVE.getStatus());
        int length = receiveBatch.size() >= Constant.RECEIVE_PART_LIMIT ? 0 :(Constant.RECEIVE_PART_LIMIT - receiveBatch.size());
        jsonResult.setResult(length);
        return jsonResult;
    }

    @RequestMapping(params={"updatePartLimit"}, method = RequestMethod.POST)
    @MethodMonitor
    public JsonResult<String> updatePartLimit(Integer partLimit) {
        if(EmptyUtils.isNotEmpty(partLimit)){
            Constant.RECEIVE_PART_LIMIT = partLimit;
        }
        JsonResult<String> jsonResult = new JsonResult<>();
        return jsonResult;
    }


    @RequestMapping(params={"mainBatchNo"}, method = RequestMethod.GET)
    @MethodMonitor
    public JsonResult<String> mainBatchNo(String mainBatchNo) throws Exception {
        JsonResult<String> jsonResult = new JsonResult<String>();
        ReceiveBatch receiveBatch = receiveBatchService.listByMainBatchNo(mainBatchNo);
        List<SendDetail> sendDetails = partDataTransform.fitPushBatchNo(receiveBatch);
        partDataTransform.push(receiveBatch, sendDetails);
        return jsonResult;
    }

    @RequestMapping(value = "/transferOrder", method = RequestMethod.GET)
    @MethodMonitor
    public JsonResult<String> transferOrder(@RequestParam String mainBatchNo) throws Exception {
        JsonResult<String> jsonResult = new JsonResult<String>();
        ReceiveBatch receiveBatch = receiveBatchService.listByMainBatchNo(mainBatchNo);
        if(receiveBatch!=null){
            jsonResult.setResult(String.valueOf(receiveBatch.getTransferOrder()));
        }
        return jsonResult;
    }

    @RequestMapping(value = "/updateOrder", method = RequestMethod.POST)
    @MethodMonitor
    public JsonResult<String> updateOrder(@RequestBody ReceiveBatch receiveBatch) throws Exception {
        if(EmptyUtils.isEmpty(receiveBatch.getMainBatchNo())){
            throw new Exception("无需要紧急推送的主批次号");
        }
        String code = receiveBatch.getMainBatchNo().split("_")[0];
        Integer transferOrder = (EmptyUtils.isEmpty(receiveBatch.getTransferOrder()) || receiveBatch.getTransferOrder() <= TransferOrderEnum.orderOf(code) / 10) ?  (TransferOrderEnum.orderOf(code) / 10 + 1) : receiveBatch.getTransferOrder();
        receiveBatch.setTransferOrder(transferOrder);
        JsonResult<String> jsonResult = new JsonResult<String>();
        receiveBatchService.update(receiveBatch);
        return jsonResult;
    }


    @RequestMapping(params={"urgentOrder","mainBatchNo"}, method = RequestMethod.GET)
    @MethodMonitor
    public JsonResult<String> urgentOrder(ReceiveBatch receiveBatch) throws Exception {
        if(EmptyUtils.isEmpty(receiveBatch.getMainBatchNo())){
            throw new Exception("无需要紧急推送的主批次号");
        }
        String code = receiveBatch.getMainBatchNo().split("_")[0];
        Integer transferOrder = (EmptyUtils.isEmpty(receiveBatch.getTransferOrder()) || receiveBatch.getTransferOrder() <= TransferOrderEnum.orderOf(code) / 10) ?  (TransferOrderEnum.orderOf(code) / 10 + 1) : receiveBatch.getTransferOrder();
        receiveBatch.setTransferOrder(transferOrder);
        JsonResult<String> jsonResult = new JsonResult<String>();
        receiveBatchService.update(receiveBatch);
        return jsonResult;
    }

    @RequestMapping(params={"tableShortName", "clientCode"}, method = RequestMethod.POST)
    @MethodMonitor
    public JsonResult<String> syncData(String tableShortName, String ppbm, String certainKey, String certainValue, String clientCode, String url, String path) throws Exception {
        if(EmptyUtils.isEmpty(tableShortName) || (EmptyUtils.isEmpty(certainKey) && EmptyUtils.isEmpty(certainValue))){
            throw new Exception("选择同步的信息不完整");
        }
        String tableName = tableShortName;
        if(EmptyUtils.isNotEmpty(ppbm)){
            tableName = tableShortName + "_" + ppbm;
        }
        List<String> certains = new ArrayList<>();
        if(EmptyUtils.isNotEmpty(certainValue)){
            certains.add(certainValue);
        } else {
            String fTableName = Constant.F_DB_SUFFIX + tableName;
            List<String> certainIds = commonService.listCertain(fTableName, certainKey);
            certains.addAll(certainIds);
        }
        List<String> certainIdTrails = syncDetailService.listByTableName(tableName);
        for(String certainId : certains){
            if(!certainIdTrails.contains(certainId)){
              /*  try{
                    Thread.currentThread().sleep(10000);
                    logger.info("休息10s~~~~~~~~~~~~~~~~~~~~~~~~~~~~");
                }catch(InterruptedException ie){
                    ie.printStackTrace();
                }*/
                //建立主批次号
                BatchUtils batchUtils = new BatchUtils();
                String mainBatchNo = batchUtils.nextMainBatchNo(PublishTypeEnum.SYNCDATA.getCode());
                ReceiveBatch receiveBatch = new ReceiveBatch(mainBatchNo, certainId, clientCode);
                receiveBatchService.save(receiveBatch);
                SyncDetail syncDetail = new SyncDetail(certainId, tableName, ppbm, mainBatchNo);
                syncDetailService.save(syncDetail);
                //数据写入c库
                Map<String,Object> result = procedureService.compareSyncProcedure(mainBatchNo, tableShortName, ppbm, certainId);
                if(!ResultStatus.SUCCESS.getStatus().equals(result.get("status"))){
                    throw new CompareException(tableName + ":" + result.get("message").toString());
                }
                //组装c库数据，生成小批次号
                List<SendDetail> sendDetails = partDataTransform.fitPushSendDetail(mainBatchNo, tableShortName, ppbm, 1);

                //删除指定客户端数据
                JSONObject json = this.send(url, path, certainId, mainBatchNo);
                if(ResultStatus.SUCCESS.getStatus().equals(json.getString("status"))){
                    //推送数据
                    partDataTransform.push(receiveBatch, sendDetails, clientCode);
                }
                syncDetail.setStatus(json.getString("status"));
                syncDetailService.update(syncDetail);
            }
        }
        JsonResult<String> jsonResult = new JsonResult<String>();
        return jsonResult;
    }

    private JSONObject send(String url, String path, String certainId, String mainBatchNo){
        Map<String, String> querys = new HashMap<String, String>();
        querys.put("clVehicleId", certainId);
        querys.put("stdItemType", "part");
        JSONObject json = null;
        int i = 0;
        while(i < 3){
            try {
                json = facadeUtils.doDelete(url, path, querys);
                logger.info("数据同步推送结果:" + json);
            } catch (Exception e) {
                logger.error("数据同步推送结果失败:" + e.getMessage() + ":mainBatchNo为:" + mainBatchNo);
            }
            if(EmptyUtils.isNotEmpty(json) && json.getString("status").equals(ResultStatus.SUCCESS.getStatus())){
                break;
            }
            i++;
        }
        return json;
    }


    private JsonResult fitData(BaseDataDTOs baseDataDTOs, String type){
        JsonResult<String> jsonResult = new JsonResult<String>();
        baseDataDTOs.setTotal(baseDataDTOs.getData().size());
        try {
            //写入文件
            String localFilePath = BatchUtils.getDateByBatchNo(baseDataDTOs.getMainBatchNo())+ File.separator + baseDataDTOs.getMainBatchNo() + "_" + baseDataDTOs.getBatchNo() + "_" + baseDataDTOs.getTableName() + ".json";
            FileUtils.writeFile(JSONObject.toJSONString(baseDataDTOs), filePath + localFilePath);

            //存储主批次接收表详情
           // for(TransferTableMp transferTableMp : list){
                BatchDetail batchDetail = new BatchDetail(baseDataDTOs, localFilePath);
                batchDetailService.save(batchDetail);
         //   }
            //主批次涉及文件接收完成，则将主批次信息入库，进入待处理状态
            if(Constant.YES_STRING.equals(baseDataDTOs.getEndFlag())){
                ReceiveBatch receiveBatch = new ReceiveBatch(baseDataDTOs, type);
                receiveBatchService.save(receiveBatch);
                this.trail(baseDataDTOs, type);
            }
            DataTraceUtils.sendTrace((JSONObject) JSON.toJSON(baseDataDTOs), DataTraceMenu.SRC_RECEIVE_DESC.getName(), ResultStatus.SUCCESS.getStatus(), ResultStatus.SUCCESS.getMessage());
        } catch (Exception e) {
            String message = ToolUtils.getExceptionMsg(e);
            jsonResult.setStatus(ResultStatus.INTERNAL_SERVER_ERROR.getStatus());
            jsonResult.setMessage(message);
            DataTraceUtils.sendTrace((JSONObject) JSON.toJSON(baseDataDTOs), DataTraceMenu.SRC_RECEIVE_DESC.getName(), ResultStatus.INTERNAL_SERVER_ERROR.getStatus(), message);
            logger.error("产品数据转换平台接收数据文件失败：mainBatchNo：{}, tableName: {}, message: {}", baseDataDTOs.getMainBatchNo(), baseDataDTOs.getTableName(), message);
        }
        return jsonResult;
    }

    private void trail(BaseDataDTOs baseDataDTOs, String type){
        JSONObject obj = new JSONObject();
        obj.put("mainBatchNo", baseDataDTOs.getMainBatchNo());
        if(PublishTypeEnum.PART.getCode().equals(type)){
            obj.put("dataRemark", baseDataDTOs.getGroupCode() + "(" + baseDataDTOs.getGroupName() + ")");
        } else  if(PublishTypeEnum.REPLACER.getCode().equals(type)){
            obj.put("dataRemark", baseDataDTOs.getBrandCode() + "(" + baseDataDTOs.getBrandName() + ")");
        } else if(PublishTypeEnum.ORI_REPAIR.getCode().equals(type)){
            obj.put("dataRemark", baseDataDTOs.getClVehicleCode() + "(" + baseDataDTOs.getClVehicleName() + ")");
        }
        obj.put("clientCode", clientCode);
        rabbitTemplate.convertAndSend("tmp-mainBatch-queue", obj.toJSONString());
    }

}

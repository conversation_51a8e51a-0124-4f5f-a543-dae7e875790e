package com.jy.controller;

import com.jy.bean.result.JsonResult;
import com.jy.service.ReceiveGroupDataService;
import com.jy.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;

/**
 * 转换数据后台管理
 * @date 20190311
 * <AUTHOR>
 */
@Controller
@RequestMapping("admin/receiveGroupData")
public class ReceiveGroupDataWebController {

    @Autowired
    private ReceiveGroupDataService receiveGroupDataService;

    @ResponseBody
    @RequestMapping("getList")
    public JsonResult getList(String mainVersionCode,String versionCode,int page,String state,String groupCode){
        JsonResult jsonResult = new JsonResult();
        jsonResult.setResult(receiveGroupDataService.getReceiveListByPage(mainVersionCode,versionCode,page,state,groupCode));
        return jsonResult;
    }

    /**
     * 重新获取数据,进行转换和对比
     * @param mainVersionCode
     * @return
     */
    @ResponseBody
    @RequestMapping("againGetData")
    public JsonResult againGetData(String mainVersionCode,String groupCode,String receiveDate){

        JsonResult jsonResult = new JsonResult();
        Map<String,Object> resultMap =  receiveGroupDataService.judgeExistsGroupCodeByDate(mainVersionCode,receiveDate,groupCode);
        if(resultMap != null && !resultMap.isEmpty()){
            jsonResult.setStatus(resultMap.get("code").toString());
            jsonResult.setMessage(resultMap.get("msg").toString());
        }
        return jsonResult;
    }

    /**
     * 重新回写F库
     * @param dataType 数据类型
     * @param mainVersionCode 主批次号
     * @param versionCode 小批次号
     * @param groupId 车组id
     * @param brandId 品牌id
     * @return
     */
    @ResponseBody
    @RequestMapping("againWriteBack")
    public JsonResult againWriteBack(String dataType,String mainVersionCode,String versionCode,
                                     String groupId,String brandId,String brandCode,String groupCode){

        /*String certain = "";
        JsonResult jsonResult = new JsonResult();
        if("1".equals(dataType)){
            certain = groupId;
        }else if("3".equals(dataType) || "4".equals(dataType)){
            certain = brandId;
        }
        List<String> list = receiveGroupDataService.getSendTableHXErrDataByMainVersionCode(mainVersionCode);
        for (String str : list){
            if(!"success".equals(receiveGroupDataService.pUpdateData(mainVersionCode,str,certain))){
                jsonResult.setMessage("回写失败");
                jsonResult.setCode("1001");
            }
        }
        String suffix = null;
        try {
            suffix = receiveGroupDataService.groupIsExists(groupId);
            //发送数据
            receiveGroupDataService.dataAssembly(mainVersionCode,brandCode,groupCode,suffix,false,brandId,"0");
        } catch (Exception e) {
            e.printStackTrace();
            jsonResult.setMessage("数据发送失败");
            jsonResult.setCode("1001");
        }*/
        return  null;
    }

    /**
     * 根据主批次号重新发送数据
     * @param mainVersionCode
     * @return
     */
    @ResponseBody
    @RequestMapping("againSendByMainVersionCode")
    public JsonResult againSendByMainVersionCode(String mainVersionCode){

        JsonResult jsonResult = new JsonResult();
        if(!StringUtils.isEmpty(mainVersionCode)){
            if(!receiveGroupDataService.againSend(mainVersionCode)){
                jsonResult.setStatus("20000");
                jsonResult.setMessage("发送失败");
            }
        }
        return jsonResult;
    }

    @ResponseBody
    @RequestMapping("selDataByTableName")
    public JsonResult selDataByTableName(String mainVersionCode,String versionCode,String tableName,int page){
        JsonResult jsonResult = new JsonResult();
        if(StringUtils.isEmpty(tableName)){
            jsonResult.setStatus("400");
            jsonResult.setMessage("表名不能为空");
            return jsonResult;
        }
        jsonResult.setResult(receiveGroupDataService.selDataByTableName(tableName,mainVersionCode,versionCode,page));
        return jsonResult;
    }
}
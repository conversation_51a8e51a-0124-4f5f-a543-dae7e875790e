package com.jy.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jy.bean.dto.ReceiveGroupDataDto;
import com.jy.bean.result.JsonResult;
import com.jy.rabbitMq.MQClientMonitor;
import com.jy.rabbitMq.RabbitConfig;
import com.jy.service.*;
import com.jy.util.Dictionary;
import com.jy.util.*;
import com.jy.util.rabbitmq.DataTraceUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.RabbitListenerEndpointRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.File;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jy.util.HttpClientUtil.doPost;

/**
 * Created by jdd on 2018/12/5.
 */
@Controller
@RequestMapping("receiveGroupData")
public class ReceiveGroupDataController {

    private static final Log log = LogFactory.getLog(ReceiveGroupDataController.class);
    @Autowired
    private ReceiveGroupDataService receiveGroupDataService;
    @Autowired
    private GroupBrandAssocitedService groupBrandAssocitedService;
    @Autowired
    private DataTablesService dataTablesService;

    @Autowired
    private AmqpTemplate template;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private RabbitListenerEndpointRegistry registry;

    @Autowired
    private DataStatisticsService dataStatisticsService;

    @Autowired
    private DataPublishUtils dataPublishUtils;

    @Value("${gainData.accessoriesUrl}")
    private String url;

    @Value("${gainData.saveFilePath}")
    private String saveFilePath;

    @Value("${gainData.accessoriesResultUrl}")
    private String accessoriesResultUrl;

    @Value("${statistics.productUrl}")
    private String productUrl;

    @Value("${statistics.facadeTokenUrl}")
    private String facadeTokenUrl;

    @Value("${statistics.facadeStatisticsUrl}")
    private String facadeStatisticsUrl;

    @Value("${gainData.pageSize}")
    private int pageSize;

    @Value("${statistics.clientUrl}")
    private String clientUrl;

    @Autowired
    private GroupWeightService groupWeightService;

    @Autowired
    private MQClientMonitor mqClientMonitor;

    private int sendErrNum = 0;

    @ResponseBody
    @RequestMapping(value = "receive",method = RequestMethod.POST)
    public synchronized JsonResult receive(@RequestBody String dataJson){
        log.info("发送的参数值:"+dataJson);
        JsonResult jsonResult = new JsonResult();
        if(StringUtils.isEmpty(dataJson)){
            jsonResult.setStatus("0001");
            jsonResult.setMessage("参数为空");
            return jsonResult;
        }
        JSONArray jsonArray = null;
        try{
            JSONObject jsonObject = JSONObject.parseObject(dataJson);
            if(jsonObject.isEmpty()){
                jsonResult.setStatus("0001");
                jsonResult.setMessage("参数为空");
                return jsonResult;
            }
            jsonArray = jsonObject.getJSONArray("dataJson");
        }catch (Exception e){
            jsonResult.setStatus("0003");
            jsonResult.setMessage("json转换异常,异常信息:"+e.getMessage());
            e.printStackTrace();
            return jsonResult;
        }
        //数据类型(1:配件,2:属于字典)
        String type = "";
        //表名
        String tableName = "";
        ReceiveGroupDataDto receiveGroupDataDto = null;
        //主键id
        String id = "";
        List<ReceiveGroupDataDto> list = new ArrayList<>();
        //数据类型
        String dataType = "";
        JSONObject jsonObject = null;

        List<Map<String,Object>> tableDataList = new ArrayList<>();
        List<Map<String,Object>> tableList = null;
        List<Map<String,Object>> sendTableList = new ArrayList<>();
        for (int i = 0;i < jsonArray.size();i++){
            jsonObject = jsonArray.getJSONObject(i);
            DataTraceUtils.sendTrace(jsonObject, "数据通知", "200", "success", null);
            //单车组不能车型换车组
            if("6".equals(jsonObject.getString("type")) && !StringUtils.isEmptyObj(jsonObject.get("oldGroupId"))){
                continue;
            }
            if(jsonObject.get("type") != null){
                type = jsonObject.getString("type");
                if(jsonObject.getString("mainVersionCode").contains("AutoTest")){
                    //自动化测试
                    dataType = "AutoTest";
                } else if("1".equals(type) || "6".equals(type)){
                    //车组
                    dataType = "GroupData";
                }else if("2".equals(type)){
                    //字典
                    dataType = "DicData";
                }else if("3".equals(type)){
                    //替换件
                    dataType = "ReplaceData";
                }else if("4".equals(type)){
                    //品牌件
                    dataType = "AftData";
                }else if("5".equals(type)){
                    dataType = "DicData";
                    tableName = "m_"+jsonObject.getString("tableName");
                }else if("7".equals(type)){
                    dataType = "SingleTable";
                    tableName = jsonObject.getString("tableName");
                }else if("8".equals(type)){
                    dataType = "CommercialVehicle";
                }else{
                    jsonResult.setStatus("0003");
                    jsonResult.setMessage("数据类型错误");
                    return jsonResult;
                }
            }else{
                jsonResult.setStatus("0004");
                jsonResult.setMessage("数据类型为空");
                return jsonResult;
            }

            //获取需要查询的表数据
            tableList = dataTablesService.getListByEnable(type,tableName.toLowerCase());
            if(tableList == null || tableList.isEmpty()){
                jsonResult.setStatus("0002");
                jsonResult.setMessage("数据表不匹配");
                return jsonResult;
            }
            String tableName1 = "";
            receiveGroupDataDto = jsonObject.toJavaObject(ReceiveGroupDataDto.class);
            if(receiveGroupDataDto != null){
                //生成id
                id = UUID.randomUUID().toString().replace("-","");
                receiveGroupDataDto.setId(id);
                receiveGroupDataDto.setErrNum(0);
                receiveGroupDataDto.setIsModify("0");
                receiveGroupDataDto.setDataType(type);
                receiveGroupDataDto.setState("500");
                list.add(receiveGroupDataDto);

                //如果车组是修改车型的
                if("1".equals(type) && jsonObject.get("oldGroupId") != null && !"".equals(jsonObject.getString("oldGroupId"))){
                    String state = generateMainVersionCode(list,tableDataList,sendTableList,jsonObject.getString("oldGroupId"),receiveGroupDataDto.getMainVersionCode(),receiveGroupDataDto.getTableSuffix());
                    if(!"success".equals(state)){
                        jsonResult.setStatus("0005");
                        jsonResult.setMessage("修改前的车组不存在或者后缀不存在");
                        return jsonResult;
                    }
                }
                int type6 = 0;
                //循环表数据生成小批次号
                for (Map<String,Object> map1:tableList) {
                    tableName = map1.get("tableName").toString().toLowerCase();
                    tableName1 = tableName;
                    if(tableName.indexOf("m_") == 0){
                        tableName1 = tableName.substring(2);
                    }
                    //根据查询规则配置查询表名
                    if("1".equals(map1.get("tableStructure").toString()) ){
                        map1.put("tableName",tableName1+"_"+receiveGroupDataDto.getGroupCode());
                    }else if("2".equals(map1.get("tableStructure").toString())){
                        map1.put("tableName",tableName1+"_"+receiveGroupDataDto.getBrandCode());
                    }else{
                        map1.put("tableName",tableName1);
                    }
                    //根据分表规则生成保存的表名
                    if("0".equals(map1.get("isSeparate").toString())){
                        map1.put("saveTable",tableName);
                    }else if("1".equals(map1.get("isSeparate").toString())){
                        map1.put("saveTable",tableName+"_"+receiveGroupDataDto.getTableSuffix());
                    }else if("2".equals(map1.get("isSeparate").toString())){
                        map1.put("saveTable",map1.get("mergeTable"));
                    }
                    map1.put("type",type);
                    //关联主批次号
                    map1.put("mainVersionCode",receiveGroupDataDto.getMainVersionCode());
                    String versionCode = receiveGroupDataDto.getMainVersionCode()+StringUtils.randomStr(5);
                   // String versionCode = dataType+TimestampTool.yyyymmddhhmmss()+StringUtils.randomStr(5);
                    //生成小批次号groupTable+yymmddHHmmss+5位随机数
                    map1.put("versionCode",versionCode);
                    tableDataList.add(map1);

                    //查询当前表需要发送的表信息
                    Map<String,Object> sendTableMap = null;
                    if("6".equals(type) ){
                        if(type6 == 0){
                            sendTableMap = Dictionary.getOneGroupOrTable();
                            type6 ++;
                        }
                    }
                    /*else if("7".equals(type)){
                        sendTableMap = Dictionary.getOneTable(jsonObject.getString("tableName").toUpperCase());
                    }*/
                    else{
                        sendTableMap = receiveGroupDataService.getSendTable(tableName);
                    }
                    String contrastTableName = "";
                    if(sendTableMap != null && sendTableMap.size() > 0){
                        contrastTableName = sendTableMap.get("contrastTableName") == null ? "" : sendTableMap.get("contrastTableName").toString();
                        if(!StringUtils.isEmpty(contrastTableName)){
                            String[] contrastTable = contrastTableName.split(",");
                            for (int j = 0;j<contrastTable.length;j++){
                                if(contrastTable[j].indexOf("c_wl_") == 0){
                                    Map<String,Object> map = receiveGroupDataService.getWLTableByBrandCode(receiveGroupDataDto.getBrandCode());
                                    if(map == null || map.isEmpty()){
                                        continue;
                                    }
                                }
                                if("1".equals(sendTableMap.get("isBrandCode").toString())){
                                    sendTableList.add(assembleSendTable(contrastTable[j]+"_"+receiveGroupDataDto.getBrandCode(),contrastTable[j].substring(2)+"_"+receiveGroupDataDto.getBrandCode(),receiveGroupDataDto.getMainVersionCode(),map1.get("versionCode").toString(),versionCode+j+"a","1"));
                                }else{
                                    sendTableList.add(assembleSendTable(contrastTable[j],contrastTable[j].substring(2),receiveGroupDataDto.getMainVersionCode(),map1.get("versionCode").toString(),versionCode+j+"a","1"));
                                }
                            }
                        }
                    }
                }
                //判断当前品牌是否是蔚来的品牌
                //getWLTable(jsonObject.getString("brandCode"),sendTableList,receiveGroupDataDto.getMainVersionCode(),dataType+TimestampTool.yyyymmddhhmmss()+StringUtils.randomStr(5));
            }
        }
        try{
            //存储信息
            receiveGroupDataService.insertBatch(list,tableDataList,sendTableList);
        }catch (Exception e){
            e.printStackTrace();
            jsonResult.setStatus("2000");
            jsonResult.setMessage("数据保存失败,异常信息:"+e.getMessage());
            return jsonResult;
        }
        //数据保存成功后将mainVersionCode放入mq
        for (ReceiveGroupDataDto re:list) {
            template.convertAndSend(RabbitConfig.MQ_NAME, re.getMainVersionCode());

        }
        return  jsonResult;
    }

    public void getWLTable(String brandCode,List<Map<String,Object>> sendTableList,String mainVersionCode,String versionCode){
        Map<String,Object> map = receiveGroupDataService.getWLTableByBrandCode(brandCode);
        if(map != null && !map.isEmpty()){
            String tableNameSuffix = map.get("tableNameSuffix") == null ? "" : map.get("tableNameSuffix").toString();
            String tableName = map.get("tableName") == null ? "" : map.get("tableName").toString();

            String str = "";
            String[] table;
            if(!StringUtils.isEmpty(tableNameSuffix)){
                table = tableNameSuffix.split(",");
                for (int i = 0;i < table.length;i++){
                    str = table[i];
                    if(!StringUtils.isEmpty(str)){
                        sendTableList.add(assembleSendTable("c_"+str+"_"+brandCode,str+"_"+brandCode,mainVersionCode,versionCode,versionCode+i+"b","1"));
                    }
                }
            }
            if(!StringUtils.isEmpty(tableName)){
                table = tableNameSuffix.split(",");
                for (int i = 0;i < table.length;i++){
                    str = table[i];
                    if(!StringUtils.isEmpty(str)){
                        sendTableList.add(assembleSendTable("c_"+str,str,mainVersionCode,versionCode,versionCode+i+"c","1"));
                    }
                }
            }
        }
    }

    public Map<String,Object> assembleSendTable(String sendTable,String handleTable,String mainVersionCode,String versionCode,String sendCode,String state){
        Map<String,Object> saveMap = new HashMap<>(5);
        saveMap.put("sendTable",sendTable);
        saveMap.put("handleTable",handleTable);
        saveMap.put("mainVersionCode",mainVersionCode);
        saveMap.put("versionCode",versionCode);
        saveMap.put("sendCode",sendCode);
        saveMap.put("state",state);
        return saveMap;
    }

    public String generateMainVersionCode(List<ReceiveGroupDataDto> list,List<Map<String,Object>> tableDataList,List<Map<String,Object>> sendTableList,String oldGroupId,String pMainVersionCode,String tableSuffix){

        String[] oldGroupIds = oldGroupId.split(",");

        //如果是车型换车组的情况,则手动生成表关系
        for(int i = 0;i < oldGroupIds.length;i++){
            String str = oldGroupIds[i];
            //查询转换的车组之前是否发送过-------判断车组是否存在
            Map<String,Object> listMap = receiveGroupDataService.getClfzxxbByGroupCode(str);
            if(listMap == null || listMap.isEmpty()){
                return "error";
            }
            tableSuffix = listMap.get("brandCode").toString();
            ReceiveGroupDataDto dto = new ReceiveGroupDataDto();
            String id = StringUtils.getGUID();
            String mainVersionCode = "GroupData"+TimestampTool.yyyymmddhhmmss()+StringUtils.randomStr(7);
            dto.setId(id);
            dto.setMainVersionCode(mainVersionCode);
            dto.setGroupId(str);
            dto.setIsModify("1");
            dto.setErrNum(0);
            dto.setDelFlag("0");
            dto.setState("500");
            dto.setpMainVersionCode(pMainVersionCode);
            dto.setTableSuffix(tableSuffix);
            dto.setSeriesId(listMap.get("cxid").toString());
            dto.setSeriesCode(listMap.get("seriesCode").toString());
            dto.setBrandId(listMap.get("brandId").toString());
            dto.setBrandCode(listMap.get("brandCode").toString());
            list.add(dto);
            Map<String,Object> map = null;
            String[] tableNames = Dictionary.getTransformation();
            for (int j = 0;j < tableNames.length; j++){
                String versionCode = "GroupData"+TimestampTool.yyyymmddhhmmss()+StringUtils.randomStr(5);
                if(tableNames[j].equals("pj_czlbjdyb") || tableNames[j].equals("pj_cllbjdyb") || tableNames[j].equals("pj_clljtxdyb")){
                    map = assembleSendTable("c_"+tableNames[j]+"_"+tableSuffix,tableNames[j]+"_"+tableSuffix,mainVersionCode,versionCode,versionCode+j,"1");
                }else{
                    map = assembleSendTable("c_"+tableNames[j],tableNames[j],mainVersionCode,versionCode,versionCode+j,"1");
                }
                map.put("state","40");
                sendTableList.add(map);
            }
        }
        return "success";
    }

    public void mqGainData(){
     //   log.info("=============================================当前状态:"+Dictionary.IS_CONTINUE);
        while (Dictionary.IS_COMPARE && Dictionary.IS_CONTINUE){
            if(mqClientMonitor.getCount(RabbitConfig.MQ_NAME) > 0){
                processC(mqClientMonitor.processQueue(RabbitConfig.MQ_NAME));
            }
        }
    }

    public void processC(String mainVersionCode) {
        log.info("-----------------当前处理的mainVersionCode:"+mainVersionCode+"----------------------");
        Long startTime = System.currentTimeMillis();
        if(!StringUtils.isEmpty(mainVersionCode)){
            boolean flag = false;
            try{
                //修改获取数据状态
                receiveGroupDataService.updateGroupStateByMainVersionCode("501",mainVersionCode,"");
                //查询当前批次的信息
                Map<String,Object> map = receiveGroupDataService.getModifyByMainVersionCode(mainVersionCode);
                //判断是否是调换的车组信息(如果是则只转换发送部分表)
                if("0".equals(map.get("isModify"))){
                    //根据mainVersionCode获取表数据
                    List<ReceiveGroupDataDto> list = receiveGroupDataService.getTableListMainVersionCode(mainVersionCode);
                    if(list != null && !list.isEmpty()){

                        JSONObject params = new JSONObject();
                        params.put("mainVersionCode",mainVersionCode);
                        String data = "";
                        //保存请求回来的数据
                        JSONObject json = null;
                        //保存返回回来的实际表数据
                        JSONArray jsonArray = null;
                        //转换后的数据
                        List<Map> listMap = null;
                        for (int i = 0;i<list.size();i++) {
                            ReceiveGroupDataDto dto = list.get(i);
                            params.put("groupCode", dto.getGroupCode());
                            params.put("groupId",dto.getGroupId());
                            params.put("tableName", dto.getTableName().toUpperCase());
                            //获取表数据(如果获取过则直接从文件中获取,否则发送请求接口获取)
                            jsonArray = isFileExist(mainVersionCode, dto.getTableName(), params, dto.getGroupCode(), dto.getVersionCode(), dto.getDataType());
                            log.info("---------------------获取第" + i + "张表数据1" + mainVersionCode + "----------------------" + (System.currentTimeMillis() - startTime));
                            //如果数据为空则表示数据异常,结束循环标记错误
                            if (jsonArray == null || jsonArray.isEmpty()) {
                                break;
                            }
                            /*json = JSONObject.parseObject(data);
                            jsonArray = json.getJSONObject("data").getJSONArray("field");*/
                            listMap = jsonArray.toJavaList(Map.class);

                            //保存表数据
                            receiveGroupDataService.insertTableDataBatch(listMap, dto.getSaveTable(), dto.getGroupId(), dto.getGroupCode(), dto.getBrandId(), dto.getDataType(), dto.getTableName(), dto.getBrandCode());
                            log.info("---------------------获取第" + i + "张表处理" + dto.getSaveTable() + "数据长度" + listMap.size() + ",----------------------" + (System.currentTimeMillis() - startTime));

                            //修改获取数据状态
                            receiveGroupDataService.updateTableStateByMianVersionCode("01", "", mainVersionCode, dto.getTableName(), dto.getVersionCode());

                            //修改整个车组获取状态
                            if (i == (list.size() - 1)) {
                                DataTraceUtils.sendTrace(params, "数据接收", "200", "success", null);
                                //数据接收完成
                                receiveGroupDataService.updateGroupStateByMainVersionCode("020", mainVersionCode,"");
                                //通知数据接收完成
                                HttpClientUtil.doPost(accessoriesResultUrl, result(mainVersionCode, dto.getGroupCode(), new String("成功".getBytes("UTF-8")), "000000", dto.getDataType()));

                                //单车组是校验是否有删除车型,如果有删除车型,判断删除的车型下是否有配件
                                if("6".equals(dto.getDataType())){
                                    if(receiveGroupDataService.getOneGroupDelCount(dto.getGroupId())){
                                        HttpClientUtil.doPost(accessoriesResultUrl, result(mainVersionCode, dto.getGroupCode(), new String("单车组不允许删除带配件的车型".getBytes("UTF-8")), "000001", dto.getDataType()));
                                        receiveGroupDataService.updateGroupStateByMainVersionCode("701", mainVersionCode,"单车组不允许删除带配件的车型");
                                        break;
                                    }
                                }
                                boolean isWL = false;
                                Map<String,Object> wlTable = null;
                                //如果是车组信息
                                if("1".equals(dto.getDataType())){
                                    //判断当前品牌所属的分表是否存在
                                    int num = receiveGroupDataService.selUserTableNumByBrandCode(dto.getBrandCode());
                                    if(num < 6){
                                        //调用存储过程判断当前品牌分表是否存在,不存在则新建表并授权且建立同义词
                                        Map<String,Object> map3 = receiveGroupDataService.createFTable(dto.getTableSuffix());
                                        if(StringUtils.isEmptyObj(map3.get("errMsg"))){
                                            Map<String,Object> map4 = receiveGroupDataService.createCTable(dto.getTableSuffix());
                                            if(StringUtils.isEmptyObj(map4.get("errMsg"))){
                                                if(Double.parseDouble(map3.get("createNum").toString()) > 0 || Double.parseDouble(map4.get("createNum").toString()) > 0){
                                                    receiveGroupDataService.saveBrandRelation(dto);
                                                    //建立同义词
                                                    List<String> lists = new ArrayList<>();
                                                    lists.add("PJ_CLLBJDYB_"+dto.getTableSuffix());
                                                    lists.add("PJ_CLLJTXDYB_"+dto.getTableSuffix());
                                                    lists.add("PJ_CZLBJDYB_"+dto.getTableSuffix());
                                                    receiveGroupDataService.createTYC(lists);

                                                    //新建客户端表
                                                    Map<String,String> querys = new HashMap<>(4);
                                                    querys.put("create","");
                                                    querys.put("url",clientUrl);
                                                    querys.put("tableNames","pj_cllbjdyb,pj_clljtxdyb,pj_czlbjdyb");
                                                    querys.put("tableSuffix",dto.getTableSuffix());
                                                    JSONObject s = dataPublishUtils.doPost("/compares",querys,"");
                                                }
                                            }else{
                                                receiveGroupDataService.updateGroupStateByMainVersionCode("700", mainVersionCode,map4.get("errMsg").toString());
                                                break;
                                            }
                                        }else{
                                            receiveGroupDataService.updateGroupStateByMainVersionCode("700", mainVersionCode,map3.get("errMsg").toString());
                                            break;
                                        }
                                    }

                                    //判断是否是蔚来的品牌并判断表是否存在
                                    wlTable = receiveGroupDataService.getWLTableByBrandCode(dto.getBrandCode());
                                    if(wlTable != null && !wlTable.isEmpty()){
                                        isWL = true;
                                        if(!receiveGroupDataService.judgeWL(dto.getBrandCode(),wlTable,mainVersionCode)){
                                            break;
                                        }
                                    }

                                }

                                //调用数据转换对比存储过程  mid转换为产品结构
                                if(receiveGroupDataService.transformation(mainVersionCode, dto.getGroupId(),dto.getDataType(),dto.getTableName(),false,isWL)){
                                    log.info("数据转换结束---------------------------------------------------------");

                                    //处理vehicleTypeName和vehicleTypeCode
                                    //如果是发送配件/单车组信息/车型单表,则需要从整车获取车型类型和等级信息
                                    boolean flag_ = "1".equals(dto.getDataType()) || "6".equals(dto.getDataType()) || ("7".equals(dto.getDataType()) && "dic_vehicle".equals(dto.getTableName().toLowerCase()));
                                    if(flag_){
                                        receiveGroupDataService.vehTypeData(dto.getGroupId(),dto.getSeriesId(),dto.getBrandId(),mainVersionCode);
                                    }
                                    if( "8".equals(dto.getDataType())){
                                        //修改精细化商用的车型类型和等级
                                        receiveGroupDataService.truckVehicleType(mainVersionCode);
                                        //修改精细化商用车表后缀
                                        receiveGroupDataService.truckGroupSuffix();
                                    }
                                    //判断当前批次数据是否转换完成
                                    //如果当前发送的是车组数据
                                    String suffix = "";
                                    if(!StringUtils.isEmpty(dto.getGroupId())){
                                        //修改表后缀
                                        suffix = receiveGroupDataService.groupIsExists(dto.getGroupId());
                                    }
                                    if ("1".equals(dto.getDataType()) || "6".equals(dto.getDataType())) {

                                        //修改brandlevel
                                        receiveGroupDataService.updateBrandLevel(dto.getBrandCode());

                                        //修改值
                                        List<Map<String,Object>> nullPartName = receiveGroupDataService.getNullPartNameByGroupCode(dto.getGroupId());
                                        if(nullPartName != null && !nullPartName.isEmpty()){
                                            List<Map<String,Object>> maxPartName = receiveGroupDataService.getMaxHzNumber(dto.getGroupId());
                                            Map<String,Object> map2 = new HashMap<>();
                                            if(maxPartName != null && !maxPartName.isEmpty()){
                                                for (int m = 0;m < maxPartName.size();m++){
                                                    map2.put(maxPartName.get(m).get("partName").toString(),maxPartName.get(m).get("hzNumber"));
                                                }
                                                Map<String,Object> paramer = null;
                                                String partName = "";
                                                String number = "";
                                                List<Map<String,Object>> paraList = new ArrayList<>();
                                                for (int n = 0;n < nullPartName.size();n++){
                                                    paramer = new HashMap<>(2);
                                                    paramer.put("id",nullPartName.get(n).get("id"));
                                                    partName = nullPartName.get(n).get("partName").toString();
                                                    number = map2.get(partName).toString();
                                                    paramer.put("number",(Integer.parseInt(number) + 1));
                                                    map2.put(partName,(Integer.parseInt(number) + 1));
                                                    paraList.add(paramer);
                                                }
                                                paramer = new HashMap<>(1);
                                                paramer.put("list",paraList);
                                                receiveGroupDataService.updateHzNumber(paramer);
                                            }
                                        }
                                        receiveGroupDataService.updateCllbjdyb();
                                        if(isWL){
                                            receiveGroupDataService.updateWLCllbjdyb();
                                        }
                                    }
                                    //判断当前批次数据是否对比完成
                                    //对比
                                    if (dataContrast(mainVersionCode, list)) {
                                        //追加发送的表
                                        appendSendTable(mainVersionCode,isWL,dto.getBrandCode());
                                        //调用数据组装方法并发送数据
                                        receiveGroupDataService.dataAssembly(dto.getMainVersionCode(), dto.getBrandCode(), dto.getGroupCode(), suffix, true,dto.getBrandId(),map.get("delFlag") == null ? "0" : map.get("delFlag").toString(),dto.getClientCode(),dto.getClientUrl());
                                    }
                                }
                            }
                        }
                    }
                }else{
                    //查询是否有车型换车组的数据
                    List<ReceiveGroupDataDto> list2 = receiveGroupDataService.getByPMainVersionCode(mainVersionCode);
                    //如果存在,则不需要获取数据,直接进行数据转换和数据对比,然后发送
                    if(list2 != null && list2.size() > 0){
                        //获取车组的品牌信息
                        ReceiveGroupDataDto d = list2.get(0);
                        Map<String,Object> brandAndSuffix = receiveGroupDataService.getBrandAndTableSuffixByGroupId(d.getGroupId());
                        if(brandAndSuffix != null && brandAndSuffix.size() > 0){
                            if(StringUtils.notEmptyObj(brandAndSuffix.get("brandId")) && StringUtils.notEmptyObj(brandAndSuffix.get("brandCode")) && StringUtils.notEmptyObj(brandAndSuffix.get("groupCode")) && StringUtils.notEmptyObj(brandAndSuffix.get("tableSuffix"))){
                                Map<String,Object> para = new HashMap<>(3);
                                para.put("mainVersionCode",mainVersionCode);
                                para.put("oldBrandCode",d.getBrandCode());
                                para.put("newBrandCode",brandAndSuffix.get("brandCode"));
                                receiveGroupDataService.updateTableNameByMainVersionCode(para);
                                //修改车组的品牌信息
                                receiveGroupDataService.updateBrandByMainVersionCode(brandAndSuffix.get("brandId").toString(),brandAndSuffix.get("brandCode").toString(),brandAndSuffix.get("groupCode").toString(),mainVersionCode,brandAndSuffix.get("tableSuffix").toString());
                            }

                            //for (GainTableDataDto dto : list2){
                            //数据转换

                            //判断当前批次数据是否转换完成
                            //String state = receiveGroupDataService.getReceiceGroupStateByMainVersionCode(mainVersionCode);
                            if (receiveGroupDataService.transformation(mainVersionCode,d.getGroupId(),"","",true,false)) {
                                String suffix = receiveGroupDataService.groupIsExists(d.getGroupId());

                                //更新车型分类
                                receiveGroupDataService.vehTypeData(d.getGroupId(),d.getSeriesId(),d.getBrandId(),mainVersionCode);
                                //更新品牌等级
                                receiveGroupDataService.updateBrandLevel(brandAndSuffix.get("brandCode").toString());

                                //数据对比
                                //判断当前批次数据是否对比完成
                                //String state1 = receiveGroupDataService.getReceiceGroupStateByMainVersionCode(mainVersionCode);
                                if ( dataContrast(mainVersionCode, list2)) {
                                    //调用数据组装方法并发送数据
                                    receiveGroupDataService.dataAssembly(mainVersionCode, brandAndSuffix.get("brandCode").toString(), brandAndSuffix.get("groupCode").toString(), suffix, true,d.getBrandId(),"0","","");
                                }
                            }

                            //}
                        }else{
                            receiveGroupDataService.updateGroupStateByMainVersionCode("800", mainVersionCode,"查询不到表后缀");
                        }

                    }
                }
            }catch (Exception e){
                //修改错误次数
                updateErrorNum(mainVersionCode,"","获取表数据失败:"+e.getMessage(),"","2000","","");
                e.printStackTrace();
            }
        }
    }

    /**
     * 追加需要特殊处理的表
     * @param mainVersionCode 主批次号
     * @param isWl 是否是蔚来(蔚来品牌不是每次都需要追加)
     * @param brandCode 品牌编码
     */
    public void appendSendTable(String mainVersionCode,boolean isWl,String brandCode) throws Exception{

        List<Map<String,Object>> sendTableList = new ArrayList<>();
        Map<String,List<String>> sendTable = Dictionary.SUPPLEMENT_TABLE;
        List<Map<String,Object>> list = receiveGroupDataService.getSendTableByWlTable(mainVersionCode);
        Map<String,Map<String,Object>> map = list.stream().collect(Collectors.toMap((e -> e.get("handleTable").toString()), Function.identity(), (key1, key2) -> key2));
        sendTable.forEach((client,tables) -> {
            //如果不是蔚来的品牌,则不追加蔚来的表
            if("WL".equals(client) && !isWl){
                return;
            }
            int num = 1;
            for(Map.Entry<String, Map<String,Object>> entry : map.entrySet()){
                String tableNameNoSuffix = groupBrandAssocitedService.fitTableName(entry.getKey());
                if(tables.contains(tableNameNoSuffix)){
                    sendTableList.add(assembleSendTable("c_" + entry.getKey(), client, mainVersionCode, entry.getValue().get("versionCode").toString(),entry.getValue().get("versionCode").toString()+num+"b","71"));
                    num++;
                }
            }
        });
        if(sendTableList.size() > 0){
            receiveGroupDataService.saveSendTable(sendTableList);
        }
    }

    /**
     * 1/对比 product - final  结果进compare
     * 2、回写   final
     * @param mainVersionCode
     * @param list
     * @return
     */
    public boolean dataContrast(String mainVersionCode,List<ReceiveGroupDataDto> list){
        String certain = "";
        String state = "";
        boolean returnFlag = true;
        JSONObject obj = new JSONObject();
        obj.put("mainVersionCode", mainVersionCode);
        String status = "200";
        for (ReceiveGroupDataDto dto : list) {
            if("1".equals(dto.getDataType())){
                certain = dto.getGroupId();
            }else if("3".equals(dto.getDataType()) || "4".equals(dto.getDataType())){
                certain = dto.getBrandId();
            }
            state = receiveGroupDataService.dataContrast(mainVersionCode,dto.getVersionCode(),certain);
            if(!"success".equals(state)){
                status = "500";
                returnFlag = false;
                break;
            }
            log.info("数据对比日志输出:"+state+"--------------------------------------------------------");
        }
        DataTraceUtils.sendTrace(obj, "数据对比", status, state, null);
        if(returnFlag){
            for(ReceiveGroupDataDto dto : list){
                if("1".equals(dto.getDataType())){
                    certain = dto.getGroupId();
                }else if("3".equals(dto.getDataType()) || "4".equals(dto.getDataType())){
                    certain = dto.getBrandId();
                }
                state = receiveGroupDataService.pUpdateData(mainVersionCode,dto.getVersionCode(),certain);
                if(!"success".equals(state)){
                    status = "500";
                    returnFlag = false;
                    break;
                }
                log.info("数据回写日志输出:"+state+"--------------------------------------------------------");
            }
            DataTraceUtils.sendTrace(obj, "数据回写", status, state, null);
        }
        return returnFlag;
    }

    public String result(String mainVersionCode,String groupCode,String msg,String code,String dataType){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("mainVersionCode",mainVersionCode);
        jsonObject.put("groupCode",groupCode);
        jsonObject.put("msg",msg);
        jsonObject.put("code",code);
        jsonObject.put("type",dataType);
        return jsonObject.toJSONString();
    }

    /**
     * 根据是否保存文件判断是否重新请求数据
     * @param mainVersionCode 主批次号
     * @param tableName 表名
     * @param params 向加工平台请求数据的参数
     * @param groupCode 车组编码
     * @param versionCode 批次号
     * @param dataType 数据类型
     * @return
     */
    public JSONArray isFileExist(String mainVersionCode,String tableName,JSONObject params,String groupCode,String versionCode,String dataType){

        String data = "";
        String path = saveFilePath+"/"+TimestampTool.getCurrentDate().replace("-","");
        String fileName = tableName+"_"+mainVersionCode+".json";
        //查询当前表数据是否获取过(获取当前日期TimestampTool.getCurrentDate().replace("-",""),暂时弃用)
        File file = FileUtils.searchFile(fileName,saveFilePath);
        JSONArray jsonArray = new JSONArray(500000);
        if(file == null){
            try {
                int pageNum = 0;
                String isEnd = "0";
                JSONObject json;
                params.put("pageSize",pageSize);
                StringBuilder stringBuilder;
                while ("0".equals(isEnd)){
                    stringBuilder = new StringBuilder(20000000);
                    params.put("pageNum",++pageNum);
                    log.info("请求参数:"+params.toJSONString()+"-------------------");
                    data = doPost(url,params.toJSONString());
                    log.info("查询结束");
                    json = JSONObject.parseObject(data);
                    if("00000".equals(json.getString("code"))){
                        isEnd = json.getString("isEnd");
                        stringBuilder.append(JSON.toJSONString(json.getJSONObject("data").getJSONArray("field"),SerializerFeature.WriteMapNullValue));
                        if("0".equals(isEnd) && pageNum == 1){
                            FileUtils.exportSql(path,fileName,stringBuilder.substring(0,stringBuilder.length()-1));
                        }else if("0".equals(isEnd) && pageNum != 1){
                            FileUtils.exportSql(path,fileName,stringBuilder.substring(1,stringBuilder.length()-1));
                        }else if("1".equals(isEnd) && pageNum > 1){
                            FileUtils.exportSql(path,fileName,stringBuilder.substring(1));
                        }else{
                            FileUtils.exportSql(path,fileName,stringBuilder.toString());
                        }
                        jsonArray.addAll(json.getJSONObject("data").getJSONArray("field"));
                    }else{
                        //删除文件避免读取错误数据
                        new File(path+fileName).delete();
                        updateErrorNum(mainVersionCode,tableName,"请求返回失败:"+data,groupCode,"0008",versionCode,dataType);
                        return new JSONArray(0);
                    }
                }
                return jsonArray;
                /*if(!StringUtils.isEmpty(isEnd)){
                    json.getJSONObject("data").put("field",jsonArray);
                    data =JSON.toJSONString(json, SerializerFeature.WriteMapNullValue);
                }*/
                //FileUtils.exportSql(saveFilePath+"/"+TimestampTool.getCurrentDate().replace("-",""),tableName+"_"+mainVersionCode+".json",data);
            } catch (Exception e) {
                //删除文件避免读取错误数据
                new File(path+fileName).delete();
                //修改错误次数
                updateErrorNum(mainVersionCode,tableName,e.getMessage(),groupCode,"2002",versionCode,dataType);
                e.printStackTrace();
                log.error("文件保存失败,mainVersionCode:"+mainVersionCode+"tableName:"+tableName);
                return new JSONArray(0);
            }
        }else{
            //如果文件存在则从文件中获取数据
            log.info("开始转换");
            jsonArray = JSONArray.parseArray(FileUtils.fileData(file));
            log.info("转换结束");
            return jsonArray;
        }
        /*if(StringUtils.isEmpty(data)){
            //修改错误次数
            updateErrorNum(mainVersionCode,tableName,"获取的数据为空或连接失败",groupCode,"0008",versionCode,dataType);
            return "";
        }
        return data;*/
    }

    //修改车组获取数据的错误次数
    public void updateErrorNum(String mainVersionCode,String tableName,String errMsg,String groupCode,String code,String versionCode,String dataType){

        try{
            //修改错误次数
            Integer num = receiveGroupDataService.updateErrorNum(mainVersionCode);
            if(!StringUtils.isEmpty(tableName)){
                if(errMsg.length() > 1000){
                    errMsg = errMsg.substring(0,1000);
                }
                //修改获取数据状态
                receiveGroupDataService.updateTableStateByMianVersionCode("02",errMsg,mainVersionCode,tableName,versionCode);
            }
            //如果错误次数小于3次,则放入mq重新请求
            if(num != null && num > 0){
                //groupMainVersionCode
                //记录错误信息
                receiveGroupDataService.updateGroupStateByMainVersionCode(code,mainVersionCode,errMsg);
                template.convertAndSend(RabbitConfig.MQ_NAME,mainVersionCode);
            }else{
                //修改获取状态
                receiveGroupDataService.updateGroupStateByMainVersionCode("021",mainVersionCode,errMsg);
                //其他处理
                //通知数据接收失败
                HttpClientUtil.doPost(accessoriesResultUrl,result(mainVersionCode,groupCode,errMsg,code,dataType));
            }
        }catch (Exception e){
            log.error("修改车组查询状态出错,请检查mainVersionCode:"+mainVersionCode);
            e.printStackTrace();
        }
    }

    @ResponseBody
    @RequestMapping(value = "getList",method = RequestMethod.POST)
    public JsonResult getList(@RequestBody List<Map<String,Object>> list,String date){

        JsonResult jsonResult = new JsonResult();
        try {
            if(list != null && !list.isEmpty()) {

                StringBuilder str = new StringBuilder(100);
                int i = 0;
                for (Map<String, Object> map : list){
                    try {
                        //统计转换平台数据
                        //dataStatisticsService.transformationData(map.get("brandCode").toString(), map.get("groupId").toString(), map.get("seriesId").toString(), map.get("brandId").toString(), null, date);
                    }catch (Exception e){
                        log.error("错误的车组id:"+map.get("groupId"));
                        e.printStackTrace();
                        continue;
                    }
                    //统计facade数据
                    if (str.indexOf(map.get("groupId").toString()) < 0) {
                        str.append(map.get("groupId").toString() + ",");
                    }
                    log.info("---------------"+i);
                    i++;
                    if(i%100 == 0){
                        String groupIds = str.toString().substring(0,str.length()-1);
                        str = new StringBuilder(100);
                        String data = HttpClientUtil.doPost(facadeTokenUrl,"","");
                        log.info("地址:"+facadeTokenUrl+"获取的token:"+data);
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("vehGroupIds",groupIds.split(","));
                        jsonObject.put("url","http://192.168.80.80:8080/transfer_platfrom/statistics/dataStatistics");
                        jsonObject.put("date",date);
                        if(!StringUtils.isBlank(data)){
                            JSONObject json = JSONObject.parseObject(data);
                            log.info("发送的地址:"+facadeStatisticsUrl);
                            String facade = HttpClientUtil.doPost(facadeStatisticsUrl,jsonObject.toJSONString(),json.getString("access_token"));
                            log.info("发送的结果:"+facade);
                        }
                    }
                }
                if(str.length() > 0){
                    String data = HttpClientUtil.doPost(facadeTokenUrl,"","");
                    log.info("地址:"+facadeTokenUrl+"获取的token:"+data);
                    JSONObject jsonObject = new JSONObject();
                    String groupIds = str.toString().substring(0,str.length()-1);
                    jsonObject.put("vehGroupIds",groupIds.split(","));
                    jsonObject.put("url","http://192.168.80.80:8080/transfer_platfrom/statistics/dataStatistics");
                    jsonObject.put("date",date);
                    if(!StringUtils.isBlank(data)){
                        JSONObject json = JSONObject.parseObject(data);
                        String facade = HttpClientUtil.doPost(facadeStatisticsUrl,jsonObject.toJSONString(),json.getString("access_token"));
                        log.info("发送的结果:"+facade);
                    }
                }
            }
        }catch (Exception e){
            log.error("错误信息:"+e.getMessage());
            e.printStackTrace();
        }
        return jsonResult;
    }

    @ResponseBody
    @RequestMapping("getOriginalData")
    public JSONArray getOriginalData(String mainVersionCode,String date,String suffix){

        FileUtils utile = new FileUtils();
        String filePath = saveFilePath+"/"+TimestampTool.getCurrentDate().replace("-","");
        if(!StringUtils.isEmpty(date)){
            filePath = saveFilePath+"/"+date;
        }
        List<File> list = utile.searchVagueFile(mainVersionCode,filePath,"json");
        String tableName = "";
        JSONArray jsonArray = new JSONArray();
        JSONObject jsonObject = null;
        for (File file : list ) {
            jsonObject = new JSONObject();
            tableName = file.getName().substring(0,file.getName().lastIndexOf("_"));
            JSONObject json = JSONObject.parseObject(FileUtils.fileData(file));
            jsonObject.put("tableName",tableName);
            jsonObject.put("data",json);
            jsonArray.add(jsonObject);
        }
        return jsonArray;
    }

    @ResponseBody
    @RequestMapping("mqState")
    public void mqState(String state){
        if("start".equals(state)){
            Dictionary.setIsContinue(true);
        }else if("stop".equals(state)){
            Dictionary.setIsContinue(false);
        }

    }

    @ResponseBody
    @RequestMapping("getSuffix")
    public Map<String,Object> getSuffix(String groupIds){
        Dictionary.setIsContinue(false);
        log.info(Dictionary.IS_CONTINUE);
        return null;
    }

    /**
     * 判断是否可以验证数据
     */
    public void isVerification(){


    }
}

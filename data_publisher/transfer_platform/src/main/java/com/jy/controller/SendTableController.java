package com.jy.controller;

import com.alibaba.fastjson.JSONObject;
import com.jy.bean.common.MainVersionState;
import com.jy.bean.common.VersionState;
import com.jy.bean.dto.DealDataLogDTO;
import com.jy.bean.po.LogDetail;
import com.jy.bean.po.SendTable;
import com.jy.bean.result.JsonResult;
import com.jy.bean.result.ResultStatus;
import com.jy.service.SendTableService;
import com.jy.util.EmptyUtils;
import com.jy.util.HttpUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/sendTables")
public class SendTableController {

    @Value("${publishConfig.sendStateJson}")
    private String sendStateJson;

    @Autowired
    private SendTableService sendTableService;

    @RequestMapping(params={"mainVersionCode"}, method = RequestMethod.GET)
    public JsonResult<LogDetail> listBatchState(String mainVersionCode) throws Exception {
        JsonResult<LogDetail> jsonResult = new JsonResult<>();
        LogDetail logDetail = new LogDetail();
        List<SendTable> sendTables = sendTableService.listBatchState(mainVersionCode);
        if(EmptyUtils.isNotEmpty(sendTables)){
            logDetail.setSendTables(sendTables);
            fitSendTable(sendTables);
            String transferState = sendTables.get(0).getState();
            String baseState = "022";

            Map<String, String> querys = new HashMap<>();
            querys.put("mainVersionCode", mainVersionCode);
            String str = HttpUtils.doGet(sendStateJson, "", new HashMap(0), querys);
            JSONObject jsonObject = JSONObject.parseObject(str);
            if(jsonObject.containsKey("code") && jsonObject.get("code").equals(ResultStatus.SUCCESS.getStatus())){
                List<DealDataLogDTO> dealDataLogDTOS = JSONObject.parseArray(JSONObject.toJSONString(jsonObject.get("result")), DealDataLogDTO.class);
                baseState = getDealDataLogState(dealDataLogDTOS);
                logDetail.setDealDataLogDTOS(dealDataLogDTOS);
            }

            //200 成功 500失败 202 处理中
            String state = fitState(transferState, baseState);
            logDetail.setState(state);
            logDetail.setTransferState(transferState);
            logDetail.setBaseState(baseState);
        }

        jsonResult.setResult(logDetail);
        return jsonResult;
    }

    private String fitState(String transferState, String dealDataLogState){
        String errorMainVersionCodeState = "021,903,904,905,000,2000";
        String dealLogState = "003,908,909,022";
        if(!errorMainVersionCodeState.contains(transferState) || !dealLogState.contains(dealDataLogState)){
            return "202";
        }
        if(transferState.equals(MainVersionState.SUCCESS.getCode()) && dealDataLogState.equals("022")){
            return "200";
        } else {
            return "500";
        }
    }

    private String getDealDataLogState(List<DealDataLogDTO> dealDataLogDTOS){
        String dealDataLogState = "022";
        if(EmptyUtils.isNotEmpty(dealDataLogDTOS)){
            for(DealDataLogDTO dealDataLogDTO : dealDataLogDTOS){
                if(!dealDataLogDTO.getState().equals("022")){
                    dealDataLogState = dealDataLogDTO.getState();
                    break;
                }
            }
        }
        return dealDataLogState;
    }
    private List<SendTable> fitSendTable(List<SendTable> sendTables){
        for(SendTable sendTable : sendTables){
            sendTable.setStateName(MainVersionState.messageOf(sendTable.getState()));
            sendTable.setVersionStateName(VersionState.messageOf(sendTable.getVersionState()));
        }
        return sendTables;
    }
}

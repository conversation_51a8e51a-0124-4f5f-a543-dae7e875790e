package com.jy.controller;

import com.jy.bean.dto.DataTablesDTO;
import com.jy.bean.po.UserPo;
import com.jy.bean.result.JsonResult;
import com.jy.service.DataTablesService;
import com.jy.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by jdd on 2018/11/29.
 */
@Controller
@RequestMapping("dataTable")
public class DataTablesController {

    @Autowired
    private DataTablesService dataTablesService;

    @ResponseBody
    @RequestMapping("getList")
    public JsonResult getList(int page,String tableName,String isEnable){

        Map<String,Object> map = new HashMap<>();
        map.put("tableName",tableName.toLowerCase());
        map.put("page",page);
        map.put("isEnable",isEnable);
        JsonResult jsonResult = new JsonResult();
        jsonResult.setResult(dataTablesService.getListByPage(map));
        return jsonResult;
    }

    @RequestMapping("jump")
    public String jump(DataTablesDTO dataTablesDTO, Model view){
        dataTablesDTO.setTableName("test");
        view.addAttribute("dataTables",dataTablesDTO);
        return "dataTables/dataTables_edit";
    }

    @ResponseBody
    @RequestMapping("save")
    public JsonResult save(@RequestBody DataTablesDTO dataTablesDTO, HttpSession session){

        JsonResult jsonResult = new JsonResult();
        UserPo user = (UserPo) session.getAttribute("currentUser");
        if(user == null){
            jsonResult.setStatus("600103");
            jsonResult.setMessage("用户信息不存在,请重新登陆");
            return jsonResult;
        }
        dataTablesDTO.setUpdateUser(user.getId());
        dataTablesDTO.setTableName(dataTablesDTO.getTableName().toLowerCase());
        if(StringUtils.isEmpty(dataTablesDTO.getId())){
            dataTablesDTO.setCreateUser(user.getId());
            dataTablesService.insert(dataTablesDTO);
        }else{
            dataTablesService.update(dataTablesDTO);
        }
        return jsonResult;
    }

    @ResponseBody
    @RequestMapping("delete")
    public JsonResult delete(String id){

        JsonResult jsonResult = new JsonResult();
        if(StringUtils.isEmpty(id)){
            jsonResult.setStatus("20000");
            jsonResult.setMessage("删除数据不存在");
        }else{
            dataTablesService.deleteById(id);
        }
        return jsonResult;
    }
}

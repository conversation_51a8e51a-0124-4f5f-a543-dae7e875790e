package com.jy.controller;

import com.alibaba.fastjson.JSONObject;
import com.jy.bean.result.JsonResult;
import com.jy.service.ReceiveGroupDataService;
import com.jy.service.VehicleService;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2019/7/12.
 */
@RestController
@RequestMapping("vehicle")
public class VehicleController {

    private static final Log log = LogFactory.getLog(VehicleController.class);

    @Autowired
    private VehicleService vehicleService;

    @Autowired
    private ReceiveGroupDataService receiveGroupDataService;

    /**
     * 根据承保车型人保编码获取理赔车型id
     * @param json
     * @return
     */
    @RequestMapping(value = "vehicleConversion",method = RequestMethod.POST)
    public JsonResult vehicleConversion(@RequestBody JSONObject json){

        log.info("整车请求参数:"+json);
        JsonResult jsonResult = new JsonResult();
        try {
            if(json == null || json.isEmpty() || json.get("vehicleCode") == null || "".equals(json.getString("vehicleCode"))){
                jsonResult.setStatus("400");
                jsonResult.setMessage("参数为空");
                return jsonResult;
            }
            String vehicleCode = json.getString("vehicleCode");
            String[] vehicleIds = vehicleCode.split(",");
            if(vehicleIds.length > 500){
                jsonResult.setStatus("1001");
                jsonResult.setMessage("请求数据量太大,请分批请求");
                return jsonResult;
            }
            List<String> vehicleRbCode = Arrays.asList(vehicleCode.split(","));
            List<String> vehicleRbCode2 = new ArrayList<>(vehicleRbCode);
            List<Map<String,Object>> list = vehicleService.vehicleConversion(vehicleRbCode);
            Map<String,Object> nullMap;
            for (Map<String,Object> map : list){
                if(vehicleRbCode2.contains(map.get("vehicleCode").toString())){
                    vehicleRbCode2.remove(map.get("vehicleCode").toString());
                }
            }
            for (String str : vehicleRbCode2){
                nullMap = new HashMap<>(2);
                nullMap.put("claimVehicleId","");
                nullMap.put("vehicleCode",str);
                list.add(nullMap);
            }
            if(list == null || list.isEmpty()){
                jsonResult.setStatus("404");
                jsonResult.setMessage("没有符合条件的数据");
                return jsonResult;
            }
            jsonResult.setResult(list);
        }catch (Exception e){
            jsonResult.setStatus("500");
            jsonResult.setMessage("发生未知错误,错误信息:"+e.getMessage());
            e.printStackTrace();
        }
        return jsonResult;
    }

    /**
     * 重新推送整车数据
     * @param versionCode
     */
    @RequestMapping("resend")
    public void resend(String versionCode){

        vehicleService.vehicleConversion(versionCode);
    }
}

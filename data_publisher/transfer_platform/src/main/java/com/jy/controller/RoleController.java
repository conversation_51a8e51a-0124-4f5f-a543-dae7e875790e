package com.jy.controller;

import com.jy.bean.po.SysRolePo;
import com.jy.bean.result.JsonResult;
import com.jy.bean.result.ResultStatus;
import com.jy.service.RoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/role")
public class RoleController {

    @Autowired
    private RoleService roleServiceImpl;

    /**
     * 查询角色列表信息
     */
    @RequestMapping("/getRoleList")
    @ResponseBody
    public JsonResult<List<SysRolePo>> getRoleList(){
        JsonResult<List<SysRolePo>> jsonResult = new JsonResult<List<SysRolePo>>();
        List<SysRolePo> list  = roleServiceImpl.getRoleList();
        if(list != null){
            jsonResult.setMessage("查询角色列表成功");
            jsonResult.setResult(list);
        }else{
            jsonResult.setResultStatus(ResultStatus.NO_DATA);
        }
        return jsonResult;
    }
}
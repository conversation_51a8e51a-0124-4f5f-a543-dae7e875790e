package com.jy.controller;

import com.alibaba.fastjson.JSONObject;
import com.jy.bean.result.JsonResult;
import com.jy.rabbitMq.MQClientMonitor;
import com.jy.rabbitMq.RabbitConfig;
import com.jy.service.ReceiveGroupDataService;
import com.jy.service.WorkHoursService;
import com.jy.util.Dictionary;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2019/6/4.
 */
@RestController
@RequestMapping("workHours")
public class WorkHoursController {

    private static final Log log = LogFactory.getLog(WorkHoursController.class);
    @Autowired
    private WorkHoursService workHoursService;

    @Autowired
    private ReceiveGroupDataService receiveGroupDataService;

    @Autowired
    private MQClientMonitor mqClientMonitor;

    private Long time = 0L;
    @ResponseBody
    @RequestMapping(value = "saveWorkHours",method = RequestMethod.POST)
    public synchronized JsonResult saveWorkHours(@RequestBody JSONObject data){

        log.info("------------工时调用信息:"+data);
        long startTime = System.currentTimeMillis();
        JsonResult jsonResult = new JsonResult();
        if(data == null || data.isEmpty() ||
        data.getJSONArray("groupData") == null || data.getJSONArray("groupData").size() <= 0){
            jsonResult.setStatus("4004");
            jsonResult.setMessage("数据为空");
            return jsonResult;
        }
        try {
            workHoursService.saveWorkHours(data.getJSONArray("groupData"));
        } catch (Exception e) {
            jsonResult.setStatus("2000");
            jsonResult.setMessage("处理失败,错误信息:"+e.getMessage());
            log.error("工时标记错误信息:"+e.getMessage());
            e.printStackTrace();
        }
        log.info("总的执行时间:"+(System.currentTimeMillis() - startTime));
        return jsonResult;
    }

    public void mqGainData(){
        while (Dictionary.IS_COMPARE && Dictionary.IS_CONTINUE){
            if(mqClientMonitor.getCount(RabbitConfig.WORK_MQ_NAME) > 0){
                sendData(mqClientMonitor.processQueue(RabbitConfig.WORK_MQ_NAME));
            }
        }
    }

    public void sendData(String mainVersionCode) {
        long startTime = System.currentTimeMillis();
        try {
            workHoursService.handleWorkHours(mainVersionCode);
        }catch (Exception e){
            log.error("工时标记mq错误信息:"+e.getMessage());
            e.printStackTrace();
        }
        time += (System.currentTimeMillis() - startTime);
        log.info("处理一个批次的时间:"+(System.currentTimeMillis() - startTime));
        log.info("总的时间:"+time);
    }
}

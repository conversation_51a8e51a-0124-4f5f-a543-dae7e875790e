package com.jy.transform;

import com.jy.bean.dto.BaseDataDTO;
import com.jy.bean.dto.BaseDataDTOs;
import com.jy.bean.po.BatchDetail;
import com.jy.bean.po.ReceiveBatch;
import com.jy.bean.po.SendDetail;
import com.jy.bean.po.TransferTableMp;

import java.util.List;

/**
 * @Author: caolt
 * @Date: Created in 2020/5/19
 */
public interface DataTransform {

    void handle(ReceiveBatch receiveBatch) ;

    void transform(ReceiveBatch receiveBatch) throws Exception;

    void compare(ReceiveBatch receiveBatch) throws Exception;

    void update(ReceiveBatch receiveBatch) throws Exception;

    void push(ReceiveBatch receiveBatch, List<SendDetail> sendDetails) throws Exception;

    void push(ReceiveBatch receiveBatch, List<SendDetail> sendDetails, String clientCode) throws Exception;

    List<SendDetail>fitPushSendDetail(String mainBatchNo, String tableName, String partSuffix, Integer batchIndex) throws Exception;

    List<SendDetail> fitPushBatchNo(ReceiveBatch receiveBatch) throws Exception;

    void updateTransformData(List<BaseDataDTO> dataDTOs, String type) throws Exception;

    /**
     * 给无需走转换过程，直接出p库的执行语句的组装动态sql方法
     * @param baseDataDTOs
     * @return
     * @throws Exception
     */
    List<BaseDataDTO> fitTransformData(BaseDataDTOs baseDataDTOs) throws Exception;
}

package com.jy.transform;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jy.bean.common.Constant;
import com.jy.bean.common.DataTraceMenu;
import com.jy.bean.common.PublishTypeEnum;
import com.jy.bean.dto.BaseDataDTOs;
import com.jy.bean.po.BatchDetail;
import com.jy.bean.po.PartTrail;
import com.jy.bean.po.ReceiveBatch;
import com.jy.bean.po.SendDetail;
import com.jy.bean.result.ResultStatus;
import com.jy.service.*;
import com.jy.util.*;
import com.jy.util.rabbitmq.DataTraceUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: caolt
 * @Date: Created in 2020/5/19
 */
@Service
public class PartDataTransform extends DataAbstractTransform {
    private static final Logger logger = LogManager.getLogger(PartDataTransform.class);
    @Value("${srcData.filePath}")
    private String filePath;
    @Value("${httpUtils.part.partUrl}")
    private String partUrl;
    @Value("${httpUtils.part.partPicPath}")
    private String partPath;
    @Value("${httpUtils.facade.newPartEmailTo}")
    private String newPartEmailTo;
    @Value("${httpUtils.facade.newPartEmailCc}")
    private String newPartEmailCc;


    @Autowired
    private CommonUtils commonUtils;
    @Autowired
    private CommonService commonService;
    @Autowired
    private SendDetailService sendDetailService;
    @Autowired
    private PartTrailService partTrailService;
    @Autowired
    private BatchDetailService batchDetailService;
    @Autowired
    private ProcedureService procedureService;
    @Autowired
    private ReceiveBatchService receiveBatchService;


    @Override
    public void handle(ReceiveBatch receiveBatch) {
        try{
            DataTraceUtils.sendTrace((JSONObject) JSON.toJSON(receiveBatch), DataTraceMenu.SRC_START_DESC.getName(), ResultStatus.SUCCESS.getStatus(), ResultStatus.SUCCESS.getMessage());
            //1、转换
            transform(receiveBatch);
            //2、对比
            compare(receiveBatch);
            //3、回写
            update(receiveBatch);
            //通知配件加工平台上传图片数据
            notice(receiveBatch);
            //4、数据推送
            push(receiveBatch, fitPushBatchNo(receiveBatch));
            //5、记录配件车组处理完成轨迹
            trail(receiveBatch);
        } catch (Exception e) {
            logger.error("配件服务处理定时执行失败:" + ToolUtils.getExceptionMsg(e));
            commonUtils.sendWorkWechatPath("mainBatchNo:" + receiveBatch.getMainBatchNo() + "批次处理失败");
        }
    }

    @Override
    public void transform(ReceiveBatch receiveBatch) throws Exception {
        try{
            //1、获取主批次对应的表
            List<BatchDetail> batchDetails = batchDetailService.listByMainBatchNo(receiveBatch.getMainBatchNo());
            //2、清除m库数据
            for(BatchDetail batchDetail: batchDetails){
                procedureService.truncateMData(batchDetail.getTableName());
            }
            //3、数据入m库
            List<String> paths = batchDetails.stream().map(BatchDetail::getFilePath).collect(Collectors.toList());
            for(String path : paths){
                //1、读取文件
                String json = FileUtils.readToString(filePath + path);
                BaseDataDTOs baseDataDTOs = JSONObject.parseObject(json, BaseDataDTOs.class);
                baseDataDTOs.getData().forEach(baseDataDTO->{
                    baseDataDTO.setOperate(Constant.OPERATE_INSERT);
                    baseDataDTO.setTableName(baseDataDTOs.getTableName());
                    if(!baseDataDTO.getFields().containsKey("GROUP_ID")){
                        baseDataDTO.getFields().put("GROUP_ID", receiveBatch.getCertainId());
                        baseDataDTO.getFields().put("GROUP_CODE", receiveBatch.getCertainCode());
                    }
                });
                updateTransformData(baseDataDTOs.getData(), PublishTypeEnum.PART.getCode());
            }
            DataTraceUtils.sendTrace((JSONObject) JSON.toJSON(receiveBatch), DataTraceMenu.SRC_ACCEPT_DATA.getName(), ResultStatus.SUCCESS.getStatus(), ResultStatus.SUCCESS.getMessage());
            //判断是否已有配件相关表，如无新增
            createTable(receiveBatch);
            //4、调用转换存储过程 非蔚来车组
            Map<String,Object> procedureRes = procedureService.partProcedure(receiveBatch.getMainBatchNo(), receiveBatch.getCertainId());
            if(!ResultStatus.SUCCESS.getStatus().equals(procedureRes.get("status"))){
                throw new Exception(procedureRes.get("message").toString());
            }
            //车组维度零件表，品牌维度零件表，车型换车组场景 ---不处理 融合至普通车组转换
            //蔚来车组，蔚来配件单独处理
            String partSuffix = commonService.getPartSuffix(receiveBatch.getCertainId());
            if(Constant.WL_BRAND_CODE.equals(partSuffix)){
                procedureRes = procedureService.wlPartProcedure(receiveBatch.getMainBatchNo(), receiveBatch.getCertainId());
                if(!ResultStatus.SUCCESS.getStatus().equals(procedureRes.get("status"))){
                    throw new Exception(procedureRes.get("message").toString());
                }
            }
            fitPartName(receiveBatch);
            DataTraceUtils.sendTrace((JSONObject) JSON.toJSON(receiveBatch), DataTraceMenu.SRC_TRANSFER_DESC.getName(), ResultStatus.SUCCESS.getStatus(), ResultStatus.SUCCESS.getMessage());
        } catch (Exception e) {
            String message = ToolUtils.getExceptionMsg(e);
            DataTraceUtils.sendTrace((JSONObject)JSON.toJSON(receiveBatch), DataTraceMenu.SRC_TRANSFER_DESC.getName(), ResultStatus.INTERNAL_SERVER_ERROR.getStatus(), "配件服务转换解析定时执行失败"+message);
            logger.error("配件服务转换解析定时执行失败: receiveBatch:{}, message:{}" + receiveBatch, message);
            throw new Exception(message);
        }
    }

    /**
     * 判断是否存在配件表，如果不存在则创建
     * @param receiveBatch
     */
    private void createTable(ReceiveBatch receiveBatch) throws Exception {
        try{
            Map<String,Object> procedureRes = procedureService.createFCTable(receiveBatch.getCertainId());
            if(!ResultStatus.SUCCESS.getStatus().equals(procedureRes.get("status"))){
                throw new Exception(procedureRes.get("message").toString());
            }
            if((Integer)procedureRes.get("tableNum") < 6){
                String brandCode = procedureRes.get("partSuffix").toString();
                String msg = brandCode + "品牌已加工配件，需要创建对应表。主批次号："
                        + receiveBatch.getMainBatchNo() + ",语句如下: \n" ;
                msg += fitSqlMsg(brandCode);
                String title = "即时更新：" + brandCode + "品牌加工配件-创建对应表";
                commonUtils.sendEmail(title, msg, newPartEmailTo, newPartEmailCc);

            }
            DataTraceUtils.sendTrace((JSONObject) JSON.toJSON(receiveBatch), DataTraceMenu.SRC_CREATE_TABLE.getName(), ResultStatus.SUCCESS.getStatus(), ResultStatus.SUCCESS.getMessage());
        } catch (Exception e) {
            String message = ToolUtils.getExceptionMsg(e);
            DataTraceUtils.sendTrace((JSONObject)JSON.toJSON(receiveBatch), DataTraceMenu.SRC_CREATE_TABLE.getName(), ResultStatus.INTERNAL_SERVER_ERROR.getStatus(), "车型服务转换解析定时执行失败"+message);
            logger.error("车型服务转换解析定时执行失败: receiveBatch:{}, message:{}" + receiveBatch, message);
            throw new Exception(message);
        }
    }

    public String fitSqlMsg(String brandCode){
        String msg = "CREATE TABLE sup_part_jy_" + brandCode + " (id serial8, sup_table_id varchar(64) COLLATE pg_catalog.default,  " +
                "sup_part_id varchar(64) COLLATE pg_catalog.default,  " +
                "sup_part_code varchar(64) COLLATE pg_catalog.default, " +
                "sup_part_name varchar(200) COLLATE pg_catalog.default, " +
                "original_part_name varchar(200) COLLATE pg_catalog.default, " +
                "original_part_code varchar(64) COLLATE pg_catalog.default,  " +
                "original_short_code varchar(64) COLLATE pg_catalog.default,  " +
                "original_part_id varchar(64) COLLATE pg_catalog.default,  " +
                "brand_id varchar(64) COLLATE pg_catalog.default,  " +
                "series_id varchar(64) COLLATE pg_catalog.default,  " +
                "group_id varchar(64) COLLATE pg_catalog.default,  " +
                "model_id varchar(64) COLLATE pg_catalog.default,  " +
                "status varchar(1) COLLATE pg_catalog.default,  " +
                "create_time timestamp(6), update_time timestamp(6),  " +
                "part_remark varchar(500) COLLATE pg_catalog.default ); \n\n" +
                "COMMENT ON COLUMN sup_part_jy_" + brandCode + ".id IS '编号'; " +
                "COMMENT ON COLUMN sup_part_jy_" + brandCode + ".sup_table_id IS '编号'; " +
                "COMMENT ON COLUMN sup_part_jy_" + brandCode + ".sup_part_id IS '数据商标准配件id'; " +
                "COMMENT ON COLUMN sup_part_jy_" + brandCode + ".sup_part_code IS '数据商标准配件编码'; " +
                "COMMENT ON COLUMN sup_part_jy_" + brandCode + ".sup_part_name IS '数据商标准配件名称'; " +
                "COMMENT ON COLUMN sup_part_jy_" + brandCode + ".original_part_name IS '原厂配件名称'; " +
                "COMMENT ON COLUMN sup_part_jy_" + brandCode + ".original_part_code IS '配件oe'; " +
                "COMMENT ON COLUMN sup_part_jy_" + brandCode + ".original_short_code IS '去特殊符号OE号'; " +
                "COMMENT ON COLUMN sup_part_jy_" + brandCode + ".original_part_id IS '原厂零件id，'; " +
                "COMMENT ON COLUMN sup_part_jy_" + brandCode + ".brand_id IS '品牌id'; " +
                "COMMENT ON COLUMN sup_part_jy_" + brandCode + ".series_id IS '车系id'; " +
                "COMMENT ON COLUMN sup_part_jy_" + brandCode + ".group_id IS '车组id'; " +
                "COMMENT ON COLUMN sup_part_jy_" + brandCode + ".model_id IS '车型id'; " +
                "COMMENT ON COLUMN sup_part_jy_" + brandCode + ".status IS '是否有效 0：无效  1：有效'; " +
                "COMMENT ON COLUMN sup_part_jy_" + brandCode + ".create_time IS '创建日期'; " +
                "COMMENT ON COLUMN sup_part_jy_" + brandCode + ".update_time IS '修改日期'; " +
                "COMMENT ON COLUMN sup_part_jy_" + brandCode + ".part_remark IS '零件备注'; " +
                "COMMENT ON TABLE sup_part_jy_" + brandCode + " IS '车型配件关系表';\n\n" +

                "ALTER TABLE sup_part_jy_" + brandCode + " ADD CONSTRAINT sup_part_jy_" + brandCode + "_pky PRIMARY KEY (id); \n" +

                "CREATE UNIQUE INDEX idx_part_jy_" + brandCode + "_stid ON sup_part_jy_" + brandCode + "  USING btree ( sup_table_id COLLATE pg_catalog.default pg_catalog.text_ops ASC NULLS LAST ); \n" +
               "CREATE INDEX idx_part_jy_" + brandCode + "_gid ON sup_part_jy_" + brandCode + " USING btree (  group_id COLLATE pg_catalog.default pg_catalog.text_ops ASC NULLS LAST); \n" +
                "CREATE INDEX idx_part_jy_" + brandCode + "_mid ON sup_part_jy_" + brandCode + " USING btree (  model_id COLLATE pg_catalog.default pg_catalog.text_ops ASC NULLS LAST); \n" +
                "CREATE INDEX idx_part_jy_" + brandCode + "_po ON sup_part_jy_" + brandCode + " USING btree (  original_part_code COLLATE pg_catalog.default pg_catalog.text_ops ASC NULLS LAST); \n" +
                "CREATE INDEX idx_part_jy_" + brandCode + "_so ON sup_part_jy_" + brandCode + " USING btree (  original_short_code COLLATE pg_catalog.default pg_catalog.text_ops ASC NULLS LAST); \n" +
                "CREATE INDEX idx_part_jy_" + brandCode + "_spc ON sup_part_jy_" + brandCode + " USING btree (  sup_part_code COLLATE pg_catalog.default pg_catalog.text_ops ASC NULLS LAST); \n" ;
        return msg;
    }

    private void fitPartName(ReceiveBatch receiveBatch){
        //修改值
        List<Map<String,Object>> nullPartName = commonService.getNullPartNameByGroupCode(receiveBatch.getCertainId());
        if(EmptyUtils.isNotEmpty(nullPartName)){
            List<Map<String,Object>> maxPartName = commonService.getMaxHzNumber(receiveBatch.getCertainId());
            Map<String,Object> map2 = maxPartName.stream().collect(Collectors.toMap(map->map.get("partName").toString(), map->map.get("hzNumber"), (k1,k2)->k1));
            if(EmptyUtils.isNotEmpty(maxPartName)){
                Map<String,Object> paramer = null;
                String partName = "";
                String number = "";
                List<Map<String,Object>> paraList = new ArrayList<>();
                for (Map<String,Object> map : nullPartName){
                    partName = map.get("partName").toString();
                    number = map2.get(partName).toString();
                    paramer = new HashMap<>();
                    paramer.put("id",map.get("id"));
                    paramer.put("number",(Integer.parseInt(number) + 1));
                    map2.put(partName,(Integer.parseInt(number) + 1));
                    paraList.add(paramer);
                }
                paramer = new HashMap<>();
                paramer.put("list",paraList);
                commonService.updateHzNumber(paramer);
            }
        }
        commonService.updateCllbjdyb();
        String partSuffix = commonService.getPartSuffix(receiveBatch.getCertainId());
        if(Constant.WL_BRAND_CODE.equals(partSuffix)){
            commonService.updateWLCllbjdyb();
        }
    }

    /**
     * 通知配件加工平台配件已经处理完成，可以将图片上传腾讯云
     * @param receiveBatch
     */
    private void notice(ReceiveBatch receiveBatch){
        Map<String, String> querys = new HashMap<>();
        querys.put("mainBatchNo", receiveBatch.getMainBatchNo());
        //失败重复发送  最多三次
        String status = ResultStatus.INTERNAL_SERVER_ERROR.getStatus();
        int i = 0;
        while(i < 3){
            JSONObject result = null;
            try {
                String response = HttpUtils.doPost(partUrl, partPath, new HashMap<>(), querys, "");
                result = JSONObject.parseObject(response);
                logger.info("notice回调结果:" + result);
            } catch (Exception e) {
                status = ResultStatus.INTERNAL_SERVER_ERROR.getStatus();
                logger.error("notice通知配件加工平台数据处理完成结果失败，message: {}, mainBatchNo: {}", ToolUtils.getExceptionMsg(e), receiveBatch.getMainBatchNo());
            }
            if(EmptyUtils.isNotEmpty(result) && (result.getString("status").equals(ResultStatus.SUCCESS.getStatus()) || result.getString("status").equals(ResultStatus.NO_DATA.getStatus()))){
                status = result.getString("status");
                break;
            }
            i++;
        }
        ReceiveBatch receiveBatch1 = new ReceiveBatch();
        receiveBatch1.setMainBatchNo(receiveBatch.getMainBatchNo());
        receiveBatch1.setNoticeStatus(status);
        receiveBatchService.update(receiveBatch1);
    }

    private void trail(ReceiveBatch receiveBatch)  {
        try{
        List<Map<String,Object>> list = commonService.listCertainByWhere("p_zc_clzlb", "ppid,ppbm,ppmc,cxid,cxbm,cxmc", "zbid",receiveBatch.getCertainId());
        List<String> cpicPpbms = commonService.listCertain("cpic_filter_ppbm", "ppbm");
        List<SendDetail> sendDetails = sendDetailService.listByMainBatchNo(receiveBatch.getMainBatchNo());
        sendDetails = sendDetails.stream().filter(sendDetail-> ResultStatus.SUCCESS.getStatus().equals(sendDetail.getStatus())).collect(Collectors.toList());
        //记录标准： 1、存在待推送的车组数据 2、太保所要的品牌
        if(EmptyUtils.isNotEmpty(list) && cpicPpbms.contains(list.get(0).getOrDefault("ppbm", "-")) && EmptyUtils.isNotEmpty(sendDetails)){
            PartTrail partTrail = new PartTrail(receiveBatch.getMainBatchNo());
            BeanUtils.mapToObject(list.get(0), partTrail);
            partTrailService.save(partTrail);
        }
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}

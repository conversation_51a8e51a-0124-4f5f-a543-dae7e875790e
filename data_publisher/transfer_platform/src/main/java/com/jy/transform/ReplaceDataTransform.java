package com.jy.transform;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jy.bean.common.BatchNoStatus;
import com.jy.bean.common.Constant;
import com.jy.bean.common.DataTraceMenu;
import com.jy.bean.common.PublishTypeEnum;
import com.jy.bean.dto.BaseDataDTO;
import com.jy.bean.dto.BaseDataDTOs;
import com.jy.bean.po.BatchDetail;
import com.jy.bean.po.ReceiveBatch;
import com.jy.bean.po.RepOutRelation;
import com.jy.bean.po.TransferTableMp;
import com.jy.bean.result.ResultStatus;
import com.jy.service.*;
import com.jy.util.*;
import com.jy.util.graph.IncEdge;
import com.jy.util.graph.IncGraphGroupUtils;
import com.jy.util.graph.IncVertex;
import com.jy.util.rabbitmq.DataTraceUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: caolt
 * @Date: Created in 2020/5/19
 */
@Service
public class ReplaceDataTransform extends DataAbstractTransform {
    private static final Logger logger = LogManager.getLogger(ReplaceDataTransform.class);
    @Value("${srcData.filePath}")
    private String filePath;

    @Autowired
    private CommonUtils commonUtils;
    @Autowired
    private BatchDetailService batchDetailService;
    @Autowired
    private ProcedureService procedureService;
    @Autowired
    private ReceiveBatchService receiveBatchService;
    @Autowired
    private ReplaceTransformService replaceTransformService;
    @Autowired
    private TransferTableMpService transferTableMpService;

    @Override
    public void handle(ReceiveBatch receiveBatch) {
        try{
            DataTraceUtils.sendTrace((JSONObject) JSON.toJSON(receiveBatch), DataTraceMenu.SRC_START_DESC.getName(), ResultStatus.SUCCESS.getStatus(), ResultStatus.SUCCESS.getMessage());
            //1、转换
            transform(receiveBatch);
            //2、对比
            compare(receiveBatch);
            //3、回写
            update(receiveBatch);
            //4、数据推送
            push(receiveBatch, fitPushBatchNo(receiveBatch));
        } catch (Exception e) {
            logger.error("替换件服务处理定时执行失败:" + ToolUtils.getExceptionMsg(e));
            commonUtils.sendWorkWechatPath("mainBatchNo:" + receiveBatch.getMainBatchNo() + "批次处理失败");
        }
    }

    @Override
    public void transform(ReceiveBatch receiveBatch) throws Exception{
        try{
            //1、获取主批次对应的表
            List<BatchDetail> batchDetails = batchDetailService.listByMainBatchNo(receiveBatch.getMainBatchNo());
            //2、清除m库数据
            List<String> list = batchDetails.stream().map(BatchDetail::getTableName).collect(Collectors.toList());
            String tableNames = SqlUtils.List2SqlInString(list);
            List<TransferTableMp> transferTableMps = transferTableMpService.listByBaseTableNames(receiveBatch, tableNames, Constant.YES_STRING);
            transferTableMps.forEach(k->{k.setBaseTableName(null);k.setId(null);});
            transferTableMps = ListUtils.removeDuplicate(transferTableMps);
            for(TransferTableMp transferTableMp: transferTableMps){
                procedureService.truncateMData("m_" + transferTableMp.getTableName());
            }
            for (BatchDetail batchDetail : batchDetails) {
                //1、读取文件
                String json = FileUtils.readToString(filePath + batchDetail.getFilePath());
                BaseDataDTOs baseDataDTOs = JSONObject.parseObject(json, BaseDataDTOs.class);
                List<BaseDataDTO> dataDTOs = baseDataDTOs.getData();
                dataDTOs.forEach(baseDataDTO->{
                    baseDataDTO.getFields().put("OPERATE", baseDataDTO.getOperate());
                    baseDataDTO.setOperate(Constant.OPERATE_INSERT);
                    baseDataDTO.setTableName(baseDataDTOs.getTableName());
                    if(!baseDataDTO.getFields().containsKey("BRAND_ID")){
                        baseDataDTO.getFields().put("BRAND_ID", receiveBatch.getCertainId());
                        baseDataDTO.getFields().put("BRAND_CODE", receiveBatch.getCertainCode());
                        baseDataDTO.getFields().put("BRAND_NAME", receiveBatch.getCertainName());
                    }
                });
                //2、更新入m库
                updateTransformData(dataDTOs, PublishTypeEnum.REPLACER.getCode());
            }
            //2、调用转换存储过程
            //2.1、F库数据插入至p库
            //2.2、根据推送的增量数据查出rel_group_id，并清空这些rel_group_id的值
            //2.3、执行变更数据（增量）
            Map<String,Object> procedureRes = procedureService.replaceProcedure(receiveBatch.getMainBatchNo());
            if(!ResultStatus.SUCCESS.getStatus().equals(procedureRes.get("status"))){
                throw new Exception(procedureRes.get("message").toString());
            }

            //3、查出F库当前品牌下rel_group_id最大值,以及rel_group_id为空的数据、重新计算为空的rel_group_id，更新入库
            String maxRelGroupId = replaceTransformService.getMaxRelGroupId(receiveBatch.getCertainId());
            List<RepOutRelation> repOutRelations = replaceTransformService.getTableDataByNoRelGroupId(receiveBatch.getCertainId());
            List<BaseDataDTO> baseDataDTOList = this.fitInitIncGraph(repOutRelations, maxRelGroupId);
            //更新分组id
            updateTransformData(baseDataDTOList, PublishTypeEnum.REPLACER.getCode());
           // replaceTransformService.updateByRelReplace();
            DataTraceUtils.sendTrace((JSONObject) JSON.toJSON(receiveBatch), DataTraceMenu.SRC_TRANSFER_DESC.getName(), ResultStatus.SUCCESS.getStatus(), ResultStatus.SUCCESS.getMessage());
        } catch (Exception e) {
            receiveBatch.setStatus(BatchNoStatus.ERROR_TRANSFER.getStatus());
            receiveBatchService.update(receiveBatch);
            String message = ToolUtils.getExceptionMsg(e);
            DataTraceUtils.sendTrace((JSONObject)JSON.toJSON(receiveBatch), DataTraceMenu.SRC_TRANSFER_DESC.getName(), ResultStatus.INTERNAL_SERVER_ERROR.getStatus(), "替换件服务转换解析定时执行失败"+message);
            logger.error("替换件服务转换解析定时执行失败: receiveBatch:{}, message:{}" + receiveBatch, message);
            throw new Exception(message);
        }
    }


    private List<BaseDataDTO> fitInitIncGraph(List<RepOutRelation> repOutRelations, String maxRelGroupId) throws Exception {
        List<BaseDataDTO> baseDataDTOs = new ArrayList<>();
        if(EmptyUtils.isEmpty(repOutRelations)){
            return  null;
        }
        int startRelGroupId = EmptyUtils.isEmpty(maxRelGroupId) ? 0 : Integer.parseInt(maxRelGroupId);
        //分组处理
        String brandId = repOutRelations.get(0).getBrandId();
        List<IncEdge<String>> incEdgeList = new ArrayList<>(repOutRelations.size());
        for(RepOutRelation repOutRelation : repOutRelations){
            incEdgeList.add(new IncEdge<>(new IncVertex<>(repOutRelation.getTmpPartNumber()), new IncVertex<>(repOutRelation.getRepTmpPartNumber())));
        }
        IncGraphGroupUtils<String> incGraphGroupUtils = new IncGraphGroupUtils(false, startRelGroupId);
        incGraphGroupUtils.autoGroupInc(incEdgeList);
        incEdgeList = incGraphGroupUtils.graphMapToEdgeList();
        //组装结果
        for(IncEdge<String> incEdge : incEdgeList){
            BaseDataDTO baseDataDTO = new BaseDataDTO();
            baseDataDTO.setTableName("p_" + Constant.REP_TABLE);
            baseDataDTO.setOperate(Constant.OPERATE_UPDATE);
            Map<String, String> keys =  new HashMap<>();
            keys.put(Constant.PART_NUMBER, incEdge.getStart().getLabel());
            keys.put(Constant.REP_PART_NUMBER, incEdge.getEnd().getLabel());
            keys.put("brand_id", brandId);
            Map<String, String> fields =  new HashMap<>();
            fields.put("rel_group_id", incEdge.getGroupId());
            baseDataDTO.setFields(fields);
            baseDataDTO.setKeys(keys);
            baseDataDTOs.add(baseDataDTO);
        }
        Set<BaseDataDTO> baseDataDTOs2 = new HashSet<>();
        for(IncEdge<String> incEdge : incEdgeList){
            BaseDataDTO baseDataDTO = new BaseDataDTO();
            baseDataDTO.setTableName("p_" + Constant.REP_NUMBER_TABLE);
            baseDataDTO.setOperate(Constant.OPERATE_UPDATE);
            Map<String, String> keys =  new HashMap<>();
            keys.put(Constant.PART_NUMBER, incEdge.getStart().getLabel());
            keys.put("brand_id", brandId);
            Map<String, String> fields =  new HashMap<>();
            fields.put("rel_group_id", incEdge.getGroupId());
            baseDataDTO.setFields(fields);
            baseDataDTO.setKeys(keys);
            baseDataDTOs2.add(baseDataDTO);

            BaseDataDTO baseDataDTO1 = new BaseDataDTO();
            baseDataDTO1.setTableName("p_" + Constant.REP_NUMBER_TABLE);
            baseDataDTO1.setOperate(Constant.OPERATE_UPDATE);
            Map<String, String> keys1 =  new HashMap<>();
            keys1.put(Constant.PART_NUMBER, incEdge.getEnd().getLabel());
            keys1.put("brand_id", brandId);
            Map<String, String> fields1 =  new HashMap<>();
            fields1.put("rel_group_id", incEdge.getGroupId());
            baseDataDTO1.setFields(fields1);
            baseDataDTO1.setKeys(keys1);
            baseDataDTOs2.add(baseDataDTO1);
        }
        baseDataDTOs.addAll(baseDataDTOs2);
        return baseDataDTOs;
    }

}

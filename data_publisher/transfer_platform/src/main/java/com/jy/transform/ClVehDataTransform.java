package com.jy.transform;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jy.ann.MethodMonitor;
import com.jy.bean.common.BatchNoStatus;
import com.jy.bean.common.Constant;
import com.jy.bean.common.DataTraceMenu;
import com.jy.bean.common.PublishTypeEnum;
import com.jy.bean.dto.BaseDataDTO;
import com.jy.bean.dto.BaseDataDTOs;
import com.jy.bean.po.BatchDetail;
import com.jy.bean.po.ReceiveBatch;
import com.jy.bean.po.SendDetail;
import com.jy.bean.result.ResultStatus;
import com.jy.service.*;
import com.jy.util.*;
import com.jy.util.rabbitmq.DataTraceUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: caolt
 * @Date: Created in 2020/5/19
 */
@Service
public class ClVehDataTransform extends DataAbstractTransform {
    private static final Logger logger = LogManager.getLogger(ClVehDataTransform.class);

    @Value("${srcData.filePath}")
    private String filePath;

    @Autowired
    private CommonUtils commonUtils;
    @Autowired
    private VehicleService vehicleService;
    @Autowired
    private BatchDetailService batchDetailService;
    @Autowired
    private ReceiveBatchService receiveBatchService;

    @Override
    @MethodMonitor
    public void handle(ReceiveBatch receiveBatch) {
        try {
            DataTraceUtils.sendTrace((JSONObject) JSON.toJSON(receiveBatch), DataTraceMenu.SRC_START_DESC.getName(), ResultStatus.SUCCESS.getStatus(), ResultStatus.SUCCESS.getMessage());
            //1、转换(不走存储过程),1、车型表2、承保理赔对应表 3、flag表
            transform(receiveBatch);
            //2、对比
            compare(receiveBatch);
            //3、回写
            update(receiveBatch);
            //4、数据推送
            push(receiveBatch, fitPushBatchNo(receiveBatch));
        } catch (Exception e) {
            logger.error("车型服务处理定时执行失败:" + ToolUtils.getExceptionMsg(e));
            commonUtils.sendWorkWechatPath("mainBatchNo:" + receiveBatch.getMainBatchNo() + "批次处理失败");
            try{
                pushExpData(receiveBatch, ToolUtils.getExceptionMsg(e));
            }catch (Exception ex){
                logger.error("尝试将轨迹信息发送到发布平台失败:" + ToolUtils.getExceptionMsg(ex));
            }

        }

    }

    @Override
    @MethodMonitor
    public void transform(ReceiveBatch receiveBatch) throws Exception {
        try {
            //1、车型表2、承保理赔对应表 3、flag表 组装转换后进入p库
            List<BatchDetail> batchDetails = batchDetailService.listByMainBatchNo(receiveBatch.getMainBatchNo());
            for (BatchDetail batchDetail : batchDetails) {
                //1、读取文件
                String json = FileUtils.readToString(filePath + batchDetail.getFilePath());
                BaseDataDTOs baseDataDTOs = JSONObject.parseObject(json, BaseDataDTOs.class);
                baseDataDTOs.getData().forEach(baseDataDTO->{
                    baseDataDTO.getFields().put("SCBZ", "0");
                    if(EmptyUtils.isEmpty(baseDataDTO.getFields().get("PART_NUM"))){
                        baseDataDTO.getFields().remove("PART_NUM");
                    } else {
                        //精细化商用车车型涉及真实配件数量用原配件数量覆盖
                        baseDataDTO.getFields().put("REALITY_PART_NUM", baseDataDTO.getFields().get("PART_NUM"));
                    }
                 //   baseDataDTO.getFields().put("REPAIR_NUM", "0");
                    baseDataDTO.getFields().put("ASSIST_NUM", "19");
                    baseDataDTO.getFields().put("TYPE_FLAG", "0");
                    baseDataDTO.getFields().put("EXISTS_FLAG", Constant.YES_STRING);
                });
                //过滤出涉及新增的精细化商用车的车组
                if("m_vehicle_info".equals(batchDetail.getTableName())){
                    Set<String> czids = baseDataDTOs.getData().stream().filter(baseDataDTO->Constant.OPERATE_INSERT.equals(baseDataDTO.getOperate())).map(baseDataDTO -> baseDataDTO.getFields().get("GROUP_ID")).collect(Collectors.toSet());
                    //删除此车组下一代车数据
                    deleteVehByTypeFlag(czids);
                }
                //2、组装
                List<BaseDataDTO> dataDTOs = fitTransformData(baseDataDTOs);
                //3、更新
                updateTransformData(dataDTOs, PublishTypeEnum.CL_VEH.getCode());
            }
            receiveBatch.setStatus(BatchNoStatus.SUCCESS_TRANSFER.getStatus());
            receiveBatchService.update(receiveBatch);
            DataTraceUtils.sendTrace((JSONObject) JSON.toJSON(receiveBatch), DataTraceMenu.SRC_TRANSFER_DESC.getName(), ResultStatus.SUCCESS.getStatus(), ResultStatus.SUCCESS.getMessage());
        } catch (Exception e) {
            receiveBatch.setStatus(BatchNoStatus.ERROR_TRANSFER.getStatus());
            receiveBatchService.update(receiveBatch);
            String message = ToolUtils.getExceptionMsg(e);
            DataTraceUtils.sendTrace((JSONObject) JSON.toJSON(receiveBatch), DataTraceMenu.SRC_TRANSFER_DESC.getName(), ResultStatus.INTERNAL_SERVER_ERROR.getStatus(), "车型服务转换解析定时执行失败"+message);
            logger.error("车型服务转换解析定时执行失败: receiveBatch:{}, message:{}" + receiveBatch, message);
            throw new Exception(message);
        }
    }

    private void deleteVehByTypeFlag(Set<String> czids) throws Exception {
        int batchLimit = 50;
        List<String> list = new ArrayList<>(czids);
        int pageSize = (int)Math.ceil(Double.valueOf(list.size()) / Double.valueOf(batchLimit));
        //分批次调用更新
        for(int i=0; i<pageSize; i++){
            int endNum = i == pageSize -1 ? list.size() : i* batchLimit + batchLimit;
            List<String> temp = list.subList(i* batchLimit, endNum);
            String ids = SqlUtils.List2SqlInString(temp);
            //标识为1的车型与承保理赔对应关系
            vehicleService.deletePjZcCxdybByCzids(ids);
            vehicleService.deleteZcClzlbFlagByCzids(ids);
            vehicleService.deleteZcClzlbByCzids(ids);
        }
    }

}

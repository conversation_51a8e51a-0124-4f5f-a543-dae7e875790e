package com.jy.transform;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jy.ann.MethodMonitor;
import com.jy.bean.common.BatchNoStatus;
import com.jy.bean.common.DataTraceMenu;
import com.jy.bean.common.PublishTypeEnum;
import com.jy.bean.dto.BaseDataDTO;
import com.jy.bean.dto.BaseDataDTOs;
import com.jy.bean.po.BatchDetail;
import com.jy.bean.po.ReceiveBatch;
import com.jy.bean.result.ResultStatus;
import com.jy.service.*;
import com.jy.util.CommonUtils;
import com.jy.util.FileUtils;
import com.jy.util.ToolUtils;
import com.jy.util.rabbitmq.DataTraceUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @Author: caolt
 * @Date: Created in 2020/5/19
 */
@Service
public class AmClDataTransform extends DataAbstractTransform {
    private static final Logger logger = LogManager.getLogger(AmClDataTransform.class);

    @Value("${srcData.filePath}")
    private String filePath;

    @Autowired
    private CommonUtils commonUtils;
    @Autowired
    private BatchDetailService batchDetailService;
    @Autowired
    private ReceiveBatchService receiveBatchService;


    @Override
    @MethodMonitor
    public void handle(ReceiveBatch receiveBatch) {
        try {
            DataTraceUtils.sendTrace((JSONObject) JSON.toJSON(receiveBatch), DataTraceMenu.SRC_START_DESC.getName(), ResultStatus.SUCCESS.getStatus(), ResultStatus.SUCCESS.getMessage());
            //1、转换(不走存储过程),1、后市场理赔对应表
            transform(receiveBatch);
            //2、对比
            compare(receiveBatch);
            //3、回写
            update(receiveBatch);
            //4、数据推送
            push(receiveBatch, fitPushBatchNo(receiveBatch));
        } catch (Exception e) {
            logger.error("后市场理赔关系服务处理定时执行失败:" + ToolUtils.getExceptionMsg(e));
            commonUtils.sendWorkWechatPath("mainBatchNo:" + receiveBatch.getMainBatchNo() + "批次处理失败");
        }

    }

    @Override
    @MethodMonitor
    public void transform(ReceiveBatch receiveBatch) throws Exception {
        try {
            //1、车型表2、承保理赔对应表 3、flag表 组装转换后进入p库
            List<BatchDetail> batchDetails = batchDetailService.listByMainBatchNo(receiveBatch.getMainBatchNo());
            for (BatchDetail batchDetail : batchDetails) {
                //1、读取文件
                String json = FileUtils.readToString(filePath + batchDetail.getFilePath());
                BaseDataDTOs baseDataDTOs = JSONObject.parseObject(json, BaseDataDTOs.class);
                baseDataDTOs.getData().forEach(baseDataDTO->{
                    baseDataDTO.getFields().put("SCBZ", "0");
                });
                //2、组装
                List<BaseDataDTO> dataDTOs = fitTransformData(baseDataDTOs);
                //3、更新
                updateTransformData(dataDTOs, PublishTypeEnum.AM_CL.getCode());
            }
            receiveBatch.setStatus(BatchNoStatus.SUCCESS_TRANSFER.getStatus());
            receiveBatchService.update(receiveBatch);
            DataTraceUtils.sendTrace((JSONObject) JSON.toJSON(receiveBatch), DataTraceMenu.SRC_TRANSFER_DESC.getName(), ResultStatus.SUCCESS.getStatus(), ResultStatus.SUCCESS.getMessage());
        } catch (Exception e) {
            receiveBatch.setStatus(BatchNoStatus.ERROR_TRANSFER.getStatus());
            receiveBatchService.update(receiveBatch);
            String message = ToolUtils.getExceptionMsg(e);
            DataTraceUtils.sendTrace((JSONObject) JSON.toJSON(receiveBatch), DataTraceMenu.SRC_TRANSFER_DESC.getName(), ResultStatus.INTERNAL_SERVER_ERROR.getStatus(), "后市场理赔关系服务转换解析定时执行失败"+message);
            logger.error("后市场理赔关系服务转换解析定时执行失败: receiveBatch:{}, message:{}" + receiveBatch, message);
            throw new Exception(message);
        }
    }

}

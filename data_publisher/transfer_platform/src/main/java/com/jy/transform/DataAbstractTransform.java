package com.jy.transform;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jy.ann.MethodMonitor;
import com.jy.bean.common.BatchNoStatus;
import com.jy.bean.common.Constant;
import com.jy.bean.common.DataTraceMenu;
import com.jy.bean.common.PublishTypeEnum;
import com.jy.bean.dto.BaseDataDTO;
import com.jy.bean.dto.BaseDataDTOs;
import com.jy.bean.po.*;
import com.jy.bean.result.ResultStatus;
import com.jy.exception.CompareException;
import com.jy.service.*;
import com.jy.util.*;
import com.jy.util.rabbitmq.DataTraceUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/5/20
 */
@Component
public abstract class DataAbstractTransform implements DataTransform {
    private static final Logger logger = LogManager.getLogger(DataAbstractTransform.class);

    @Value("${transform.batchLimit}")
    private int batchToplimit = 500;
    @Value("${transform.pushLimit}")
    private int pushToplimit = 10000;


    @Autowired
    private CommonService commonService;
    @Autowired
    private MidDataService midDataService;
    @Autowired
    private SendDataService sendDataService;
    @Autowired
    private ProcedureService procedureService;
    @Autowired
    private SendDetailService sendDetailService;
    @Autowired
    private BatchDetailService batchDetailService;
    @Autowired
    private ReceiveBatchService receiveBatchService;
    @Autowired
    private TransferTableMpService transferTableMpService;
    @Autowired
    private TransferTableFieldMpService transferTableFieldMpService;

    @Override
    @MethodMonitor
    public void updateTransformData(List<BaseDataDTO> dataDTOs, String type) throws Exception {
        if(EmptyUtils.isEmpty(dataDTOs)){
            return;
        }
        int pageSize = (int)Math.ceil(Double.valueOf(dataDTOs.size()) / Double.valueOf(batchToplimit));
        //分批次调用更新
        for(int i=0; i<pageSize; i++){
            int endNum = i == pageSize -1 ? dataDTOs.size() : i* batchToplimit + batchToplimit;
            List<BaseDataDTO> temp = dataDTOs.subList(i* batchToplimit, endNum);
            if(PublishTypeEnum.PART.getCode().equals(type)){
                midDataService.jdbcInsertBatch(temp);
            } else {
                midDataService.doBatch(temp);
            }
        }
    }

    @Override
    @MethodMonitor
    public void compare(ReceiveBatch receiveBatch) throws Exception {
        try{
            List<BatchDetail> batchDetails = batchDetailService.listByMainBatchNo(receiveBatch.getMainBatchNo());
            List<String> list = batchDetails.stream().map(BatchDetail::getTableName).collect(Collectors.toList());
            String tableNames = SqlUtils.List2SqlInString(list);
            List<TransferTableMp> transferTableMps = transferTableMpService.listByBaseTableNames(receiveBatch, tableNames, Constant.YES_STRING);
            transferTableMps.forEach(k->{k.setBaseTableName(null);k.setId(null);});
            transferTableMps = ListUtils.removeDuplicate(transferTableMps);
            for(int i=0; i<transferTableMps.size(); i++){
                String endFlag = i == transferTableMps.size() - 1 ? "1" : "0";
                Map<String,Object> result = procedureService.compareProcedure(receiveBatch.getMainBatchNo(), transferTableMps.get(i).getTableName(), transferTableMps.get(i).getSuffixFlag(), endFlag);
                if(!ResultStatus.SUCCESS.getStatus().equals(result.get("status"))){
                    throw new CompareException(transferTableMps.get(i).getTableName() + ":" + result.get("message").toString());
                }
            }
            //对比出车组车系品牌维度flag表变更
            Map<String,Object> result = procedureService.compareFlagProcedure(receiveBatch.getMainBatchNo());
            if(!ResultStatus.SUCCESS.getStatus().equals(result.get("status"))){
                throw new CompareException("flag:" + result.get("message").toString());
            }
            DataTraceUtils.sendTrace((JSONObject)JSON.toJSON(receiveBatch), DataTraceMenu.SRC_COMPARE_DESC.getName(), ResultStatus.SUCCESS.getStatus(), ResultStatus.SUCCESS.getMessage());
        } catch (Exception e) {
            String message = ToolUtils.getExceptionMsg(e);
            DataTraceUtils.sendTrace((JSONObject)JSON.toJSON(receiveBatch), DataTraceMenu.SRC_COMPARE_DESC.getName(), ResultStatus.INTERNAL_SERVER_ERROR.getStatus(), "服务对比阶段定时执行失败"+message);
            logger.error("服务对比阶段定时执行失败: receiveBatch:{}, message:{}" + receiveBatch, message);
            throw new Exception(message);
        }
    }

    @Override
    @MethodMonitor
    public void update(ReceiveBatch receiveBatch) throws Exception {
        try{
            List<BatchDetail> batchDetails = batchDetailService.listByMainBatchNo(receiveBatch.getMainBatchNo());
            List<String> list = batchDetails.stream().map(BatchDetail::getTableName).collect(Collectors.toList());
            String tableNames = SqlUtils.List2SqlInString(list);
            List<TransferTableMp> transferTableMps = transferTableMpService.listByBaseTableNames(receiveBatch, tableNames, Constant.YES_STRING);
            transferTableMps.forEach(k->{k.setBaseTableName(null);k.setId(null);});
            transferTableMps = ListUtils.removeDuplicate(transferTableMps);
            for(int i=0; i<transferTableMps.size(); i++){
                String endFlag = i == transferTableMps.size() - 1 ? "1" : "0";
                Map<String,Object> result = procedureService.updateProcedure(receiveBatch.getMainBatchNo(), transferTableMps.get(i).getTableName(), transferTableMps.get(i).getSuffixFlag(), endFlag);
                if(!ResultStatus.SUCCESS.getStatus().equals(result.get("status"))){
                    throw new CompareException(result.get("message").toString());
                }
            }
            DataTraceUtils.sendTrace((JSONObject)JSON.toJSON(receiveBatch), DataTraceMenu.SRC_UPDATE_DESC.getName(), ResultStatus.SUCCESS.getStatus(), ResultStatus.SUCCESS.getMessage());
        } catch (Exception e) {
            String message = ToolUtils.getExceptionMsg(e);
            DataTraceUtils.sendTrace((JSONObject)JSON.toJSON(receiveBatch), DataTraceMenu.SRC_UPDATE_DESC.getName(), ResultStatus.INTERNAL_SERVER_ERROR.getStatus(), "服务回写阶段定时执行失败"+message);
            logger.error("服务回写阶段定时执行失败: receiveBatch:{}, message:{}" + receiveBatch, message);
            throw new Exception(message);
        }
    }

    @Override
    @MethodMonitor
    public List<SendDetail> fitPushSendDetail(String mainBatchNo, String tableName, String partSuffix, Integer batchIndex) throws Exception {
        List<SendDetail> datas = new ArrayList<>();
        String cTableName = Constant.C_DB_SUFFIX + tableName;
        if(EmptyUtils.isNotEmpty(partSuffix)){
            cTableName = cTableName + "_" + partSuffix;
        }
        int total = commonService.getTableCountByTableNameAndBatchNo(cTableName, mainBatchNo);
        if(0 == total){
            String batchNo = BatchUtils.getBatchNoByMainBatchNo(mainBatchNo, batchIndex,0);
            SendDetail sendDetail = new SendDetail(mainBatchNo, batchNo, tableName, cTableName, total);
            sendDetail.setStatus(ResultStatus.NO_DATA.getStatus());
            sendDetailService.save(sendDetail);
            datas.add(sendDetail);
        } else {
            int pageSize = (int)Math.ceil(Double.valueOf(total) / Double.valueOf(pushToplimit));
            for(int i=0; i<pageSize; i++){
                int size = (i !=pageSize -1) ? pushToplimit : (total - i * pushToplimit);
                String batchNo = BatchUtils.getBatchNoByMainBatchNo(mainBatchNo, batchIndex, i+1);
                SendDetail sendDetail = new SendDetail(mainBatchNo, batchNo, tableName, cTableName, size);
                sendDetailService.save(sendDetail);
                datas.add(sendDetail);
            }
        }
        return datas;
    }

    @Override
    @MethodMonitor
    public List<SendDetail> fitPushBatchNo(ReceiveBatch receiveBatch) throws Exception {
        //组装将要推送的数据
        List<SendDetail> datas = new ArrayList<>();
        try{
            //获取将要推送的表
            List<BatchDetail> batchDetails = batchDetailService.listByMainBatchNo(receiveBatch.getMainBatchNo());
            List<String> list = batchDetails.stream().map(BatchDetail::getTableName).collect(Collectors.toList());
            String tableNames = SqlUtils.List2SqlInString(list);
            List<TransferTableMp> transferTableMps = transferTableMpService.listByBaseTableNames(receiveBatch, tableNames, null);
            transferTableMps.forEach(k->{k.setBaseTableName(null);k.setId(null);});
            transferTableMps = ListUtils.removeDuplicate(transferTableMps);

            int tableIndex = 0;
            for(TransferTableMp transferTableMp :transferTableMps){
                tableIndex ++;
                String partSuffix = "";
                if(Constant.YES_STRING.equals(transferTableMp.getSuffixFlag())){
                    partSuffix = commonService.getPartSuffix(receiveBatch.getCertainId());
                }
                List<SendDetail> temp = this.fitPushSendDetail(receiveBatch.getMainBatchNo(), transferTableMp.getTableName(), partSuffix, tableIndex);
                datas.addAll(temp);
            }
        } catch (Exception e) {
            receiveBatch.setStatus(BatchNoStatus.ERROR_FIT.getStatus());
            receiveBatchService.update(receiveBatch);
            String message = ToolUtils.getExceptionMsg(e);
            DataTraceUtils.sendTrace((JSONObject)JSON.toJSON(receiveBatch), DataTraceMenu.SRC_PUSH_DESC.getName(), ResultStatus.INTERNAL_SERVER_ERROR.getStatus(), "服务数据组装推送数据阶段定时执行失败"+message);
            logger.error("服务数据组装推送数据阶段定时执行失败: receiveBatch:{}, message:{}" + receiveBatch, message);
            throw new Exception(message);
        }
        return datas;
    }

    @Override
    @MethodMonitor
    public void push(ReceiveBatch receiveBatch, List<SendDetail> sendDetails) throws Exception {
        String clientCode = EmptyUtils.isNotEmpty(receiveBatch.getClientCode()) ? receiveBatch.getClientCode() : null;
        this.push(receiveBatch, sendDetails, clientCode);
    }

    @Override
    @MethodMonitor
    public void push(ReceiveBatch receiveBatch, List<SendDetail> datas, String clientCode) throws Exception {
        try{
            //保存发送信息，推送数据
            List<SendDetail> sendDetailList = datas.stream().filter(sendDetail -> !ResultStatus.NO_DATA.getStatus().equals(sendDetail.getStatus()) && !ResultStatus.SUCCESS.getStatus().equals(sendDetail.getStatus())).collect(Collectors.toList());
            CountDownLatch countDownLatch = new CountDownLatch(sendDetailList.size());
            for(SendDetail sendDetail : sendDetailList){
                sendDataService.sendData(receiveBatch, sendDetail, clientCode, countDownLatch);
            }
            countDownLatch.await();
            if(EmptyUtils.isEmpty(sendDetailList)){
                DataTraceUtils.sendTrace((JSONObject)JSON.toJSON(receiveBatch), DataTraceMenu.SRC_NO_PUSH_DESC.getName(), ResultStatus.SUCCESS.getStatus(), ResultStatus.SUCCESS.getMessage());
            }
            //pushTransData(receiveBatch, datas, clientCode);
            List<SendDetail> sendDetails = sendDetailService.listByMainBatchNo(receiveBatch.getMainBatchNo());
            sendDetails = sendDetails.stream().filter(sendDetail -> !ResultStatus.NO_DATA.getStatus().equals(sendDetail.getStatus()) && !ResultStatus.SUCCESS.getStatus().equals(sendDetail.getStatus())).collect(Collectors.toList());
            if(EmptyUtils.isEmpty(sendDetails)){
                receiveBatch.setStatus(BatchNoStatus.SUCCESS.getStatus());
                receiveBatchService.update(receiveBatch);
            } else {
             //   Constant.IS_CONTINUE = false;
                throw new Exception("转换平台数据推送失败");
               // receiveBatch.setStatus(BatchNoStatus.ERROR_SEND.getStatus());
              //  receiveBatchService.update(receiveBatch);
            }

        } catch (Exception e) {
            Constant.IS_CONTINUE = false;
            receiveBatch.setStatus(BatchNoStatus.ERROR_SEND.getStatus());
            receiveBatchService.update(receiveBatch);
            String message = ToolUtils.getExceptionMsg(e);
            DataTraceUtils.sendTrace((JSONObject)JSON.toJSON(receiveBatch), DataTraceMenu.SRC_PUSH_DESC.getName(), ResultStatus.INTERNAL_SERVER_ERROR.getStatus(), message);
            logger.error("服务数据推送阶段定时执行失败: receiveBatch:{}, message:{}" + receiveBatch, message);
            throw new Exception(message);
        }
    }

    public void pushExpData(ReceiveBatch receiveBatch, String exceptionMsg) {
        String clientCode = EmptyUtils.isNotEmpty(receiveBatch.getClientCode()) ? receiveBatch.getClientCode() : null;
        sendDataService.sendExpData(receiveBatch, clientCode, exceptionMsg);
    }

    @Override
    @MethodMonitor
    public List<BaseDataDTO> fitTransformData(BaseDataDTOs baseDataDTOs) throws Exception {
        List<BaseDataDTO> result = new ArrayList<>();
        //配件发送出来的表对应产品结构有可能为多张表
        List<TransferTableMp> list = transferTableMpService.listByBaseTableNameAndTransferFlag(baseDataDTOs.getTableName(), Constant.YES_STRING);
        for (TransferTableMp transferTableMp : list) {
            Map<String, List<TransferTableFieldMp>> fieldMpMap = transferTableFieldMpService.mapByTableName(transferTableMp.getTableName());
            //更换表名，更换字段，过滤字段
            List<BaseDataDTO> data = baseDataDTOs.getData();
            for (BaseDataDTO baseDataDTO : data) {
                Map<String, String> fields = baseDataDTO.getFields();
                BaseDataDTO baseDataDTO1 = new BaseDataDTO();
                baseDataDTO1.setTableName(Constant.P_DB_SUFFIX + transferTableMp.getTableName());
                baseDataDTO1.setFields(fitDatas(fields, fieldMpMap));
                baseDataDTO1.setKeys(fitDatas(baseDataDTO.getKeys(), fieldMpMap));
                baseDataDTO1.setOperate(baseDataDTO.getOperate());
                result.add(baseDataDTO1);
            }
        }
        return result;

    }


    private Map<String, String> fitDatas(Map<String, String> map, Map<String, List<TransferTableFieldMp>> fieldMpMap) {
        Map<String, String> result = new HashMap<>();
        map.forEach((k, v) -> {
            if (fieldMpMap.containsKey(k)) {
                List<TransferTableFieldMp> values = fieldMpMap.get(k);
                for(TransferTableFieldMp value : values){
                    result.put(value.getTableField(), v);
                }

            }
        });
        return result;
    }
}

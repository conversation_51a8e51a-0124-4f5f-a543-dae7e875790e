package com.jy.transform;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jy.ann.MethodMonitor;
import com.jy.bean.common.BatchNoStatus;
import com.jy.bean.common.Constant;
import com.jy.bean.common.DataTraceMenu;
import com.jy.bean.common.PublishTypeEnum;
import com.jy.bean.dto.BaseDataDTO;
import com.jy.bean.dto.BaseDataDTOs;
import com.jy.bean.po.BatchDetail;
import com.jy.bean.po.ReceiveBatch;
import com.jy.bean.result.ResultStatus;
import com.jy.service.*;
import com.jy.util.*;
import com.jy.util.graph.IncEdge;
import com.jy.util.graph.IncGraphGroupUtils;
import com.jy.util.graph.IncVertex;
import com.jy.util.rabbitmq.DataTraceUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @Author: caolt
 * @Description:
 * @Version:
 * @Date: Created in  2020/08/07
 */
@Service
public class OriRepairTransform extends DataAbstractTransform {
    private static final Logger logger = LogManager.getLogger(OriRepairTransform.class);

    @Value("${srcData.filePath}")
    private String filePath;
    @Value("${httpUtils.part.partUrl}")
    private String partUrl;
    @Value("${httpUtils.part.svgPicPath}")
    private String svgtPath;
    @Autowired
    private CommonUtils commonUtils;
    @Autowired
    private CommonService commonService;
    @Autowired
    private ProcedureService procedureService;
    @Autowired
    private BatchDetailService batchDetailService;
    @Autowired
    private ReceiveBatchService receiveBatchService;

    @Override
    @MethodMonitor
    public void handle(ReceiveBatch receiveBatch) {
        try {
            DataTraceUtils.sendTrace((JSONObject) JSON.toJSON(receiveBatch), DataTraceMenu.SRC_START_DESC.getName(), ResultStatus.SUCCESS.getStatus(), ResultStatus.SUCCESS.getMessage());
            //1、转换(不走存储过程),直接写入p库
            transform(receiveBatch);
            //2、对比
            compare(receiveBatch);
            //3、回写
            update(receiveBatch);
            //通知配件加工平台上传图片数据
            notice(receiveBatch);
            //4、数据推送
            push(receiveBatch, fitPushBatchNo(receiveBatch));
        } catch (Exception e) {
            logger.error("原厂工时服务处理定时执行失败:" + ToolUtils.getExceptionMsg(e));
            commonUtils.sendWorkWechatPath("mainBatchNo:" + receiveBatch.getMainBatchNo() + "批次处理失败");
        }
    }

    @Override
    @MethodMonitor
    public void transform(ReceiveBatch receiveBatch) throws Exception {
        try {
            //1、车型表2、承保理赔对应表 3、flag表 组装转换后进入p库
            List<BatchDetail> batchDetails = batchDetailService.listByMainBatchNo(receiveBatch.getMainBatchNo());
            //2、清除p库数据
            for(BatchDetail batchDetail: batchDetails){
                procedureService.truncatePData(batchDetail.getTableName());
            }
            List<BaseDataDTO> incList = new ArrayList<>();
            for (BatchDetail batchDetail : batchDetails) {
                //1、读取文件
                String json = FileUtils.readToString(filePath + batchDetail.getFilePath());
                BaseDataDTOs baseDataDTOs = JSONObject.parseObject(json, BaseDataDTOs.class);

                //2、组装
                List<BaseDataDTO> dataDTOs = fitOriTransformData(baseDataDTOs);
                if(batchDetail.getTableName().toLowerCase().contains(Constant.INC_TABLE)){
                    incList.addAll(dataDTOs);
                }
                //2、更新
                updateTransformData(dataDTOs, PublishTypeEnum.ORI_REPAIR.getCode());
            }
            //2、初始化、更新 原厂工时坐标点分组
            this.fitInitIncGraph(receiveBatch.getCertainId(), incList);
            commonService.updateIncGraphGroup(receiveBatch.getCertainId());

            receiveBatch.setStatus(BatchNoStatus.SUCCESS_TRANSFER.getStatus());
            receiveBatchService.update(receiveBatch);
            DataTraceUtils.sendTrace((JSONObject) JSON.toJSON(receiveBatch), DataTraceMenu.SRC_TRANSFER_DESC.getName(), ResultStatus.SUCCESS.getStatus(), ResultStatus.SUCCESS.getMessage());
        } catch (Exception e) {
            receiveBatch.setStatus(BatchNoStatus.ERROR_TRANSFER.getStatus());
            receiveBatchService.update(receiveBatch);
            String message = ToolUtils.getExceptionMsg(e);
            DataTraceUtils.sendTrace((JSONObject) JSON.toJSON(receiveBatch), DataTraceMenu.SRC_TRANSFER_DESC.getName(), ResultStatus.INTERNAL_SERVER_ERROR.getStatus(), "原厂工时服务解析定时执行失败"+message);
            logger.error("原厂工时服务解析定时执行失败: receiveBatch:{}, message:{}" + receiveBatch, message);
            throw new Exception(message);
        }
    }

    private void fitInitIncGraph(String clVehicleId, List<BaseDataDTO> incList) throws Exception {
        List<BaseDataDTO> baseDataDTOs = new ArrayList<>();
        List<IncEdge<String>> incEdgeList = new ArrayList<>();
        for(BaseDataDTO baseDataDTO : incList){
            Map<String, String> fields = baseDataDTO.getFields();
            incEdgeList.add(new IncEdge<>(new IncVertex<>(fields.get(Constant.INC_TABLE_F_INC.toUpperCase())), new IncVertex<>(fields.get(Constant.INC_TABLE_INC.toUpperCase()))));
        }
        IncGraphGroupUtils<String> incGraphGroupUtils = new IncGraphGroupUtils();
        incGraphGroupUtils.autoGroupInc(incEdgeList);
        incEdgeList = incGraphGroupUtils.graphMapToEdgeList();
        for(IncEdge<String> incEdge : incEdgeList){
            BaseDataDTO baseDataDTO = new BaseDataDTO();
            baseDataDTO.setTableName("p_" + Constant.INC_TABLE);
            baseDataDTO.setOperate(Constant.OPERATE_UPDATE);
            Map<String, String> keys =  new HashMap<>();
            keys.put(Constant.INC_TABLE_F_INC, incEdge.getStart().getLabel());
            keys.put(Constant.INC_TABLE_INC, incEdge.getEnd().getLabel());
            keys.put("cl_vehicle_id", clVehicleId);
            Map<String, String> fields =  new HashMap<>();
            fields.put("inc_group_id", incEdge.getGroupId());
            baseDataDTO.setFields(fields);
            baseDataDTO.setKeys(keys);
            baseDataDTOs.add(baseDataDTO);
        }

        updateTransformData(baseDataDTOs, PublishTypeEnum.ORI_REPAIR.getCode());
    }


    private List<BaseDataDTO> fitOriTransformData(BaseDataDTOs baseDataDTOs) {
        //更换表名
        List<BaseDataDTO> data = baseDataDTOs.getData();
        for (BaseDataDTO baseDataDTO : data) {
            baseDataDTO.setTableName(Constant.P_DB_SUFFIX + baseDataDTOs.getTableName());
            baseDataDTO.setOperate(Constant.OPERATE_INSERT);
        }
        return data;
    }

    /**
     * 通知配件加工平台数据已经处理完成，可以将矢量图上传腾讯云
     * @param receiveBatch
     */
    private void notice(ReceiveBatch receiveBatch){
        Map<String, String> querys = new HashMap<>();
        querys.put("mainBatchNo", receiveBatch.getMainBatchNo());
        //失败重复发送  最多三次
        String status = ResultStatus.INTERNAL_SERVER_ERROR.getStatus();
        int i = 0;
        while(i < 3){
            JSONObject result = null;
            try {
                String response = HttpUtils.doPost(partUrl, svgtPath, new HashMap<>(), querys, "");
                result = JSONObject.parseObject(response);
                logger.info("notice回调结果:" + result);
            } catch (Exception e) {
                status = ResultStatus.INTERNAL_SERVER_ERROR.getStatus();
                logger.error("notice通知配件加工平台数据处理完成结果失败，message: {}, mainBatchNo: {}", ToolUtils.getExceptionMsg(e), receiveBatch.getMainBatchNo());
            }
            if(EmptyUtils.isNotEmpty(result) && (result.getString("status").equals(ResultStatus.SUCCESS.getStatus()) || result.getString("status").equals(ResultStatus.NO_DATA.getStatus()))){
                status = result.getString("status");
                break;
            }
            i++;
        }
        ReceiveBatch receiveBatch1 = new ReceiveBatch();
        receiveBatch1.setMainBatchNo(receiveBatch.getMainBatchNo());
        receiveBatch1.setNoticeStatus(status);
        receiveBatchService.update(receiveBatch1);
    }
}

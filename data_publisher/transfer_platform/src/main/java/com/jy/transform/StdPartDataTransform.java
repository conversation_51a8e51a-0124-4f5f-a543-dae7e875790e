package com.jy.transform;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jy.bean.common.BatchNoStatus;
import com.jy.bean.common.Constant;
import com.jy.bean.common.DataTraceMenu;
import com.jy.bean.common.PublishTypeEnum;
import com.jy.bean.dto.BaseDataDTOs;
import com.jy.bean.po.BatchDetail;
import com.jy.bean.po.ReceiveBatch;
import com.jy.bean.result.ResultStatus;
import com.jy.service.BatchDetailService;
import com.jy.service.CommonService;
import com.jy.service.ProcedureService;
import com.jy.service.ReceiveBatchService;
import com.jy.util.CommonUtils;
import com.jy.util.EmptyUtils;
import com.jy.util.FileUtils;
import com.jy.util.ToolUtils;
import com.jy.util.rabbitmq.DataTraceUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: caolt
 * @Date: Created in 2020/5/19
 */
@Service
public class StdPartDataTransform extends DataAbstractTransform {
    private static final Logger logger = LogManager.getLogger(StdPartDataTransform.class);
    @Value("${srcData.filePath}")
    private String filePath;

    @Autowired
    private CommonUtils commonUtils;
    @Autowired
    private BatchDetailService batchDetailService;
    @Autowired
    private ProcedureService procedureService;
    @Autowired
    private ReceiveBatchService receiveBatchService;

    @Override
    public void handle(ReceiveBatch receiveBatch) {
        try{
            DataTraceUtils.sendTrace((JSONObject) JSON.toJSON(receiveBatch), DataTraceMenu.SRC_START_DESC.getName(), ResultStatus.SUCCESS.getStatus(), ResultStatus.SUCCESS.getMessage());
            //1、转换
            transform(receiveBatch);
            //2、对比
            compare(receiveBatch);
            //3、回写
            update(receiveBatch);
            //4、数据推送
            push(receiveBatch, fitPushBatchNo(receiveBatch));
        } catch (Exception e) {
            logger.error("配件服务处理定时执行失败:" + ToolUtils.getExceptionMsg(e));
            commonUtils.sendWorkWechatPath("mainBatchNo:" + receiveBatch.getMainBatchNo() + "批次处理失败");
        }
    }

    @Override
    public void transform(ReceiveBatch receiveBatch) throws Exception{
        try{
            //1、入m库
            List<BatchDetail> batchDetails = batchDetailService.listByMainBatchNo(receiveBatch.getMainBatchNo());
            //2、清除m库数据
            for(BatchDetail batchDetail: batchDetails){
                procedureService.truncateMData(batchDetail.getTableName());
            }
            List<String> paths = batchDetails.stream().map(BatchDetail::getFilePath).collect(Collectors.toList());
            for(String path : paths){
                //1、读取文件
                String json = FileUtils.readToString(filePath + path);
                BaseDataDTOs baseDataDTOs = JSONObject.parseObject(json, BaseDataDTOs.class);
                baseDataDTOs.getData().forEach(baseDataDTO->{
                    baseDataDTO.setOperate(Constant.OPERATE_INSERT);
                    baseDataDTO.setTableName(baseDataDTOs.getTableName());
                });
                //2、更新入m库
                updateTransformData(baseDataDTOs.getData(), PublishTypeEnum.STD_PART.getCode());
            }
            //2、调用转换存储过程
            Map<String,Object> procedureRes = procedureService.stdPartProcedure(receiveBatch.getMainBatchNo());
            if(!ResultStatus.SUCCESS.getStatus().equals(procedureRes.get("status"))){
                throw new Exception(procedureRes.get("message").toString());
            }
            DataTraceUtils.sendTrace((JSONObject) JSON.toJSON(receiveBatch), DataTraceMenu.SRC_TRANSFER_DESC.getName(), ResultStatus.SUCCESS.getStatus(), ResultStatus.SUCCESS.getMessage());
        } catch (Exception e) {
            receiveBatch.setStatus(BatchNoStatus.ERROR_TRANSFER.getStatus());
            receiveBatchService.update(receiveBatch);
            String message = ToolUtils.getExceptionMsg(e);
            DataTraceUtils.sendTrace((JSONObject)JSON.toJSON(receiveBatch), DataTraceMenu.SRC_TRANSFER_DESC.getName(), ResultStatus.INTERNAL_SERVER_ERROR.getStatus(), "标准零件服务转换解析定时执行失败"+message);
            logger.error("标准零件服务转换解析定时执行失败: receiveBatch:{}, message:{}" + receiveBatch, message);
            throw new Exception(message);
        }
    }
}

package com.jy.transform;

import com.jy.util.BatchUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: caolt
 * @Date: Created in 2020/5/19
 */
@Component
public class TransformFactory {

    @Autowired
    private ClVehDataTransform clVehDataTransform;
    @Autowired
    private PartDataTransform partDataTransform;
    @Autowired
    private ReplaceDataTransform replaceDataTransform;
    @Autowired
    private StdPartDataTransform stdPartDataTransform;
    @Autowired
    private OriRepairTransform oriRepairTransform;
    @Autowired
    private AmClDataTransform amClDataTransform;

    public DataTransform create(String batchNo) {
        String type = BatchUtils.getTypeByBatchNo(batchNo);
        switch(type){
            case "Part":
                return partDataTransform;
            //先注释掉这几个case
            // case "ClBrand":
            // case "ClBrandFlag":
            // case "ClSeries":
            // case "ClSeriesFlag":
            // case "ClGroup":
            // case "CLGroupFlag":
            // case "ClFactory":
            case "ClVeh":
                return clVehDataTransform;
            case "Replacer":
                return replaceDataTransform;
            case "StdPart":
                return stdPartDataTransform;
            case "OriRepair":
                return oriRepairTransform;
            case "AmCl":
                return amClDataTransform;
            default:
                return null;
        }
    }
}

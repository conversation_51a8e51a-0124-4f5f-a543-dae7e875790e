package com.jy.util;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: zy
 * @Description: facade接口工具
 * @Version: 1.0.0
 * @Date: Created in 2018/1/4
 */
public abstract class BaseFacadeUtils {

    protected static Map<String, Auth> authMap = new HashMap<>();

    @Data
    public class Auth {
        /**环境地址*/
        private String url;
        /**用户名*/
        private String username;
        /**密码*/
        private String password;
        /**认证秘钥*/
        private String authorization;
        /**token*/
        private String token;
    }

    /**
     * 初始化参数
     * @return
     */
    public abstract void initParam();

    /**
     * 从缓存中获取token
     * @return
     */
    public abstract String getTokenCache(String authKey) throws Exception;

    /**
     * 更新缓存中的token
     * @return
     */
    public abstract void updateTokenCache(String authKey,String token) throws Exception;

    /**
     * 通过用户名密码获取token
     * @param
     * @return
     * @throws Exception
     */
    private String getTokenHttp(String url) throws Exception {
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", "Basic " + authMap.get(url).getAuthorization());
        Map<String, String> querys = new HashMap<String, String>();
        querys.put("grant_type", "password");
        querys.put("username", authMap.get(url).getUsername());
        querys.put("password",  authMap.get(url).getPassword());
        String response = HttpUtils.doPost(url, "auth-service/oauth/token", headers, querys, "");
        JSONObject jsonObject = JSONObject.parseObject(response);
        String accessToken = (String) jsonObject.get("access_token");
        updateTokenCache(url, accessToken);
        return accessToken;
    }

    public JSONObject doPost(String url, String path, Map<String, String> querys, String body) throws Exception {
        String token = getTokenCache(url);
        Map<String, String> headers = new HashMap<>();
        headers.put("access_token", token);
        String response = HttpUtils.doPost(url, path, headers, querys, body);
        JSONObject jsonObject = JSONObject.parseObject(response);
        if("401".equals(jsonObject.get("status"))){
            String access_token = getTokenHttp(url);
            headers.put("access_token", access_token);
            response = HttpUtils.doPost(url, path, headers, querys, body);
            jsonObject = JSONObject.parseObject(response);
        }
        return jsonObject;
    }

    public JSONObject doPut(String url, String path, Map<String, String> querys, String body) throws Exception {
        String token = getTokenCache(url);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("access_token", token);
        String response = HttpUtils.doPut(url, path, headers, querys, body);
        JSONObject jsonObject = JSONObject.parseObject(response);
        if("401".equals(jsonObject.get("status"))){
            String access_token = getTokenHttp(url);
            headers.put("access_token", access_token);
            response = HttpUtils.doPut(url, path, headers, querys, body);
            jsonObject = JSONObject.parseObject(response);
        }
        return jsonObject;
    }

    public JSONObject doDelete(String url, String path,
                               Map<String, String> querys) throws Exception {
        String token = getTokenCache(url);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("access_token", token);
        String response = HttpUtils.doDelete(url, path, headers, querys);
        JSONObject jsonObject = JSONObject.parseObject(response);
        if("401".equals(jsonObject.get("status"))){
            String access_token = getTokenHttp(url);
            headers.put("access_token", access_token);
            response = HttpUtils.doDelete(url, path, headers, querys);
            jsonObject = JSONObject.parseObject(response);
        }
        return jsonObject;
    }


    /**
     * @param path    资源路径
     * @param querys    参数
     * @return
     * @throws Exception
     */
    public JSONObject doGet(String url, String path, Map<String, String> querys) throws Exception {
        String token = getTokenCache(url);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("access_token", token);
        String response = HttpUtils.doGet(url, path, headers, querys);
        JSONObject jsonObject = JSONObject.parseObject(response);
        if("401".equals(jsonObject.get("status"))){
            String access_token = getTokenHttp(url);
            headers.put("access_token", access_token);
            response = HttpUtils.doGet(url, path, headers, querys);
            jsonObject = JSONObject.parseObject(response);
        }
        return jsonObject;
    }

}

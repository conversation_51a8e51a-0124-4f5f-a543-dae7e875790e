package com.jy.util;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
/**
 * @Author: zy
 * @Date: Created in 2019/11/5
 */
public class ToolUtils {

    /**
     * 获取异常的具体信息
     */
    public static String getExceptionMsg(Exception e) {
        StringWriter sw = new StringWriter();
        try {
            e.printStackTrace(new PrintWriter(sw));
        } finally {
            try {
                sw.close();
            } catch (IOException e1) {
                e1.printStackTrace();
            }
        }
        return sw.getBuffer().toString().replaceAll("\\$", "T");
    }

    public static void main(String[] args){
        String localrRepairRelation = "";
        List<String> repairRelationList =  EmptyUtils.isNotEmpty(localrRepairRelation) ? Arrays.asList(localrRepairRelation.split(",")) : new ArrayList<>();

        System.out.println(repairRelationList.size());
    }
}

package com.jy.util;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: zy
 * @Date: Created in 2018/4/26
 */
@Component
public class DataPublishUtils extends BaseDataPublishUtils {
    @Autowired
    private Environment env;

    @Override
    @PostConstruct
    /** 项目启动时，从配置文件、或者数据库获取 */
    public void initParam() {
        try {
            Auth auth = new Auth();
            auth.setUsername(env.getProperty("httpUtils.dataPublish.username"));
            auth.setPassword(env.getProperty("httpUtils.dataPublish.password"));
            auth.setAuthorization(env.getProperty("httpUtils.authorization"));
            authMap.put(DEFAULT_AUTH_KEY, auth);
            URL = env.getProperty("httpUtils.dataPublish.url");

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    /**
     * 从缓存中获取token
     * @return
     */
    public String getTokenCache(String authKey) throws Exception {
        if(authMap.get(authKey) == null){
            throw new Exception("BaseDataPublishUtils 中的 authKey: " + authKey + " 的用户为空");
        }
        return authMap.get(authKey).getToken();
    }

    @Override
    /**
     * 更新缓存中的token
     * @return
     */
    public void updateTokenCache(String authKey, String token) throws Exception {
        if(authMap.get(authKey) == null){
            throw new Exception("BaseDataPublishUtils 中的 authKey: " + authKey + " 的用户为空");
        }
        authMap.get(authKey).setToken(token);
    }


    public static void main(String args[]) throws Exception {
        DataPublishUtils dataPublishUtils = new DataPublishUtils();
        Auth auth = dataPublishUtils.new Auth();
        auth.setUsername("admin");
        auth.setPassword("admin2018");
        auth.setAuthorization("YW5kcm9pZDphbmRyb2lk");
        dataPublishUtils.authMap.put(DEFAULT_AUTH_KEY, auth);
        dataPublishUtils.URL = "http://localhost:8080";

        Map<String, String> querys = new HashMap<String, String>();
        querys.put("clientCode", "PICC");
        try {
            // 正常调用
//            JSONObject jsonObject = facadeUtils.doPostFile("http://**************:8765/","vehicle-service/imageOCRs", querys, byt, "1.jpg");
            // 压缩调用
            JSONObject jsonObject = dataPublishUtils.doGet("/clientTable", querys);
            System.out.println(jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}

package com.jy.util.rabbitmq;

/**
 * 
 * <AUTHOR>
 * @date 2019/11/1
 **/
public class MessageWithTime {
    private long id;
    private long time;
    private Object message;
    private MessageSender sender;

    public MessageWithTime(long id, long time, Object message, MessageSender sender) {
        this.id = id;
        this.time = time;
        this.message = message;
        this.sender = sender;
    }

    public long getId() {
        return id;
    }

    public long getTime() {
        return time;
    }

    public Object getMessage() {
        return message;
    }

    public MessageSender getSender() {
        return sender;
    }
}

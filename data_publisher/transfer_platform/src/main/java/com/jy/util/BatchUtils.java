package com.jy.util;

import com.jy.bean.dto.BaseDataDTO;
import org.springframework.stereotype.Component;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Enumeration;
import java.util.List;

@Component
public class BatchUtils {

    /* 主批次序列号 */
    private long mainBatchNoSequence = 0L;
    /* 上次生产id时间戳 */
    private static long lastTimestamp = -1L;
    private String spacer = "_";

    private final static String machineId = getMachineId();

    public String nextMainBatchNo(String type){
       return type  + spacer + machineId + spacer + this.nextSequence();
    }

    /**
     * 机器id部分 无则默认为取127.0.0.1
     */
    private static String getMachineId() {
        String ip = getIp();
        String[] addreses = ip.split("\\.");
        return addreses[addreses.length - 1];
    }

    public static String getIp(){
        String hostAddress = "";
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface ni = (NetworkInterface) networkInterfaces.nextElement();
                if(ni.getName().contains("vir")){
                    continue;
                }
                Enumeration<InetAddress> nias = ni.getInetAddresses();
                while (nias.hasMoreElements()) {
                    InetAddress ia = (InetAddress) nias.nextElement();
                    if (!ia.isLinkLocalAddress() && !ia.isLoopbackAddress() && ia instanceof Inet4Address) {
                        hostAddress = ia.getHostAddress();
                    }
                }
            }
        } catch (SocketException e) {
            System.out.println(e.getMessage());
        }
        if(EmptyUtils.isEmpty(hostAddress)){
            hostAddress = "127.0.0.1";
        }
        return hostAddress;
    }

    /**
     * 生成 时间+序列
     * @return
     */
    private synchronized String nextSequence() {
        long timestamp = timeGen();
        //如果当前时间小于上一次ID生成的时间戳，说明系统时钟回退过这个时候应当抛出异常
        if (timestamp < lastTimestamp) {
            throw new RuntimeException(
                    String.format("Clock moved backwards.  Refusing to generate id for %d milliseconds", lastTimestamp - timestamp));
        }
        //如果是同一时间生成的，则进行秒内序列
        if (lastTimestamp == timestamp) {
            mainBatchNoSequence = mainBatchNoSequence + 1;
            //秒内序列溢出
            if (mainBatchNoSequence == 0) {
                //阻塞到下一秒,获得新的时间戳
                timestamp = tilNextSecond(lastTimestamp);
            }
        } else {//时间戳改变，秒内序列重置
            mainBatchNoSequence = 0L;
        }

        //上次生成ID的时间截
        lastTimestamp = timestamp;
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd_HHmmss");
        String date = df.format(timestamp * 1000);
        return date + "_" + mainBatchNoSequence;
    }

    /**
     * 获取下一个时间
     * @param lastTimestamp
     * @return
     */
    private long tilNextSecond(final long lastTimestamp) {
        long timestamp = this.timeGen();
        while (timestamp <= lastTimestamp) {
            timestamp = this.timeGen();
        }
        return timestamp;
    }

    public static String getBatchNoByMainBatchNo(String mainBatchNo, Integer tableIndex, Integer batchSuffix){
        return mainBatchNo + "_" + tableIndex + "-" + batchSuffix;
    }

    public static String getDateByBatchNo(String mainBatchNo){
        String year = DateUtil.convertDateToString("yyyy", new Date());
        String[] batch = mainBatchNo.split("_");
        return batch.length > 3 ? batch[2] : mainBatchNo.substring(mainBatchNo.indexOf(year), mainBatchNo.indexOf(year) + 8);
    }

    public static String getTypeByBatchNo(String mainBatchNo){
        String year = DateUtil.convertDateToString("yyyy", new Date());
        String[] batch = mainBatchNo.split("_");
        return batch.length > 2 ? batch[0] : mainBatchNo.substring(0, mainBatchNo.indexOf(year));
    }

    /**
     * 生成时间（单位秒）
     * @return
     */
    private long timeGen() {
        return System.currentTimeMillis() / 1000;
    }

    public static void main(String[] args){
      /*  BatchUtils batchUtils = new BatchUtils();
        System.out.println(batchUtils.nextMainBatchNo("Part"));*/
      /*  int batchToplimit = 100;
        List<BaseDataDTO> dataDTOs = new ArrayList<>();
        for(int i=0; i<1002; i++){
            BaseDataDTO baseDataDTO = new BaseDataDTO();
            baseDataDTO.setId( i + "");
            dataDTOs.add(baseDataDTO);
        }
        int pageSize = (int)Math.ceil(Double.valueOf(dataDTOs.size()) / Double.valueOf(batchToplimit));
        System.out.println(dataDTOs.size() + "----" + pageSize);
        //分批次调用更新
        for(int i=0; i<pageSize; i++){
            int endNum = i == pageSize -1 ? dataDTOs.size() : i* batchToplimit + batchToplimit;
            List<BaseDataDTO> temp = dataDTOs.subList(i* batchToplimit, endNum);
            System.out.println(i* batchToplimit + "-----------" + endNum + "----" + temp.size() + "---" + temp.get(temp.size() -1).getId());
        }*/
    }

}

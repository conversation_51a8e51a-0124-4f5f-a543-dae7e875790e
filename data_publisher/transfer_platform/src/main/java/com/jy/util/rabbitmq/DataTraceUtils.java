package com.jy.util.rabbitmq;
import java.util.Date;

import com.alibaba.fastjson.JSONObject;
import com.jy.bean.common.DataTraceMenu;
import com.jy.bean.po.SendDetail;
import com.jy.service.SendDataService;
import com.jy.util.DateUtil;
import com.jy.util.EmptyUtils;
import com.jy.util.StringUtils;
import com.jy.util.ToolUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.sql.Timestamp;

/**
 * @Author: zy
 * @Date: Created in 2019/11/1
 */
@Component
public class DataTraceUtils {
    private static final Logger logger = LogManager.getLogger(DataTraceUtils.class);

    private static String serviceName;

    private static SendDataService sendDataService;

    @Value("${serviceName}")
    public void setServiceName(String serviceName) {
        DataTraceUtils.serviceName = serviceName;
    }

    @Autowired
    public void setSendDataService(SendDataService sendDataService) {
        this.sendDataService = sendDataService;
    }

    /* @Value("${httpUtils.dataPublish.username}")
    public static  String clientCode;*/


    public static boolean sendTrace(JSONObject jsonObject, String nodeName, String status, String message){
        return sendTrace(jsonObject, nodeName, status, message, DateUtil.crunttime());
    }

    public static boolean sendTrace(JSONObject jsonObject, String nodeName, String status, String message, Timestamp time){
        boolean f = false;
        JSONObject trace = new JSONObject();
        //s_id作为es文档中的主键
        trace.put("s_id", "transfer_"+StringUtils.getGUID());
        trace.put("id", jsonObject.getString("id"));
        trace.put("mainBatchNo", jsonObject.get("mainBatchNo"));
        trace.put("batchNo", jsonObject.get("batchNo"));
        //trace.put("dataVersionId", jsonObject.containsKey("dataVersionId") ? jsonObject.get("dataVersionId") : "");
        trace.put("num", jsonObject.containsKey("total") ? jsonObject.get("total") : 0);
        trace.put("status", status);
        trace.put("message", message);
        trace.put("serviceName", serviceName);
        trace.put("clientCode", "SRC_PART");
        trace.put("nodeName", nodeName);
        trace.put("currentTime", time);
        trace.put("sendTimes", 1);
        trace.put("dataSource", "transfer");


        System.out.println("记录轨迹=------------------" + trace.toJSONString());
        logger.info("记录轨迹=------------------" + trace.toJSONString());

        try {
            f = RabbitMqUtil.sendElk(trace);
        } catch (IOException e) {
            logger.error(ToolUtils.getExceptionMsg(e));
        }
        try {
            SendDetail sendDetail = new SendDetail();
            sendDetail.setTableName(jsonObject.getString("tableName"));
            sendDetail.setTotal(jsonObject.containsKey("total") ? jsonObject.getInteger("total") : 0);
            sendDetail.setStatus(status);
            sendDetail.setMessage(StringUtils.substring(message, 200));
            sendDetail.setServiceName(serviceName);
            sendDetail.setNodeName(nodeName);
            sendDetail.setBatchNo(EmptyUtils.isNotEmpty(jsonObject.getString("batchNo"))?jsonObject.getString("batchNo"):jsonObject.getString("mainBatchNo")+"-*");
            sendDetail.setMainBatchNo(jsonObject.getString("mainBatchNo"));
            sendDetail.setCTime(time);
            sendDataService.sendTransData(sendDetail, "SRC_PART");
        } catch (Exception e) {
            logger.error(ToolUtils.getExceptionMsg(e));
        }
        return f;
    }
}

package com.jy.util;

import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.StatusLine;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.XMLWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * HttpClient请求工具类
 */
public class HttpClientUtil {

    private static final Logger logger = LoggerFactory.getLogger(HttpClientUtil.class);

    /**
     * HTTP-POST请求（用于请求json格式的参数）
     * @param url
     * @param params
     * @return
     */
    public static String doPost(String url, String params){

        String jsonString = "";
        //创建默认的httpClient实例
        CloseableHttpClient httpclient = HttpClients.createDefault();

        //创建httpPost
        HttpPost httpPost = new HttpPost(url);

        //设置请求头信息
        httpPost.setHeader("Accept", "application/json");
        httpPost.setHeader("Content-Type", "application/json");

        //设置请求参数
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectionRequestTimeout(3000)//从连接池中获取可用连接 超时
                .setConnectTimeout(300000)//连接目标 超时
                .setSocketTimeout(600000)//读取数据 超时
                .build();
        httpPost.setConfig(requestConfig);

        String charSet = "UTF-8";
        StringEntity entity = new StringEntity(params, charSet);
        httpPost.setEntity(entity);
        CloseableHttpResponse response = null;

        try {
            response = httpclient.execute(httpPost);
            StatusLine status = response.getStatusLine();
            int state = status.getStatusCode();
            if (state == HttpStatus.SC_OK) {
                HttpEntity responseEntity = response.getEntity();
                jsonString = EntityUtils.toString(responseEntity);
            } else {
                logger.error("请求返回:" + state + "(" + url +")");
            }
        } catch (Exception e){
            logger.error("HTTP-POST请求错误："+e.getMessage());
            e.printStackTrace();
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            try {
                httpclient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return jsonString;
    }

    //编码
    public static String doc2String(String str) {
        String s = "";
        try {
            // 使用输出流来进行转化
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            // 使用，GB2312，GBK编码
            OutputFormat format = new OutputFormat("  ", true, "UTF-8");
            XMLWriter writer = new XMLWriter(out, format);
            writer.write(str);
            s = out.toString("UTF-8");
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return s;
    }

    /**
     * HTTP-POST请求（用于请求json格式的参数）
     * @param url
     * @param params
     * @return
     */
    public static String doPost(String url, String params,String token){

        String jsonString = "";
        //创建默认的httpClient实例
        CloseableHttpClient httpclient = HttpClients.createDefault();

        //创建httpPost
        HttpPost httpPost = new HttpPost(url);

        //设置请求头信息
        httpPost.setHeader("Accept", "application/json");
        httpPost.setHeader("Content-Type", "application/json");
        if(!StringUtils.isBlank(token)){
            httpPost.addHeader("access_token",token);
        }
        httpPost.addHeader("Authorization","Basic YW5kcm9pZDphbmRyb2lk");
        //httpPost.setHeader("Basic","YW5kcm9pZDphbmRyb2lk");
        //设置请求参数
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectionRequestTimeout(3000)//从连接池中获取可用连接 超时
                .setConnectTimeout(300000)//连接目标 超时
                .setSocketTimeout(600000)//读取数据 超时
                .build();
        httpPost.setConfig(requestConfig);

        String charSet = "UTF-8";
        StringEntity entity = new StringEntity(params, charSet);
        httpPost.setEntity(entity);
        CloseableHttpResponse response = null;

        try {
            response = httpclient.execute(httpPost);
            StatusLine status = response.getStatusLine();
            int state = status.getStatusCode();
            if (state == HttpStatus.SC_OK) {
                HttpEntity responseEntity = response.getEntity();
                jsonString = EntityUtils.toString(responseEntity);
            } else {
                logger.error("请求返回:" + state + "(" + url +")");
            }
        } catch (Exception e){
            logger.error("HTTP-POST请求错误："+e.getMessage());
            e.printStackTrace();
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            try {
                httpclient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return jsonString;
    }
}

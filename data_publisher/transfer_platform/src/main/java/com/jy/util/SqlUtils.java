package com.jy.util;

import java.lang.reflect.Field;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2017/12/26
 */
public class SqlUtils {

    public static String List2SqlInString(List<?> list, String filedName) throws NoSuchFieldException, IllegalAccessException {
        String sqlIn = "";
        list = ListUtils.removeDuplicate(list);
        for(Object o : list){
            Class tempClass = o.getClass();
            boolean isClassHasField = false;
            while(!isClassHasField && tempClass != null){
                isClassHasField = ClassUtils.isClassHasField(tempClass, filedName);
                if (!isClassHasField) {
                    tempClass = tempClass.getSuperclass();
                }
            }
            Field field = tempClass.getDeclaredField(filedName);
            field.setAccessible(true);
            sqlIn = sqlIn + "'" + field.get(o) + "',";
        }
        if(sqlIn.length() > 0){
            sqlIn = "(" + sqlIn.substring(0, sqlIn.length() -1) + ")";
        }
        return sqlIn;
    }

    public static String List2SqlInString(List<String> list) throws NoSuchFieldException, IllegalAccessException {
        String sqlIn = "";
        list = ListUtils.removeDuplicate(list);
        for(String s : list){
            sqlIn = sqlIn + "'" + s + "',";
        }
        if(sqlIn.length() > 0){
            sqlIn = "(" + sqlIn.substring(0, sqlIn.length() -1) + ")";
        }
        return sqlIn;
    }

    public static String List2SqlInString(String str) throws NoSuchFieldException, IllegalAccessException {
        if(EmptyUtils.isEmpty(str)){
            return null;
        }
        String [] array = str.split(",");
        String sqlIn = "";
        for(String s : array){
            sqlIn = sqlIn + "'" + s + "',";
        }
        if(sqlIn.length() > 0){
            sqlIn = "(" + sqlIn.substring(0, sqlIn.length() -1) + ")";
        }
        return sqlIn;
    }

    public static Set<String> List2SqlInSet(List<?> list, String filedName) throws NoSuchFieldException, IllegalAccessException {
        Set<String> set = new HashSet<String>();
        for(Object o : list){
            Class tempClass = o.getClass();
            boolean isClassHasField = false;
            while(!isClassHasField && tempClass != null){
                isClassHasField = ClassUtils.isClassHasField(tempClass, filedName);
                if (!isClassHasField) {
                    tempClass = tempClass.getSuperclass();
                }
            }
            Field field = tempClass.getDeclaredField(filedName);
            field.setAccessible(true);
            set.add((String)field.get(o));
        }
        return set;
    }

    public static Set<String> List2SqlInSet(List<String> list) throws NoSuchFieldException, IllegalAccessException {
        Set<String> set = new HashSet<String>();
        for(String s : list){
            set.add(s);
        }
        return set;
    }

    public static String sqlString(String value){
        String sql = "";
        if (StringUtils.isDate1(value)) {
            sql = "to_date('" + value + "','yyyy-mm-dd hh24:mi:ss')";
        } else {
            //解决字符串中本身带有‘问题
            value = value.replaceAll("'", "''");
            sql = "'" + value + "'";
        }
        return sql;
    }
}

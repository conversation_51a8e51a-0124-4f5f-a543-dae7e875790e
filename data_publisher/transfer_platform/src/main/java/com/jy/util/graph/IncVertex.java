package com.jy.util.graph;

import com.google.common.base.MoreObjects;
import lombok.Data;

import java.util.Objects;

/**
 * @Author: zy
 * @Date: Created in 2020/7/22
 */
@Data
public class IncVertex<T> {
    private T label; //顶点标签，即值
    /**相同组号的节点和边同属于一个有向图*/
    private String groupId;
    /**访问次数*/
    private int accessedNum = 0;

    public IncVertex(T lab) {
        label = lab;
    }

    public IncVertex(T label, String groupId) {
        this.label = label;
        this.groupId = groupId;
    }

    public int getAccessedNum() {
        return accessedNum;
    }

    public void setAccessedNum(int accessedNum) {
        this.accessedNum = accessedNum;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        IncVertex<?> incVertex = (IncVertex<?>) o;
        return Objects.equals(getLabel(), incVertex.getLabel()) &&
                Objects.equals(getGroupId(), incVertex.getGroupId());
    }

    @Override
    public int hashCode() {

        return Objects.hash(getLabel(), getGroupId());
    }
}

package com.jy.util.rabbitmq;

import com.jy.util.EmptyUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentSkipListMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 
 * <AUTHOR>
 * @date 2019/11/1
 **/
public class RetryCache {
    private boolean stop = false;
    private Map<Long, MessageWithTime> map = new ConcurrentSkipListMap<>();
    private AtomicLong id = new AtomicLong();

    public RetryCache(){
        startRetry();
    }

    public long generateId() {
        return id.incrementAndGet();
    }

    public void add(MessageWithTime messageWithTime) {
        map.putIfAbsent(messageWithTime.getId(), messageWithTime);
    }

    public void del(long id) {
        map.remove(id);
    }

    private void startRetry() {
        new Thread(() ->{
            while (!stop) {
                try {
                    Thread.sleep(Constants.RETRY_TIME_INTERVAL);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }

                long now = System.currentTimeMillis();

                for (Map.Entry<Long, MessageWithTime> entry : map.entrySet()) {
                    MessageWithTime messageWithTime = entry.getValue();

                    if (null != messageWithTime) {
                        if (messageWithTime.getTime() + 3 * Constants.VALID_TIME < now) {
                            System.out.println("send message {} failed after 3 min ");
                            del(entry.getKey());
                        } else if (messageWithTime.getTime() + Constants.VALID_TIME < now) {
                            if (EmptyUtils.isNotEmpty(messageWithTime.getSender())){
                                DetailRes res = messageWithTime.getSender().send(messageWithTime);
                                if (!res.isSuccess()) {
                                    System.out.println("retry send message failed {} errMsg {}");
                                }
                            }
                        }
                    }
                }
            }
        }).start();
    }
}

package com.jy.util;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 字典
 * Created by jdd on 2018/12/18.
 */
public class Dictionary {

    private static final Log log = LogFactory.getLog(Dictionary.class);
    public static boolean IS_CONTINUE = true;

    public static boolean IS_COMPARE = true;

    public static void setIsContinue(boolean isContinue) {
        IS_CONTINUE = isContinue;
    }

    public static boolean getIsContinue() {
        return IS_CONTINUE;
    }

    public static boolean getIsCompare() {
        return IS_COMPARE;
    }

    public static void setIsCompare(boolean isCompare) {
        IS_COMPARE = isCompare;
    }

    /**
     * //需要补充发送的表名
     */
    public static Map<String, List<String>> SUPPLEMENT_TABLE = new HashMap<>(10);
    static {
        SUPPLEMENT_TABLE.put("WL",new ArrayList<String>(){{add("pj_cxljfzdyb"); add("pj_clljtxdyb"); add("zc_clfzxxb");
                add("zc_qccjxxb");add("zc_clppxxb");add("zc_cxxxb");add("zc_clzlb"); }});
    }

    public static Map<String,Object> returnTableName(String tableName,String brandCode){

        Map<String,Object> map = new HashMap<>();
        tableName = tableName.toLowerCase();
        if("m_vehicle_info".equals(tableName)){
            map.put("conversionTableName","zc_qccjxxb,zc_clppxxb,zc_cxxxb,zc_clfzxxb,zc_clzlb");
            map.put("productTableName","zc_qccjxxb,zc_clppxxb,zc_cxxxb,zc_clfzxxb,zc_clzlb");
            map.put("contrastTableName","c_zc_qccjxxb,c_zc_clppxxb,c_zc_cxxxb,c_zc_clfzxxb,c_zc_clzlb");
        }else if("m_part_base".equals(tableName)){
            map.put("conversionTableName","pj_cllbjdyb_"+brandCode);
            map.put("productTableName","pj_cllbjdyb_"+brandCode);
            map.put("contrastTableName","c_pj_cllbjdyb_"+brandCode);
        }else if("m_part_hot".equals(tableName)){
            map.put("conversionTableName","pj_clljtxdyb_"+brandCode);
            map.put("productTableName","pj_clljtxdyb_"+brandCode);
            map.put("contrastTableName","c_pj_clljtxdyb_"+brandCode);
        }else if("m_part_dic_std_part".equals(tableName)){
            map.put("conversionTableName","pj_zd_lbjbzb");
            map.put("productTableName","pj_zd_lbjbzb");
            map.put("contrastTableName","c_pj_zd_lbjbzb");
        }else if("m_part_dic_group".equals(tableName) || "m_part_dic_category".equals(tableName) || "m_part_dic_subcategory".equals(tableName)
        ||"m_part_pic_list".equals(tableName) || "m_part_mutex".equals(tableName)){
            map.put("conversionTableName","");
            map.put("productTableName","");
            map.put("contrastTableName","");
        }else if("m_aft_brand_inventory".equals(tableName)){
            map.put("conversionTableName","brand_inventory");
            map.put("productTableName","brand_inventory");
            map.put("contrastTableName","c_brand_inventory");
        }else if("m_aft_brand_oe_info".equals(tableName)){
            map.put("conversionTableName","brand_oe_info");
            map.put("productTableName","brand_oe_info");
            map.put("contrastTableName","c_brand_oe_info");
        }else if("m_aft_brand_spec".equals(tableName)){
            map.put("conversionTableName","brand_spec");
            map.put("productTableName","brand_spec");
            map.put("contrastTableName","c_brand_spec");
        }else if("m_rep_out_part_number".equals(tableName)){
            map.put("conversionTableName","rep_out_part_number");
            map.put("productTableName","rep_out_part_number");
            map.put("contrastTableName","c_rep_out_part_number");
        }else if("m_rep_out_relation".equals(tableName)){
            map.put("conversionTableName","rep_out_relation");
            map.put("productTableName","rep_out_relation");
            map.put("contrastTableName","c_rep_out_relation");
        }else if("m_pj_pzshljdyb".equals(tableName)){
            map.put("conversionTableName","pj_pzshljdyb");
            map.put("productTableName","pj_pzshljdyb");
            map.put("contrastTableName","c_pj_pzshljdyb");
        }else if("m_pj_zd_pzbwzdb".equals(tableName)){
            map.put("conversionTableName","pj_zd_pzbwzdb");
            map.put("productTableName","pj_zd_pzbwzdb");
            map.put("contrastTableName","c_pj_zd_pzbwzdb");
        }else if("m_pj_zd_pzcdzdb".equals(tableName)){
            map.put("conversionTableName","pj_zd_pzcdzdb");
            map.put("productTableName","pj_zd_pzcdzdb");
            map.put("contrastTableName","c_pj_zd_pzcdzdb");
        }else if("m_pj_zd_qyzd".equals(tableName)){
            map.put("conversionTableName","pj_zd_qyzd");
            map.put("productTableName","pj_zd_qyzd");
            map.put("contrastTableName","c_pj_zd_qyzd");
        }else if("m_pj_cxzdb".equals(tableName)){
            map.put("conversionTableName","pj_cxzdb");
            map.put("productTableName","pj_cxzdb");
            map.put("contrastTableName","c_pj_cxzdb");
        }else if("m_pj_cztx".equals(tableName)){
            map.put("conversionTableName","pj_cztx");
            map.put("productTableName","pj_cztx");
            map.put("contrastTableName","c_pj_cztx");
        }else if("m_pj_exclusive_info".equals(tableName)){
            map.put("conversionTableName","pj_exclusive_info");
            map.put("productTableName","pj_exclusive_info");
            map.put("contrastTableName","c_pj_exclusive_info");
        }else{
            map.put("conversionTableName","");
            map.put("productTableName","");
            map.put("contrastTableName","");
        }
        return map;
    }

    /**
     * 单车组和整表需要发送的表
     * @return
     */
    public static Map<String,Object> getOneGroupOrTable(){
        Map<String,Object> map = new HashMap<>(2);
        map.put("contrastTableName","c_zc_qccjxxb,c_zc_clppxxb,c_zc_cxxxb,c_zc_clfzxxb,c_zc_clzlb,c_pj_zc_cxdyb,c_zc_clppxxb_flag,c_zc_cxxxb_flag,c_zc_clfzxxb_flag,c_zc_clzlb_flag");
        map.put("isBrandCode","0");
        return map;
    }

    public static Map<String,Object> getOneTable(String tableTable){

        Map<String,Object> map = new HashMap<>(2);
        map.put("isBrandCode","0");
        if(tableTable.equals("F_DIC_MAKER")){
            map.put("contrastTableName","c_zc_qccjxxb");
        }else if(tableTable.equals("F_DIC_BRAND")){
            map.put("contrastTableName","c_zc_clppxxb");
        }else if(tableTable.equals("F_DIC_FAMILY")){
            map.put("contrastTableName","c_zc_cxxxb");
        }else if(tableTable.equals("F_DIC_GROUP")){
            map.put("contrastTableName","c_zc_clfzxxb");
        }else if(tableTable.equals("F_DIC_VEHICLE")){
            map.put("contrastTableName","c_zc_clzlb");
        }else if(tableTable.equals("F_DIC_VEHICLE_REF")){
            map.put("contrastTableName","c_pj_zc_cxdyb");
        }
        return map;
    }

    /**
     * 精细化商用车需要发送的表
     * @return
     */
    /*public static Map<String,Object> getOneGroupOrTable(){
        Map<String,Object> map = new HashMap<>(6);
        map.put("contrastTableName","c_zc_qccjxxb,c_zc_clppxxb,c_zc_cxxxb,c_zc_clfzxxb,c_zc_clzlb,c_pj_zc_cxdyb");
        map.put("isBrandCode","0");
        return map;
    }*/

    public static String[] getTransformation(){
        String[] str = new String[]{"pj_czlbjdyb","pj_pplbjdyb","zc_clppxxb_flag","zc_cxxxb_flag","zc_clfzxxb_flag","pj_cllbjdyb","pj_clljtxdyb"};
        return str;
    }
}

package com.jy.util;

import org.springframework.cglib.beans.BeanMap;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2018/2/1
 */
public class BeanUtils<T> {

    public static <T> T mapToObject(Map<String, Object> map, T bean) throws Exception {
        BeanMap beanMap = BeanMap.create(bean);
        beanMap.putAll(map);
        return bean;
    }

    public static <T> Map<String, Object> beanToMap(T bean) {
        Map<String, Object> map = new HashMap();
        if (EmptyUtils.isNotEmpty(bean)) {
            BeanMap beanMap = BeanMap.create(bean);
            for (Object key : beanMap.keySet()) {
                Object value = EmptyUtils.isNotEmpty(beanMap.get(key)) ? beanMap.get(key) : null;
                map.put(key + "", value);
            }
        }
        return map;
    }

    public static <T> void copyProperties(Object o1, Object o2) throws Exception{
        org.springframework.beans.BeanUtils.copyProperties(o1, o2);
    }

    public static Object getValueByKey(Object obj, String key) throws Exception {
         /*   Class cls = (Class) obj.getClass();
        Field fs = cls.getDeclaredField(key);
        fs.setAccessible(true);
        Object value = fs.get(obj);*/
        Method methdo = obj.getClass().getMethod(getGetterNameByFiledName(key));
        return methdo.invoke(obj);
    }

    /**
     * 根据属性名 得到该属性的getter方法名
     */
    public static String getGetterNameByFiledName(String fieldName){
        return "get" + fieldName.substring(0 ,1).toUpperCase() + fieldName.substring(1) ;
    }
}

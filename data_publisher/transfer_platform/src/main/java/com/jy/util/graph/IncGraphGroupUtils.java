package com.jy.util.graph;

import com.jy.exception.IncGraphException;
import com.jy.util.EmptyUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * @Author: zy
 * @Date: Created in 2020/7/22
 */
public class IncGraphGroupUtils<T> {
    private static final Logger logger = LogManager.getLogger(IncGraphGroupUtils.class);

    private Map<Integer, Set<IncVertex<T>>> vertexMap = new HashMap<>(); // key - 分组号， value - 顶点Set
    private Map<Integer, Set<IncEdge<T>>> edgeMap = new HashMap<>();// key - 分组号， value - 边Set
    private Map<Integer, IncGraph<T>> incGraphMap = new HashMap<>();// key - 分组号， value - 图
    private int groupId = 0;
    private boolean NOT_ALLOW_DAG = true;

    public IncGraphGroupUtils(boolean notAllowDag, int startRelGroupId) {
        this.NOT_ALLOW_DAG = notAllowDag;
        groupId = startRelGroupId + 1;
    }

    public IncGraphGroupUtils(boolean notAllowDag) {
        this.NOT_ALLOW_DAG = notAllowDag;
    }

    public IncGraphGroupUtils() {
    }

    public void autoGroupInc(List<IncEdge<T>> edgeList) throws Exception {
        int i=0;
        for(IncEdge<T> incEdge : edgeList){
            List<Integer> keyList = getKeyByEdge(incEdge);
            if(keyList.size() == 0){ // 新增图组
                groupId++;

                Set<IncVertex<T>> incVertexSet = new HashSet<>();
                incVertexSet.add(incEdge.getStart());
                incVertexSet.add(incEdge.getEnd());
                vertexMap.put(groupId, incVertexSet);

                Set<IncEdge<T>> incEdgeSet = new HashSet<>();
                incEdgeSet.add(incEdge);
                edgeMap.put(groupId, incEdgeSet);
            } else if (keyList.size() == 1) { // 加入到已有图组
                Set<IncVertex<T>> incVertexSet = vertexMap.get(keyList.get(0));
                incVertexSet.add(incEdge.getStart());
                incVertexSet.add(incEdge.getEnd());

                Set<IncEdge<T>> incEdgeSet = edgeMap.get(keyList.get(0));
                incEdgeSet.add(incEdge);
            } else {  // 此边属于2个图，先合并图，再添加

                groupId++;
                Set<IncVertex<T>> incVertexSet = new HashSet<>();
                Set<IncEdge<T>> incEdgeSet = new HashSet<>();
                for(int key : keyList){ //合并图
                    incVertexSet.addAll(vertexMap.get(key));
                    incEdgeSet.addAll(edgeMap.get(key));
                    vertexMap.remove(key);
                    edgeMap.remove(key);
                }
                //添加新的图
                incVertexSet.add(incEdge.getStart());
                incVertexSet.add(incEdge.getEnd());
                incEdgeSet.add(incEdge);

                vertexMap.put(groupId, incVertexSet);
                edgeMap.put(groupId, incEdgeSet);
            }

            i++;
            System.out.println("已处理个数：" + i);
         //   System.out.println(incEdge);
            int mapSize = 0;
            for(Integer key : edgeMap.keySet()){
                mapSize += edgeMap.get(key).size();
            }
           // System.out.println("map size：" + mapSize);
          //  System.out.println("==========================" );
        }

        initIncGraphMap();
    }

    public void stripOtherVertex(Set<IncVertex<T>> vertex){
        for(int groupId : incGraphMap.keySet()){
            IncGraph incGraph = incGraphMap.get(groupId);
            incGraph.stripOtherVertex(vertex);
        }
    }

    public List<IncEdge<T>> graphMapToEdgeList() {
        List<IncEdge<T>> list = new ArrayList<>();
        for(int groupId : incGraphMap.keySet()){
            IncGraph incGraph = incGraphMap.get(groupId);
            incGraph.print();
            list.addAll(incGraph.listEdge(groupId + ""));
        }
        return list;
    }

    /**
     * 获取每个组的最末节点
     * @return
     */
    public List<IncVertex<T>> graphMapToLastVertexList() {
        List<IncVertex<T>> list = new ArrayList<>();
        for(int groupId : incGraphMap.keySet()){
            IncGraph incGraph = incGraphMap.get(groupId);
            incGraph.print();
            list.addAll(incGraph.listLastVertex());
        }
        return list;
    }

    public Map<Integer, Set<IncVertex<T>>> getVertexMap() {
        return vertexMap;
    }

    private void initIncGraphMap() throws Exception {
        List<Integer> toRemoveIndexList = new ArrayList<>();
        for(int groupId : vertexMap.keySet()){
            IncGraph incGraph = new IncGraph(vertexMap.get(groupId), edgeMap.get(groupId));
            incGraphMap.put(groupId, incGraph);

            //如果有环，则数据有误，转换停止。// 此主组数据错误， 剔除此主组数据
            if(NOT_ALLOW_DAG && incGraph.isDag()) {
                logger.error("数据错误，存在环形结构：");
                for (IncEdge incEdge : edgeMap.get(groupId)) {
                    logger.error(incEdge);
                }
                throw new IncGraphException("数据错误，存在环形结构：");
             /*   toRemoveIndexList.add(groupId);
                incGraph.print();*/
            }
        }
        if(EmptyUtils.isNotEmpty(toRemoveIndexList)){
            for(int toRemoveIndex : toRemoveIndexList) {
                vertexMap.remove(toRemoveIndex);
                edgeMap.remove(toRemoveIndex);
                incGraphMap.remove(toRemoveIndex);
            }
        }
    }



    private List<Integer> getKeyByEdge(IncEdge<T> edge){
        List reList = new ArrayList();
        for(Map.Entry<Integer, Set<IncVertex<T>>> entry : vertexMap.entrySet()){
            // 如果包含边的起点或终点， 都应该加入分组
            if(entry.getValue().contains(edge.getStart()) || entry.getValue().contains(edge.getEnd())){
                reList.add(entry.getKey());
            }
        }
        return reList;
    }

    public static void main(String[] args) throws Exception {
        List<IncEdge<String>> testList = new ArrayList();
        testList.add(new IncEdge<String>("A", "B"));
        testList.add(new IncEdge<String>("B", "C"));
        testList.add(new IncEdge<String>("D", "E"));
        testList.add(new IncEdge<String>("E", "F"));
        testList.add(new IncEdge<String>("D", "F"));
        testList.add(new IncEdge<String>("B", "D"));

        testList.add(new IncEdge<String>("G", "H"));
        testList.add(new IncEdge<String>("I", "J"));
        testList.add(new IncEdge<String>("G", "I"));
        testList.add(new IncEdge<String>("H", "G"));

        testList.add(new IncEdge<String>("K", "L"));
        testList.add(new IncEdge<String>("N", "M"));
        testList.add(new IncEdge<String>("K", "M"));

        IncGraphGroupUtils<String> incGraphGroupUtils = new IncGraphGroupUtils();
        incGraphGroupUtils.autoGroupInc(testList);

        for(Map.Entry<Integer, Set<IncEdge<String>>> entry: incGraphGroupUtils.edgeMap.entrySet()){
            logger.info("主组号：" + entry.getKey());
            Iterator iterator = entry.getValue().iterator();
            IncEdge incEdge;
            while (iterator.hasNext()){
                incEdge = (IncEdge)iterator.next();
                logger.info(incEdge);
            }
        }


        for(Map.Entry<Integer, Set<IncVertex<String>>> entry: incGraphGroupUtils.vertexMap.entrySet()){
            logger.info("主组号：" + entry.getKey());
            Iterator iterator = entry.getValue().iterator();
            IncVertex incVertex;
            while (iterator.hasNext()){
                incVertex = (IncVertex)iterator.next();
                logger.info(incVertex);
            }
        }
    }

}

package com.jy.util;


import org.apache.commons.lang.StringUtils;
import org.apache.http.*;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.ConnectionKeepAliveStrategy;
import org.apache.http.conn.HttpClientConnectionManager;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.LayeredConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpRequestRetryHandler;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeaderElementIterator;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HTTP;
import org.apache.http.protocol.HttpContext;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Created by zy on 2017/10/24.
 */
@Component
public class HttpUtils {
    private static final Logger logger = LogManager.getLogger(HttpUtils.class);

    @Autowired
    private Environment env;
    static String PROXY_HOST;
    static String PROXY_PORT;
    static String PROXY_USERNAME;
    static String PROXY_PASSWORD;
    static int MAX_CONNECTION_NUM = 50;
    static int MAX_PER_ROUTE = 50;
    private static int SOCKET_TIME_OUT = 90000;
    private static int CONNECT_TIME_OUT = 90000;
    private static int CONNECTION_REQUEST_TIME_OUT = 90000;
    private static Object LOCAL_LOCK = new Object();

    @PostConstruct
    public void initParam() {
        String socketTimeOut = env.getProperty("httpClient.socketTimeOut");
        String connectTimeOut = env.getProperty("httpClient.connectTimeOut");
        this.SOCKET_TIME_OUT = EmptyUtils.isNotEmpty(socketTimeOut) ? Integer.parseInt(socketTimeOut) : 5000;
        this.CONNECT_TIME_OUT = EmptyUtils.isNotEmpty(connectTimeOut) ? Integer.parseInt(connectTimeOut) : 5000;
        this.PROXY_HOST = env.getProperty("httpClient.proxyHost");
        this.PROXY_PORT = env.getProperty("httpClient.proxyPort");
    }

    /**
     * 连接池管理对象
     */
   private static PoolingHttpClientConnectionManager cm = null;
    private static IdleConnectionMonitorThread thread;

    /**
     * get
     *
     * @param host
     * @param path
     * @param headers
     * @param querys
     * @return
     * @throws Exception
     */
    public static String doGet(String host, String path,
                                   Map<String, String> headers,
                                   Map<String, String> querys)
            throws Exception {
        RequestConfig requestConfig = getRequestConfig();
        CloseableHttpClient httpClient = getHttpsClient(requestConfig);
        HttpGet request = new HttpGet(buildUrl(host, path, querys));
        request.setConfig(requestConfig);

        for (Map.Entry<String, String> e : headers.entrySet()) {
            request.addHeader(e.getKey(), e.getValue());
        }
        CloseableHttpResponse response = httpClient.execute(request);
        return getEntity(response);
    }

    /**
     * Post String
     *
     * @param host
     * @param path
     * @param headers
     * @param querys
     * @param body
     * @return
     * @throws Exception
     */
    public static String doPost(String host, String path,
                                      Map<String, String> headers,
                                      Map<String, String> querys,
                                      String body)
            throws Exception {

        return doPost(host, path, headers, querys, body, "application/json");
    }

    public static String doPost(String host, String path,
                                Map<String, String> headers,
                                Map<String, String> querys,
                                String body, String contentType)
            throws Exception {

        RequestConfig requestConfig = getRequestConfig();
        CloseableHttpClient httpClient = getHttpsClient(requestConfig);
        HttpPost request = new HttpPost(buildUrl(host, path, querys));
        request.setConfig(requestConfig);

        if(headers != null && headers.size() > 0){
            for (Map.Entry<String, String> e : headers.entrySet()) {
                request.addHeader(e.getKey(), e.getValue());
            }
        }

        if (StringUtils.isNotBlank(body)) {
            request.setEntity(new StringEntity(body, "utf-8"));
        }
        if (StringUtils.isNotBlank(contentType)) {
            request.addHeader("Content-Type", contentType);
        }

        CloseableHttpResponse response = httpClient.execute(request);
        return getEntity(response);
    }

    /**
     * post form
     *
     * @param host
     * @param path
     * @param headers
     * @param querys
     * @param bodys
     * @return
     * @throws Exception
     */
    public static String doPost(String host, String path,
                                Map<String, String> headers,
                                Map<String, String> querys,
                                Map<String, String> bodys)
            throws Exception {

        RequestConfig requestConfig = getRequestConfig();
        CloseableHttpClient httpClient = getHttpsClient(requestConfig);
        HttpPost request = new HttpPost(buildUrl(host, path, querys));
        request.setConfig(requestConfig);

        for (Map.Entry<String, String> e : headers.entrySet()) {
            request.addHeader(e.getKey(), e.getValue());
        }

        if (bodys != null) {
            List<NameValuePair> nameValuePairList = new ArrayList<NameValuePair>();

            for (String key : bodys.keySet()) {
                nameValuePairList.add(new BasicNameValuePair(key, bodys.get(key)));
            }
            UrlEncodedFormEntity formEntity = new UrlEncodedFormEntity(nameValuePairList, "utf-8");
            formEntity.setContentType("application/x-www-form-urlencoded; charset=UTF-8");
            request.setEntity(formEntity);
        }

        CloseableHttpResponse response = httpClient.execute(request);
        return getEntity(response);
    }
    /**
     * Put String
     * @param host
     * @param path
     * @param headers
     * @param querys
     * @param body
     * @return
     * @throws Exception
     */
    public static String doPut(String host, String path,
                               Map<String, String> headers,
                               Map<String, String> querys,
                               String body)
            throws Exception {

        RequestConfig requestConfig = getRequestConfig();
        CloseableHttpClient httpClient = getHttpsClient(requestConfig);
        HttpPut request = new HttpPut(buildUrl(host, path, querys));
        request.setConfig(requestConfig);
        request.addHeader("Content-Type", "application/json");

        for (Map.Entry<String, String> e : headers.entrySet()) {
            request.addHeader(e.getKey(), e.getValue());
        }
        if (StringUtils.isNotBlank(body)) {
            request.setEntity(new StringEntity(body, "utf-8"));
        }

        CloseableHttpResponse response = httpClient.execute(request);
        return getEntity(response);
    }

    /**
     * Delete
     *
     * @param host
     * @param path
     * @param headers
     * @param querys
     * @return
     * @throws Exception
     */
    public static String doDelete(String host, String path,
                                  Map<String, String> headers,
                                  Map<String, String> querys)
            throws Exception {

        RequestConfig requestConfig = getRequestConfig();
        CloseableHttpClient httpClient = getHttpsClient(requestConfig);
        HttpDelete request = new HttpDelete(buildUrl(host, path, querys));
        request.setConfig(requestConfig);

        for (Map.Entry<String, String> e : headers.entrySet()) {
            request.addHeader(e.getKey(), e.getValue());
        }

        CloseableHttpResponse response = httpClient.execute(request);
        return getEntity(response);
    }

    public static String buildUrl(String host, String path, Map<String, String> querys) throws UnsupportedEncodingException {
        StringBuilder sbUrl = new StringBuilder();
        sbUrl.append(host);
        if (!StringUtils.isBlank(path)) {
            sbUrl.append(path);
        }
        if (null != querys) {
            StringBuilder sbQuery = new StringBuilder();
            for (Map.Entry<String, String> query : querys.entrySet()) {
                if (0 < sbQuery.length()) {
                    sbQuery.append("&");
                }
                if (StringUtils.isBlank(query.getKey()) && !StringUtils.isBlank(query.getValue())) {
                    sbQuery.append(query.getValue());
                }
                if (!StringUtils.isBlank(query.getKey())) {
                    sbQuery.append(query.getKey());
                    if (!StringUtils.isBlank(query.getValue())) {
                        sbQuery.append("=");
                        sbQuery.append(URLEncoder.encode(query.getValue(), "utf-8"));
                    }
                }
            }
            if (0 < sbQuery.length()) {
                sbUrl.append("?").append(sbQuery);
            }
        }

        return sbUrl.toString();
    }

    public static String getEntity(CloseableHttpResponse response) throws Exception {
        String res = "";
        try {
            res = EntityUtils.toString(response.getEntity());
        } finally {
            response.close();
        }
        return res;
    }

    /**
     * 请求的iP地址
     * @param
     * @return
     */
    public static String getIpAddress(HttpServletRequest request) {

        // 获取请求主机IP地址,如果通过代理进来，则透过防火墙获取真实IP地址
        String ip = request.getHeader("X-Forwarded-For");

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("X-ClientIP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("WL-Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_CLIENT_IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_X_FORWARDED_FOR");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getRemoteAddr();
            }

        } else if (ip.length() > 15) {
            String[] ips = ip.split(",");
            for (int index = 0; index < ips.length; index++) {
                String strIp = ips[index];
                if (!("unknown".equalsIgnoreCase(strIp))) {
                    ip = strIp;
                    break;
                }
            }
        }
        return ip;
    }

    private static RequestConfig getRequestConfig(){
        RequestConfig config = null;
       if(StringUtils.isNotBlank(PROXY_HOST) && StringUtils.isNotBlank(PROXY_PORT)){
            int httpProxyPort = Integer.valueOf(PROXY_PORT);
            config = RequestConfig.custom()
                    .setProxy(new HttpHost(PROXY_HOST, httpProxyPort))
                    .setConnectTimeout(CONNECT_TIME_OUT)// connectTimeout设置服务器请求超时时间
                    .setSocketTimeout(SOCKET_TIME_OUT)// socketTimeout设置服务器响应超时时间
                    .setConnectionRequestTimeout(CONNECTION_REQUEST_TIME_OUT).build();
        } else {
            config = RequestConfig.custom()
                    .setConnectTimeout(CONNECT_TIME_OUT)
                    .setSocketTimeout(SOCKET_TIME_OUT)
                    .setConnectionRequestTimeout(CONNECTION_REQUEST_TIME_OUT).build();
        }
        return config;
    }



    /**
     *
     * 功能描述: <br>
     * 初始化连接池管理对象
     *
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    private static PoolingHttpClientConnectionManager getPoolManager() {
        final String methodName = "getPoolManager";
        if (null == cm) {
            synchronized (LOCAL_LOCK) {
                if (null == cm) {
                    try {
                        ConnectionSocketFactory plainSocketFactory = PlainConnectionSocketFactory.getSocketFactory();
                        LayeredConnectionSocketFactory sslSocketFactory = SSLConnectionSocketFactory.getSocketFactory();
                        Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory> create().register("http", plainSocketFactory)
                                .register("https", sslSocketFactory).build();
                        cm = new PoolingHttpClientConnectionManager(registry);
                        cm.setValidateAfterInactivity(2000);
                        cm.setMaxTotal(MAX_CONNECTION_NUM);//最大连接数
                        cm.setDefaultMaxPerRoute(MAX_PER_ROUTE);//默认的每个路由的最大连接数
                        thread = new IdleConnectionMonitorThread(cm);
                        thread.start();
                    } catch (Exception e) {
                        logger.error(methodName, "init PoolingHttpClientConnectionManager Error" + e);
                    }

                }
            }
        }
        logger.info("连接池状态：" + cm.getTotalStats());
        return cm;
    }

    public static ConnectionKeepAliveStrategy keepAliveStrat = new ConnectionKeepAliveStrategy() {
        @Override
        public long getKeepAliveDuration(HttpResponse response, HttpContext context) {
            HeaderElementIterator it = new BasicHeaderElementIterator
                    (response.headerIterator(HTTP.CONN_KEEP_ALIVE));
            while (it.hasNext()) {
                HeaderElement he = it.nextElement();
                String param = he.getName();
                String value = he.getValue();
                if (value != null && param.equalsIgnoreCase
                        ("timeout")) {
                    return Long.parseLong(value) * 1000;
                }
            }
            return 60 * 1000;//如果没有约定，则默认定义时长为60s
        }
    };

    /**
     * 创建线程安全的HttpClient
     *
     * @param config 客户端超时设置
     *
     * @return
     */
    public static CloseableHttpClient getHttpsClient(RequestConfig config) {
        CloseableHttpClient httpClient = null;
        if(StringUtils.isNotBlank(PROXY_USERNAME) && StringUtils.isNotBlank(PROXY_PASSWORD)){
            CredentialsProvider provider = new BasicCredentialsProvider();
            provider.setCredentials(new AuthScope(config.getProxy()), new UsernamePasswordCredentials(PROXY_USERNAME, PROXY_PASSWORD));
            httpClient = HttpClients.custom().setDefaultRequestConfig(config)
                    .setDefaultCredentialsProvider(provider)
                    .setKeepAliveStrategy(keepAliveStrat)
                    .setConnectionManager(getPoolManager())
                    .setRetryHandler(new DefaultHttpRequestRetryHandler(0, false))
                    .build();
        } else {
            httpClient = HttpClients.custom().setDefaultRequestConfig(config)
                    .setConnectionManager(getPoolManager())
                    .setKeepAliveStrategy(keepAliveStrat)
                    .setRetryHandler(new DefaultHttpRequestRetryHandler(0, false))
                    .build();
        }
        return httpClient;
    }

    public static class IdleConnectionMonitorThread extends Thread {

        private final HttpClientConnectionManager connMgr;
        private volatile boolean shutdown = false;

        public IdleConnectionMonitorThread(HttpClientConnectionManager connMgr) {
            super();
            this.connMgr = connMgr;
        }

        @Override
        public void run() {
            try {
                while (!shutdown) {
                    synchronized (this) {
                        wait(5000);
                        // Close expired connections
                        connMgr.closeExpiredConnections();
                        // Optionally, close connections
                        // that have been idle longer than 30 sec
                        connMgr.closeIdleConnections(30, TimeUnit.SECONDS);
                    }
                }
            } catch (InterruptedException ex) {
                // terminate
            }
        }

        public void shutdown() {
            shutdown = true;
            synchronized (this) {
                notifyAll();
            }
        }

    }
}

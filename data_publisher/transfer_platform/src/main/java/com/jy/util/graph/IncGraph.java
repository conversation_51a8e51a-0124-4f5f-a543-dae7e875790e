package com.jy.util.graph;

import com.jy.util.EmptyUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * @Author: zy
 * @Date: Created in 2020/7/21
 */
public class IncGraph<T> implements Graph<T>{
    private static final Logger logger = LogManager.getLogger(IncGraph.class);

    private IncVertex<T>[] vertexArray;//顶点数组
    private int graph[][];//邻接矩阵，即顶点之间的关系矩阵
    private int vertexNum;//当前顶点数量
    private Map<IncVertex<T>, Integer> vertexIndexMp;//顶点的index映射， 为了增加计算速度
    private int[] inEdgeNumArray;    // 每个节点的入度个数
    private int[] outEdgeNumArray;  // 每个节点的出度个数

    public IncGraph(int num) {
        //初始化
        vertexArray = new IncVertex[num];
        vertexIndexMp = new HashMap<>();
        graph = new int[num][num];
        for (int j = 0; j < num; j++) {
            for (int k = 0; k < num; k++) {
                graph[j][k] = 0;
            }
        }
    }

    public IncGraph(Set<IncVertex> vertexSet, Set<IncEdge> edgeSet) {
        //初始化
        vertexArray = new IncVertex[vertexSet.size()];
        vertexIndexMp = new HashMap<>();
        graph = new int[vertexSet.size()][vertexSet.size()];
        for (int j = 0; j < vertexSet.size(); j++) {
            for (int k = 0; k < vertexSet.size(); k++) {
                graph[j][k] = 0;
            }
        }
        Iterator iteratorVertex = vertexSet.iterator();
        while(iteratorVertex.hasNext()){
            IncVertex incVertex = (IncVertex)iteratorVertex.next();
            addVertex(incVertex);
        }

        Iterator iteratorEdge = edgeSet.iterator();
        while(iteratorEdge.hasNext()){
            IncEdge incEdge = (IncEdge)iteratorEdge.next();
            addEdge(incEdge);
        }
    }

    /**
     * 添加顶点
     */
    @Override
    public void addVertex(IncVertex<T> vertex) {
        int index = vertexNum++;
        vertexArray[index] = vertex;
        vertexIndexMp.put(vertex, index);
    }

    private int getIndexByVertex(IncVertex<T> vertex){
        return vertexIndexMp.get(vertex);
    }

    /**
     * 添加边
     */
    @Override
    public void addEdge(IncEdge<T> edge) {
        graph[getIndexByVertex(edge.getStart())][getIndexByVertex(edge.getEnd())] = 1;
//        logger.info(edge);
//        logger.info(getIndexByVertex(edge.getStart()));
//        logger.info(getIndexByVertex(edge.getEnd()));
//        print();
    }

    @Override
    public void remove(IncVertex<T> vertex) {
        int index = getIndexByVertex(vertex);

        //将待删除节点的关系继承给自己的下游节点
        //获取下游节点列表
        List<IncVertex<T>> nextList = listNext(vertex);

        for(int i=0;i<vertexNum;i++){
            if(graph[i][index] > 0){  //将待删除列中不为0的值复制给下游节点
                for(IncVertex<T> nextVertex : nextList) {
                    int nextIndex = getIndexByVertex(nextVertex);
                    graph[i][nextIndex] = graph[i][index];
                }
            }
        }

        if (index != vertexNum - 1) {//要删除的顶点不是最后一个就要处理邻接矩阵
            for (int j = index; j < vertexNum - 1; j++) {
                vertexArray[j] = vertexArray[j + 1];
            }
            for (int row = index; row < vertexNum - 1; row++) {//把邻接矩阵中删除行后面行向上移动
                moveRowUp(row, vertexNum);
            }
            for (int col = index; col < vertexNum - 1; col++) {//把列左移
                moveColLeft(col, vertexNum - 1);
            }
        }
        vertexNum--;//数量递减

        //
        vertexIndexMp = new HashMap<>();
        for(int i=0; i<vertexNum; i++){
            vertexIndexMp.put(vertexArray[i], i);
        }
    }

    @Override
    public void remove(IncEdge<T> edge) {
        graph[getIndexByVertex(edge.getStart())][getIndexByVertex(edge.getEnd())] = 0;
    }


    /**
     * 是否存在环
     */
    @Override
    public boolean isDag(){
        int nodeNum = graph.length;
        // 记录每个有入度的节点，及其所有的前序节点
        Map<Integer, List<Integer>> inEdge = new HashMap<>(nodeNum);
        // 记录每个节点的出度个数
        int[] outEdgeNum = new int[nodeNum];
        // 初始化数据
        for (int i = 0; i < nodeNum; i++) {
            for (int j = 0; j < nodeNum; j++) {
                if (graph[i][j] != 0) {
                    outEdgeNum[i]++;
                    if (inEdge.get(j) == null) {
                        List<Integer> list = new ArrayList<>();
                        list.add(i);
                        inEdge.put(j, list);
                    } else {
                        inEdge.get(j).add(i);
                    }
                }
            }
        }

        // 已访问的节点
        Set<Integer> visitedSet = new HashSet<>(nodeNum);
        // 循环遍历所有节点的出度
        while (visitedSet.size() < nodeNum) {
            for (int i = 0; i < nodeNum; i++) {
                //访问出度为 0 的节点
                if (outEdgeNum[i] == 0 && !visitedSet.contains(i)) {
                    visitedSet.add(i);
                    if(EmptyUtils.isNotEmpty(inEdge.get(i))){
                        for (Integer index : inEdge.get(i)) {
                            outEdgeNum[index]--;
                        }
                    }
                    break;
                }
                // 节点遍历一遍后，未找到出度为0的节点， 并且有未访问过的节点，则为环！
                if ((i == nodeNum - 1) && visitedSet.size() != nodeNum) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 将行上移
     *
     * @param row
     * @param length
     */
    private void moveRowUp(int row, int length) {
        for (int col = 0; col < length; col++)
            graph[row][col] = graph[row + 1][col];
    }

    /**
     * 列左移
     *
     * @param col
     * @param lenght
     */
    private void moveColLeft(int col, int lenght) {
        for (int row = 0; row < lenght; row++) {
            graph[row][col] = graph[row][col + 1];
        }
    }

    public void print(){
        String head = "  " + arrayToString(vertexArray, vertexNum);
        logger.info(head);
        for(int i=0; i<vertexNum; i++){
            logger.info(vertexArray[i] + " " + arrayToString(graph[i], vertexNum));
        }
    }

    //打印数组方法
    private String arrayToString(Object[] arr1, int length){
        String s = "[";
        for(int i = 0;i < length; i++){
            s = s + arr1[i] + "\t";
        }
        s = s + "]";
        return s;
    }

    //打印数组方法
    private String arrayToString(int[] arr1, int length){
        String s = "[";
        for(int i = 0;i < length; i++){
            s = s + arr1[i] + "\t";
        }
        s = s + "]";
        return s;
    }

    /**
     * 获取所有入度为0的顶点（所有头节点）
     * @return
     */
    public List<IncVertex<T>> listFirst(){
        return listVertexCompareInEdgeNum(0, 0);
    }
    /**
     * 获取所有入度大于1的顶点
     * @return
     */
    public List<IncVertex<T>> listManyPreVertex(){
        return listVertexCompareInEdgeNum(1, 1);
    }
    /**
     * 获取当前顶点的上游顶点
     * @return
     */
    public List<IncVertex<T>> listPre(IncVertex<T> vertex){
        List<IncVertex<T>> reList = new ArrayList<>();
        int index = getIndexByVertex(vertex);
        for(int i=0; i<vertexNum; i++){
            if(graph[i][index] > 0){
                reList.add(vertexArray[i]);
            }

        }
        return reList;
    }

    /**
     * 获取当前顶点的下游顶点
     * @return
     */
    public List<IncVertex<T>> listNext(IncVertex<T> vertex){
        List<IncVertex<T>> reList = new ArrayList<>();

        int index = getIndexByVertex(vertex);
        for(int i=0; i<vertexNum; i++){
            if(graph[index][i] > 0){
                reList.add(vertexArray[i]);
            }

        }
        return reList;
    }

    /**
     * 获取所有入度 满足条件的节点
     * @param inEdgeNumToCompare 被比较的数量
     * @param compareType 比较方式: 0 - 等于， 1 - 大于， -1 - 小于
     * @return
     */
    private List<IncVertex<T>> listVertexCompareInEdgeNum(int inEdgeNumToCompare, int compareType){
        List<IncVertex<T>> reList = new ArrayList<>();
        // 每个节点的入度个数
        inEdgeNumArray = new int[vertexNum];
        // 初始化数据
        for (int i = 0; i < vertexNum; i++) {
            for (int j = 0; j < vertexNum; j++) {
                if (graph[i][j] != 0) {
                    inEdgeNumArray[j]++;
                }
            }
        }

        for(int i = 0; i < vertexNum; i++){
            if(compareType == 0 ){
                if(inEdgeNumArray[i] == inEdgeNumToCompare){
                    reList.add(vertexArray[i]);
                }
            } else if (compareType > 0){
                if(inEdgeNumArray[i] > inEdgeNumToCompare){
                    reList.add(vertexArray[i]);
                }
            } else if (compareType < 0){
                if(inEdgeNumArray[i] < inEdgeNumToCompare){
                    reList.add(vertexArray[i]);
                }
            }

        }
        return reList;
    }

    /**
     * 获取所有入度 - 出度 最大的顶点
     * @return
     */
    private List<IncVertex<T>> listMaxInMinusOutVertex(){
        List<IncVertex<T>> reList = new ArrayList<>();
        // 每个节点的入度个数
        inEdgeNumArray = new int[vertexNum];
        // 每个节点的出度个数
        outEdgeNumArray = new int[vertexNum];
        // 初始化数据
        for (int i = 0; i < vertexNum; i++) {
            for (int j = 0; j < vertexNum; j++) {
                if (graph[i][j] != 0) {
                    inEdgeNumArray[j]++;
                    outEdgeNumArray[i]++;
                }
            }
        }

        int max = -999;
        int minus;
        for(int i = 0; i < vertexNum; i++){
            minus = outEdgeNumArray[i] - inEdgeNumArray[i];
            if(minus > max){
                max = minus;
                reList = new ArrayList<>();
                reList.add(vertexArray[i]);
            } else if(minus == max){
                reList.add(vertexArray[i]);
            }
        }
        return reList;
    }

    public <T> void stripOtherVertex(Set<T> vertex) {
        List<IncVertex> toRemoveList = new ArrayList();
        for(int i=0;i<vertexNum;i++){
            if(!vertex.contains(vertexArray[i])){
                toRemoveList.add(vertexArray[i]);
            }
        }
        for(IncVertex incVertex : toRemoveList){
            this.remove(incVertex);
        }
    }

    public <T> List<IncEdge<T>> listEdge(String groupId) {
        List reList = new ArrayList();
        for (int i = 0; i < vertexNum; i++) {
            for (int j = 0; j < vertexNum; j++) {
                if (graph[i][j] != 0) {
                    reList.add(new IncEdge(vertexArray[i], vertexArray[j], groupId));
                }
            }
        }
        return reList;
    }

    public List<IncVertex<T>> listLastVertex() {
        List<IncVertex<T>> reList = new ArrayList();
        List<IncVertex<T>> maxMinusList = this.listMaxInMinusOutVertex();
        IncVertex<T> maxMinus = maxMinusList.get(0);
        while(hasNext(maxMinus)){
            maxMinus = next;
        }
        IncVertex<T> lastVertex = next;
        Set<IncVertex<T>> set = new HashSet<>();
        set = hashSetRoundTripVertex(lastVertex, set);
        for (IncVertex incVertex : set) {
            reList.add(incVertex);
        }
        return reList;
    }

    public Set<IncVertex<T>> hashSetRoundTripVertex(IncVertex<T> incVertex, Set<IncVertex<T>> set){
        set.add(incVertex);
        int index = getIndexByVertex(incVertex);
        for (int i = 0; i < vertexNum; i++) {
            if (graph[i][index] == 1 && graph[index][i] == 1 ) {
                if(!set.contains(vertexArray[i])){
                    set.addAll(hashSetRoundTripVertex(vertexArray[i], set));
                }
            }
        }
        return set;
    }


    private IncVertex next;
    private boolean hasNext(IncVertex<T> incVertex){
        int index = getIndexByVertex(incVertex);
        next = incVertex;
        for(int i=0; i<vertexNum; i++){
            if(graph[index][i] > 0){
                next = vertexArray[i];
                if(next.getAccessedNum() <= outEdgeNumArray[i]){
                    if(outEdgeNumArray[i] > 0){
                        next.setAccessedNum(next.getAccessedNum() + 1);
                    }
                    break;
                }
            }
        }
        if(next.getAccessedNum() > outEdgeNumArray[getIndexByVertex(next)]){
            next = incVertex;
        }
        if(incVertex.equals(next)){
            return false;
        }
        return true;
    }

    public static void main(String args[]){
        IncGraph<String> theGraph = new IncGraph(8);
        IncVertex a = new IncVertex<String>("A");
        IncVertex b = new IncVertex<String>("B");
        IncVertex c = new IncVertex<String>("C");
        IncVertex d = new IncVertex<String>("D");
        IncVertex e = new IncVertex<String>("E");
        IncVertex f = new IncVertex<String>("F");
        IncVertex g = new IncVertex<String>("G");
        IncVertex h = new IncVertex<String>("H");
        theGraph.addVertex(a);
        theGraph.addVertex(b);
        theGraph.addVertex(c);
        theGraph.addVertex(d);
        theGraph.addVertex(e);
        theGraph.addVertex(f);
        theGraph.addVertex(g);
        theGraph.addVertex(h);

        theGraph.addEdge(new IncEdge<String>(a,d));//AD
        theGraph.addEdge(new IncEdge<String>(a,e));//AE
        theGraph.addEdge(new IncEdge<String>(b,e));//BE
        theGraph.addEdge(new IncEdge<String>(c,f));//CF
        theGraph.addEdge(new IncEdge<String>(d,g));//DG
        theGraph.addEdge(new IncEdge<String>(e,g));//EG
        theGraph.addEdge(new IncEdge<String>(f,h));//FH
        theGraph.addEdge(new IncEdge<String>(h,g));//HG
        theGraph.addEdge(new IncEdge<String>(d,a));//DA

        theGraph.print();
        System.out.println(theGraph.isDag());
        System.out.println(theGraph.listFirst());
        System.out.println(theGraph.listManyPreVertex());
        for(IncVertex incVertex : theGraph.listManyPreVertex()){
            System.out.println(theGraph.listPre(incVertex));
        }
        theGraph.remove(e);
        theGraph.print();
    }


}

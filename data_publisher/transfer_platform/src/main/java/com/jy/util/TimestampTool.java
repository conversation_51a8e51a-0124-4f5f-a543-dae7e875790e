package com.jy.util;


import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class TimestampTool {

    public static SimpleDateFormat sdfSSS=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS"); //格式化到毫秒
    public static DecimalFormat df3=new DecimalFormat("#0.0##");//保留三位小数

    // 当前时间
    public static Timestamp crunttime() {
        return new Timestamp(System.currentTimeMillis());
    }

    //获取当前时间的字符串  2006-07-07
    public static String getCurrentDate() {
        Timestamp d = crunttime();
        return d.toString().substring(0,10);
    }

    //	获取当前时间的字符串  2006-07-07 22:10:10
    public static String getCurrentDateTime() {
        Timestamp d=crunttime();
        return d.toString().substring(0,19);
    }

    //	获取给定时间的字符串,只有日期  2006-07-07
    public static String getStrDate(Timestamp t) {
        return t.toString().substring(0,10);
    }

    //获取给定时间的字符串  2006-07-07 22:10:10
    public static String getStrDateTime(Timestamp t) {
        return t.toString().substring(0,19);
    }

    //获取当前时间字符串 格式：010704120856
    public static String ymdhms(){
        SimpleDateFormat sdf = new SimpleDateFormat("yyMMddHHmmss");
        Calendar cal = Calendar.getInstance();
        String now = sdf.format(cal.getTime());
        return now;
    }

    //获取当前时间字符串 格式：20010704120856
    public static String yyyymmddhhmmss(){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        Calendar cal = Calendar.getInstance();
        String now = sdf.format(cal.getTime());
        return now;
    }

    public static String getStrIntervalDate(String days)
    {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();

        cal.add(Calendar.DATE, -Integer.parseInt(days));

        String strBeforeDays = sdf.format(cal.getTime());

        return strBeforeDays;
    }

    // 返回日期 格式:2006-07-05
    public static Timestamp date(String str) {
        Timestamp tp = null;
        if (str.length() <= 10) {
            String[] string = str.trim().split("-");
            int one = Integer.parseInt(string[0]) - 1900;
            int two = Integer.parseInt(string[1]) - 1;
            int three = Integer.parseInt(string[2]);
            tp = new Timestamp(one, two, three, 0, 0, 0, 0);
        }
        return tp;
    }

    // 返回时间和日期  格式:2006-07-05 22:10:10
    public static Timestamp datetime(String str) {
        Timestamp tp = null;
        if (str.length() > 10) {
            String[] string = str.trim().split(" ");
            String[] date = string[0].split("-");
            String[] time = string[1].split(":");
            int date1 = Integer.parseInt(date[0]) - 1900;
            int date2 = Integer.parseInt(date[1]) - 1;
            int date3 = Integer.parseInt(date[2]);
            int time1 = Integer.parseInt(time[0]);
            int time2 = Integer.parseInt(time[1]);
            int time3 = Integer.parseInt(time[2]);
            tp = new Timestamp(date1, date2, date3, time1, time2, time3, 0);
        }
        return tp;
    }


    // 返回日期和时间(没有秒)  格式:2006-07-05 22:10
    public static Timestamp datetimeHm(String str) {
        Timestamp tp = null;
        if (str.length() > 10) {
            String[] string = str.trim().split(" ");
            String[] date = string[0].split("-");
            String[] time = string[1].split(":");
            int date1 = Integer.parseInt(date[0]) - 1900;
            int date2 = Integer.parseInt(date[1]) - 1;
            int date3 = Integer.parseInt(date[2]);
            int time1 = Integer.parseInt(time[0]);
            int time2 = Integer.parseInt(time[1]);
            tp = new Timestamp(date1, date2, date3, time1, time2, 0, 0);
        }
        return tp;
    }

    /**
     * 计算耗时（秒）
     * @param startDate	格式：2013-12-26 14:53:30.906
     * @param endDate	格式：2013-12-26 14:53:31.180
     */
    public static Double computeTimeConsuming(String startDate,String endDate) {
        Double hs = new Double(0);
        try {
            long start = sdfSSS.parse(startDate).getTime();
            long end = sdfSSS.parse(endDate).getTime();
            double x = end-start;
            hs = new Double(TimestampTool.df3.format(x/1000));//毫秒转换为秒，保留三位小数
            if(hs.doubleValue()<0){
                hs = new Double(0);
            }else if(hs.doubleValue()>600){
                hs = new Double(0.1);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }finally{
            return hs;
        }
    }
    /**
     * 计算耗时（秒）
     * @param startDate	Date
     * @param endDate	Date
     */
    public static Double computeTimeConsuming(Date startDate,Date endDate) {
        long start = startDate.getTime();
        long end = endDate.getTime();
        double x = end-start;
        Double hs = new Double(TimestampTool.df3.format(x/1000));//毫秒转换为秒，保留三位小数
        if(hs.doubleValue()<0){
            hs = new Double(0);
        }else if(hs.doubleValue()>600){
            hs = new Double(0.1);
        }
        return hs;
    }
    /**
     * 计算耗时（秒）
     * @param start	long
     * @param end	long
     */
    public static Double computeTimeConsuming(long start,long end) {
        double x = end-start;
        double a = x/1000;
        Double hs = new Double(TimestampTool.df3.format(x/1000));//毫秒转换为秒，保留三位小数
        if(hs.doubleValue()<0){
            hs = new Double(0);
        }else if(hs.doubleValue()>600){
            hs = new Double(0.1);
        }
        return hs;
    }

    /**
     * 返回前一天的日期字符串(yyyy-mm-dd)
     * @return
     */
    public static String getYesterdayStr(){
        Calendar ca = Calendar.getInstance();
        ca.setTime(new Date());
        ca.add(Calendar.DATE,-1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(ca.getTime());
    }

    /**
     * 返回前一天的日期(yyyy-mm-dd)
     * @return
     */
    public static Date getYesterdayDate(){
        Calendar ca = Calendar.getInstance();
        ca.setTime(new Date());
        ca.add(Calendar.DATE,-1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return ca.getTime();
    }
    public static void main(String[] args) {
        System.out.println();
    }
}

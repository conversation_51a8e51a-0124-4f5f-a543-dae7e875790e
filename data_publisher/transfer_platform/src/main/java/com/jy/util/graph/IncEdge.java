package com.jy.util.graph;

import com.google.common.base.MoreObjects;
import lombok.Data;

/**
 * @Author: zy
 * @Date: Created in 2020/7/22
 */
@Data
public class IncEdge<T> {
    private IncVertex<T> start;
    private IncVertex<T> end;

    /**包含关系组号，相同组号的节点和边同属于一个有向图*/
    private String groupId;

    /**预留权值， 暂时无用*/
    private double weight;

    public IncEdge() {
    }

    public IncEdge(T start, T end) {
        this.start = new IncVertex<>(start);
        this.end = new IncVertex<>(end);
    }

    public IncEdge(IncVertex<T> start, IncVertex<T> end) {
        this.start = start;
        this.end = end;
    }
    public IncEdge(IncVertex<T> start, IncVertex<T> end, String groupId) {
        this.start = start;
        this.end = end;
        this.groupId = groupId;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("start", start)
                .add("end", end)
                .toString();
    }
}

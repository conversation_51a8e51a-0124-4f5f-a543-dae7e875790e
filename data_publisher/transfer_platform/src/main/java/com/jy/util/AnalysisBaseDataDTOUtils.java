package com.jy.util;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/5/4
 */
public class AnalysisBaseDataDTOUtils {

    public static StringBuffer analysisKey(Map<String, String> map, StringBuffer sBuffer){
        if(map != null) {
            for (Map.Entry<String, String> entry : map.entrySet()) {
                sBuffer.append(entry.getKey()).append(",");
            }
            sBuffer.deleteCharAt(sBuffer.length() - 1);
        }
        return sBuffer;
    }

    public static Map<String, String> filterMap(Map<String, String> fieldMap, Map<String, String> mustMap){
        Map<String, String> result = new HashMap<>();
        if(EmptyUtils.isNotEmpty(fieldMap) && EmptyUtils.isNotEmpty(mustMap)) {
            for (Map.Entry<String, String> entry : mustMap.entrySet()) {
                if(!fieldMap.containsKey(entry.getKey())){
                    result.put(entry.getKey(), entry.getValue());
                }
            }
        }
        return result;
    }

    public static StringBuffer analysisValue(Map<String, String> map, StringBuffer sBuffer){
        if(map != null) {
            for (Map.Entry<String, String> entry : map.entrySet()) {
                sBuffer.append(SqlUtils.sqlString(entry.getValue())).append(",");
            }
            sBuffer.deleteCharAt(sBuffer.length() - 1);
        }
        return sBuffer;
    }

    public static StringBuffer analysisKeyValue(Map<String, String> map, StringBuffer sBuffer, StringBuffer Connector){
        if(map != null) {
            for (Map.Entry<String, String> entry : map.entrySet()) {
                sBuffer.append(entry.getKey()) .append("=") .append(SqlUtils.sqlString(entry.getValue())) .append(Connector);
            }
            sBuffer.delete(sBuffer.length() - Connector.length(), sBuffer.length());
            //sBuffer.deleteCharAt(sBuffer.length() - Connector.length());
        }
        return sBuffer;
    }
}

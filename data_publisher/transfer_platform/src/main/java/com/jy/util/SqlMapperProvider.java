package com.jy.util;

import com.jy.bean.dto.BaseDataDTO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/4/16
 */
@Component
public class SqlMapperProvider {
    private static final Logger logger = LogManager.getLogger(SqlMapperProvider.class);

    public String insert(BaseDataDTO baseDataDTO){
        StringBuffer sBuffer = new StringBuffer();
        sBuffer.append(baseDataDTO.getOperate()) .append(" into ");
        sBuffer.append(baseDataDTO.getTableName()) .append(" (");
        sBuffer = AnalysisBaseDataDTOUtils.analysisKey(baseDataDTO.getFields(), sBuffer);
        if(EmptyUtils.isNotEmpty(baseDataDTO.getKeys())){
            sBuffer.append(", ");
            sBuffer = AnalysisBaseDataDTOUtils.analysisKey(baseDataDTO.getKeys(), sBuffer);
        }
        sBuffer.append(") values (");
        sBuffer = AnalysisBaseDataDTOUtils.analysisValue(baseDataDTO.getFields(), sBuffer);
        if(EmptyUtils.isNotEmpty(baseDataDTO.getKeys())){
            sBuffer.append(", ");
            sBuffer = AnalysisBaseDataDTOUtils.analysisValue(baseDataDTO.getKeys(), sBuffer);
        }
        sBuffer.append(") ");
       // logger.warn(String.valueOf(sBuffer));
        return String.valueOf(sBuffer);
    }

    public String jdbcInsert(String tableName, List<String> fields){
        StringBuffer sBuffer = new StringBuffer();
        sBuffer.append("insert into ");
        sBuffer.append(tableName) .append(" (");
        for(String str : fields){
            sBuffer.append(str) .append(",");
        }
        sBuffer.deleteCharAt(sBuffer.length() - 1);
        sBuffer.append(") values (");
        for(String str : fields){
            sBuffer .append("?,");
        }
        sBuffer.deleteCharAt(sBuffer.length() - 1);
        sBuffer.append(") ");
        // logger.warn(String.valueOf(sBuffer));
        return String.valueOf(sBuffer);
    }

    public String update(BaseDataDTO baseDataDTO){
        StringBuffer sBuffer = new StringBuffer();
        sBuffer.append(baseDataDTO.getOperate()) .append(" ");
        sBuffer.append(baseDataDTO.getTableName()) .append(" set ");
        sBuffer = AnalysisBaseDataDTOUtils.analysisKeyValue(baseDataDTO.getFields(), sBuffer, new StringBuffer(","));
        sBuffer.append(" where ");
        sBuffer = AnalysisBaseDataDTOUtils.analysisKeyValue(baseDataDTO.getKeys(), sBuffer, new StringBuffer(" AND "));
       // logger.warn(String.valueOf(sBuffer));
        return String.valueOf(sBuffer);
    }

    public String delete(BaseDataDTO baseDataDTO){
        StringBuffer sBuffer = new StringBuffer();
        sBuffer.append(baseDataDTO.getOperate()) .append(" from ");
        sBuffer.append(baseDataDTO.getTableName()).append(" where ");

        sBuffer = AnalysisBaseDataDTOUtils.analysisKeyValue(baseDataDTO.getKeys(), sBuffer, new StringBuffer(" AND "));
      //  logger.warn(String.valueOf(sBuffer));
        return String.valueOf(sBuffer);
    }

}

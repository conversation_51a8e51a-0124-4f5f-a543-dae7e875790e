package com.jy.util;

import com.google.common.base.Joiner;

import java.util.*;

/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2017/12/20
 */
public class ListUtils<T> {
    public static List removeDuplicate(List list) {
        list = new ArrayList<>(list);
        HashSet h = new HashSet(list);
        list.clear();
        list.addAll(h);
        return list;
    }

    /**
     * 集合转化成String(指定的separator分隔符)
     *
     * @param collection
     *            集合
     * @param separatorChar
     *            分隔符(如不传,默认为",")
     * @return
     */
    public static String toString(Collection<?> collection, String separatorChar) {
        if (EmptyUtils.isEmpty(collection)) {
            return "";
        }
        if (EmptyUtils.isEmpty(separatorChar)) {
            separatorChar = ",";
        }
        return Joiner.on(separatorChar).skipNulls().join(collection);
    }

    public static  void main(String [] args){
        String tableName = "c_wl_pjjjjio_wlm0";
        System.out.println(tableName.substring(tableName.lastIndexOf("wl_pjjjjio_") +"wl_pjjjjio_".length(), tableName.length()));
        System.out.println(tableName.substring(tableName.lastIndexOf("_") + 1, tableName.length()));
    }
}

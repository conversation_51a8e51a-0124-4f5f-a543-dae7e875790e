package com.jy.util.graph;


/**
 * @Author: zy
 * @Date: Created in 2020/7/22
 */
public interface Graph<T> {
    /**
     * 添加顶点
     */
    public void addVertex(IncVertex<T> vertex);

    /**
     * 添加边
     */
    public void addEdge(IncEdge<T> edge);

    /**
     * 删除一个顶点，与其相连的边也会被删除
     */
    public void remove(IncVertex<T> vertex);

    /**
     * 删除一条边
     */
    public void remove(IncEdge<T> edge);

    /**
     * 是否存在环
     */
    public boolean isDag();
}

package com.jy.util;

import com.alibaba.fastjson.JSONObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class CommonUtils {
    private static final Logger logger = LogManager.getLogger(CommonUtils.class);

    @Value("${httpUtils.facade.env2.url}")
    private  String url;
    @Value("${httpUtils.facade.emailPath}")
    private  String emailPath;
    @Value("${httpUtils.facade.defaultEmailTo}")
    private  String defaultEmailTo;
    @Value("${httpUtils.facade.workWechatPath}")
    private  String workWechatPath;
    @Value("${httpUtils.facade.defaultWechatName}")
    private  String defaultWechatName;



    @Autowired
    private FacadeUtils facadeUtils;

    public void sendEmail(String subject, String msg){
        sendEmail(subject, msg, defaultEmailTo, "");
    }

    public void sendEmail(String subject, String msg, String emailTo, String emailCc){
        Map<String, String> querys = new HashMap<>();
        querys.put("simple", "simple");
        querys.put("to", emailTo);
        querys.put("cc", emailCc);
        querys.put("subject", subject);
        querys.put("msg", msg);
        try {
            JSONObject jsonObject = facadeUtils.doPost(url, emailPath, querys, "");
            logger.info("邮件推送结果：" + jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void sendWorkWechatPath(String msg){
        sendWorkWechatPath(defaultWechatName, msg);
    }
    public void sendWorkWechatPath(String username, String msg){
        Map<String, String> querys = new HashMap<>();
        querys.put("text", "text");
        querys.put("names", username);
        querys.put("msg", msg);
        try {
            facadeUtils.doGet(url, workWechatPath, querys);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

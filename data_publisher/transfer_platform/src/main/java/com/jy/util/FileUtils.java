package com.jy.util;

import java.io.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by jdd on 2018/12/6.
 */
public class FileUtils implements FilenameFilter{

    private String extendName;

    private void setExtendName(String extendName) {
        this.extendName = "."+ extendName;
    }

    public  boolean accept(File dir, String name){//重写接口的方法
        return name.endsWith(extendName);//是否是以某个格式结尾。是否是某个类型文件
    }

    /**
     * 输出生成一个文件
     * @param filePath
     * @param fileName
     * @param content
     */
    public static void exportSql(String filePath ,String fileName, String content) throws IOException {
            //文件地址
            String path =filePath+ File.separator+fileName;
            //打开一个写文件器，构造函数中的第二个参数true表示以追加形式写文件
            File filed=new File(filePath);//文件夹路径
            //如果文件夹不存在则新建
            if(!filed.exists()){
                filed.mkdirs();
            }
            File file=new File(path);
            if(!file.exists()){
                file.createNewFile();
            }
            BufferedWriter bw = new BufferedWriter(new FileWriter(file,true));
            bw.write(content);
            bw.flush();
            bw.close();
            /*FileWriter writer = new FileWriter(path,true);
            writer.write(content+"\r\n");
            writer.flush();
            writer.close();*/
    }

    /**
     * 返回指定路径下的文件(文件名完全匹配)
     * @param fileName 文件名称
     * @param filePath 文件路径
     * @return
     */
    public static File searchFile(String fileName,String filePath){
        File file = new File(filePath);
        File[] files = file.listFiles();
        return forFiles(files,fileName);
    }

    /**
     * 递归查找文件
     * @param files  文件列表
     * @param fileName 文件名称
     * @return
     */
    public static File  forFiles(File[] files,String fileName){

        File file = null;
        if(files != null){
            for (File file1 : files) {
                //判断前是文件还是目录
                File[] files1 = file1.listFiles();
                if(files1 != null){
                    //如果是目录,则继续查找
                    file = forFiles(files1,fileName);
                    if(file != null){
                        //如果已经找到文件,则直接返回,结束循环
                        return file;
                    }
                }else{
                    //如果文件名称匹配就返回文件
                    if(file1.getName().equals(fileName)){
                        return file1;
                    }
                }
            }
        }
        return file;
    }

    /**
     * 返回指定路径下的文件(文件名模糊匹配)
     * @param keyWord 查询关键字
     * @param filePath 文件路径
     * @param fileType 文件类型(例如json)
     * @return
     */
    public List<File> searchVagueFile(String keyWord, String filePath, String fileType){
        File file = new File(filePath);
        setExtendName(fileType);
        //String[] fileNames = file.list(this);//返回指定类型的所有文件
        File[] files = file.listFiles();
        List<File> list = new ArrayList<>();
        if(files != null){
            for (File file1 : files) {
                //如果文件名称匹配就返回文件
                if(file1.getName().indexOf(keyWord) > -1){
                    list.add(file1);
                }
            }
        }
        return list;
    }

    /**
     * 返回文件内容
     * @param readFile 读取的文件
     * @return
     */
    public static String fileData(File readFile){
        try {
            Long readFileLength = readFile.length();
            byte[] readFileContent = new byte[readFileLength.intValue()];
            FileInputStream ins = new FileInputStream(readFile);
            ins.read(readFileContent);
            ins.close();
            return new String(readFileContent, "UTF-8");
        }catch (IOException e){
            e.printStackTrace();
        }
        return "";
    }
    public static String readToString(String fileName) {
        String encoding = "UTF-8";
        File file = new File(fileName);
        Long filelength = file.length();
        byte[] filecontent = new byte[filelength.intValue()];
        FileInputStream in = null;
        try {
            in = new FileInputStream(file);
            in.read(filecontent);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }finally{
            try {
                if (in != null) {
                    in.close();
                }
            }catch(IOException e){
                e.printStackTrace();
            }
        }
        try {
            return new String(filecontent, encoding);
        } catch (UnsupportedEncodingException e) {
            System.err.println("The OS does not support " + encoding);
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 写入文件
     * @param data
     * @param filePath
     * @throws Exception
     */
    public static void writeFile(String data, String filePath) throws Exception {
        File file = new File(filePath);
        File fileParent = file.getParentFile();
        if(!fileParent.exists()) {
            fileParent.mkdirs();
        }
        if(!file.exists()){
            file.createNewFile();
        }
        BufferedWriter bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file), "utf-8"));
        // BufferedWriter bw = new BufferedWriter(new FileWriter(file,true));
        bw.write(data);
        bw.flush();
        bw.close();
      /*  FileWriter writer = new FileWriter(file.getAbsoluteFile());
        writer.write(data+"\r\n");
        System.out.println("======================"+data+"\r\n");
        writer.flush();
        writer.close();*/
    }

    public  static void main(String [] args){

        String str = "classNew";
        System.out.println(str.lastIndexOf("New"));
        str = "Newclass";
        System.out.println(str.lastIndexOf("New"));
        str= "newclaass";
        System.out.println(str.indexOf("New"));
    }
}

package com.jy.util;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: zy
 * @Date: Created in 2018/4/26
 */
@Component
public class FacadeUtils extends BaseFacadeUtils {
    @Autowired
    private Environment env;

    @Override
    @PostConstruct
    /** 项目启动时，从配置文件、或者数据库获取 */
    public void initParam() {
        try {
            Auth auth = new Auth();
            auth.setUsername(env.getProperty("httpUtils.facade.env1.username"));
            auth.setPassword(env.getProperty("httpUtils.facade.env1.password"));
            auth.setAuthorization(env.getProperty("httpUtils.facade.authorization"));
            auth.setUrl(env.getProperty("httpUtils.facade.env1.url"));
            authMap.put(auth.getUrl(), auth);

            Auth auth1 = new Auth();
            auth1.setUsername(env.getProperty("httpUtils.facade.env2.username"));
            auth1.setPassword(env.getProperty("httpUtils.facade.env2.password"));
            auth1.setAuthorization(env.getProperty("httpUtils.facade.authorization"));
            auth1.setUrl(env.getProperty("httpUtils.facade.env2.url"));
            authMap.put(auth1.getUrl(), auth1);

            HttpUtils.PROXY_HOST = env.getProperty("httpClient.proxyHost");
            HttpUtils.PROXY_PORT = env.getProperty("httpClient.proxyPort");
            HttpUtils.PROXY_USERNAME = env.getProperty("httpClient.userName");
            HttpUtils.PROXY_PASSWORD = env.getProperty("httpClient.password");
            if(StringUtils.isNotBlank(env.getProperty("httpClient.maxConnectionNum")) ){
                HttpUtils.MAX_CONNECTION_NUM =  Integer.parseInt(env.getProperty("httpClient.maxConnectionNum"));
            }
            if(StringUtils.isNotBlank(env.getProperty("httpClient.maxPerRoute"))){
                HttpUtils.MAX_PER_ROUTE = Integer.parseInt(env.getProperty("httpClient.maxPerRoute"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    /**
     * 从缓存中获取token
     * @return
     */
    public String getTokenCache(String authKey) throws Exception {
        if(authMap.get(authKey) == null){
            throw new Exception("BaseFacadeUtils 中的 authKey: " + authKey + " 的用户为空");
        }
        return authMap.get(authKey).getToken();
    }

    @Override
    /**
     * 更新缓存中的token
     * @return
     */
    public void updateTokenCache(String authKey, String token) throws Exception {
        if(authMap.get(authKey) == null){
            throw new Exception("BaseFacadeUtils 中的 authKey: " + authKey + " 的用户为空");
        }
        authMap.get(authKey).setToken(token);
    }


    public static void main(String args[]) throws Exception {
        FacadeUtils facadeUtils = new FacadeUtils();
        Auth auth = facadeUtils.new Auth();
        auth.setUsername("test");
        auth.setPassword("test");
        auth.setAuthorization("YW5kcm9pZDphbmRyb2lk");
        auth.setUrl("http://192.168.80.52:8765/");
        facadeUtils.authMap.put(auth.getUrl(), auth);

        Map<String, String> querys = new HashMap<String, String>();
        querys.put("oe", "6103121JA");
        try {
            // 正常调用
//            JSONObject jsonObject = facadeUtils.doPostFile("http://192.168.120.10:8765/","vehicle-service/imageOCRs", querys, byt, "1.jpg");
            // 压缩调用
         //   JSONObject jsonObject = facadeUtils.doCompressGet("part-service/partDetails", querys);
         //   System.out.println(jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}

package com.jy.util;

import java.lang.reflect.Field;

/**
 * @Author: zy
 * @Date: Created in 2018/6/5
 */
public class ClassUtils {
    public static boolean isClassHasField(Class cls, String filedName){
        Field[] fields = cls.getDeclaredFields();
        boolean b = false;
        for(Field field : fields){
            if(field.getName().equals(filedName)){
                b = true;
                break;
            }
        }
        return b;
    }
}

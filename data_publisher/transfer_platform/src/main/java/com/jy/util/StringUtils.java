package com.jy.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 */
public class StringUtils {

	public StringUtils() {
		super();
		// TODO Auto-generated constructor jy
	}

	public static String escapeSQLTags(String input)
	{
		return input;
	}

	public static String substring(String str, int max) {
		String tmp = empty2Null(str);
		if (tmp == null || tmp.length() <= max)
			return str;
		else
			return str.substring(0, max);
	}

	public static String randomString(int length) {
		String str = null;
		if (length <= 0)
			return null;
		String charset = "abcdefghijklmnopqrstuvwxyz1234567890!#$@%&*-=+|/ABCDEFGHIJKLMNOQPRSTUVWXYZ";
		Random r = new Random();
		Random r1 = new Random();
		StringBuffer bf = new StringBuffer();
		int ba = Math.abs(r1.nextInt() % length) + 1;
		for (int i = 0; i < ba; i++) {
			int radix = Math.abs(r.nextInt(ba) % charset.length());
			char c = charset.charAt(radix);
			bf.append(c);
		}

		str = bf.toString();
		return str;
	}

	public static String null2Empty(String s) {
		if (s == null)
			s = "";
		return s;
	}

	public static String empty2Null(String s) {
		if (s != null && s.trim().length() == 0)
			s = null;
		return s;
	}

	public static boolean isNumeric(String str) {
		if (str == null)
			return false;
		int sz = str.length();
		for (int i = 0; i < sz; i++)
			if (!Character.isDigit(str.charAt(i)))
				return false;

		return true;
	}

	public static boolean isBlank(String str) {
		int strLen;
		if (str == null || (strLen = str.length()) == 0)
			return true;
		for (int i = 0; i < strLen; i++)
			if (!Character.isWhitespace(str.charAt(i)))
				return false;

		return true;
	}

	public static boolean isEmpty(String s) {
		return s == null || s.length() == 0 || s.trim().length()==0;
	}

	public static boolean isEmptyObj(Object o) {
		return o == null || "".equals(o.toString().trim())
				|| "null".equalsIgnoreCase(o.toString())
				|| "undefined".equalsIgnoreCase(o.toString());
	}

	public static String toHtml(String s) {
		String html = s;
		if (s == null || s.length() == 0)
			return "&nbsp";
		char symbol[] = { '&', '<', '>', '"', '\r' };
		String obj[] = { "&amp;", "&lt;", "&gt;", "&quot;", "<br>" };
		for (int i = 0; i < symbol.length; i++)
			html = html.replaceAll(String.valueOf(symbol[i]), obj[i]);

		return html;
	}

	public static boolean notEmpty(String s) {
		return s != null && s.length() != 0 && s.trim().length()!=0;
	}

	public static boolean notEmptyObj(Object o) {
		return o != null && !"".equals(o.toString().trim())
				&& !"null".equalsIgnoreCase(o.toString())
				&& !"undefined".equalsIgnoreCase(o.toString());
	}

	/**
	 * 可变参数判断不为空
	 * @param os
	 * @return
	 */
	public static boolean notEmpty(Object... os) {
		boolean flag = true;
		for (Object o : os) {
			if (flag) {
				flag = o != null && !"".equals(o.toString())
						&& !"null".equalsIgnoreCase(o.toString())
						&& !"undefined".equalsIgnoreCase(o.toString());
			}
		}
		return flag;
	}

	/**
	 * 去除所有空格
	 * @param str
	 * @return
	 */
	public static String trimAll(String str){
		return str.replaceAll(" ", "");
	}

	public static String toQueryStr(String s) {
		if (s.indexOf(",") != -1) {
			String[] tmp = s.split(",");
			StringBuffer str = new StringBuffer();
			for (int i = 0; i < tmp.length; i++) {
				str.append("'").append(tmp[i].trim()).append("',");
			}
			return str.toString().substring(0, str.lastIndexOf(","));
		}
		return "'" + s + "'";
	}
	public static String getLineSeparatoByOs() {
		return System.getProperty("line.separator");
	}

	public static String getGUID() {
		return UUID.randomUUID().toString().replaceAll("-", "");
	}

	public static String getUUID() {
		return UUID.randomUUID().toString();
	}

	/**
	 * 生成指定长度的随机数
	 * @param length 长度
	 * @return
	 */
	public static String randomStr(int length){
		if (length <= 0)
			return "";
		String charset = "0123456789";
		Random r = new Random();
		StringBuffer bf = new StringBuffer();
		for (int i = 0; i < length; i++) {
			int radix = r.nextInt(10);
			char c = charset.charAt(radix);
			bf.append(c);
		}
		return bf.toString();
	}

	public static boolean isDate(String str){

		if(!StringUtils.isEmpty(str)){
			if(str.split(" ").length == 2){
				if(str.split("-").length == 3){
					if(str.split(":").length == 3){
						return true;
					}
				}
			}
		}
		return false;
	}

	//验证是否是yyyy-MM-dd HH:mm:ss时间格式的数据(区分平年和闰年)
	public static boolean isDate1(String str){
		if(str != null && !"".equals(str)){
			String datePattern = "^((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29))\\s+([0-1]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$";
			Pattern pattern = Pattern.compile(datePattern);
			Matcher match = pattern.matcher(str);
			return match.matches();
		} else {
			return false;
		}
	}

	public static void main(String[] args){
		String str = "[{\"cxljfzid\":\"4028801c0c3834e4010c3834e62a004e\",\"ljsl\":\"1\",\"ycljh\":\"4G8 807 065 BD GRU\",\"ljbzbid\":\"402880670c38aa0c010c38b0d57612d5\",\"id\":\"984BFB710211F446E055000000000001\",\"clzlid\":\"4028b286525262a20152ab4c0d9b3251\",\"ljbzbm\":\"001100\"},{\"cxljfzid\":\"4028801c0c3834e4010c3834e62a004e\",\"ljsl\":\"1\",\"ycljh\":\"4G8 807 065 BA GRU\",\"ljbzbid\":\"402880670c38aa0c010c38b0d57612d5\",\"id\":\"984BFB71079DF446E055000000000001\",\"clzlid\":\"4028b286525262a20152ab4c0d9b3251\",\"ljbzbm\":\"001100\"},{\"cxljfzid\":\"4028801c0c3834e4010c3834e62a004e\",\"ljsl\":\"1\",\"ycljh\":\"4G8 807 065 BG GRU\",\"ljbzbid\":\"402880670c38aa0c010c38b0d57612d5\",\"id\":\"984BFB7104C1F446E055000000000001\",\"clzlid\":\"4028b286525262a20152ab4c0d9b3251\",\"ljbzbm\":\"001100\"}]";
		JSONArray array = JSON.parseArray(str);
		List<Map<String,Object>> fList = new ArrayList<>();
		for(int i=0; i< array.size();i++){
			JSONObject obj = array.getJSONObject(i);
            obj.put("repairRelation",null);
			fList.add(obj);
		}
		Map<String, List<Map<String,Object>>> fMap = fList.stream().collect(Collectors.groupingBy(e -> e.get("ljbzbid").toString()));
		Map<String, Object> repairRelationMap = fList.stream().collect(Collectors.toMap(s1->s1.get("ljbzbid").toString(), s2->s2.getOrDefault("repairRelation", ""), (k1, k2)->k1));
		System.out.println(repairRelationMap.get("402880670c38aa0c010c38b0d57612d5"));

		System.out.println(fList.get(0).get("repairRelation"));
		String dat = (String) fList.get(0).getOrDefault("repairRelation", "");
		System.out.println(dat);
	}
}
package com.jy.sqlUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * Created by anxing on 2018/9/13.
 */
public class SqlMapperProvider {

    private Logger logger = LoggerFactory.getLogger(SqlMapperProvider.class);

    public String insert(Map<String,List> map){
        StringBuffer sqlBuffer = new StringBuffer();
        List<String> list = map.get("list");
        sqlBuffer.append("insert all ");
        for (int i=0 ;i<list.size();i++){
            sqlBuffer.append(list.get(i));
            logger.warn(String.valueOf(list.get(i)));
        }
        sqlBuffer.append("select 1 from dual");
        return String.valueOf(sqlBuffer);
    }

//    public String delete(BaseDataDTO dataDTO){
//        StringBuffer sBuffer = new StringBuffer();
//        sBuffer.append("truncate table " +dataDTO.getTableName());
//        logger.warn(String.valueOf(sBuffer));
//        return String.valueOf(sBuffer);
//    }
}

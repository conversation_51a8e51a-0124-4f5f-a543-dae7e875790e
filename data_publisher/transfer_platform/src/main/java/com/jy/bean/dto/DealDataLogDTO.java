package com.jy.bean.dto;

import lombok.Data;

/**
 * @Auther: miao
 * @Date: 2018/7/19 09:37
 * @Description:
 */
@Data
public class DealDataLogDTO {
    //批次号
    private String versionCode;
    //平台编码
    private String platformCode;
    //平台名称
    private String platformName;
    //处理节点名称
    private String dealNodeName;
    //状态
    private String state;
    //文件名
    private String fileName;
    //表名
    private String tableName;

    //处理时间
    private String dealTime;
    //发送条数
    private Integer sendNum;
    private Integer sendCount;
    //用于显示，不做db映射
    //状态名
    private String stateName;
    private String mainLogId;
    private String description;//表描述
    private String orgCode;
    private String orgName;
    private String clzlid;
}

package com.jy.bean.common;

public enum VersionState {
    SUCCESS("2", "发送成功" ),
    ERROR("3", "发送失败" ),
    NO_DATA("4", "数据为空" ),
    SUCCESS_ACCEPTED("40","待转换" ),
    ERROR_TRANSFER("50","转换失败" ),
    SUCCESS_TRANSFER("51","转换成功" ),
    ERROR_COMPARE("60","对比失败" ),
    SUCCESS_COMPARE("61","对比成功" ),
    ERROR_BACK("70","回写失败" ),
    SUCCESS_BACK("71","回写成功" );
    private String code;
    private String message;

    VersionState(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static String messageOf(String code) {
        for (VersionState a : VersionState.values()) {
            if (a.code.equals(code)){
                return a.message;
            }
        }
        return "";
    }
}

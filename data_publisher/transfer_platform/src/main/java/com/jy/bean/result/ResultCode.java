/*
package com.jy.bean.result;

*/
/**
 * Created by Administrator on 2017/6/23.
 *//*

public enum ResultCode {
    SUCCESS("000000", "成功" ),

    NO_DATA_ERROR("100100", "查询无结果" ),
    NO_BRAND_DATA_ERROR("100200", "品牌编码错误或无此品牌数据" ),

    REQUEST_PARAMS_IS_NULL("200100", "请求参数为空" ),
    PARAMETER_ERROR("200200", "参数错误" ),
    
    REQUIRE_PARAM_IS_NULL("200300", "必传参数有为空的,请检查" ),
    
    ERROR( "300000", "服务器异常" ),
    INTFC_REQUEST_ERROR("300100", "接口请求异常"),
    VALIDATE_ERROR( "300101", "参数校验异常" ),
    BAD_SQL_ERROR("300102", "SQL异常"),

    USER_IS_NO_REMAINING_NUMBER("400101", "用户没有可查询条数" ),

    REQUEST_USER_IS_NULL("500101", "接口请求用户名密码不能为空" ),
    REQUEST_USER_NAME_IS_NULL("500102", "接口请求用户名为空" ),
    REQUEST_PASSWORD_IS_NULL("500103", "接口请求密码为空" ),

    REQUEST_USER_NAME_IS_ERROR("600101", "接口请求用户名不存在" ),
    REQUEST_PASSWORD_IS_ERROR("600102", "接口请求密码错误" ),

    FILE_IS_NULL("700101", "上传文件为空" ),
    FIlE_CREATE_FAIL("700102","创建文件失败"),

    ERROR_COMPARE("800101","不能对账");

    private String code;
    private String message;

    ResultCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
*/

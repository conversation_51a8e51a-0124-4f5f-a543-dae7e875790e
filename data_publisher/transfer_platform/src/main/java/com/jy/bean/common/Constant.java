package com.jy.bean.common;

/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2017/12/18
 */
public class Constant {

    public static final String ERROR_RECEIVE_TABLE = "compare_error_data";

    public static final String YES_STRING = "1";

    public static final String NO_STRING = "0";

    public static final String ROLE_QUERY = "ROLE_QUERY";

    public static final String ROLE_UPDATE = "ROLE_UPDATE";


    //配件默认排序
    public static final String AM_DEFALUT_SORT = "amOrder";
    public static final String AM_VEHICLE_DEFALUT_SORT = "amYear";


    /**
     * 日志追踪Id
     */
    public static final String LOG_TRACE_ID = "log_trace_id";

    public static final int MILEAGE_MAX = 200000;
    public static final int MONTH_MAX = 288;

    public static final String WL_BRAND_CODE = "WLM0";

    public static final String OPERATE_INSERT = "insert";

    public static final String OPERATE_UPDATE = "update";

    public static final String C_DB_SUFFIX = "c_";

    public static final String P_DB_SUFFIX = "p_";

    public static final String F_DB_SUFFIX = "f_";

    public static boolean IS_CONTINUE = true;

    public static final String INC_TABLE = "ori_repair_inc";

    public static final String INC_TABLE_F_INC = "f_ori_repair_id";

    public static final String INC_TABLE_INC = "ori_repair_id";

    public static final String REP_PART_NUMBER = "rep_tmp_part_number";

    public static final String PART_NUMBER = "tmp_part_number";

    public static final String REP_TABLE = "rep_out_relation";

    public static final String REP_NUMBER_TABLE = "rep_out_part_number";

    public static int RECEIVE_PART_LIMIT = 10;
}

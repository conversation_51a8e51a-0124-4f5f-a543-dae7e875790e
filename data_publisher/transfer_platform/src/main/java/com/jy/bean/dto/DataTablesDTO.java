package com.jy.bean.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by jdd on 2018/11/29.
 */
public class DataTablesDTO implements Serializable{

    private String id;
    private String tableName;
    private String isEnable;
    private String createUser;
    private Date createTime;
    private String updateUser;
    private Date updateTime;
    private String type;
    private String tableStructure;
    private String isSeparate;
    private String mergeTable;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getTableStructure() {
        return tableStructure;
    }

    public void setTableStructure(String tableStructure) {
        this.tableStructure = tableStructure;
    }

    public String getIsSeparate() {
        return isSeparate;
    }

    public void setIsSeparate(String isSeparate) {
        this.isSeparate = isSeparate;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMergeTable() {
        return mergeTable;
    }

    public void setMergeTable(String mergeTable) {
        this.mergeTable = mergeTable;
    }
}

package com.jy.bean.common;

public enum BatchNoStatus {
    SUCCESS_RECEIVE("101", "数据获取完成" ),
    SUCCESS_WAIT("202", "数据获取完成,进入等待线程" ),
    ACCEPTEDING("501", "数据获取中" ),
    ERROR_ACCEPTED("021", "数据获取失败" ),
    SUCCESS_ACCEPTED("020","数据获取成功" ),
    TRANSFERING("005","数据转换中" ),
    ERROR_TRANSFER("903","数据转换失败" ),
    SUCCESS_TRANSFER("006","数据转换完成" ),
    COMPAREING("007","数据增量对比中" ),
    ERROR_COMPARE("904","数据增量对比失败" ),
    SUCCESS_COMPARE("008","数据增量对比完成" ),
    UPDATEING("009","数据回写中" ),
    ERROR_UPDATEI("905","数据回写失败" ),
    SUCCESS_UPDATEI("010","数据回写完成" ),
    //FITTING("009","数据增量组装中" ),
    ERROR_FIT("906","数据增量组装失败" ),
    //SUCCESS_FIT("010","数据增量组装完成" ),
    //SENDING("002","数据发送中" ),
    ERROR_SEND("907","数据发送失败" ),
    SUCCESS("200","数据发布成功" ),
    ERROR("500","转换平台失败" );
    private String status;
    private String message;

    BatchNoStatus(String status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getStatus() {
        return status;
    }

    public String getMessage() {
        return message;
    }

    public static String messageOf(String status) {
        for (BatchNoStatus a : BatchNoStatus.values()) {
            if (a.status.equals(status)){
                return a.message;
            }
        }
        return "";
    }
}

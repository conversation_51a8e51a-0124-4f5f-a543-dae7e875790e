package com.jy.bean.po;

import com.jy.bean.common.BatchNoStatus;
import com.jy.bean.dto.BaseDataDTOs;
import com.jy.util.StringUtils;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/5/22
 */
@Data
public class SendDetail extends BasePo {
    private String id;
    private String tableName;
    private String cTableName;
    private String status;
    private String message;
    private String batchNo;
    private String mainBatchNo;
    private String nodeName;
    private String serviceName;
    private String clientCode;
    private Integer total;

    public SendDetail(){}

    public SendDetail(String mainBatchNo, String batchNo, String tableName, String cTableName, Integer total){
        this.id = StringUtils.getGUID();
        this.tableName = tableName;
        this.cTableName = cTableName;
        this.mainBatchNo = mainBatchNo;
        this.batchNo = batchNo;
        this.total = total;
    }

    public void setResult(String status, String message){
        this.status = status;
        this.message = message;
    }

}

package com.jy.bean.vo;

/**
 * <AUTHOR>
 * Created by Administrator on 2019/3/11.
 */
public class ReceiveGroupDataVo {

    private String id;
    //主批次号
    private String mainVersionCode;
    //小批次号
    private String versionCode;
    //获取数据的表名
    private String tableName;
    //车组编号
    private String groupCode;
    //品牌编号
    private String brandCode;
    //数据获取状态
    private String dataState;
    //数据发送状态
    private String sendState;
    //发送的表
    private String sendTable;
    //错误信息
    private String errMsg;
    //车组id
    private String groupId;
    //发送时间
    private String receiveDate;
    //处理时间
    private String endDate;
    //数据类型
    private String dataType;
    //品牌id
    private String brandId;
    //表后缀
    private String tableSuffix;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMainVersionCode() {
        return mainVersionCode;
    }

    public void setMainVersionCode(String mainVersionCode) {
        this.mainVersionCode = mainVersionCode;
    }

    public String getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(String versionCode) {
        this.versionCode = versionCode;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getBrandCode() {
        return brandCode;
    }

    public void setBrandCode(String brandCode) {
        this.brandCode = brandCode;
    }

    public String getDataState() {
        return dataState;
    }

    public void setDataState(String dataState) {
        this.dataState = dataState;
    }

    public String getSendState() {
        return sendState;
    }

    public void setSendState(String sendState) {
        this.sendState = sendState;
    }

    public String getSendTable() {
        return sendTable;
    }

    public void setSendTable(String sendTable) {
        this.sendTable = sendTable;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getReceiveDate() {
        return receiveDate;
    }

    public void setReceiveDate(String receiveDate) {
        this.receiveDate = receiveDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getBrandId() {
        return brandId;
    }

    public void setBrandId(String brandId) {
        this.brandId = brandId;
    }

    public String getTableSuffix() {
        return tableSuffix;
    }

    public void setTableSuffix(String tableSuffix) {
        this.tableSuffix = tableSuffix;
    }
}

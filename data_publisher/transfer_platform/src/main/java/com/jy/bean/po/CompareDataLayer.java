package com.jy.bean.po;

import lombok.Data;

import java.math.BigInteger;

/**
 * <AUTHOR>
 * @date 2019/8/19
 */
@Data
public class CompareDataLayer {
    private Integer count;
    private BigInteger maxVersionId;
    private BigInteger minVersionId;
    private String baseTableName;

    private String clientCode;
    private String clientUrl;
    private String compareBatchNo;


    private String partTableSuffix;

    //private Integer begin;

    public CompareDataLayer(){}

    public CompareDataLayer(String baseTableName, BigInteger maxVersionId, BigInteger minVersionId, Integer count){
        this.baseTableName = baseTableName;
        this.maxVersionId = maxVersionId;
        this.minVersionId = minVersionId;
        this.count = count;
    }
}

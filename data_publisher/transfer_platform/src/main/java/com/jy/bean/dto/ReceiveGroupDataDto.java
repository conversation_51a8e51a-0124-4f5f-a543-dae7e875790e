package com.jy.bean.dto;

import com.jy.util.StringUtils;
import lombok.Data;

/**
 * Created by jdd on 2018/12/5.
 */
@Data
public class ReceiveGroupDataDto {

    private String id;
    private String groupId;
    private String groupName;
    private String groupCode;
    private String mainVersionCode;
    private String receiveDataState;
    private String gainDataState;
    private String state;
    private Integer errNum;
    private String brandId;
    private String brandName;
    private String brandCode;
    private String vehicleId;
    private String vehicleName;
    private String vehicleCode;
    private String seriesId;
    private String seriesName;
    private String seriesCode;
    private String isModify;
    private String pMainVersionCode;
    private String tableSuffix;
    private String errMsg;
    private String isStatistics;
    private String delFlag;
    private String dataType;
    private String clientCode;
    private String clientUrl;

    //查询用
    private String versionCode;
    private String tableName;
    private String saveTable;
    //对账批次
    private String compareBatchNo;
    private String clientKey;

    public ReceiveGroupDataDto(){}
    public ReceiveGroupDataDto(String compareBatchNo, String mainVersionCode, String clientUrl, String clientCode){
        this.compareBatchNo = compareBatchNo;
        this.mainVersionCode = mainVersionCode;
        this.clientCode = clientCode;
        this.clientUrl = clientUrl;
        this.state = "008";
        this.errNum = 0;
        this.isModify = "0";
        this.id = StringUtils.getGUID();
        this.delFlag = "0";
    }

    public String getpMainVersionCode() {
        return pMainVersionCode;
    }

    public void setpMainVersionCode(String pMainVersionCode) {
        this.pMainVersionCode = pMainVersionCode;
    }
}

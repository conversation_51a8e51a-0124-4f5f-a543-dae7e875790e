package com.jy.bean.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/8/19
 */
@Data
public class CompareDataDTO {

    private String baseTableName;
    private String tableName;
    private String path;
    private String baseCompareLayerKey; //维度字段
    private String compareLayerKey;
    private List<String> compareLayerValue; //维度值

    private String clientUrl;
    private String clientCode;

    public CompareDataDTO(){}

    public CompareDataDTO(String baseTableName, String tableName, String path){
        this.baseTableName = baseTableName;
        this.tableName = tableName;
        this.path = path;
    }

}

package com.jy.bean.dto;

import java.util.HashMap;
import java.util.Map;

public class PageCustom<T> {
	
	private Integer limit = 10;
	private Integer page = 1;
	private Long total;
	private T data;

	public Integer getLimit() {
		return limit;
	}

	public void setLimit(Integer limit) {
		this.limit = limit;
	}

	public Integer getPage() {
		return page;
	}

	public void setPage(Integer page) {
		if( page > 0 ){
			this.page = page;
		}else {
			this.page = 1;
		}
	}

	public Long getTotal() {
		return total;
	}

	public void setTotal(Long total) {
		this.total = total;
	}

	public T getData() {
		return data;
	}

	public void setData(T data) {
		this.data = data;
	}
	
	public Map<String, Object> resuest(){
		Map<String, Object> res = new HashMap<>();
		res.put( "limit" , getPage() * getLimit() );
		res.put( "row",  (getPage()-1) * getLimit() + 1 );
		return res;
	}
}

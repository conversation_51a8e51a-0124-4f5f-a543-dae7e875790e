package com.jy.bean.po;

import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 菜单
 */
public class SysPowerPo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 数据库主键
	 */
	private String id;
	
	/**
	 * 编码ID
	 */
	private String powerCode;
	
	/**
	 * 菜单级别
	 */
	private Integer powerLevel;
	
	/**
	 * 菜单对应的父亲ID
	 */
	private String parentId;
	
	/**
	 * 菜单名称
	 */
	private String powerName;
	
	/**
	 * 菜单对应的路径
	 */
	private String url;
	
	private String icon;
	
	/**
	 * 数据创建时间
	 */
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date ctime;
	
	/**
	 * 数据修改时间
	 */
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date utime;

	/**
	 * 权限类型1：页面；2：接口
	 */
	private Integer type;
	
	private Integer delFlag;

	/**
	 * 用户是否启用此接口，用户接口权限设置时用
	 */
	private boolean checked;

	/**
	 * 排序字段
	 */
	private Integer powerOrder;
	
	public void setChecked(boolean checked) {
		this.checked = checked;
	}

	public Integer getPowerLevel() {
		return powerLevel;
	}

	public void setPowerLevel(Integer powerLevel) {
		this.powerLevel = powerLevel;
	}

	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	public String getPowerName() {
		return powerName;
	}

	public void setPowerName(String powerName) {
		this.powerName = powerName;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Date getCtime() {
		return ctime;
	}

	public void setCtime(Date ctime) {
		this.ctime = ctime;
	}

	public Date getUtime() {
		return utime;
	}

	public void setUtime(Date utime) {
		this.utime = utime;
	}

	public String getIcon() {
		return icon;
	}

	public void setIcon(String icon) {
		this.icon = icon;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getDelFlag() {
		return delFlag;
	}

	public void setDelFlag(Integer delFlag) {
		this.delFlag = delFlag;
	}

	public String getPowerCode() {
		return powerCode;
	}

	public void setPowerCode(String powerCode) {
		this.powerCode = powerCode;
	}

	public boolean isChecked() {
		return checked;
	}

	public Integer getPowerOrder() {
		return powerOrder;
	}

	public void setPowerOrder(Integer powerOrder) {
		this.powerOrder = powerOrder;
	}

	@Override
	public String toString() {
		return "SysPowerPo{" +
				"id='" + id + '\'' +
				", powerCode='" + powerCode + '\'' +
				", powerLevel=" + powerLevel +
				", parentId='" + parentId + '\'' +
				", powerName='" + powerName + '\'' +
				", url='" + url + '\'' +
				", icon='" + icon + '\'' +
				", ctime=" + ctime +
				", utime=" + utime +
				", type=" + type +
				", delFlag=" + delFlag +
				", checked=" + checked +
				", powerOrder=" + powerOrder +
				'}';
	}
}

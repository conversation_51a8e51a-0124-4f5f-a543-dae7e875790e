package com.jy.bean.po;

import com.jy.util.StringUtils;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/8/21
 */
@Data
public class SendTable {

    private String id;
    private String mainVersionCode;
    private String versionCode;
    private String sendTable;
    private String state;
    private String errMsg;
    private String handleTable;
    private String sendCode;
    private Date createDate;
    private Date errCode;

    private String versionState;
    private String stateName;
    private String versionStateName;

    public SendTable(){}

    public SendTable(String sendTable, String handleTable, String mainVersionCode, String versionCode, String sendCode, String state){
        this.id = StringUtils.getGUID();
        this.sendTable = sendTable;
        this.handleTable = handleTable;
        this.mainVersionCode = mainVersionCode;
        this.versionCode = versionCode;
        this.sendCode = sendCode;
        this.state = state;
    }

}

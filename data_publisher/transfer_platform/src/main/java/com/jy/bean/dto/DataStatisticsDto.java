package com.jy.bean.dto;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/4/1.
 */
public class DataStatisticsDto {

    private String id = "";
    private String czid = "";
    private String zcjsl = "";
    private String zppsl = "";
    private String zcxxsl = "";
    private String zczsl = "";
    private String zcxsl = "";
    private String cjsl = "";
    private String ppsl = "";
    private String cxxsl = "";
    private String czsl = "";
    private String cxsl = "";
    private String lpcbsl = "";
    private String cxljsl = "";
    private String cxljxssl = "";
    private String czhcmsl = "";
    private String cxbzjsl = "";
    private String cxfbzjsl = "";
    private String cxtpsl = "";
    private String cxtprdsl = "";
    private String czljsl = "";
    private String czbzjsl = "";
    private String czfbzjsl = "";
    private String ppljsl = "";
    private String ppbzjsl = "";
    private String ppfbzj = "";
    private String ppbsbsl = "";
    private String cxxbsbsl = "";
    private String czbsbsl = "";
    private String cxbsbsl = "";
    private String dataType = "";
    private String zppbsbsl = "";
    private String zcxxbsbsl = "";
    private String zcxbsbsl = "";
    private String zczbsbsl = "";
    private Date addTime;
    private String zlpcbsl = "";
    private String receiveDate = "";
    private String xytpsl = "";
    private String cztpzl = "";
    private String czljzl = "";
    private String group_part_num = "";
    private String brand_part_num = "";

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCzid() {
        return czid;
    }

    public void setCzid(String czid) {
        this.czid = czid;
    }

    public String getZcjsl() {
        return zcjsl;
    }

    public void setZcjsl(String zcjsl) {
        this.zcjsl = zcjsl;
    }

    public String getZppsl() {
        return zppsl;
    }

    public void setZppsl(String zppsl) {
        this.zppsl = zppsl;
    }

    public String getZcxxsl() {
        return zcxxsl;
    }

    public void setZcxxsl(String zcxxsl) {
        this.zcxxsl = zcxxsl;
    }

    public String getZczsl() {
        return zczsl;
    }

    public void setZczsl(String zczsl) {
        this.zczsl = zczsl;
    }

    public String getZcxsl() {
        return zcxsl;
    }

    public void setZcxsl(String zcxsl) {
        this.zcxsl = zcxsl;
    }

    public String getCjsl() {
        return cjsl;
    }

    public void setCjsl(String cjsl) {
        this.cjsl = cjsl;
    }

    public String getPpsl() {
        return ppsl;
    }

    public void setPpsl(String ppsl) {
        this.ppsl = ppsl;
    }

    public String getCxxsl() {
        return cxxsl;
    }

    public void setCxxsl(String cxxsl) {
        this.cxxsl = cxxsl;
    }

    public String getCzsl() {
        return czsl;
    }

    public void setCzsl(String czsl) {
        this.czsl = czsl;
    }

    public String getCxsl() {
        return cxsl;
    }

    public void setCxsl(String cxsl) {
        this.cxsl = cxsl;
    }

    public String getLpcbsl() {
        return lpcbsl;
    }

    public void setLpcbsl(String lpcbsl) {
        this.lpcbsl = lpcbsl;
    }

    public String getCxljsl() {
        return cxljsl;
    }

    public void setCxljsl(String cxljsl) {
        this.cxljsl = cxljsl;
    }

    public String getCxljxssl() {
        return cxljxssl;
    }

    public void setCxljxssl(String cxljxssl) {
        this.cxljxssl = cxljxssl;
    }

    public String getCzhcmsl() {
        return czhcmsl;
    }

    public void setCzhcmsl(String czhcmsl) {
        this.czhcmsl = czhcmsl;
    }

    public String getCxbzjsl() {
        return cxbzjsl;
    }

    public void setCxbzjsl(String cxbzjsl) {
        this.cxbzjsl = cxbzjsl;
    }

    public String getCxfbzjsl() {
        return cxfbzjsl;
    }

    public void setCxfbzjsl(String cxfbzjsl) {
        this.cxfbzjsl = cxfbzjsl;
    }

    public String getCxtpsl() {
        return cxtpsl;
    }

    public void setCxtpsl(String cxtpsl) {
        this.cxtpsl = cxtpsl;
    }

    public String getCxtprdsl() {
        return cxtprdsl;
    }

    public void setCxtprdsl(String cxtprdsl) {
        this.cxtprdsl = cxtprdsl;
    }

    public String getCzljsl() {
        return czljsl;
    }

    public void setCzljsl(String czljsl) {
        this.czljsl = czljsl;
    }

    public String getCzbzjsl() {
        return czbzjsl;
    }

    public void setCzbzjsl(String czbzjsl) {
        this.czbzjsl = czbzjsl;
    }

    public String getCzfbzjsl() {
        return czfbzjsl;
    }

    public void setCzfbzjsl(String czfbzjsl) {
        this.czfbzjsl = czfbzjsl;
    }

    public String getPpljsl() {
        return ppljsl;
    }

    public void setPpljsl(String ppljsl) {
        this.ppljsl = ppljsl;
    }

    public String getPpbzjsl() {
        return ppbzjsl;
    }

    public void setPpbzjsl(String ppbzjsl) {
        this.ppbzjsl = ppbzjsl;
    }

    public String getPpfbzj() {
        return ppfbzj;
    }

    public void setPpfbzj(String ppfbzj) {
        this.ppfbzj = ppfbzj;
    }

    public String getPpbsbsl() {
        return ppbsbsl;
    }

    public void setPpbsbsl(String ppbsbsl) {
        this.ppbsbsl = ppbsbsl;
    }

    public String getCxxbsbsl() {
        return cxxbsbsl;
    }

    public void setCxxbsbsl(String cxxbsbsl) {
        this.cxxbsbsl = cxxbsbsl;
    }

    public String getCzbsbsl() {
        return czbsbsl;
    }

    public void setCzbsbsl(String czbsbsl) {
        this.czbsbsl = czbsbsl;
    }

    public String getCxbsbsl() {
        return cxbsbsl;
    }

    public void setCxbsbsl(String cxbsbsl) {
        this.cxbsbsl = cxbsbsl;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getZppbsbsl() {
        return zppbsbsl;
    }

    public void setZppbsbsl(String zppbsbsl) {
        this.zppbsbsl = zppbsbsl;
    }

    public String getZcxxbsbsl() {
        return zcxxbsbsl;
    }

    public void setZcxxbsbsl(String zcxxbsbsl) {
        this.zcxxbsbsl = zcxxbsbsl;
    }

    public String getZcxbsbsl() {
        return zcxbsbsl;
    }

    public void setZcxbsbsl(String zcxbsbsl) {
        this.zcxbsbsl = zcxbsbsl;
    }

    public String getZczbsbsl() {
        return zczbsbsl;
    }

    public void setZczbsbsl(String zczbsbsl) {
        this.zczbsbsl = zczbsbsl;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public String getZlpcbsl() {
        return zlpcbsl;
    }

    public void setZlpcbsl(String zlpcbsl) {
        this.zlpcbsl = zlpcbsl;
    }

    public String getReceiveDate() {
        return receiveDate;
    }

    public void setReceiveDate(String receiveDate) {
        this.receiveDate = receiveDate;
    }

    public String getXytpsl() {
        return xytpsl;
    }

    public void setXytpsl(String xytpsl) {
        this.xytpsl = xytpsl;
    }

    public String getCztpzl() {
        return cztpzl;
    }

    public void setCztpzl(String cztpzl) {
        this.cztpzl = cztpzl;
    }

    public String getCzljzl() {
        return czljzl;
    }

    public void setCzljzl(String czljzl) {
        this.czljzl = czljzl;
    }

    public String getGroup_part_num() {
        return group_part_num;
    }

    public void setGroup_part_num(String group_part_num) {
        this.group_part_num = group_part_num;
    }

    public String getBrand_part_num() {
        return brand_part_num;
    }

    public void setBrand_part_num(String brand_part_num) {
        this.brand_part_num = brand_part_num;
    }
}

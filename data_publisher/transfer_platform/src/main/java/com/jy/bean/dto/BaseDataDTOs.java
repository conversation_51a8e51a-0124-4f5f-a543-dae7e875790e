package com.jy.bean.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class BaseDataDTOs {

    private String mainBatchNo;
    private String tableName;
    private String groupId;
    private String groupCode;
    private String groupName;
    private String brandId;
    private String brandCode;
    private String brandName;
    private String clVehicleId;
    private String clVehicleCode;
    private String clVehicleName;
    private String endFlag;//主批次是否推送完成
    private List<BaseDataDTO> data;


    private int total;
    private String batchNo;

    private String clientCode;

    private Integer batchOrder;


    public BaseDataDTOs(){}

    public BaseDataDTOs(String mainBatchNo, String batchNo, String tableName, int total){
        this.mainBatchNo = mainBatchNo;
        this.batchNo = batchNo;
        this.tableName = tableName;
        this.total = total;
        int hour = new Date().getHours();
        Integer batchOrder = (8 <= hour && hour <= 20) ? 10 : 20;
        this.batchOrder = batchOrder;
    }

}

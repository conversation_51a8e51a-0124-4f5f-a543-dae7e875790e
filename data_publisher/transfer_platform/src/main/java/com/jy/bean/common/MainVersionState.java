package com.jy.bean.common;

public enum MainVersionState {
    //SUCCESS_CREATED("500", "数据队列中" ),
    //ACCEPTEDING("501", "数据获取中" ),
    //ERROR_ACCEPTED("021", "数据获取失败" ),
    SUCCESS_ACCEPTED("020","数据获取成功" ),
    TRANSFERING("005","数据转换中" ),
    ERROR_TRANSFER("903","数据转换失败" ),
    SUCCESS_TRANSFER("006","数据转换完成" ),
    COMPAREING("007","数据增量对比中" ),
    ERROR_COMPARE("904","数据增量对比失败" ),
    SUCCESS_COMPARE("008","数据增量对比完成" ),
    FITTING("009","数据增量组装中" ),
    ERROR_FIT("905","数据增量组装失败" ),
    SUCCESS_FIT("010","数据增量组装完成" ),
    SENDING("002","数据发送中" ),
    ERROR_SEND("901","数据发送失败" ),
    SUCCESS("200","数据发布" ),
    ERROR("2000","转换平台失败" );
    private String code;
    private String message;

    MainVersionState(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static String messageOf(String code) {
        for (MainVersionState a : MainVersionState.values()) {
            if (a.code.equals(code)){
                return a.message;
            }
        }
        return "";
    }
}

package com.jy.bean.dto;

import lombok.Data;

import java.util.Map;

/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2018/1/31
 */
@Data
public class BaseDataDTO implements Cloneable{

    private String id;
    /**  insert, update, delete */
    private String operate;

    private String tableName;

    /**  批次号 */
    private String mainBatchNo;
    /**  主键 */
    private Map<String, String> keys;
    /**  属性信息 */
    private Map<String, String> fields;

    /**  表尾缀信息 */
    private Map<String, String> suffix;
    public BaseDataDTO(){}

    @Override
    public BaseDataDTO clone() {
        BaseDataDTO baseDataDTO = null;
        try {
            baseDataDTO = (BaseDataDTO)super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        return baseDataDTO;
    }

    @Override
    public String toString() {
        return "BaseDataDTO{" +
                "id='" + id + '\'' +
                ", operate='" + operate + '\'' +
                ", tableName='" + tableName + '\'' +
                ", keys=" + keys +
                ", fields=" + fields +
                '}';
    }
}

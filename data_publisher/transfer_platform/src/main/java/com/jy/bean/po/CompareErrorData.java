package com.jy.bean.po;

import com.jy.util.StringUtils;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/8/21
 */
@Data
public class CompareErrorData {

    private String id;
    //主批次号
    private String mainVersionCode;
    //小批次号
    private String versionCode;
    //获取数据的表名
    private String tableName;
    //发送时间
    private Date receiveDate;
    private String message;//组装的数据


    public CompareErrorData(){}

    public CompareErrorData(String mainVersionCode, String versionCode, String tableName, String message){
        this.id = StringUtils.getGUID();
        this.mainVersionCode = mainVersionCode;
        this.versionCode = versionCode;
        this.tableName = tableName;
        this.message = message;
    }

}

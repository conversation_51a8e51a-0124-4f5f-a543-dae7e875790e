/**
 *
 * __   (__`\
 * (__`\   \\`\
 *  `\\`\   \\ \
 *    `\\`\  \\ \
 *      `\\`\#\\ \#
 *        \_ ##\_ |##
 *        (___)(___)##
 *         (0)  (0)`\##
 *          |~   ~ , \##
 *          |      |  \##
 *          |     /\   \##         __..---'''''-.._.._
 *          |     | \   `\##  _.--'                _  `.
 *          Y     |  \    `##'                     \`\  \
 *         /      |   \                             | `\ \
 *        /_...___|    \                            |   `\\
 *       /        `.    |                          /      ##
 *      |          |    |                         /      ####
 *      |          |    |                        /       ####
 *      | () ()    |     \     |          |  _.-'         ##
 *      `.        .'      `._. |______..| |-'|
 *        `------'           | | | |    | || |
 *                           | | | |    | || |
 *                           | | | |    | || |
 *                           | | | |    | || |     Jia <PERSON>ang
 *                     _____ | | | |____| || |
 *                    /     `` |-`/     ` |` |
 *                    \________\__\_______\__\
 *                     """""""""   """""""'"""
 */
package com.jy.bean.result;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jy.bean.common.Constant;
import com.jy.util.EmptyUtils;
import org.slf4j.MDC;

/**
 *	2016年12月9日
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class JsonResult<T> {

    private String status;
    private String message;
    private String path;
    private String traceId;
    private T result;
    private Page pageInfo;

    public JsonResult() {
        super();
        this.status = ResultStatus.SUCCESS.getStatus();
        this.message = ResultStatus.SUCCESS.getMessage();
        this.traceId = MDC.get(Constant.LOG_TRACE_ID);
    }

    public JsonResult(ResultStatus code  ) {
        super();
        this.traceId = MDC.get(Constant.LOG_TRACE_ID);
        setResultStatus(code);
    }
    public JsonResult(String code, String message ) {
        super();
        this.status = code ;
        this.message = message;
        this.traceId = MDC.get(Constant.LOG_TRACE_ID);
    }

    public void setResultStatus(ResultStatus code ){
        this.status = code.getStatus();
        this.message = code.getMessage();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public void addMessage(String message) {
        if(message.startsWith(this.message)){
            this.message = message;
        } else {
            this.message = this.message + "; " + message;
        }
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public T getResult() {
        return result;
    }
    public void setResult(T result) {
        if(EmptyUtils.isEmpty(result)){
            setResultStatus(ResultStatus.NO_DATA);
        } else{
            this.result = result;
        }
    }

    public Page getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(Page pageInfo) {
        this.pageInfo = pageInfo;
    }

    @JsonIgnore
    public boolean isSuccess(){
        return ResultStatus.SUCCESS.getStatus().equals(this.status);
    }
    @Override
    public String toString() {
        return "JsonResult [status=" + status + ", message=" + message + ", result=" + result + ", pageInfo=" + pageInfo + "]";
    }
}

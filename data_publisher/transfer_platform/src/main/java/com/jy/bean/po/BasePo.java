package com.jy.bean.po;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.Getter;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by zy on 2017/11/13.
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class BasePo implements Serializable{

    private static final long serialVersionUID = 1L;
    /*
    * To put the @JsonIgnore to the generated getter method,
    * you can use onMethod = @__( @JsonIgnore ).
    * This will generate the getter with the specific annotation.
    * For more details check http://projectlombok.org/features/GetterSetter.html
    * */
    @Getter(onMethod = @__( @JsonIgnore ))
    private String delFlag;
    @Getter(onMethod = @__( @JsonIgnore ))
    private String remark;
    @Getter(onMethod = @__( @JsonIgnore ))
    private String cBy;
    @Getter(onMethod = @__( @JsonIgnore ))
    private Date cTime;
    @Getter(onMethod = @__( @JsonIgnore ))
    private String uBy;
    @Getter(onMethod = @__( @JsonIgnore ))
    private Date uTime;
}

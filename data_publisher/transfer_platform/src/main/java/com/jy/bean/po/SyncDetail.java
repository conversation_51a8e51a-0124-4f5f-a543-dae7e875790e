package com.jy.bean.po;

import com.jy.util.StringUtils;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/5/18
 */
@Data
public class SyncDetail extends BasePo {
    private String id;
    private String clzlid;
    private String tableName;
    private String ppbm;
    private String mainBatchNo;
    private String status;

    public SyncDetail(){}

    public SyncDetail(String clzlid, String tableName, String ppbm, String mainBatchNo){
        this.id = StringUtils.getGUID();
        this.clzlid = clzlid;
        this.tableName = tableName;
        this.ppbm = ppbm;
        this.mainBatchNo = mainBatchNo;
    }

}

package com.jy.bean.common;

public enum TransferOrderEnum {

    CL_VEH("ClVeh", 1,"车型数据"),
    AM_CL("AmCl", 10,"后市场理赔车型数据"),
    REPLACER("Replacer", 9, "替换件数据"),
    STD_PART("StdPart", 10,"字典数据"),
    PART("Part", 100, "配件数据"),
    PART_WLM0("Part_wlm0", 100, "配件-蔚来数据"),
    ORI_REPAIR("OriRepair", 1000, "原厂工时数据"),
    SENDING("AutoTest", 100000,"自动化数据");

    private String code;
    private Integer order;
    private String name;

    TransferOrderEnum(String code, Integer order, String name) {
        this.code = code;
        this.order = order;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public Integer getOrder() {
        return order;
    }

    public String getName() {
        return name;
    }

    public static Integer orderOf(String code) {
        for (TransferOrderEnum a : TransferOrderEnum.values()) {
            if (a.code.equals(code)){
                return a.order;
            }
        }
        return 10000;
    }
}

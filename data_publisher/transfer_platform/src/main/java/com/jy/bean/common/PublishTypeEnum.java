package com.jy.bean.common;

public enum PublishTypeEnum {
    AP_VEH("ApVeh", "承保数据", "车型数据发布平台"),
    AM_VEH("AmVeh", "后市场数据", "车型数据发布平台" ),
    VIN("Vin", "vin码", "车型数据发布平台"),
    VIN_QUESTION("VinQuestion","vin码问题", "车型数据发布平台"),
    PART("Part","配件数据", "配件数据发布平台"),
    STD_PART("StdPart","字典数据", "配件数据发布平台"),
    REPLACER("Replacer","替换件数据", "配件数据发布平台"),
    ORI_REPAIR("OriRepair","原厂工时数据", "配件数据发布平台"),
    BR_OE("BrOe","品牌件数据", "配件数据发布平台"),
    CL_VEH("ClVeh","车型数据", "配件数据发布平台"),
    TRAIL("Trail","轨迹数据", "数仓"),
    FITTING("MaketPrice","分省/分析价格数据", "价格数据发布平台"),
    ERROR_FIT("RegionPrice","区域价格数据", "价格数据发布平台"),
    SUCCESS_FIT("SysPrice","4s店价格数据" ,"价格数据发布平台"),
    SENDING("AutoTest","自动化数据" ,"自动化测试"),
    SYNCDATA("SyncData","同步数据" ,"数据转换平台"),
    AM_CL("AmCl","后市场车-理赔车型关系数据", "配件数据发布平台");

    private String code;
    private String name;
    private String partentName;

    PublishTypeEnum(String code, String name, String partentName) {
        this.code = code;
        this.name = name;
        this.partentName = partentName;
    }

    public String getCode() {
        return code;
    }

    public static String nameOf(String code) {
        for (PublishTypeEnum a : PublishTypeEnum.values()) {
            if (a.code.equals(code)){
                return a.name;
            }
        }
        return "";
    }

    public static String parentNameOf(String code) {
        for (PublishTypeEnum a : PublishTypeEnum.values()) {
            if (a.code.equals(code)){
                return a.partentName;
            }
        }
        return "";
    }
}

package com.jy.bean.po;

import com.jy.bean.common.BatchNoStatus;
import com.jy.bean.dto.BaseDataDTOs;
import com.jy.util.StringUtils;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/5/18
 */
@Data
public class BatchDetail extends BasePo {
    private String id;
    private String mainBatchNo;
    private String batchNo;
    private String tableName;
    private String status;
    private String filePath;

    public BatchDetail(){}

    public BatchDetail(BaseDataDTOs baseDataDTOs, String filePath){
        this.id = StringUtils.getGUID();
        this.mainBatchNo = baseDataDTOs.getMainBatchNo();
        this.batchNo = baseDataDTOs.getBatchNo();
        this.tableName = baseDataDTOs.getTableName();
        this.status = BatchNoStatus.SUCCESS_RECEIVE.getStatus();
        this.filePath = filePath;
    }
}

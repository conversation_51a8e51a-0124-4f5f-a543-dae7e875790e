package com.jy.bean.common;

import java.util.HashMap;
import java.util.Map;

public enum DataTraceMenu {

    SRC_RECEIVE_DESC("src_receive", "加工数据接收"),
    SRC_START_DESC("src_start", "加工数据开始处理"),
    SRC_ACCEPT_DATA("src_accept_data", "加工数据入库"),
    SRC_CREATE_TABLE("src_create_table", "产品配件表创建"),
    SRC_TRANSFER_DESC("src_transfer", "加工数据转换"),
    SRC_COMPARE_DESC("src_compare", "加工数据对比"),
    SRC_UPDATE_DESC("src_update", "加工数据回写"),
    SRC_PUSH_DESC("src_push", "加工数据推送"),
    SRC_NO_PUSH_DESC("src_no_push", "发布平台无需处理");

    private String code;
    private String name;

    DataTraceMenu(String code , String name){
        this.code = code;
        this.name = name;
    }

    public static String nameOf(String code) {
        Map<String, String> map = new HashMap<>();
        for (DataTraceMenu a : DataTraceMenu.values()) {
            if (a.code.equals(code)){
                return a.getName();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}

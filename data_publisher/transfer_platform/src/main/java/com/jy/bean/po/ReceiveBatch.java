package com.jy.bean.po;

import com.jy.bean.common.BatchNoStatus;
import com.jy.bean.common.PublishTypeEnum;
import com.jy.bean.common.TransferOrderEnum;
import com.jy.bean.dto.BaseDataDTOs;
import com.jy.util.StringUtils;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/5/18
 */
@Data
public class ReceiveBatch extends BasePo {
    private String id;
    private String certainId;
    private String certainCode;
    private String certainName;
    private String mainBatchNo;
    private String status;
    private String noticeStatus;
    private Integer transferOrder;
    private String clientCode;

    public ReceiveBatch(){}

    public ReceiveBatch(String mainBatchNo, String certainId, String clientCode){
        this.id = StringUtils.getGUID();
        this.mainBatchNo = mainBatchNo;
        this.certainId = certainId;
        this.clientCode = clientCode;
    }

    public ReceiveBatch(BaseDataDTOs baseDataDTOs, String type){
        this.id = StringUtils.getGUID();
        this.status = BatchNoStatus.SUCCESS_RECEIVE.getStatus();
        this.transferOrder = TransferOrderEnum.orderOf(type);
        if(PublishTypeEnum.PART.getCode().equals(type)){
            this.certainId = baseDataDTOs.getGroupId();
            this.certainCode = baseDataDTOs.getGroupCode();
            this.certainName = baseDataDTOs.getGroupName();
            int hour = new Date().getHours();
            if(baseDataDTOs.getGroupCode().contains("WLM0") && hour >= 9 && hour < 21){
                this.transferOrder = TransferOrderEnum.PART_WLM0.getOrder();
            }
        } else  if(PublishTypeEnum.REPLACER.getCode().equals(type)){
            this.certainId = baseDataDTOs.getBrandId();
            this.certainCode = baseDataDTOs.getBrandCode();
            this.certainName = baseDataDTOs.getBrandName();
        } else if(PublishTypeEnum.ORI_REPAIR.getCode().equals(type)){
            this.certainId = baseDataDTOs.getClVehicleId();
            this.certainCode = baseDataDTOs.getClVehicleCode();
            this.certainName = baseDataDTOs.getClVehicleName();
        }

        this.mainBatchNo = baseDataDTOs.getMainBatchNo();

    }

}

package com.jy.task;

import com.jy.bean.common.BatchNoStatus;
import com.jy.bean.common.Constant;
import com.jy.bean.po.ReceiveBatch;
import com.jy.bean.po.SendDetail;
import com.jy.service.ReceiveBatchService;
import com.jy.service.SendDetailService;
import com.jy.transform.ClVehDataTransform;
import com.jy.transform.TransformFactory;
import com.jy.util.CommonUtils;
import com.jy.util.EmptyUtils;
import com.jy.util.ToolUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class BaseDataTask {
    private static final Logger logger = LogManager.getLogger(BaseDataTask.class);

    @Autowired
    private TransformFactory transformFactory;
    @Autowired
    private ReceiveBatchService receiveBatchService;
    @Autowired
    private SendDetailService sendDetailService;
    @Autowired
    private ClVehDataTransform clVehDataTransform;
    @Autowired
    private CommonUtils commonUtils;

    @Value("${srcData.filePath}")
    private  String filePath;

    /**
     * 每次取一个批次处理
     */
    @Scheduled(cron="0/1 * * * * ?")
    public void srcClientPush() {
        try {
            if(Constant.IS_CONTINUE){
                 List<ReceiveBatch> receiveBatches = receiveBatchService.listByStatus(BatchNoStatus.SUCCESS_RECEIVE.getStatus());
                 if(EmptyUtils.isNotEmpty(receiveBatches)){
                     ReceiveBatch receiveBatch = receiveBatches.get(0);
                     transformFactory.create(receiveBatch.getMainBatchNo()).handle(receiveBatch);
                 }
            }
        } catch (Exception e) {
            logger.error("BaseDataTask定时srcClientPush执行失败:" + ToolUtils.getExceptionMsg(e));
        }
    }


    @Scheduled(cron="0 0/1 * * * ?")
    public void pushErrorData() {
        String mainBatchNo = "";
        try {
            if(!Constant.IS_CONTINUE){
                boolean flag = true;
                List<ReceiveBatch> receiveBatches = receiveBatchService.listByStatus(BatchNoStatus.ERROR_SEND.getStatus());
                for(ReceiveBatch receiveBatch : receiveBatches){
                    mainBatchNo = receiveBatch.getMainBatchNo();
                    List<SendDetail> sendDetails = sendDetailService.listByMainBatchNo(receiveBatch.getMainBatchNo());
               /*     for(SendDetail sendDetail : sendDetails){
                        if(!BatchNoStatus.SUCCESS.getStatus().equals(sendDetail.getStatus()) ){
                            List<BaseDataDTOs> baseDataDTOs = clVehDataTransform.fitBaseDataDTOs(sendDetail.getMainBatchNo(), sendDetail.getTableName());
                            datas.addAll(baseDataDTOs);
                        }
                    }*/
                    clVehDataTransform.push(receiveBatch, sendDetails);
                    if(!BatchNoStatus.SUCCESS.getStatus().equals(receiveBatch.getStatus())){
                        flag = false;
                    }
                }
                Constant.IS_CONTINUE = flag;
            }
        } catch (Exception e) {
            Constant.IS_CONTINUE = false;
            logger.error("BaseDataTask定时pushErrorData执行失败:" + ToolUtils.getExceptionMsg(e));
            commonUtils.sendWorkWechatPath("mainBatchNo:" + mainBatchNo + "批次数据推送失败");
        }
    }

    @Scheduled(cron="0 0 21 * * ?")
    public void updateWLM0Status() {
        try {
            receiveBatchService.updateWaitStatus();
        } catch (Exception e) {
            logger.error("updateWaitStatus:" + ToolUtils.getExceptionMsg(e));
            commonUtils.sendWorkWechatPath("配件数据蔚来车组更新等待状态至处理状态失败");
        }
    }

}

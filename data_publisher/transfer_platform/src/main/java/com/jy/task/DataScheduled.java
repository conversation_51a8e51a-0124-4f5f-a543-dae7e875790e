package com.jy.task;

import com.alibaba.fastjson.JSONObject;
import com.jy.controller.ReceiveGroupDataController;
import com.jy.controller.WorkHoursController;
import com.jy.service.DataStatisticsService;
import com.jy.service.ReceiveGroupDataService;
import com.jy.util.HttpClientUtil;
import com.jy.util.StringUtils;
import com.jy.util.TimestampTool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Created by jdd on 2018/11/2.
 */
@Component
public class DataScheduled {

    private final static Logger log = LoggerFactory.getLogger(DataScheduled.class);

    @Autowired
    private ReceiveGroupDataService receiveGroupDataService;

    @Autowired
    private DataStatisticsService dataStatisticsService;

    @Autowired
    private ReceiveGroupDataController receiveGroupDataController;

    @Autowired
    private WorkHoursController workHoursController;

    @Value("${statistics.productUrl}")
    private String productUrl;

    @Value("${statistics.facadeTokenUrl}")
    private String facadeTokenUrl;

    @Value("${statistics.facadeStatisticsUrl}")
    private String facadeStatisticsUrl;

    @Value("${statistics.localUrl}")
    private String localUrl;

 /*   *//**
     * 自动重发所有发送失败的数据
     *//*
    @Async
    @Scheduled(cron = "0 0/1 * * * ?")
    public void againSend(){
        receiveGroupDataService.againSend("");
    }

    *//**
     * 定时从主批次号记录mq中处理转换
     *//*
    @Scheduled(cron = "0/1 * * * * ?")
    public void againMQ(){
        log.info("配件");
        //配件mq
        receiveGroupDataController.mqGainData();
    }*/

 /*   @Scheduled(cron = "0/7 * * * * ?")
    public void againWorkHoursMQ(){
        log.info("工时");
        //工时标记mq
        workHoursController.mqGainData();
    }*/

    /**
     * 统计发送数据
     */
  //  @Async
   // @Scheduled(cron = "0 0 5 * * ?")
   /* public void dataStatistics(){

        try {
            String date = TimestampTool.getYesterdayStr();
            List<Map<String,Object>> list = receiveGroupDataService.getStatisticsData(date,"0","");
            JSONObject json;
            if(list != null && !list.isEmpty()){

                StringBuilder str = new StringBuilder();
                list.forEach((Map<String,Object> map) -> {
                    //统计转换平台数据
                    dataStatisticsService.transformationData(map.get("brandCode").toString(),map.get("groupId").toString(),map.get("seriesId").toString(),map.get("brandId").toString(),(List<Map<String,Object>>)map.get("mainCodeList"),date);
                    //统计facade数据
                    if(str.indexOf(map.get("groupId").toString()) < 0){
                        str.append(map.get("groupId").toString()+",");
                    }
                });
                String groupIds = str.toString().substring(0,str.length()-1);
                //获取加工平台数据
                String jgData = HttpClientUtil.doPost(productUrl+"?groupId="+groupIds,"");
                dataStatisticsService.insertDataStatisticsBatch(jgData,"1",date);

                String data = HttpClientUtil.doPost(facadeTokenUrl,"","");
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("vehGroupIds",groupIds.split(","));
                jsonObject.put("url",localUrl);
                jsonObject.put("date",date);
                if(!StringUtils.isBlank(data)){
                    json = JSONObject.parseObject(data);
                    HttpClientUtil.doPost(facadeStatisticsUrl,jsonObject.toJSONString(),json.getString("access_token"));
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }*/
}

package com.jy.task;

import com.jy.service.ProcedureService;
import com.jy.util.ToolUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @Author: caolt
 * @Description:
 * @Version:
 * @Date: Created in  2020/09/15
 */
//@Component
public class AnalyzeDataTask {
    private final static Logger logger = LoggerFactory.getLogger(AnalyzeDataTask.class);

    @Autowired
    private ProcedureService procedureService;

    /**
     * 每日凌晨2点30  定时执行transfer、product库统计分析
     */
    //@Scheduled(cron="0 30 2 * * ?")
    public void analyzeData() {
        try {
            procedureService.tAnalyzeData();

        } catch (Exception e) {
            logger.error("analyzeData定时执行失败:" + ToolUtils.getExceptionMsg(e));
        }
    }
}

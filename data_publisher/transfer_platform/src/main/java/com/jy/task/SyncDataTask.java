package com.jy.task;

import com.jy.controller.ReceiveBatchController;
import com.jy.util.ToolUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class SyncDataTask {
    private static final Logger logger = LogManager.getLogger(SyncDataTask.class);

    @Autowired
    private ReceiveBatchController receiveBatchController;
    /**
     * 每次取一个批次处理
     */
   // @Scheduled(cron = "0 26 11 * * ?")
    public void syncPush() {
        try {
            List<String> list = new ArrayList<>();
            list.add("BZA2");
            list.add("HQF0");
            list.add("KLU0");
            list.add("SHA1");
            list.add("DDA0");
            list.add("GZA0");
            list.add("HLB0");
            list.add("JNA0");
            list.add("XYG3");
            list.add("ZNE0");

            for(String ppbm : list){
                receiveBatchController.syncData("pj_cllbjdyb",ppbm,"clzlid","",
                        "FACADE-PART-PRE", "http://testfacade.jingyougroup.com:8765/","part-service/itemDetails");
            }
        } catch (Exception e) {
            logger.error("BaseDataTask定时srcClientPush执行失败:" + ToolUtils.getExceptionMsg(e));
        }
    }


}

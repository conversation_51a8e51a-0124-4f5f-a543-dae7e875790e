package com.jy.task;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jy.bean.common.BatchNoStatus;
import com.jy.bean.common.Constant;
import com.jy.bean.po.ReceiveBatch;
import com.jy.service.CommonService;
import com.jy.service.ReceiveBatchService;
import com.jy.util.EmptyUtils;
import com.jy.util.HttpUtils;
import com.jy.util.ToolUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class ApClMpSendTask {
    private static final Logger logger = LogManager.getLogger(ApClMpSendTask.class);

    @Autowired
    private ReceiveBatchService receiveBatchService;
    @Autowired
    private CommonService commonService;

    /**
     * 每日定时执行，承保理赔关系数据推送至整车加工平台
     * 临时方案，整车平台待排期
     */
   // @Scheduled(cron="0 42 15 * * ?")
    public void srcClientPush() {
        try {
            List<ReceiveBatch> receiveBatches = receiveBatchService.listBatchByApCl();
            List<String> mainBatchNos = receiveBatches.stream().map(ReceiveBatch::getMainBatchNo).collect(Collectors.toList());

            for(String mainBatchNo : mainBatchNos){
                JSONObject json = new JSONObject();
                JSONArray array = new JSONArray();
                List<Map<String,Object>> datas = null;//commonService.getTableDataByTableNameAndBatchNo("c_pj_zc_cxdyb", mainBatchNo);
                for(Map<String,Object> data : datas){
                    JSONObject jsonObj = new JSONObject();
                    jsonObj.put("vehicleCode", data.get("zccxbmRb"));
                    jsonObj.put("claimVehicleId", data.get("pjcxid"));
                    jsonObj.put("changeFlag", data.get("state"));
                    array.add(jsonObj);
                }
                json.put("vehicle", array);
                System.out.println("转换JSON数据：");
                System.out.println(json.toJSONString());
                String response = HttpUtils.doPost("http://172.16.10.34:8080/publish-platform/",
                        "receiveData/receivePartClaimVeh", new HashMap<>(), new HashMap<>(), json.toJSONString());
                //   String response = HttpUtils.doPost("http://192.168.80.203:8080/publish-platform/",
                //            "receiveData/receivePartClaimVeh", new HashMap<>(), new HashMap<>(), json.toJSONString());
                JSONObject result = JSONObject.parseObject(response);
                System.out.println("整车平台notice回调结果:" + result);

                //   String sql1 = "update RECEIVE_BATCH set notice_status = '" + result.getString("code") +"' where main_batch_no ='" + mainBatchNo + "'";
                //    int num = stet.executeUpdate(sql1);
                //    System.out.println(num);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}

package com.jy.task;

import com.jy.bean.common.BatchNoStatus;
import com.jy.bean.po.ReceiveBatch;
import com.jy.service.ReceiveBatchService;
import com.jy.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: caolt
 * @Description:
 * @Version:
 * @Date: Created in  2020/09/15
 */
@Component
public class EmailNoticeTask {
    private final static Logger logger = LoggerFactory.getLogger(EmailNoticeTask.class);

    @Autowired
    private CommonUtils commonUtils;
    @Autowired
    private ReceiveBatchService receiveBatchService;

    @Scheduled(cron="0 30 8 * * ?")
    public void srcClientPush() {
        try {
            List<ReceiveBatch> receiveBatches = receiveBatchService.listByErrorStatus();
            if(receiveBatches.size() > 0){
                String ip = BatchUtils.getIp();
                String msg = "即时更新-转换平台(" + ip + ")：在" + DateUtil.convertDateToString("yyyy-MM-dd", receiveBatches.get(0).getCTime()) + "时间存在转换失败车组。车组数量为：" + receiveBatches.size() ;
                String subject = "即时更新转换平台数据转换失败提醒";
                //昨日有失败数据，邮件提醒
                commonUtils.sendEmail(subject, msg);
            }

        } catch (Exception e) {
            logger.error("BaseDataTask定时srcClientPush执行失败:" + ToolUtils.getExceptionMsg(e));
        }
    }
}

package com.jy.shiro;

import com.jy.bean.po.UserPo;
import com.jy.service.UserService;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.*;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.util.ByteSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;

/**
 * 自定义的Shiro权限控制
 *
 * <AUTHOR>
 * @create 2017-06-01 12:56
 **/
@Component
public class PublishPlatformRealm extends AuthorizingRealm {

    @Autowired
    private UserService userService;

    /**
     * 为当前登录的Subject授予角色和权限
     * 页面标签权限权限查询
     */
    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
        SimpleAuthorizationInfo simpleAuthorInfo = new SimpleAuthorizationInfo();
        //添加用户角色(添加角色修改 new ArrayList<String>())
        simpleAuthorInfo.addRoles(new ArrayList<String>());
        //添加用户权限(添加角色修改 new ArrayList<String>())
        simpleAuthorInfo.addStringPermissions(new ArrayList<String>());
        return simpleAuthorInfo;
    }

    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken authcToken) throws AuthenticationException {
        UsernamePasswordToken token = (UsernamePasswordToken) authcToken;

        UserPo user = null;
        int usercount = userService.selectUserPoByName(token.getUsername());
        if (!(usercount > 0)) {
            //用户名查询不到用户
            throw new UnknownAccountException();
        }
        user = userService.getUserPoByUp(token.getUsername(), String.valueOf(token.getPassword()));
        if (user == null) {
            //密码错误
            throw new IncorrectCredentialsException();
        }
        //判断用户的密码是否正确
        if (user != null && ByteSource.Util.bytes(token.getPassword()).equals(ByteSource.Util.bytes(user.getPassWord()))) {//用户校验
            this.setSession("currentUser", user);
            return new SimpleAuthenticationInfo(user.getUserName(), user.getPassWord(), getName());
        } else {
            //抛出密码不正确的异常
            throw new IncorrectCredentialsException();
        }
    }

    /**
     * 将一些数据放到ShiroSession中,以便于其它地方使用
     * @param key
     * @param value
     */
    private void setSession(Object key, Object value) {
        Subject currSubject = SecurityUtils.getSubject();
        if (currSubject != null) {
            Session session = currSubject.getSession();
            if (session != null) {
                session.setAttribute(key, value);
            }
        }
    }

    @Override
    public String getName() {
        return getClass().getName();
    }
}

package com.jy.shiro;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;

/**
 * 拦截器-用户session超时 跳转到登录页
 */
public class PublishPlatformFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
            throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        HttpSession session = request.getSession();
        session.getAttribute("currentUser");
        if(session.getAttribute("currentUser") == null){
            Subject subject = SecurityUtils.getSubject();
            if(subject != null){
                subject.logout();
            }
            response.sendRedirect("/index");
        }
    }

    @Override
    public void destroy() {

    }
}

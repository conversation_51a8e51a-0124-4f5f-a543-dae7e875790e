package com.jy.mapper;

import org.apache.ibatis.annotations.Mapper;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/20
 */
@Mapper
public interface ProcedureMapper {

    Map<String,Object> tAnalyzeData(Map<String,Object> map);

    Map<String,Object> partProcedure(Map<String,Object> map);

    Map<String,Object> wlPartProcedure(Map<String,Object> map);

    Map<String,Object> compareFlagProcedure(Map<String,Object> map);

    Map<String,Object> compareProcedure(Map<String,Object> map);

    Map<String,Object> compareSyncProcedure(Map<String,Object> map);

    Map<String,Object> updateProcedure(Map<String,Object> map);

    Map<String,Object> createFCTable(Map<String,Object> map);

    Map<String,Object> replaceProcedure(Map<String,Object> map);

    Map<String,Object> stdPartProcedure(Map<String,Object> map);

    Map<String,Object> truncateMData(Map<String,Object> map);

    Map<String,Object> truncatePData(Map<String,Object> map);
}


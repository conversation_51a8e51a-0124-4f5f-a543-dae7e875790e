package com.jy.mapper;

import com.jy.bean.dto.UserDTO;
import com.jy.bean.po.SysPowerPo;
import com.jy.bean.po.UserPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface UserMapper {

	/**
	 * 忘记密码/重置密码/修改密码
	 * @param user
	 * @return
	 */
	boolean updatePwd(UserPo user);

	/**
	 * 查看用户名是否存在
	 * @param userName
	 * @return
	 */
	int selectUserPoByName(@Param("userName") String userName);

	/**
	 * 根据用户id关联查询到所有菜单
	 * @param userId
	 * @return
	 */
	List<SysPowerPo> selectPowerByUserId(@Param("userId") String userId);


	UserPo getUserPoByUp(@Param("userName") String userName, @Param("passWord") String passWord);

	/**
	 * 根据用户查询用户信息
	 * @param id
	 * @return
	 */
	UserPo getUserPoById(@Param("id") String id);


	UserDTO getUserDtoById(@Param("id") String id);

	/**
	 * 用户列表
	 * @param
	 * @return
	 */
	List<UserDTO> getUserList(Map<String, Object> map);

	/**
	 * 统计
	 * @param dto
	 * @return
	 */
	Long selectCount(@Param("dto") UserDTO dto);

	/**
	 * 添加用户
	 * @param user
	 * @return
	 */
	int insertUserPo(UserPo user);


	/**
	 * 更新用户
	 * @param user
	 * @return
	 */
	int updateUser(UserPo user);



	/**
	 * 删除用户已有客户
	 * @param userId
	 * @param customerId
	 * @return
	 */
	boolean delRelation(@Param("userId") String userId, @Param("customerId") String customerId);





}

package com.jy.mapper;

import com.jy.bean.dto.WorkHoursDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/6/5.
 */
@Mapper
public interface WorkHoursMapper {

    /**
     * 保存产品端发送过来的工时信息
     * @param map
     */
    void saveWorkHours(Map<String,Object> map);

    /**
     * 获取车型零件表主轴字段
     * @return
     */
    List<Map<String,Object>> getPJMainCloum();

    /**
     * 根据批次号查询工时信息
     * @param mainVersionCode 批次号
     * @return
     */
    List<WorkHoursDto> getWorkHoursListByMainVersionCode(String mainVersionCode);
}

package com.jy.mapper;

import com.jy.bean.po.TransferTableMp;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/19
 */
@Mapper
public interface TransferTableMpMapper {

    List<TransferTableMp> listTransferTableMp(Map<String, Object> map);

    void save(TransferTableMp transferTableMp);

    void update(TransferTableMp transferTableMp);

    void delete(Map<String, Object> map);
}

package com.jy.mapper;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/22
 */
@Mapper
public interface CommonMapper {

    int getTableCount(Map<String,Object> map);

    List<Map<String,Object>> getTableData(Map<String,Object> map);

    List<Map<String,Object>> getMaxHzNumber(String groupId);

    List<Map<String,Object>> getNullPartNameByGroupCode(String groupId);

    void updateHzNumber(Map<String, Object> map);

    void updateCllbjdyb();

    void updateWLCllbjdyb();

    List<String> getPartSuffix(Map<String,Object> map);

    void updateIncGraphGroup(Map<String,Object> map);

    List<String> listCertain(Map<String,Object> map);

    List<Map<String,Object>> listCertainByWhere(Map<String,Object> map);

}

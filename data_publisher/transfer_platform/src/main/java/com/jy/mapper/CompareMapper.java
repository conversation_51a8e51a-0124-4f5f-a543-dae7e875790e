package com.jy.mapper;

import com.jy.bean.po.CompareDataLayer;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/8/20
 */
@Mapper
public interface CompareMapper {

    Integer getCountByTableName(Map<String, Object> map);

    Integer resendInsert(Map<String, Object> map);

    List<Map<String, Object>> query(Map<String, Object> map);

     List<BigInteger> listRangeByTableName(Map<String, Object> map);

  //  List<CompareDataLayer> listRangeByTableName(List<CompareDataLayer> compareDataLayers);

    List<String> listTableName(Map<String, Object> map);

    List<String> listCompareLayer(Map<String, Object> map);

}

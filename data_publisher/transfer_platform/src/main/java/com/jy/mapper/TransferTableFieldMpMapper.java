package com.jy.mapper;

import com.jy.bean.po.TransferTableFieldMp;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/20
 */
@Mapper
public interface TransferTableFieldMpMapper {
    List<TransferTableFieldMp> listTransferTableFieldMp(Map<String, Object> map);

    void save(TransferTableFieldMp transferTableFieldMp);

    void update(TransferTableFieldMp transferTableFieldMp);

    void delete(Map<String, Object> map);
}

package com.jy.mapper;

import com.jy.bean.dto.SendStateDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface SendStateMapper {

	void SaveSendState(SendStateDto sendStateDto);
	void updateSendState(Map<String,Object> map);
	Map<String,Object> getSendListByTableId(Map<String,Object> map);

	List<SendStateDto> listSendState(Map<String,Object> map);

	List<SendStateDto> listCompareData(Map<String,Object> map);
}

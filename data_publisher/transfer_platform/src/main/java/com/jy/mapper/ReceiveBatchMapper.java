package com.jy.mapper;

import com.jy.bean.po.ReceiveBatch;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/18
 */
@Mapper
public interface ReceiveBatchMapper {

    List<ReceiveBatch> listReceiveBatch(Map<String, Object> map);

    List<ReceiveBatch> listBatchByApCl();

    void save(ReceiveBatch receiveBatch);

    void update(ReceiveBatch receiveBatch);

    void updateWaitStatus(Map<String, Object> map);
}

package com.jy.mapper;

import com.jy.bean.po.SysPowerPo;
import com.jy.bean.po.UserPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface PowerMapper {

	/**
	 * 批量删除权限
	 * @param powerIds powerId数组
	 * @return
	 */
    boolean delPowers(@Param("powerIds") String[] powerIds, @Param("roleCode") String roleCode);

	/**
	 * 为角色添加相应的权限
	 */
    boolean addRolePower(@Param("userPo") UserPo userPo);

	/**
	 *查询已经拥有的角色菜单
	 */
	List<SysPowerPo> getHavePowerList(String roleCode);

	/**
	 * 查询未拥有的角色菜单
	 * @param roleCode
	 * @return
	 */
	List<SysPowerPo> getNonePowerList(String roleCode);

	/**
	 * 新增菜单
	 * @param power
	 * @return
	 */
    Long addMenu(SysPowerPo power);

	/**
	 * 查询菜单数量
	 * @param map
	 * @return
	 */
	Long findMenuCount(Map map);

	/**
	 * 分页查询菜单数据
	 * @param map
	 * @return
	 */
	List<SysPowerPo> findMenuByList(Map map);

	/**
	 *
	 * @param id
	 * @return
	 */
    List<SysPowerPo> findMenuList(@Param("id") String id);

	/**
	 * 删除菜单
	 * @param id
	 * @return
	 */
    Long delMenu(@Param("id") String id);

	/**
	 * 修改菜单信息
	 * @param power
	 * @return
	 */
	Long updateMenu(SysPowerPo power);
}

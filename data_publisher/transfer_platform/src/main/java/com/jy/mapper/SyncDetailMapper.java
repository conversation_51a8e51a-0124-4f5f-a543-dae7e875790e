package com.jy.mapper;

import com.jy.bean.po.SyncDetail;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @Author: caolt
 * @Description:
 * @Version:
 * @Date: Created in  2021/01/13
 */
@Mapper
public interface SyncDetailMapper {

    List<SyncDetail> listSyncDetail(Map<String, Object> map);

    void save(SyncDetail syncDetail);

    void update(SyncDetail syncDetail);
}

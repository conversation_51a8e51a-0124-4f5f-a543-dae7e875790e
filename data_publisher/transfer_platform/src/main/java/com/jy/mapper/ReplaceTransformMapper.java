package com.jy.mapper;

import com.jy.bean.po.RepOutRelation;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @Author: caolt
 * @Description:
 * @Version:
 * @Date: Created in  2020/12/14
 */
@Mapper
public interface ReplaceTransformMapper {

    String getMaxRelGroupId(Map<String, Object> map);

    List<RepOutRelation> ListByNoRelGroupId(Map<String, Object> map);

    void updateByRelReplace();

}

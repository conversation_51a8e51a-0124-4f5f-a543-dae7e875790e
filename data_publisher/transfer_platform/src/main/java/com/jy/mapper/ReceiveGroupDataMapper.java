package com.jy.mapper;

import com.jy.bean.dto.ReceiveGroupDataDto;
import com.jy.bean.vo.ReceiveGroupDataVo;
import com.jy.sqlUtils.SqlMapperProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * Created by jdd on 2018/12/5.
 */
@Mapper
public interface ReceiveGroupDataMapper {

    int insert(ReceiveGroupDataDto receiveGroupDataDto);

    List<ReceiveGroupDataDto> listReceiveGroupDataDto(Map<String, Object> map);

    void insertBatch(List<ReceiveGroupDataDto> list);

    void insertTableBatch(List<Map<String, Object>> list);

    /**
     * 获取要查询的数据表
     * @param mainVersionCode 主批次号
     * @return
     */
    List<ReceiveGroupDataDto> getTableListMainVersionCode(String mainVersionCode);

    /**
     * 通过主批次号修改当前车组获取数据的错误次数
     * @param mainVersionCode 主批次号
     * @return
     */
    Integer updateErrorNum(String mainVersionCode);

    /**
     * 保存表数据
     * @param map
     */
    void insertTableDataBatch(Map<String, Object> map);

    /**
     * 修改获取表数据状态
     * @param map
     */
    void updateTableStateByMianVersionCode(Map<String, Object> map);

    /**
     * 修改车组获取数据状态
     * @param map
     */
    void updateGroupStateByMainVersionCode(Map<String, Object> map);

    /**
     * 根据表名和versionCode查询发送的数据
     * @param tableName
     * @param versionCode
     * @return
     */
    List<Map<String,Object>> getTableDataList(@Param("tableName") String tableName, @Param("versionCode") String versionCode, @Param("vehicleId") String vehicleId, @Param("oldMainVersionCode") String oldMainVersionCode);

    List<Map<String,Object>> getTest();

    void saveConversionTables(List<Map> list);

    /**
     * 保存车组和品牌关联表信息
     * @param groupBrandAssociatedDto
     */
    void saveAssociated(List<Map<String, Object>> groupBrandAssociatedDto);

    /**
     * 查询车组信息存放的表
     * @param map
     * @return
     */
    List<Map<String,Object>> getAssociatedTableNameList(Map<String, Object> map);

    /**
     * 创建数据表
     * @param map
     */
    List<Map<String,Object>> createTables(Map<String, Object> map);

    /**
     * 删除旧的车组数据
     * @param map
     */
    void delOldGroupData(Map<String, Object> map);

    /**
     * 查询保存的表是否存在
     * @return
     */
    int isExistsTable();

    /**
     * 根据当前车组编码查询车组是否是新增数据
     * @param groupId
     * @return
     */
    Map<String,Object> getClfzxxbByGroupCode(String groupId);

    List<Map<String,Object>> listClfzxxbByNullSuffix();

    /**
     * 根据车组编码修改表后缀
     * @param map
     */
    void updateSuffix(Map<String, Object> map);

    /**
     * 根据groupCode获取partNum
     * @param groupCode
     * @return
     */
    Map<String,Object> getPartNum(String groupCode);

    List<Map> getTest1(String groupId);

    @InsertProvider(type = SqlMapperProvider.class, method = "insert")
    int insertDataJsonToSql(Map<String, List> map);

    List<Map<String,Object>> getTableMainFields(String tableName);

    /**
     * 查询当前表对应发送的表信息
     * @param tableName
     * @return
     */
    Map<String,Object> getSendTable(String tableName);

    /**
     * 保存当前小批次需要发送的表信息
     * @param list
     */
    void saveSendTable(List<Map<String, Object>> list);

    /**
     * 获取当前批次发送的表数据
     * @param map
     * @return
     */
    List<Map<String,Object>> getSendTableListByMainVersionCode(Map<String, Object> map);

    int updateSendTableState(Map<String, Object> map);

    /**
     * 查询是否是热门品牌和配件数量
     * @param brandCode
     * @return
     */
    Map<String,Object> getPartNumAndHotFlag(String brandCode);

    /**
     * 修改品牌等级
     * @param map
     * @return
     */
    int updateBrandLevel(Map<String, Object> map);

    /**
     * 查询发送失败的批次信息
     * @param mainVersionCode 主批次号
     * @return
     */
    List<ReceiveGroupDataDto> getSendFailMainVersionCode(String mainVersionCode);

    /**
     * 查询当前批次的状态
     * @param mainVersionCode
     * @return
     */
    String getReceiceGroupStateByMainVersionCode(String mainVersionCode);

    /**
     * 车组数据转换
     * @param map
     * @return
     */
    List<Map<String,Object>> groupTransformation(Map<String, Object> map);

    /**
     * 数据对比
     * @param map
     * @return
     */
    String dataContrast(Map<String, Object> map);

    /**
     * 品牌件数据转换
     * @param map
     * @return
     */
    List<Map<String,Object>> brandTransformation(Map<String, Object> map);

    /**
     * 替换件数据转换
     * @param map
     * @return
     */
    List<Map<String,Object>> repOutTransformation(Map<String, Object> map);

    /**
     * 碰撞部位方式与零件关系表转换
     * @param map
     * @return
     */
    List<Map<String,Object>> pzshTransformation(Map<String, Object> map);

    /**
     * 零件标准表转换
     * @param map
     * @return
     */
    List<Map<String,Object>> stdPartTransformation(Map<String, Object> map);

    /**
     * 车型换车组数据转换
     * @param map
     */
    void clgroupTransformation(Map<String, Object> map);

    /**
     * 查询是否有车型换车组的数据
     * @param mainVersionCode
     * @return
     */
    List<ReceiveGroupDataDto> getByPMainVersionCode(String mainVersionCode);

    /**
     * 测试
     * @return
     */
    List<Map<String,Object>> getSendData(Map<String, Object> map);

    Map<String,Object> getModifyByMainVersionCode(String mainVersionCode);

    List<Map<String,Object>> getMaxHzNumber(String groupId);

    List<Map<String,Object>> getNullPartNameByGroupCode(String groupId);

    void updateHzNumber(Map<String, Object> map);

    void updateCllbjdyb();

    /**
     * 获取当前车组的品牌信息
     * @param groupId
     * @return
     */
    Map<String,Object> getBrandAndTableSuffixByGroupId(String groupId);

    /**
     * 修改车组的品牌信息
     * @param map
     */
    void updateBrandByMainVersionCode(Map<String, Object> map);

    /**
     * 根据品牌编码查询品牌对应的分表是否存在
     * @param brandCode 品牌编码
     * @return
     */
    int selUserTableNumByBrandCode(String brandCode);

    /**
     * 创建新增品牌的分表
     * @param map
     * @return
     */
    String createFCTable(Map<String, Object> map);

    /**
     * 创建新增品牌的分表
     * @param map
     * @return
     */
    Map<String,Object> createFTable(Map<String, Object> map);

    /**
     * 创建新增品牌的分表
     * @param map
     * @return
     */
    Map<String,Object> createCTable(Map<String, Object> map);

    /**
     * 创建新增品牌的分表
     * @param list
     * @return
     */
    void createTYC(List<String> list);

    /**
     * 修正发送的表后缀信息
     * @param map
     * @return
     */
    int updateTableNameByMainVersionCode(Map<String, Object> map);

    /**
     * 数据对比成功后会写final库
     * @param map
     * @return
     */
    String pUpdateData(Map<String, Object> map);

    /**
     * 根据表名和批次号查询表数据
     * @param map
     * @return
     */
    List<Map<String,Object>> selDataByTableName(Map<String, Object> map);

    /**
     * 根据表名和批次号查询表总条数
     * @param map
     * @return
     */
    long selDataByTableNameCount(Map<String, Object> map);


    /**
     * 获取蔚来的品牌和需要发送的表
     * @param brandCode 品牌编码
     * @return
     */
    Map<String,Object> getWLTableByBrandCode(String brandCode);

    /**
     * 查询蔚来的当前表是否存在
     * @param tableName
     * @return
     */
    int getWLTableCount(String tableName);

    /**
     * 创建蔚来的表同义词
     * @param list
     */
    void createWLTYC(List<Map<String, Object>> list);

    /**
     * 创建蔚来的final库表
     * @param map
     */
    void createWLFTable(Map<String, Object> map);

    /**
     * 创建蔚来的compare库表
     * @param map
     */
    void createWLCTable(Map<String, Object> map);

    /**
     * 创建蔚来的product库表
     * @param map
     */
    void createWLPTable(Map<String, Object> map);

    /**
     * 分页获取转换数据
     * @param map
     * @return
     */
    List<ReceiveGroupDataVo> getReceiveListByPage(Map<String, Object> map);

    /**
     * 获取转换数据总数
     * @param map
     * @return
     */
    long getListCount(Map<String, Object> map);

    /**
     * 查询当前车组是否重新发送过
     * @param map
     * @return
     */
    int judgeExistsGroupCodeByDate(Map<String, Object> map);

    /**
     * 根据主批次号将数据获取状态初始化
     * @param mainVersionCode 主批次号
     */
    void updateGainTableByMainVersionCode(String mainVersionCode);

    /**
     * 蔚来品牌转换
     * @param map
     */
    void groupTransformationByWL(Map<String, Object> map);

    /**
     * 查询蔚来需要特别处理的表信息
     * @param map
     * @return
     */
    List<Map<String,Object>> getSendTableByWlTable(Map<String, Object> map);

    /**
     * 查询蔚来需要特殊处理的表数据
     * @param tableName
     * @param versionCode
     * @param brandId
     * @return
     */
    List<Map<String,Object>> getCompareDataByTableName(@Param("tableName") String tableName, @Param("versionCode") String versionCode, @Param("brandId") String brandId, @Param("vehicleId") String vehicleId, @Param("oldMainVersionCode") String oldMainVersionCode);

    /**
     * 根据主批次号查询回写失败的小批次号
     * @param mainVersionCode
     * @return
     */
    List<String> getSendTableHXErrDataByMainVersionCode(String mainVersionCode);

    /**
     * 查询需要统计的批次号
     * @param map
     * @return
     */
    List<Map<String,Object>> getStatisticsData(Map<String, Object> map);

    /**
     * 根据车组id获取承保车型id
     * @param groupId
     * @return
     */
    List<Map<String,Object>> getSubVehicleIdByGroupId(String groupId);

    /**
     * 判断获取到的承包车类型信息是否正确
     * @return
     */
    List<Map<String,Object>> judgeVehicleType();

    /**
     * 批量保存获取到的承保车型信息
     * @param map
     */
    void saveSubVehicleTypeBatch(Map<String, Object> map);

    void testTransUpdate();

    /**
     * 修改是否统计的状态
     */
    void updateReStatistics(List<Map<String, Object>> list);

    /**
     * 给蔚来的同零件名称追加数字
     */
    void updateWLCllbjdyb();

    /**
     * 获取当前批次改动的车型id
     * @param map
     * @return
     */
    List<Map<String,Object>> getVehicleIdByC(Map<String, Object> map);

    /**
     * 获取当前车组上一批回写失败的主批次号
     * @param map
     * @return
     */
    Map<String,Object> getMainVersionCodeByDelFlag(Map<String, Object> map);

    int deleteZhPartTemp(Map<String, Object> map);
    int insertZhPartTemp(Map<String, Object> map);
    int updateClzVehicleType(Map<String, Object> map);

    int updateTruckClzVehicleType(Map<String, Object> map);

    int updateClzFlagVehicleType(Map<String, Object> map);

    /**
     * 获取当前车组下所有车型的vehicleTypeName,vehicleType
     * @param groupId
     * @return
     */
    Map<String,Object> getVehicleTypeData(String groupId);

    /**
     * 获取车组原来的vehicleTypeName和vehicleTypeCode
     * @param groupId
     * @return
     */
    Map<String,Object> getVehicleTypeDataByGroup(String groupId);

    /**
     * 修改车组标识表vehicleTypeName和vehicleTypeCode
     * @param map
     */
    void updateClfFlagVehicleType(Map<String, Object> map);

    /**
     * 获取车系原来的vehicleTypeName和vehicleTypeCode
     * @param seriesId
     * @return
     */
    Map<String,Object> getCxxVehicleTypeData(String seriesId);

    /**
     * 修改车系标识表vehicleTypeName和vehicleTypeCode
     * @param map
     */
    void updateCxxFlagVehicleType(Map<String, Object> map);

    /**
     * 获取品牌原来的vehicleTypeName和vehicleTypeCode
     * @param brandId
     * @return
     */
    Map<String,Object> getClpFlagVehicleTypeData(String brandId);

    /**
     * 修改品牌标识表vehicleTypeName和vehicleTypeCode
     * @param map
     */
    void updateClpFlagVehicleType(Map<String, Object> map);

    /**
     * 保存工时的批次号
     * @param list
     */
    void insertBatchByWorkHours(List<ReceiveGroupDataDto> list);

    /**
     * 获取车型零件表主轴字段
     * @return
     */
    List<Map<String,Object>> getConverMainField();

    /**
     * 查询F库中当前车组原来的工时信息
     * @param map
     * @return
     */
    List<Map<String,Object>> getFCLLByLjbzid(Map<String, Object> map);

    /**
     * 根据mainVersionCode查询数据
     * @param mainVersionCode
     * @return
     */
    ReceiveGroupDataDto getReceiveGroupDataModel(String mainVersionCode);

    List<Map<String,Object>> getAllGroup();

    /**
     * 新增send_table
     * @param map
     */
    void insertSendTable(Map<String, String> map);

    /**
     * 修改send_table状态
     * @param map
     */
    void updateSendTable(Map<String, String> map);

    /**
     * 查询承保理赔对应表变更记录
     * @param versionCode
     * @return
     */
    List<Map<String,Object>> selCxdybByVersionCode(String versionCode);

    /**
     * 单车组
     * @param map
     */
    void oneGroup(Map<String, Object> map);

    /**
     * 单表
     * @param map
     */
    void oneTable(Map<String, Object> map);

    void updateClzVehicleTypeId(Map<String, Object> map);

    /**
     * 查询是否存在未处理或者处理中的数据
     * @return
     */
    int getConductCount();

    /**
     * 精细化商用车转换方法
     * @param map
     */
    void truckVehicle(Map<String, Object> map);

    /**
     * 查询没有表后缀的精细化商用车id和partNum以重新生成后缀
     * @return
     */
    List<Map<String,Object>> commercialVehicleList();

    /**
     * 获取精细化商用车车型
     * @return
     */
    List<Map<String,Object>> getTruckVehicleId();

    /**
     * 修改车型vehicleTypeId
     * @param map
     */
    void updateVehicleTypeId(Map<String, Object> map);

    /**
     * 查询当前批次总的数据量
     * @param map
     * @return
     */
    int getTableDataCount(Map<String, Object> map);

    /**
     * 查询单车组时是否有推出删除车型的数量
     * @param groupId
     * @return
     */
    int getOneGroupDelCount(String groupId);

    /**
     * 保存品牌关系表
     * @param map
     */
    void saveBrandRelation(Map<String, Object> map);
}

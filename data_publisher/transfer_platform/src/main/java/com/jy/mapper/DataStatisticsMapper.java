package com.jy.mapper;

import com.jy.bean.dto.DataStatisticsDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface DataStatisticsMapper {

	/**
	 * 查询加工平台-转换平台-facade的统计信息
	 * @param map
	 * @return
	 */
	List<DataStatisticsDto> listDataStatistics(Map<String,Object> map);

	/**
	 * 批量新增统计数据
	 * @param list
	 */
	void insertDataStatisticsBatch(List<DataStatisticsDto> list);

	/**
	 * 查询当前批次新增删除修改的数据量
	 * @param map
	 * @return
	 */
	List<Map<String,Object>> getCTableDataStaticticsList(Map<String,Object> map);

	/**
	 * 保存当前批次新增删除修改的数据量
	 * @param list
	 */
	void insertCTableDataStatictics(List<DataStatisticsDto> list);

	/**
	 * 根据车组统计转换整体数据
	 * @param map
	 */
	void transformationData(Map<String,Object> map);

	/**
	 * 导出统计数据
	 * @param date 导出数据的日期
	 * @return
	 */
	List<Map<String,Object>> exportData(String date);

	void updateStisticsTest();

	/**
	 * 获取统计数据进行对比校验
	 * @param date 统计日期
	 * @return
	 */
	List<Map<String,Object>> getStisticsDataContrast(String date);
}

package com.jy.mapper;

import com.jy.bean.po.DictInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DictionaryTypeMapper {

	/**
	 * 字典类型查询列表
	 * @param type
	 * @return
	 */
    List<DictInfo> getDictionaryTypeList(@Param("type") String type);


	String getDictNameByCode(@Param("code") String code);


	List<DictInfo> getCompanyCodeList();

}

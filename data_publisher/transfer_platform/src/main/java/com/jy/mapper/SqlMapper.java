package com.jy.mapper;

import com.jy.bean.dto.BaseDataDTO;
import com.jy.util.SqlMapperProvider;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.UpdateProvider;

/**
 * <AUTHOR>
 * @date 2020/5/19
 */
@Mapper
public interface SqlMapper {

    @InsertProvider(type = SqlMapperProvider.class, method = "insert")
    int insert(BaseDataDTO baseDataDTO);

    @DeleteProvider(type = SqlMapperProvider.class, method = "delete")
    int delete(BaseDataDTO baseDataDTO);

    @UpdateProvider(type = SqlMapperProvider.class, method = "update")
    int update(BaseDataDTO baseDataDTO);

}

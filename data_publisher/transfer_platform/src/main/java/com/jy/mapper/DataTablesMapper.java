package com.jy.mapper;

import com.jy.bean.dto.DataTablesDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * Created by jdd on 2018/11/29.
 */
@Mapper
public interface DataTablesMapper {

    /**
     * 获取列表
     * @param map
     * @return
     */
    List<DataTablesDTO> getListByPage(Map<String,Object> map);
    long getListCount(Map<String,Object> map);

    /**
     * 新增
     * @param dataTablesDTO
     * @return
     */
    Integer insert(DataTablesDTO dataTablesDTO);

    /**
     * 修改
     * @param dataTablesDTO
     * @return
     */
    Integer update(DataTablesDTO dataTablesDTO);

    /**
     * 删除
     * @param id
     * @return
     */
    Integer deleteById(String id);

    /**
     * 查询启用的表数据
     * @param map 查询的参数
     */
    List<Map<String,Object>> getListByEnable(Map<String,Object> map);
}

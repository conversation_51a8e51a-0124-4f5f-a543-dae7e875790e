package com.jy.mapper;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface VehicleMapper {

	/**
	 * 根据承保车型人保编码获取理赔车型信息
	 * @param vehicleCode 承保车型人保编码
	 * @return
	 */
	List<Map<String,Object>> vehicleConversion(List<String> vehicleCode);

	void deletePjZcCxdyb(Map<String, Object> map);

	void deleteZcClzlb(Map<String, Object> map);

	void deleteZcClzlbFlag(Map<String, Object> map);
}

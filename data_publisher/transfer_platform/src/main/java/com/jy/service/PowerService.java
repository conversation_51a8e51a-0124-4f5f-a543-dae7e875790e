package com.jy.service;

import com.jy.bean.dto.PageCustom;
import com.jy.bean.dto.UserDTO;
import com.jy.bean.po.SysPowerPo;
import com.jy.bean.po.UserPo;

import java.util.List;

public interface PowerService {


	/**
	 * 删除菜单(多删)
	 * @param ids
	 * @return
	 */
    boolean delPowers(String[] ids, String roleCode);

	/**
	 * 为角色添加权限
	 */
    boolean addRolePower(UserPo userPo);

	/**
	 * 查找用户对应的菜单权限
	 * @param userId
	 * @param
	 * @return
	 */
    List<SysPowerPo> showMenu(String userId);

	/**
	 * 查询已拥有的权限菜单
	 * @param roleCode
	 * @return
	 */
	List<SysPowerPo> getHavePowerList(String roleCode);

	/**
	 * 查询未拥有的权限菜单
	 * @param roleCode
	 * @return
	 */
	List<SysPowerPo> getNonePowerList(String roleCode);

	/**
	 * 添加菜单
	 * @param power
	 * @return
	 */
    String addMenuPower(SysPowerPo power);

	/**
	 * 分页查询菜单列表
	 * @param dto
	 * @param pages
	 * @return
	 */
    PageCustom<List<SysPowerPo>> findMenuByList(UserDTO dto, PageCustom<List<UserDTO>> pages);

	/**
	 * 删除菜单
	 * @param power
	 * @return
	 */
    String delMenuPower(SysPowerPo power);

	/**
	 * 查找一级菜单
	 * @param list
	 * @return
	 */
	List<SysPowerPo> findMenuList(List list);

	/**
	 * 修改菜单
	 * @param power
	 * @return
	 */
    String updateMenuPower(SysPowerPo power);
}

package com.jy.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.jy.bean.dto.ClientTableDTO;
import com.jy.bean.dto.ClientTableFieldMpDTO;
import com.jy.bean.dto.ClientUrlDTO;
import com.jy.service.ClientService;
import com.jy.util.EmptyUtils;
import com.jy.util.DataPublishUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/8/21
 */
@Service
public class ClientServiceImpl implements ClientService {

    @Value("${httpUtils.dataPublish.listClientTablePath}")
    private String listClientTablePath;
    @Value("${httpUtils.dataPublish.listClientUrlPath}")
    private String listClientUrlPath;
    @Value("${httpUtils.dataPublish.listClientFieldPath}")
    private String listClientFieldPath;

    @Autowired
    private ClientService clientService;
    @Autowired
    private DataPublishUtils dataPublishUtils;
    @Autowired
    private ClientService clientTableService;

    @Override
    public List<ClientUrlDTO> listCompareClient(String baseClientCode, String clientCode) throws Exception {
        Map<String, String> querys = new HashMap<String, String>();
        querys.put("baseClientCode", baseClientCode);
        querys.put("clientCode", clientCode);
        JSONObject json = dataPublishUtils.doGet(listClientUrlPath, querys);
        if("200".equals(json.get("status"))){
            List<ClientUrlDTO> clientUrlDTOs = JSONObject.parseArray(json.getString("result"),ClientUrlDTO.class );
            return clientUrlDTOs;
        }
        return null;
    }

    @Override
    public Map<String, ClientUrlDTO> mapClientUrlByClientCode(String clientCode) throws Exception {
        Map<String, String> querys = new HashMap<String, String>();
        querys.put("clientCode", clientCode);
        JSONObject json = dataPublishUtils.doGet(listClientUrlPath, querys);
        if("200".equals(json.get("status"))){
            List<ClientUrlDTO> clientUrlDTOs = JSONObject.parseArray(json.getString("result"),ClientUrlDTO.class );
            return clientUrlDTOs.stream().filter(clientUrlDTO -> EmptyUtils.isNotEmpty(clientUrlDTO.getTableName())).collect(Collectors.toMap(ClientUrlDTO::getTableName, clientUrlDTO -> clientUrlDTO, (k1,k2)->k1));
        }
        return null;
    }

    @Override
    public Map<String, ClientTableDTO> mapClientTableByClientCode(String clientCode) throws Exception {
        Map<String, String> querys = new HashMap<String, String>();
        querys.put("clientCode", clientCode);
        JSONObject json = dataPublishUtils.doGet(listClientTablePath, querys);
        if("200".equals(json.get("status"))){
            List<ClientTableDTO> clientTableDTOs = JSONObject.parseArray(json.getString("result"), ClientTableDTO.class );
            return clientTableDTOs.stream().collect(Collectors.toMap(ClientTableDTO::getBaseTableName, clientTableDTO -> clientTableDTO));
        }

        return null;
    }

    @Override
    public Map<String, ClientTableFieldMpDTO> mapClientTableFieldByTableName(String clientCode, String tableName) throws Exception {
        Map<String, String> querys = new HashMap<String, String>();
        querys.put("clientCode", clientCode);
        querys.put("tableName", tableName);
        JSONObject json = dataPublishUtils.doGet(listClientFieldPath, querys);
        if("200".equals(json.get("status"))){
            List<ClientTableFieldMpDTO> clientTableFieldMpDTOs = JSONObject.parseArray(json.getString("result"),ClientTableFieldMpDTO.class );
            return clientTableFieldMpDTOs.stream().collect(Collectors.toMap(ClientTableFieldMpDTO::getBaseTableField, clientTableFieldMpDTO -> clientTableFieldMpDTO, (k1, k2)->k1));
        }
        return null;
    }
}

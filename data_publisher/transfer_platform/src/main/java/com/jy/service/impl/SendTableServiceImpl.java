package com.jy.service.impl;

import com.jy.bean.po.SendTable;
import com.jy.mapper.SendTableMapper;
import com.jy.service.SendTableService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/8/21
 */
@Service
public class SendTableServiceImpl implements SendTableService {

    @Autowired
    private SendTableMapper sendTableMapper;

    @Override
    public int insert(SendTable sendTable) {
        return sendTableMapper.insert(sendTable);
    }

    @Override
    public List<SendTable> listBatchState(String mainVersionCode) {
        Map<String, Object> map = new HashMap<>();
        map.put("mainVersionCode", mainVersionCode);
        return sendTableMapper.listBatchState(map);
    }
}

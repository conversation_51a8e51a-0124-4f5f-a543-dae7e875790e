package com.jy.service.impl;

import com.jy.ann.MethodMonitor;
import com.jy.bean.po.PartTrail;
import com.jy.mapper.PartTrailMapper;
import com.jy.service.PartTrailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: caolt
 * @Description:
 * @Version:
 * @Date: Created in  2023/04/13
 */
@Service
public class PartTrailServiceImpl implements PartTrailService {

    @Autowired
    private PartTrailMapper partTrailMapper;

    @Override
    @MethodMonitor
    public PartTrail save(PartTrail partTrail) {
        partTrailMapper.save(partTrail);
        return partTrail;
    }

    @Override
    @MethodMonitor
    public PartTrail update(PartTrail partTrail) {
        partTrailMapper.update(partTrail);
        return partTrail;
    }
}

package com.jy.service;

import com.jy.bean.po.ReceiveBatch;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/18
 */
public interface ReceiveBatchService {
    ReceiveBatch listByMainBatchNo(String mainBatchNo);

    List<ReceiveBatch> listByStatus(String status);

    List<ReceiveBatch> listByErrorStatus() throws Exception;

    List<ReceiveBatch> listBatchByApCl() throws Exception;

    ReceiveBatch save(ReceiveBatch receiveBatch) ;

    ReceiveBatch update(ReceiveBatch receiveBatch) ;

    void updateWaitStatus() ;
}

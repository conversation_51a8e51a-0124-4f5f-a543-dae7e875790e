package com.jy.service.impl;

import com.jy.bean.po.DictInfo;
import com.jy.mapper.DictionaryTypeMapper;
import com.jy.service.DictionaryTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DictionaryTypeServiceImpl implements DictionaryTypeService {

	@Autowired
	private DictionaryTypeMapper dictionaryTypeMapper;

	@Override
	public List<DictInfo> getDictionaryTypeList(String type) {
		return dictionaryTypeMapper.getDictionaryTypeList(type);
	}

	@Override
	public String getDictNameByCode(String code){
		return dictionaryTypeMapper.getDictNameByCode(code);
	}

}

package com.jy.service;

import com.jy.bean.dto.DataTablesDTO;
import com.jy.bean.dto.PageCustom;

import java.util.List;
import java.util.Map;

/**
 * Created by jdd on 2018/11/29.
 */
public interface DataTablesService {

    /**
     * 获取列表
     * @param map
     * @return
     */
    PageCustom<List<DataTablesDTO>> getListByPage(Map<String,Object> map);

    /**
     * 新增
     * @param dataTablesDTO
     * @return
     */
    Integer insert(DataTablesDTO dataTablesDTO);

    /**
     * 修改
     * @param dataTablesDTO
     * @return
     */
    Integer update(DataTablesDTO dataTablesDTO);

    /**
     * 删除
     * @param id
     * @return
     */
    Integer deleteById(String id);

    /**
     * 查询启用的表数据
     * @param type 查询类型
     * @param tableName 表名
     * @return
     */
    List<Map<String,Object>> getListByEnable(String type,String tableName);
}

package com.jy.service.impl;

import com.jy.ann.MethodMonitor;
import com.jy.bean.po.SendDetail;
import com.jy.mapper.SendDetailMapper;
import com.jy.service.SendDetailService;
import com.jy.util.EmptyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/22
 */
@Service
public class SendDetailServiceImpl implements SendDetailService {

    @Autowired
    private SendDetailMapper sendDetailMapper;

    @Override
    @MethodMonitor
    public List<SendDetail> listByMainBatchNo(String mainBatchNo) {
        Map<String, Object> map = new HashMap<>();
        map.put("mainBatchNo", mainBatchNo);
        return sendDetailMapper.listSendDetail(map);
    }

    @Override
    @MethodMonitor
    public SendDetail getByBatchNo(String batchNo) {
        Map<String, Object> map = new HashMap<>();
        map.put("batchNo", batchNo);
        List<SendDetail> list = sendDetailMapper.listSendDetail(map);
        if(EmptyUtils.isNotEmpty(list)){
            return list.get(0);
        }
        return null;
    }

    @Override
    @MethodMonitor
    public SendDetail save(SendDetail sendDetail) throws Exception {
        sendDetail.setCTime(new Date());
        sendDetail.setUTime(new Date());
        sendDetailMapper.save(sendDetail);
        return sendDetail;
    }

    @Override
    @MethodMonitor
    public SendDetail update(SendDetail sendDetail) throws Exception {
        sendDetail.setUTime(new Date());
        sendDetailMapper.update(sendDetail);
        return sendDetail;
    }
}

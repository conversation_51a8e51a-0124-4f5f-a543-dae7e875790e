package com.jy.service.impl;

import com.jy.bean.dto.DataTablesDTO;
import com.jy.bean.dto.PageCustom;
import com.jy.mapper.DataTablesMapper;
import com.jy.service.DataTablesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by jdd on 2018/11/29.
 */
@Service
public class DataTablesServiceImpl implements DataTablesService {

    @Autowired
    private DataTablesMapper dataTablesMapper;

    @Override
    public PageCustom<List<DataTablesDTO>> getListByPage(Map<String,Object> map) {

        PageCustom<List<DataTablesDTO>> listPageCustom = new PageCustom<>();
        PageCustom pageCustom = new PageCustom();
        pageCustom.setPage((int)map.get("page"));
        map.putAll(pageCustom.resuest());
        listPageCustom.setData(dataTablesMapper.getListByPage(map));
        listPageCustom.setTotal(dataTablesMapper.getListCount(map));
        return listPageCustom;
    }

    @Override
    public Integer insert(DataTablesDTO dataTablesDTO) {
        return dataTablesMapper.insert(dataTablesDTO);
    }

    @Override
    public Integer update(DataTablesDTO dataTablesDTO) {
        return dataTablesMapper.update(dataTablesDTO);
    }

    @Override
    public Integer deleteById(String id) {
        return dataTablesMapper.deleteById(id);
    }

    @Override
    public List<Map<String, Object>> getListByEnable(String type,String tableName) {
        Map<String,Object> map = new HashMap<>();
        map.put("type",type);
        map.put("tableName",tableName);
        return dataTablesMapper.getListByEnable(map);
    }
}

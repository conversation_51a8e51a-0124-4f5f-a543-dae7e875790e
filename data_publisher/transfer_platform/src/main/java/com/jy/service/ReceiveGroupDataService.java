package com.jy.service;

import com.jy.bean.dto.PageCustom;
import com.jy.bean.dto.ReceiveGroupDataDto;
import com.jy.bean.vo.ReceiveGroupDataVo;

import java.util.List;
import java.util.Map;

/**
 * Created by jdd on 2018/12/5.
 */
public interface ReceiveGroupDataService {

    int insert(ReceiveGroupDataDto receiveGroupDataDto);
    /**
     * 保存车组信息
     * @param list 车组信息
     * @param listMap 需要查询的表信息
     * @param sendTableList
     */
    void insertBatch(List<ReceiveGroupDataDto> list, List<Map<String,Object>> listMap,List<Map<String,Object>> sendTableList);

    /**
     * 获取要查询的数据表
     * @param mainVersionCode 主批次号
     * @return
     */
    List<ReceiveGroupDataDto> getTableListMainVersionCode(String mainVersionCode);

    /**
     * 通过主批次号修改当前车组获取数据的错误次数
     * @param mainVersionCode 主批次号
     * @return
     */
    Integer updateErrorNum(String mainVersionCode);

    /**
     * 保存表数据
     * @param list 表数据
     * @param tableName 保存表名
     * @param groupId 车组id
     * @param groupCode 车组code
     * @param brandId 品牌id
     * @param dataType
     * @param sourceTable 数据来源表名
     * @param brandCode 品牌编码
     */
    void insertTableDataBatch(List<Map> list, String tableName,String groupId,String groupCode,String brandId,String dataType,String sourceTable,String brandCode) throws Exception;

    /**
     * 修改获取表数据状态
     * @param state 状态
     * @param errMsg 错误信息
     * @param mainVersionCode 主批次号
     * @param tableName 表名
     */
    void updateTableStateByMianVersionCode(String state,String errMsg,String mainVersionCode,String tableName,String versionCode);

    /**
     * 修改车组获取数据状态
     * @param state 状态
     * @param mainVersionCode 主批次号
     * @param errMsg 错误信息
     */
    void updateGroupStateByMainVersionCode(String state,String mainVersionCode,String errMsg);

    List<Map<String,Object>> getTest();

    void saveConversionTables(List<Map> list);

    /**
     * 创建表并保存车组和品牌关联表信息
     * @param list 需要创建和保存的表集合
     */
    void saveAssociated(List<Map<String,Object>> list);

    /**
     * 查询车组信息存放的表
     * @param tableName 保存的表名
     * @return
     */
    List<Map<String,Object>> getAssociatedTableName(String tableName);

    /**
     * 查询保存的表是否存在
     * @return
     */
    int isExistsTable();

    /**
     * 根据车组编码修改表后缀
     * @param map
     */
    void updateSuffix(Map<String,Object> map);

    /**
     * 根据groupCode获取partNum
     * @param groupCode
     * @return
     */
    Map<String,Object> getPartNum(String groupCode);

    List<Map> getTest1(String groupId);


    /**
     * 组装数据并发送到及时更新
     * @param brandCode 品牌编码
     * @param mainVersionCode 主批次号
     * @param groupCode 车组编码
     * @param suffix 表后缀
     * @param isFirst 是否是第一次发送
     * @param brandId 品牌id
     * @param delFlag 是否是清空的数据
     * @param clientCode 需要发送的客户端编码
     * @param clientUrl 需要发送的客户端地址
     * @return
     */
    void dataAssembly(String mainVersionCode,String brandCode,String groupCode,String suffix,boolean isFirst,String brandId,String delFlag,String clientCode,String clientUrl);

    /**
     * 查询当前表对应发送的表信息
     * @param tableName
     * @return
     */
    Map<String,Object> getSendTable(String tableName);

    /**
     * 修改品牌等级
     * @param brandCode
     * @return
     */
    int updateBrandLevel(String brandCode) throws Exception;

    /**
     * 修改表后缀
     * @param groupId
     * @return
     * @throws Exception
     */
    String groupIsExists(String groupId) throws Exception;

    /**
     * 重新发送数据
     * @param mainVersionCode 主批次号
     * @return
     */
    boolean againSend(String mainVersionCode);

    /**
     * 查询当前批次的状态
     * @param mainVersionCode
     * @return
     */
    String getReceiceGroupStateByMainVersionCode(String mainVersionCode);

    /**
     * 车组数据转换
     * @param mainVersionCode 主批次
     * @param groupId 车组id
     * @return
     */
    List<Map<String,Object>> groupTransformation(String mainVersionCode,String groupId);

    /**
     * 数据对比
     * @param mainVersionCode 主批次
     * @param versionCode 小批次
     * @param certain 维度(车组或品牌)
     * @return
     */
    String dataContrast(String mainVersionCode,String versionCode,String certain);

    /**
     * 品牌件数据转换
     * @param mainVersionCode 主批次号
     * @return
     */
    List<Map<String,Object>> brandTransformation(String mainVersionCode);

    /**
     * 替换件数据转换
     * @param mainVersionCode 主批次号
     * @return
     */
    List<Map<String,Object>> repOutTransformation(String mainVersionCode);

    /**
     * 碰撞部位方式与零件关系表转换
     * @param mainVersionCode 主批次号
     * @return
     */
    List<Map<String,Object>> pzshTransformation(String mainVersionCode);

    /**
     * 零件标准表转换
     * @param mainVersionCode 主批次号
     * @return
     */
    List<Map<String,Object>> stdPartTransformation(String mainVersionCode);

    /**
     * 数据转换
     * @param mainVersionCode 主批次号
     * @param groupId 车组id
     * @param dataType 数据类型
     * @param tableName 表名
     * @param flag 是否是车型换车组
     * @param isWL 当前车组是否是蔚来的品牌
     * @return
     */
    boolean transformation(String mainVersionCode,String groupId,String dataType,String tableName,boolean flag,boolean isWL);

    /**
     * 查询是否有车型换车组的数据
     * @param mainVersionCode
     * @return
     */
    List<ReceiveGroupDataDto> getByPMainVersionCode(String mainVersionCode);

    Map<String,Object> getModifyByMainVersionCode(String mainVersionCode);

    List<Map<String,Object>> getMaxHzNumber(String groupId);

    List<Map<String,Object>> getNullPartNameByGroupCode(String groupId);

    void updateHzNumber(Map<String,Object> map);

    void updateCllbjdyb();

    /**
     * 查询车组信息
     * @param groupId
     * @return
     */
    Map<String,Object> getClfzxxbByGroupCode(String groupId);

    /**
     * 获取当前车组的品牌信息
     * @param groupId
     * @return
     */
    Map<String,Object> getBrandAndTableSuffixByGroupId(String groupId);

    /**
     * 修改车组的品牌信息
     * @param brandId 品牌di
     * @param brandCode 品牌code
     * @param groupCode 车组code
     * @param mainVersionCode 逐批次号
     * @param tableSuffix 表后缀
     */
    void updateBrandByMainVersionCode(String brandId,String brandCode,String groupCode,String mainVersionCode,String tableSuffix);

    /**
     * 根据品牌编码查询品牌对应的分表是否存在
     * @param brandCode 品牌编码
     * @return
     */
    int selUserTableNumByBrandCode(String brandCode);

    /**
     * 创建新增品牌的分表
     * @param brandCode
     * @return
     */
    String createFCTable(String brandCode);

    /**
     * 创建新增品牌的分表
     * @param brandCode
     * @return
     */
    Map<String,Object> createFTable(String brandCode);

    /**
     * 创建新增品牌的分表
     * @param brandCode
     * @return
     */
    Map<String,Object> createCTable(String brandCode);

    /**
     * 创建新增品牌的分表
     * @param list
     * @return
     */
    void createTYC(List<String> list);

    /**
     * 修正发送的表后缀信息
     * @param map
     * @return
     */
    int updateTableNameByMainVersionCode(Map<String,Object> map);

    /**
     * 数据对比成功后会写final库
     * @param mainVersionCode 主批次
     * @param versionCode 小批次
     * @param certain 维度(车组或品牌)
     * @return
     */
    String pUpdateData(String mainVersionCode,String versionCode,String certain);

    /**
     * 根据表名和批次号查询表数据
     * @param tableName 表名
     * @param mainVersionCode 主批次号
     * @param versionCode 小批次号
     * @param page 当前页码
     * @return
     */
    PageCustom<List<Map<String,Object>>> selDataByTableName(String tableName, String mainVersionCode, String versionCode, int page);

    /**
     * 获取蔚来的品牌和需要发送的表
     * @param brandCode 品牌编码
     * @return
     */
    Map<String,Object> getWLTableByBrandCode(String brandCode);

    /**
     * 判断是否是蔚来的品牌,如果是并做相对处理
     * @param brandCode 品牌编码
     * @param mainVersionCode 主批次号
     * @param map 蔚来品牌的表信息
     * @return
     */
    boolean judgeWL(String brandCode,Map<String,Object> map,String mainVersionCode);

    /**
     * 分页获取转换数据
     * @param mainVersionCode 主批次号
     * @param versionCode 小批次号
     * @param page 当前页码
     * @param state 数据状态
     * @param groupCode 车组编号
     * @return
     */
    PageCustom<List<ReceiveGroupDataVo>> getReceiveListByPage(String mainVersionCode, String versionCode, int page, String state, String groupCode);

    /**
     * 查询当前车组是否重新发送过
     * @param mainVersionCode 主批次号
     * @param receiveDate 发送时间
     * @param groupCode 车组编号
     * @return
     */
    Map<String,Object> judgeExistsGroupCodeByDate(String mainVersionCode,String receiveDate,String groupCode);

    /**
     * 查询蔚来需要特别处理的表信息
     * @param mainVersionCode 主批次号
     * @return
     */
    List<Map<String,Object>> getSendTableByWlTable(String mainVersionCode);

    /**
     * 根据主批次号查询回写失败的小批次号
     * @param mainVersionCode
     * @return
     */
    List<String> getSendTableHXErrDataByMainVersionCode(String mainVersionCode);

    /**
     * 保存send_table表的数据
     * @param list
     */
    void saveSendTable(List<Map<String,Object>> list);

    /**
     * 查询需要统计的批次号
     * @param date 查询的日期
     * @return
     */
    List<Map<String,Object>> getStatisticsData(String date,String isStatistics,String groupId);

    /**
     * 承保车型信息处理
     * @param groupId
     * @return
     */
    String vehTypeData(String groupId,String seriesId,String brandId,String mainVersionCode) throws Exception;

    void testTransUpdate();

    /**
     * 修改是否统计的状态
     */
    void updateReStatistics(List<Map<String,Object>> list);

    /**
     * 给蔚来的同零件名称追加数字
     */
    void updateWLCllbjdyb();

    /**
     * 判断获取到的承保车类型信息是否正确(即不同的理赔车型是否会对应同一个承保车型)
     * @return
     */
    boolean judgeVehicleType();

    void test(int i);

    /**
     * 根据mainVersionCode查询数据
     * @param mainVersionCode
     * @return
     */
    ReceiveGroupDataDto getReceiveGroupDataModel(String mainVersionCode);

    void vehicleChangePush(List<Map<String,Object>> list,String mainVersionCode,String versionCode);

    /**
     * 查询是否存在未处理或者处理中的数据
     * @param mqNames 需要查询的mq名称(多个mq以逗号分隔)
     * @return
     */
    boolean isVerification(String mqNames);

    /**
     * 精细化商用车车型类型和等级修改
     * @param mainVersionCode
     */
    void truckVehicleType(String mainVersionCode) throws Exception;

    /**
     * 生成精细化商用车的表后缀
     * @throws Exception
     */
    void truckGroupSuffix() throws Exception;

    /**
     * 判断单车组
     * @param groupId
     * @return
     */
    boolean getOneGroupDelCount(String groupId);

    List<ReceiveGroupDataDto> listByCompareBatchNo(String compareBatchNo);

    /**
     * 保存品牌关系表
     * @param dto
     * @throws Exception
     */
    void saveBrandRelation(ReceiveGroupDataDto dto) throws Exception;
}

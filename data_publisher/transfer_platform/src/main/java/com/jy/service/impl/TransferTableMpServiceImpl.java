package com.jy.service.impl;

import com.jy.ann.MethodMonitor;
import com.jy.bean.common.Constant;
import com.jy.bean.common.PublishTypeEnum;
import com.jy.bean.po.BatchDetail;
import com.jy.bean.po.ReceiveBatch;
import com.jy.bean.po.TransferTableMp;
import com.jy.mapper.TransferTableMpMapper;
import com.jy.service.CommonService;
import com.jy.service.TransferTableMpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/19
 */
@Service
public class TransferTableMpServiceImpl implements TransferTableMpService {

    @Autowired
    private TransferTableMpMapper transferTableMpMapper;
    @Autowired
    private CommonService commonService;

    @Override
    @MethodMonitor
    public List<TransferTableMp>  listByBaseTableNameAndTransferFlag(String baseTableName, String transferFlag) {
        Map<String, Object> map = new HashMap<>();
        map.put("baseTableName", baseTableName);
        map.put("transferFlag", transferFlag);
        return transferTableMpMapper.listTransferTableMp(map);
    }

    @Override
    @MethodMonitor
    public List<TransferTableMp> listByBaseTableNames(ReceiveBatch receiveBatch, String baseTableNames, String transferFlag) {
        Map<String, Object> map = new HashMap<>();
        map.put("baseTableNames", baseTableNames);
        map.put("transferFlag", transferFlag);
        if(receiveBatch.getMainBatchNo().contains(PublishTypeEnum.PART.getCode())){
            String partSuffix = commonService.getPartSuffix(receiveBatch.getCertainId());
            map.put("partSuffix", partSuffix);
        }
        return transferTableMpMapper.listTransferTableMp(map);
    }

    @Override
    @MethodMonitor
    public TransferTableMp save(TransferTableMp transferTableMp) throws Exception {
        transferTableMpMapper.save(transferTableMp);
        return transferTableMp;
    }

    @Override
    @MethodMonitor
    public TransferTableMp update(TransferTableMp transferTableMp) throws Exception {
        transferTableMpMapper.update(transferTableMp);
        return transferTableMp;
    }

    @Override
    @MethodMonitor
    public void remove(String id) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        transferTableMpMapper.delete(map);
    }
}

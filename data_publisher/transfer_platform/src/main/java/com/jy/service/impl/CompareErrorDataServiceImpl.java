package com.jy.service.impl;

import com.jy.bean.po.CompareDataLayer;
import com.jy.bean.po.CompareErrorData;
import com.jy.mapper.CompareErrorDataMapper;
import com.jy.service.CompareErrorDataService;
import com.jy.util.EmptyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/8/21
 */
@Service
public class CompareErrorDataServiceImpl implements CompareErrorDataService {

    @Autowired
    private CompareErrorDataMapper compareErrorDataMapper;

    @Override
    public Integer insert(CompareErrorData compareErrorData) {
        return compareErrorDataMapper.insert(compareErrorData);
    }

    @Override
    public CompareErrorData getByVersionCode(String versionCode) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("versionCode", versionCode);
        List<CompareErrorData> compareErrorDataList = compareErrorDataMapper.listCompareErrorData(map);
        if(EmptyUtils.isNotEmpty(compareErrorDataList)){
            return compareErrorDataList.get(0);
        }
        return null;
    }

}

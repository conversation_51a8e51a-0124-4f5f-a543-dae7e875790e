package com.jy.service.impl;

import com.jy.ann.MethodMonitor;
import com.jy.mapper.ProcedureMapper;
import com.jy.service.ProcedureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/20
 */
@Service
public class ProcedureServiceImpl implements ProcedureService {

    @Autowired
    private ProcedureMapper procedureMapper;

    @Override
    @MethodMonitor
    public Map<String, Object> tAnalyzeData() {
        Map<String, Object> map = new HashMap<>();
        map.put("status", "");
        map.put("message", "");
        procedureMapper.tAnalyzeData(map);
        return map;
    }

    @Override
    @MethodMonitor
    public Map<String, Object> partProcedure(String mainBatchNo, String groupId) {
        Map<String, Object> map = new HashMap<>();
        map.put("mainBatchNo", mainBatchNo);
        map.put("groupId", groupId);
        map.put("status", "");
        map.put("message", "");
        procedureMapper.partProcedure(map);
        return map;
    }

    @Override
    @MethodMonitor
    public Map<String, Object> wlPartProcedure(String mainBatchNo, String groupId) {
        Map<String, Object> map = new HashMap<>();
        map.put("mainBatchNo", mainBatchNo);
        map.put("groupId", groupId);
        map.put("status", "");
        map.put("message", "");
        procedureMapper.wlPartProcedure(map);
        return map;
    }

    @Override
    @MethodMonitor
    public Map<String, Object> compareFlagProcedure(String mainBatchNo) {
        Map<String, Object> map = new HashMap<>();
        map.put("mainBatchNo", mainBatchNo);
        map.put("status", "");
        map.put("message", "");
        procedureMapper.compareFlagProcedure(map);
        return map;
    }

    @Override
    @MethodMonitor
    public Map<String, Object> compareProcedure(String mainBatchNo, String tableName, String suffixFlag, String endFlag) {
        Map<String, Object> map = new HashMap<>();
        map.put("mainBatchNo", mainBatchNo);
        map.put("tableName", tableName.toUpperCase());
        map.put("suffixFlag", suffixFlag);
        map.put("endFlag", endFlag);
        map.put("status", "");
        map.put("message", "");
        procedureMapper.compareProcedure(map);
        return map;
    }

    @Override
    @MethodMonitor
    public Map<String, Object> compareSyncProcedure(String mainBatchNo, String tableName, String ppbm, String certainId) {
        Map<String, Object> map = new HashMap<>();
        map.put("mainBatchNo", mainBatchNo);
        map.put("tableName", tableName.toUpperCase());
        map.put("ppbm", ppbm);
        map.put("certainId", certainId);
        map.put("status", "");
        map.put("message", "");
        procedureMapper.compareSyncProcedure(map);
        return map;
    }

    @Override
    @MethodMonitor
    public Map<String, Object> updateProcedure(String mainBatchNo, String tableName, String suffixFlag, String endFlag) {
        Map<String, Object> map = new HashMap<>();
        map.put("mainBatchNo", mainBatchNo);
        map.put("tableName", tableName.toUpperCase());
        map.put("suffixFlag", suffixFlag);
        map.put("endFlag", endFlag);
        map.put("status", "");
        map.put("message", "");
        procedureMapper.updateProcedure(map);
        return map;
    }


    @Override
    @MethodMonitor
    public Map<String,Object> createFCTable(String groupId) {
        Map<String, Object> map = new HashMap<>();
        map.put("groupId", groupId);
        map.put("tableNum",0);
        map.put("partSuffix","");
        map.put("status","");
        map.put("message", "");
        procedureMapper.createFCTable(map);
        return map;
    }

    @Override
    @MethodMonitor
    public Map<String, Object> replaceProcedure(String mainBatchNo) {
        Map<String, Object> map = new HashMap<>();
        map.put("mainBatchNo", mainBatchNo);
        map.put("status","");
        map.put("message", "");
        procedureMapper.replaceProcedure(map);
        return map;
    }

    @Override
    @MethodMonitor
    public Map<String, Object> stdPartProcedure(String mainBatchNo) {
        Map<String, Object> map = new HashMap<>();
        map.put("mainBatchNo", mainBatchNo);
        map.put("status","");
        map.put("message", "");
        procedureMapper.stdPartProcedure(map);
        return map;
    }

    @Override
    @MethodMonitor
    public Map<String, Object> truncateMData(String tableName) {
        Map<String, Object> map = new HashMap<>();
        map.put("tableName", tableName);
        map.put("status","");
        map.put("message", "");
        procedureMapper.truncateMData(map);
        return map;
    }

    @Override
    @MethodMonitor
    public Map<String, Object> truncatePData(String tableName) {
        Map<String, Object> map = new HashMap<>();
        map.put("tableName", tableName);
        map.put("status","");
        map.put("message", "");
        procedureMapper.truncatePData(map);
        return map;
    }
}

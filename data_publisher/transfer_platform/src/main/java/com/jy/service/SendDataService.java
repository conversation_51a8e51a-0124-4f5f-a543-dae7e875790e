package com.jy.service;

import com.jy.ann.MethodMonitor;
import com.jy.bean.po.ReceiveBatch;
import com.jy.bean.po.SendDetail;
import org.springframework.scheduling.annotation.Async;

import java.util.concurrent.CountDownLatch;

/**
 * @Author: caolt
 * @Description:
 * @Version:
 * @Date: Created in  2021/08/31
 */
public interface SendDataService {

    void sendData(ReceiveBatch receiveBatch, SendDetail sendDetail, String clientCode, CountDownLatch countDownLatch);

    @Async("asyncTransferExecutor")
    @MethodMonitor
    void sendTransData(SendDetail sendDetail, String clientCode);

    void sendExpData(ReceiveBatch receiveBatch, String clientCode, String exceptionMsg);
}

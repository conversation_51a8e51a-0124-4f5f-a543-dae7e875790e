package com.jy.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jy.bean.dto.DataStatisticsDto;
import com.jy.mapper.DataStatisticsMapper;
import com.jy.mapper.ReceiveGroupDataMapper;
import com.jy.service.DataStatisticsService;
import com.jy.util.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/4/1.
 */
@Service
public class DataStatisticsServiceImpl implements DataStatisticsService {

    @Autowired
    private DataStatisticsMapper dataStatisticsMapper;

    @Autowired
    private ReceiveGroupDataMapper receiveGroupDataMapper;

    @Override
    public List<DataStatisticsDto> listDataStatistics(String groupId,String date) {
        Map<String,Object> map = new HashMap<>(2);
        map.put("groupId",groupId);
        map.put("date",date);
        return dataStatisticsMapper.listDataStatistics(map);
    }

    @Override
    public void insertDataStatisticsBatch(String dataJson,String dataType,String receiveDate) {
        JSONArray jsonArray = JSONArray.parseArray(dataJson);
        List<DataStatisticsDto> list = new ArrayList<>();
        DataStatisticsDto dto = null;
        if(jsonArray != null && !jsonArray.isEmpty()){
            for (int i = 0;i < jsonArray.size();i++){
                dto = jsonArray.getJSONObject(i).toJavaObject(DataStatisticsDto.class);
                dto.setDataType(dataType);
                dto.setReceiveDate(receiveDate);
                list.add(dto);
            }
        }
        if(list != null && !list.isEmpty()){
            dataStatisticsMapper.insertDataStatisticsBatch(list);
        }
    }

    @Override
    public void transformationData(String brandCode, String groupId, String seriesId, String brandId, List<Map<String,Object>> list,String date) {
        Map<String,Object> map = new HashMap<>();
        map.put("brandCode",brandCode);
        map.put("groupId",groupId);
        map.put("seriesId",seriesId);
        map.put("brandId",brandId);
        map.put("date",date);
        dataStatisticsMapper.transformationData(map);

        if(list != null && !list.isEmpty()){
            getCTableDataStaticticsList(list,brandCode,groupId,brandId,date);
            receiveGroupDataMapper.updateReStatistics(list);
        }
    }

    @Override
    public List<Map<String, Object>> exportData(String date) {
        return dataStatisticsMapper.exportData(date);
    }

    /**
     * 查询当前批次新增删除修改的数据量
     * @param mainList 主批次号列表
     * @param brandCode 品牌编码
     * @return
     */
    @Override
    public void getCTableDataStaticticsList(List<Map<String,Object>> mainList,String brandCode,String groupId,String brandId,String date){

        Map<String,Object> para = new HashMap<>(2);
        para.put("list",mainList);
        para.put("brandCode",brandCode);
        para.put("groupId",groupId);
        para.put("brandId",brandId);
        List<Map<String,Object>> list = dataStatisticsMapper.getCTableDataStaticticsList(para);
        List<DataStatisticsDto> insertList = new ArrayList<>();
        JSONObject insertDto = new JSONObject(24);
        JSONObject updateDto = new JSONObject(24);
        JSONObject deleteDto = new JSONObject(24);

        list.forEach((Map<String,Object> map) -> {
            if("insert".equals(map.get("state"))){
                insertDto.put(map.get("type").toString(),map.get("num").toString());
            }else if("update".equals(map.get("state"))){
                updateDto.put(map.get("type").toString(),map.get("num").toString());
            }else{
                deleteDto.put(map.get("type").toString(),map.get("num").toString());
            }
        });
        if(insertDto.size() > 0){

            insertDto.put("dataType","3");
            insertDto.put("czid",groupId);
            insertDto.put("receiveDate",date);
            insertList.add(insertDto.toJavaObject(DataStatisticsDto.class));
        }
        if(updateDto.size() > 0){
            updateDto.put("dataType","4");
            updateDto.put("czid",groupId);
            updateDto.put("receiveDate",date);
            insertList.add(updateDto.toJavaObject(DataStatisticsDto.class));
        }
        if(deleteDto.size() > 0){
            deleteDto.put("dataType","5");
            deleteDto.put("czid",groupId);
            deleteDto.put("receiveDate",date);
            insertList.add(deleteDto.toJavaObject(DataStatisticsDto.class));
        }
        if(insertList.size() > 0){
            dataStatisticsMapper.insertCTableDataStatictics(insertList);
        }
    }

    @Override
    public List<Map<String,Object>> getStisticsDataContrast(String date,String filePath) {
        List<Map<String,Object>> cz = dataStatisticsMapper.getStisticsDataContrast(date);
        DataStatisticsDto dtoZH = new DataStatisticsDto();
        DataStatisticsDto dtoFac = new DataStatisticsDto();
        List<DataStatisticsDto> list;
        List<String>  list1   = new ArrayList<>();
        List<String>  list2   = new ArrayList<>();
        List<String>  list3   = new ArrayList<>();
        List<String>  list4   = new ArrayList<>();
        List<String>  list5   = new ArrayList<>();
        List<String>  list6   = new ArrayList<>();
        List<String>  list7   = new ArrayList<>();
        List<String>  list8   = new ArrayList<>();
        List<String>  list9   = new ArrayList<>();
        List<String>  list10  = new ArrayList<>();
        List<String>  list11  = new ArrayList<>();
        List<String>  list12  = new ArrayList<>();
        List<String>  list13  = new ArrayList<>();
        List<String>  list14  = new ArrayList<>();
        List<String>  list15  = new ArrayList<>();
        List<String>  list16  = new ArrayList<>();
        List<String>  list17  = new ArrayList<>();
        List<String>  list18  = new ArrayList<>();
        List<String>  list19  = new ArrayList<>();
        List<String>  list20  = new ArrayList<>();
        List<String>  list21  = new ArrayList<>();
        List<String>  list22  = new ArrayList<>();
        List<String>  list23  = new ArrayList<>();
        List<String>  list24  = new ArrayList<>();
        List<String>  list25  = new ArrayList<>();
        List<String>  list26  = new ArrayList<>();
        List<String>  list27  = new ArrayList<>();
        List<String>  list28  = new ArrayList<>();
        List<String>  list29  = new ArrayList<>();
        List<String>  list30  = new ArrayList<>();
        List<String>  list31  = new ArrayList<>();
        List<String>  list32  = new ArrayList<>();
        List<String>  list33 = new ArrayList<>();
        List<String>  list34  = new ArrayList<>();
        List<String>  list35  = new ArrayList<>();
        List<String>  list36  = new ArrayList<>();
        List<String>  list37  = new ArrayList<>();
        List<String>  list38 = new ArrayList<>();
        List<Map<String,Object>> maoList = new ArrayList<>();
        if(cz != null && cz.size() > 0){
            List<String> czList = new ArrayList<>(cz.size());
            for (int i = 0;i < cz.size();i++){
                dtoZH = new DataStatisticsDto();
                dtoFac = new DataStatisticsDto();
                list = (List<DataStatisticsDto>)cz.get(i).get("dataBeanList");

                for (int j = 0;j<list.size();j++){
                    if("2".equals(list.get(j).getDataType())){
                        dtoZH = list.get(j);
                    }else if("6".equals(list.get(j).getDataType())){
                        dtoFac = list.get(j);
                    }
                }


                if(!(dtoZH.getZcjsl()).equals(dtoFac.getZcjsl())){list1.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getZppsl()).equals(dtoFac.getZppsl())){list2.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getZcxxsl()).equals(dtoFac.getZcxxsl())){list3.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getZczsl()).equals(dtoFac.getZczsl())){list4.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getZcxsl()).equals(dtoFac.getZcxsl())){list5.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getCjsl()).equals(dtoFac.getCjsl())){list6.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getPpsl()).equals(dtoFac.getPpsl())){list7.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getCxxsl()).equals(dtoFac.getCxxsl())){list8.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getCzsl()).equals(dtoFac.getCzsl())){list9.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getCxsl()).equals(dtoFac.getCxsl())){list10.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getLpcbsl()).equals(dtoFac.getLpcbsl())){list11.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getCxljsl()).equals(dtoFac.getCxljsl())){list12.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getCxljxssl()).equals(dtoFac.getCxljxssl())){list13.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getCzhcmsl()).equals(dtoFac.getCzhcmsl())){list14.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getCxbzjsl()).equals(dtoFac.getCxbzjsl())){list15.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getCxfbzjsl()).equals(dtoFac.getCxfbzjsl())){list16.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getCxtpsl()).equals(dtoFac.getCxtpsl())){list17.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getCxtprdsl()).equals(dtoFac.getCxtprdsl())){list18.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getCzljsl()).equals(dtoFac.getCzljsl())){list19.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getCzbzjsl()).equals(dtoFac.getCzbzjsl())){list20.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getCzfbzjsl()).equals(dtoFac.getCzfbzjsl())){list21.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getPpljsl()).equals(dtoFac.getPpljsl())){list22.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getPpbzjsl()).equals(dtoFac.getPpbzjsl())){list23.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getPpfbzj()).equals(dtoFac.getPpfbzj())){list24.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getPpbsbsl()).equals(dtoFac.getPpbsbsl())){list25.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getCxxbsbsl()).equals(dtoFac.getCxxbsbsl())){list26.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getCzbsbsl()).equals(dtoFac.getCzbsbsl())){list27.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getCxbsbsl()).equals(dtoFac.getCxbsbsl())){list28.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getZppbsbsl()).equals(dtoFac.getZppbsbsl())){list29.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getZcxxbsbsl()).equals(dtoFac.getZcxxbsbsl())){list30.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getZcxbsbsl()).equals(dtoFac.getZcxbsbsl())){list31.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getZczbsbsl()).equals(dtoFac.getZczbsbsl())){list32.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getZlpcbsl()).equals(dtoFac.getZlpcbsl())){list33.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getXytpsl()).equals(dtoFac.getXytpsl())){list34.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getCztpzl()).equals(dtoFac.getCztpzl())){list35.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getCzljzl()).equals(dtoFac.getCzljzl())){list36.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getGroup_part_num()).equals(dtoFac.getGroup_part_num())){list37.add(cz.get(i).get("czid").toString());}
                if(!(dtoZH.getBrand_part_num()).equals(dtoFac.getBrand_part_num())){list38.add(cz.get(i).get("czid").toString());}
                //cy(dtoZH,dtoFac,maoList,cz.get(i).get("czid").toString());
                /*if((dtoZH.getZcjsl()).equals(dtoFac.getZcjsl()) &&
                        (dtoZH.getZppsl()).equals(dtoFac.getZppsl()) &&
                        (dtoZH.getZcxxsl()).equals(dtoFac.getZcxxsl()) &&
                        (dtoZH.getZczsl()).equals(dtoFac.getZczsl()) &&
                        (dtoZH.getZcxsl()).equals(dtoFac.getZcxsl()) &&
                        (dtoZH.getCjsl()).equals(dtoFac.getCjsl()) &&
                        (dtoZH.getPpsl()).equals(dtoFac.getPpsl()) &&
                        (dtoZH.getCxxsl()).equals(dtoFac.getCxxsl()) &&
                        (dtoZH.getCzsl()).equals(dtoFac.getCzsl()) &&
                        (dtoZH.getCxsl()).equals(dtoFac.getCxsl()) &&
                        (dtoZH.getLpcbsl()).equals(dtoFac.getLpcbsl()) &&
                        (dtoZH.getCxljsl()).equals(dtoFac.getCxljsl()) &&
                        (dtoZH.getCxljxssl()).equals(dtoFac.getCxljxssl()) &&
                        (dtoZH.getCzhcmsl()).equals(dtoFac.getCzhcmsl()) &&
                        (dtoZH.getCxbzjsl()).equals(dtoFac.getCxbzjsl()) &&
                        (dtoZH.getCxfbzjsl()).equals(dtoFac.getCxfbzjsl()) &&
                        (dtoZH.getCxtpsl()).equals(dtoFac.getCxtpsl()) &&
                        (dtoZH.getCxtprdsl()).equals(dtoFac.getCxtprdsl()) &&
                        (dtoZH.getCzljsl()).equals(dtoFac.getCzljsl()) &&
                        (dtoZH.getCzbzjsl()).equals(dtoFac.getCzbzjsl()) &&
                        (dtoZH.getCzfbzjsl()).equals(dtoFac.getCzfbzjsl()) &&
                        (dtoZH.getPpljsl()).equals(dtoFac.getPpljsl()) &&
                        (dtoZH.getPpbzjsl()).equals(dtoFac.getPpbzjsl()) &&
                        (dtoZH.getPpfbzj()).equals(dtoFac.getPpfbzj()) &&
                        (dtoZH.getPpbsbsl()).equals(dtoFac.getPpbsbsl()) &&
                        (dtoZH.getCxxbsbsl()).equals(dtoFac.getCxxbsbsl()) &&
                        (dtoZH.getCzbsbsl()).equals(dtoFac.getCzbsbsl()) &&
                        (dtoZH.getCxbsbsl()).equals(dtoFac.getCxbsbsl()) &&
                        (dtoZH.getZppbsbsl()).equals(dtoFac.getZppbsbsl()) &&
                        (dtoZH.getZcxxbsbsl()).equals(dtoFac.getZcxxbsbsl()) &&
                        (dtoZH.getZcxbsbsl()).equals(dtoFac.getZcxbsbsl()) &&
                        (dtoZH.getZczbsbsl()).equals(dtoFac.getZczbsbsl()) &&
                        (dtoZH.getZlpcbsl()).equals(dtoFac.getZlpcbsl())
                ){}else{
                    *//*if((dtoZH.getPpljsl()).equals(dtoFac.getPpljsl()) &&
                            Integer.parseInt(dtoZH.getPpfbzj()) < Integer.parseInt(dtoFac.getPpfbzj()) &&
                            Integer.parseInt(dtoZH.getPpbzjsl()) > Integer.parseInt(dtoFac.getPpbzjsl()) ){

                    }*//*
                    czList.add(cz.get(i).get("czid").toString());
                }*/
            }
            /*if(czList.size() > 0){
                try {
                    FileUtils.exportSql(filePath,"结果"+ date+".txt",JSONArray.toJSONString(czList));
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }*/
            try {


                if(list1.size()>0){FileUtils.exportSql(filePath,"总厂家数_"+date+"_"+list1.size()+".txt",JSONArray.toJSONString(list1));}
                if(list2.size()>0){FileUtils.exportSql(filePath,"总品牌数_"+date+"_"+list2.size()+".txt",JSONArray.toJSONString(list2));}
                if(list3.size()>0){FileUtils.exportSql(filePath,"总车系数_"+date+"_"+list3.size()+".txt",JSONArray.toJSONString(list3));}
                if(list4.size()>0){FileUtils.exportSql(filePath,"总车组数_"+date+"_"+list4.size()+".txt",JSONArray.toJSONString(list4));}
                if(list5.size()>0){FileUtils.exportSql(filePath,"总车型数_"+date+"_"+list5.size()+".txt",JSONArray.toJSONString(list5));}
                if(list6.size()>0){FileUtils.exportSql(filePath,"厂家数_"+date+"_"+list6.size()+".txt",JSONArray.toJSONString(list6));}
                if(list7.size()>0){FileUtils.exportSql(filePath,"品牌数_"+date+"_"+list7.size()+".txt",JSONArray.toJSONString(list7));}
                if(list8.size()>0){FileUtils.exportSql(filePath,"车系数_"+date+"_"+list8.size()+".txt",JSONArray.toJSONString(list8));}
                if(list9.size()>0){FileUtils.exportSql(filePath,"车组数_"+date+"_"+list9.size()+".txt",JSONArray.toJSONString(list9));}
                if(list10.size()>0){FileUtils.exportSql(filePath,"车型数_"+date+"_"+list10.size()+".txt",JSONArray.toJSONString(list10));}
                if(list11.size()>0){FileUtils.exportSql(filePath,"理赔承保数_"+date+"_"+list11.size()+".txt",JSONArray.toJSONString(list11));}
                if(list12.size()>0){FileUtils.exportSql(filePath,"车型零件数_"+date+"_"+list12.size()+".txt",JSONArray.toJSONString(list12));}
                if(list13.size()>0){FileUtils.exportSql(filePath,"车型零件显示数_"+date+"_"+list13.size()+".txt",JSONArray.toJSONString(list13));}
                if(list14.size()>0){FileUtils.exportSql(filePath,"车型互斥码数_"+date+"_"+list14.size()+".txt",JSONArray.toJSONString(list14));}
                if(list15.size()>0){FileUtils.exportSql(filePath,"车型标准件数_"+date+"_"+list15.size()+".txt",JSONArray.toJSONString(list15));}
                if(list16.size()>0){FileUtils.exportSql(filePath,"车型非标准件数_"+date+"_"+list16.size()+".txt",JSONArray.toJSONString(list16));}
                if(list17.size()>0){FileUtils.exportSql(filePath,"车型图片数_"+date+"_"+list17.size()+".txt",JSONArray.toJSONString(list17));}
                if(list18.size()>0){FileUtils.exportSql(filePath,"车型图片热点数_"+date+"_"+list18.size()+".txt",JSONArray.toJSONString(list18));}
                if(list19.size()>0){FileUtils.exportSql(filePath,"车组零件数_"+date+"_"+list19.size()+".txt",JSONArray.toJSONString(list19));}
                if(list20.size()>0){FileUtils.exportSql(filePath,"车组标准件数_"+date+"_"+list20.size()+".txt",JSONArray.toJSONString(list20));}
                if(list21.size()>0){FileUtils.exportSql(filePath,"车组非标准件数_"+date+"_"+list21.size()+".txt",JSONArray.toJSONString(list21));}
                if(list22.size()>0){FileUtils.exportSql(filePath,"品牌零件数_"+date+"_"+list22.size()+".txt",JSONArray.toJSONString(list22));}
                if(list23.size()>0){FileUtils.exportSql(filePath,"品牌标准件数_"+date+"_"+list23.size()+".txt",JSONArray.toJSONString(list23));}
                if(list24.size()>0){FileUtils.exportSql(filePath,"品牌非标准件数_"+date+"_"+list24.size()+".txt",JSONArray.toJSONString(list24));}
                if(list25.size()>0){FileUtils.exportSql(filePath,"品牌标识表数_"+date+"_"+list25.size()+".txt",JSONArray.toJSONString(list25));}
                if(list26.size()>0){FileUtils.exportSql(filePath,"车系标识表数_"+date+"_"+list26.size()+".txt",JSONArray.toJSONString(list26));}
                if(list27.size()>0){FileUtils.exportSql(filePath,"车组标识表数_"+date+"_"+list27.size()+".txt",JSONArray.toJSONString(list27));}
                if(list28.size()>0){FileUtils.exportSql(filePath,"车型标识表数_"+date+"_"+list28.size()+".txt",JSONArray.toJSONString(list28));}
                if(list29.size()>0){FileUtils.exportSql(filePath,"总品牌标识表数_"+date+"_"+list29.size()+".txt",JSONArray.toJSONString(list29));}
                if(list30.size()>0){FileUtils.exportSql(filePath,"总车系标识表数_"+date+"_"+list30.size()+".txt",JSONArray.toJSONString(list30));}
                if(list31.size()>0){FileUtils.exportSql(filePath,"总车型标识表数_"+date+"_"+list31.size()+".txt",JSONArray.toJSONString(list31));}
                if(list32.size()>0){FileUtils.exportSql(filePath,"总车组标识表数_"+date+"_"+list32.size()+".txt",JSONArray.toJSONString(list32));}
                if(list33.size()>0){FileUtils.exportSql(filePath,"总理赔承保数_"+date+"_"+list33.size()+".txt",JSONArray.toJSONString(list33));}
                if(list34.size()>0){FileUtils.exportSql(filePath,"xy图片数_"+date+"_"+list34.size()+".txt",JSONArray.toJSONString(list34));}
                if(list35.size()>0){FileUtils.exportSql(filePath,"车组图片总数_"+date+"_"+list35.size()+".txt",JSONArray.toJSONString(list35));}
                if(list36.size()>0){FileUtils.exportSql(filePath,"车型零件总数_"+date+"_"+list36.size()+".txt",JSONArray.toJSONString(list36));}
                if(list37.size()>0){FileUtils.exportSql(filePath,"车组零件总数_"+date+"_"+list37.size()+".txt",JSONArray.toJSONString(list37));}
                if(list38.size()>0){FileUtils.exportSql(filePath,"品牌零件总数_"+date+"_"+list38.size()+".txt",JSONArray.toJSONString(list38));}

            }catch (Exception e){}
        }
        return null;
    }

    public void cy(DataStatisticsDto dto,DataStatisticsDto dto1,List<Map<String,Object>> maoList,String czid){

        Field[] fields = dto.getClass().getDeclaredFields();
        Field[] fields1 = dto1.getClass().getDeclaredFields();
        try {
            Map<String,Object> map = new HashMap<>();
            map.put("czid",czid);
            for (Field field : fields) {
                field.setAccessible(true);
                System.out.println(field.getName() + ":" + field.get(dto));
                if(!"addTime".equals(field.getName()) && !"id".equals(field.getName()) && !"dataType".equals(field.getName()) && !"czid".equals(field.getName()) && !"receiveDate".equals(field.getName())){
                    for (Field field1 : fields1) {
                        field1.setAccessible(true);
                        if(field.getName().equals(field1.getName())){
                            if(field.get(dto)!=null && field.get(dto).toString().equals(field1.get(dto1) == null ? "" : field1.get(dto1).toString())){

                            }else{
                                map.put(field1.getName(),field.get(dto)+"-"+field.get(dto1));

                            }
                        }
                    }
                }
            }
            if(map.size() > 0){
                maoList.add(map);
            }
        }catch (Exception e){
            e.printStackTrace();
        }

    }
}

package com.jy.service;

import com.jy.bean.po.TransferTableFieldMp;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/20
 */
public interface TransferTableFieldMpService {

    List<TransferTableFieldMp> listByTableName(String tableName);

    Map<String, List<TransferTableFieldMp>> mapByTableName(String tableName);

    TransferTableFieldMp save(TransferTableFieldMp transferTableFieldMp) throws Exception;

    TransferTableFieldMp update(TransferTableFieldMp transferTableFieldMp) throws Exception;

    void remove(String id) throws Exception;
}

package com.jy.service.impl;

import com.jy.bean.dto.PageCustom;
import com.jy.bean.dto.UserDTO;
import com.jy.bean.po.UserPo;
import com.jy.mapper.UserMapper;
import com.jy.service.UserService;
import com.jy.util.MD5Util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by admin on 2017/2/26.
 */
@Service
public class UserServiceImpl implements UserService {

	private Logger log = LoggerFactory.getLogger(UserServiceImpl.class);

	@Autowired
	private UserMapper userMapper;

	@Override
	public String updatePwd(UserPo user, String oldPass, String newPass){

		String res = "";
		boolean result = false;

		//检查原密码是否正确
		String md5OldPass = MD5Util.getMD5StringWithSalt(oldPass);
		if(md5OldPass.equals(user.getPassWord())){
			result = true;
		}

		if(result){
			//修改密码
			user.setPassWord(MD5Util.getMD5StringWithSalt(newPass));//加密保存密码
			int uu = updateUser(user);
			if(uu > 0){
				res = "success";
			} else {
				res = "error";
			}
		} else {
			//原密码错误
			res = "pasError";
		}

		return res;
	}

	/**
	 * 查看用户名是否存在
	 * @param userName
	 * @return
	 */
	public int selectUserPoByName(String userName){
		return userMapper.selectUserPoByName(userName);
	}

	/**
	 *用户名密码查询用户
	 * @param userName
	 * @param passWord
	 * @return
	 */
	public UserPo getUserPoByUp(String userName, String passWord){
		return userMapper.getUserPoByUp(userName,passWord);
	}

	@Override
	public UserPo getUserPoById(String dealUserId) {
		return userMapper.getUserPoById(dealUserId);
	}

	@Override
	public UserDTO getUserDtoById(String id){
		return userMapper.getUserDtoById(id);
	}

	@Override
	public PageCustom<List<UserDTO>> getUserList(UserDTO dto, PageCustom pages) {
		Map<String, Object> map = pages.resuest();
		map.put("dto", dto);
		List<UserDTO> taskList = userMapper.getUserList(map);
		Long count = userMapper.selectCount(dto);
		PageCustom<List<UserDTO>> pageCustom = new PageCustom<List<UserDTO>>();
		pageCustom.setTotal(count);
		pageCustom.setData(taskList);
		return pageCustom;
	}

	@Override
	public int insertUserPo(UserPo user) {
		return userMapper.insertUserPo(user);
	}




	/**
	 * 保存用户
	 * @param dto
	 * @return
	 */
	@Override
	public int saveUser(UserDTO dto){

		int res = 0;
		UserPo user = null;
		if(dto.getId() != null){
			user = getUserPoById(dto.getId());
			//设置数据修改时间
			user.setUpdateTime(new Date());
			//设置数据修改人
			//user.setUpdateBy(dto.getId());
			if(dto.getUserName()!=null){
				user.setUserName(dto.getUserName().replace(" ",""));
			}
			//如果user的密码和界面的密码不同，则修改密码
			if(null != dto.getPassWord() && !dto.getPassWord().equals("") && !dto.getPassWord().equals(user.getPassWord())){
				user.setPassWord(MD5Util.getMD5StringWithSalt(dto.getPassWord()));//加密保存密码
			}
			if(dto.getRoleCode()!=null){
				user.setRoleCode(dto.getRoleCode());
			}
			if(dto.getEmail()!=null){
				user.setEmail(dto.getEmail());
			}
			if(dto.getPhone()!=null){
				user.setPhone(dto.getPhone());
			}

			res = updateUser(user);

		} else {
			user = new UserPo();
			//获取当前时间
			Date nowDate = new Date();
			//设置数据修改时间
			user.setUpdateTime(nowDate);
			user.setCreateTime(nowDate);
			//user.setCreateBy(dto.getId());
			//user.setUpdateBy(dto.getId());
			user.setDelFlag("0");
			//设置数据修改人
			//user.setId(StringUtils.getGUID());
			user.setUserName(dto.getUserName());
			user.setPassWord(MD5Util.getMD5StringWithSalt(dto.getPassWord()));//加密保存密码
			user.setRoleCode(dto.getRoleCode());
			user.setEmail(dto.getEmail());
			user.setPhone(dto.getPhone());
			res = insertUserPo(user);
		}

		return res;
	}


	/**
	 * 修改用户
	 * @param user
	 * @return
	 */
	private int updateUser(UserPo user) {
		return userMapper.updateUser(user);
	}



}
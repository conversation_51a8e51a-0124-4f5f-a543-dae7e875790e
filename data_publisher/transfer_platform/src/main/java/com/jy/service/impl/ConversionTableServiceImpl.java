package com.jy.service.impl;

import com.jy.bean.po.ConversionTable;
import com.jy.mapper.ConversionTableMapper;
import com.jy.service.ConversionTableService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/8/21
 */
@Service
public class ConversionTableServiceImpl implements ConversionTableService {

    @Autowired
    private ConversionTableMapper conversionTableMapper;
    @Autowired
    private ConversionTableService conversionTableService;

    @Override
    public List<ConversionTable> listByTableName(String tableName) {
        Map<String, Object> map = new HashMap<>();
        map.put("tableName", tableName);
        return conversionTableMapper.listConversionTable(map);
    }

    @Override
    public Map<String, String> mapByTableName(String tableName) {
        List<ConversionTable> conversionTables = conversionTableService.listByTableName(tableName);
        return conversionTables.stream().collect(
                Collectors.toMap(conversionTable->conversionTable.getFieldName(),
                        conversionTable->("0".equals(conversionTable.getIsMain()) && "0".equals(conversionTable.getDelFlag()))
                                ? conversionTable.getFieldName() + "_new" : conversionTable.getFieldName()));
    }
}

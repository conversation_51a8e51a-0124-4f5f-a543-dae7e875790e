package com.jy.service.impl;

import com.jy.bean.dto.GroupWeight;
import com.jy.mapper.GroupWeightMapper;
import com.jy.service.GroupWeightService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Author: zy
 * @Date: Created in 2018/8/15
 */
@Service
public class GroupWeightServiceImpl implements GroupWeightService {

    @Autowired
    private GroupWeightMapper groupWeightMapper;

    @Override
    @Transactional
    public void init() throws Exception {
        groupWeightMapper.deleteAll();
        groupWeightMapper.init();
    }
}

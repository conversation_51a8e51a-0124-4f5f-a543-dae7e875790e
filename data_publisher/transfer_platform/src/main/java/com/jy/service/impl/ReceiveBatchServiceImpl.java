package com.jy.service.impl;

import com.jy.ann.MethodMonitor;
import com.jy.bean.common.BatchNoStatus;
import com.jy.bean.po.ReceiveBatch;
import com.jy.mapper.ReceiveBatchMapper;
import com.jy.service.ReceiveBatchService;
import com.jy.util.EmptyUtils;
import com.jy.util.SqlUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/18
 */
@Service
public class ReceiveBatchServiceImpl implements ReceiveBatchService {

    @Autowired
    private ReceiveBatchMapper receiveBatchMapper;

    @Override
    @MethodMonitor
    public ReceiveBatch listByMainBatchNo(String mainBatchNo) {
        Map<String, Object> map = new HashMap<>();
        map.put("mainBatchNo", mainBatchNo);
        List<ReceiveBatch> list = receiveBatchMapper.listReceiveBatch(map);
        if(EmptyUtils.isNotEmpty(list)){
            return list.get(0);
        }
        return null;
    }

    @Override
    @MethodMonitor
    public List<ReceiveBatch> listByStatus(String status) {
        Map<String, Object> map = new HashMap<>();
        map.put("status", status);
        return receiveBatchMapper.listReceiveBatch(map);
    }

    @Override
    @MethodMonitor
    public List<ReceiveBatch> listByErrorStatus() throws Exception {
        String errorStatuses = BatchNoStatus.SUCCESS_RECEIVE.getStatus() + "," + BatchNoStatus.SUCCESS.getStatus();
        Map<String, Object> map = new HashMap<>();
        map.put("errorStatuses", SqlUtils.List2SqlInString(errorStatuses));
        return receiveBatchMapper.listReceiveBatch(map);
    }

    @Override
    @MethodMonitor
    public List<ReceiveBatch> listBatchByApCl() throws Exception {
        return receiveBatchMapper.listBatchByApCl();
    }

    @Override
    @MethodMonitor
    public ReceiveBatch save(ReceiveBatch receiveBatch) {
        receiveBatchMapper.save(receiveBatch);
        return receiveBatch;
    }

    @Override
    @MethodMonitor
    public ReceiveBatch update(ReceiveBatch receiveBatch) {
        receiveBatchMapper.update(receiveBatch);
        return receiveBatch;
    }

    @Override
    @MethodMonitor
    public void updateWaitStatus() {
        Map<String, Object> map = new HashMap<>();
        map.put("waitStatus", BatchNoStatus.SUCCESS_WAIT.getStatus());
        map.put("receiveStatus", BatchNoStatus.SUCCESS_RECEIVE.getStatus());
        receiveBatchMapper.updateWaitStatus(map);
    }
}

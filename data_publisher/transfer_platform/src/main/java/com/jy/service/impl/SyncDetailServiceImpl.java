package com.jy.service.impl;

import com.jy.bean.po.SyncDetail;
import com.jy.mapper.SyncDetailMapper;
import com.jy.service.SyncDetailService;
import com.jy.util.EmptyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: caolt
 * @Description:
 * @Version:
 * @Date: Created in  2021/01/13
 */
@Service
public class SyncDetailServiceImpl implements SyncDetailService {

    @Autowired
    private SyncDetailMapper syncDetailMapper;

    @Override
    public List<String> listByTableName(String tableName) {
        Map<String, Object> map = new HashMap<>();
        map.put("tableName", tableName);
        List<SyncDetail> list = syncDetailMapper.listSyncDetail(map);
        if(EmptyUtils.isNotEmpty(list)){
            return list.stream().map(SyncDetail::getClzlid).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    public SyncDetail save(SyncDetail syncDetail) {
        syncDetailMapper.save(syncDetail);
        return syncDetail;
    }

    @Override
    public SyncDetail update(SyncDetail syncDetail) {
        syncDetailMapper.update(syncDetail);
        return syncDetail;
    }
}

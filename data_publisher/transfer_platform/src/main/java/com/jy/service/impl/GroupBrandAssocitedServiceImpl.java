package com.jy.service.impl;

import com.jy.bean.po.GroupBrandAssocited;
import com.jy.mapper.GroupBrandAssocitedMapper;
import com.jy.service.GroupBrandAssocitedService;
import com.jy.util.EmptyUtils;
import com.jy.util.ListUtils;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/8/23
 */
@Service
@CacheConfig(cacheManager = "ehCacheCacheManager", cacheNames = "groupBrandAssocited")
public class GroupBrandAssocitedServiceImpl implements GroupBrandAssocitedService {

    @Autowired
    private GroupBrandAssocitedMapper groupBrandAssocitedMapper;
    @Autowired
    private GroupBrandAssocitedService groupBrandAssocitedService;

    @Override
    @Cacheable(key = "#root.targetClass + ':' + #root.methodName")
    public List<GroupBrandAssocited> listAll() {
        return groupBrandAssocitedMapper.listGroupBrandAssocited(new HashMap<String, Object>());
    }

    @Override
    @Cacheable(key = "#root.targetClass + ':' + #root.methodName + ':' + #isBrandCode")
    public List<String> listByIsBrandCode(String isBrandCode) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("isBrandCode", isBrandCode);
        List<GroupBrandAssocited> groupBrandAssociteds = groupBrandAssocitedMapper.listGroupBrandAssocited(map);
        String tableNames = groupBrandAssociteds.stream().map(GroupBrandAssocited::getConversionTableName).collect(Collectors.joining(","));
        List<String> tableArray = new ArrayList<String>();
        Collections.addAll(tableArray, tableNames.split(","));
        tableArray = ListUtils.removeDuplicate(tableArray);
        return tableArray;
    }

    @Override
    public String fitTableName(String tableName) {
        tableName = tableName.toLowerCase();
        List<String> tableArray = groupBrandAssocitedService.listByIsBrandCode("1");
        for(String str : tableArray){
            if(tableName.contains(str)){
                return str;
            }
        }
        return tableName;
    }
}

package com.jy.service.impl;

import com.jy.mapper.ReceiveGroupDataMapper;
import com.jy.mapper.VehicleMapper;
import com.jy.service.ReceiveGroupDataService;
import com.jy.service.VehicleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class VehicleServiceImpl implements VehicleService {

	@Autowired
	private VehicleMapper vehicleMapper;

	@Autowired
	private ReceiveGroupDataMapper receiveGroupDataMapper;

	@Autowired
	private ReceiveGroupDataService receiveGroupDataService;

	@Override
	public List<Map<String, Object>> vehicleConversion(List<String> vehicleCode) {
		return vehicleMapper.vehicleConversion(vehicleCode);
	}

	@Override
	public void vehicleConversion(String versionCode) {
		List<Map<String,Object>> list = receiveGroupDataMapper.selCxdybByVersionCode(versionCode);
		if(list != null && !list.isEmpty()){
			receiveGroupDataService.vehicleChangePush(list,"",versionCode);
		}
	}

	@Override
	public void deletePjZcCxdybByCzids(String czids) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("czids", czids);
		vehicleMapper.deletePjZcCxdyb(map);
	}

	@Override
	public void deleteZcClzlbByCzids(String czids) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("czids", czids);
		vehicleMapper.deleteZcClzlb(map);
	}

	@Override
	public void deleteZcClzlbFlagByCzids(String czids) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("czids", czids);
		vehicleMapper.deleteZcClzlbFlag(map);
	}
}

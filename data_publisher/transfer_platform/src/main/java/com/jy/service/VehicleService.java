package com.jy.service;

import java.util.List;
import java.util.Map;

public interface VehicleService {

	/**
	 * 根据承保车型人保编码获取理赔车型信息
	 * @param vehicleCode 承保车型人保编码
	 * @return
	 */
	List<Map<String,Object>> vehicleConversion(List<String> vehicleCode);

	/**
	 * 重新发送整车的数据
	 * @param versionCode 批次号
	 */
	void vehicleConversion(String versionCode);

	void deletePjZcCxdybByCzids(String czids);

	void deleteZcClzlbByCzids(String czids);

	void deleteZcClzlbFlagByCzids(String czids);
}

package com.jy.service.impl;

import com.jy.bean.po.SysRolePo;
import com.jy.mapper.RoleMapper;
import com.jy.service.RoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RoleServiceImpl implements RoleService {

	@Autowired
	private RoleMapper roleMapper;

	public List<SysRolePo> getRoleList(){
		return roleMapper.selectAllRoles();
	}
}

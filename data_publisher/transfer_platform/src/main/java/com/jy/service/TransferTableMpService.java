package com.jy.service;

import com.jy.bean.po.ReceiveBatch;
import com.jy.bean.po.TransferTableMp;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/18
 */
public interface TransferTableMpService {

    List<TransferTableMp> listByBaseTableNameAndTransferFlag(String baseTableName, String transferFlag);

    List<TransferTableMp> listByBaseTableNames(ReceiveBatch receiveBatch, String baseTableNames, String transferFlag);

    TransferTableMp save(TransferTableMp transferTableMp) throws Exception;

    TransferTableMp update(TransferTableMp transferTableMp) throws Exception;

    void remove(String id) throws Exception;
}

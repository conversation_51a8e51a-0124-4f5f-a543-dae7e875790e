package com.jy.service.impl;


import com.jy.ann.MethodMonitor;
import com.jy.bean.po.TransferTableFieldMp;
import com.jy.mapper.TransferTableFieldMpMapper;
import com.jy.service.TransferTableFieldMpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/5/20
 */
@Service
public class TransferTableFieldMpServiceImpl implements TransferTableFieldMpService {

    @Autowired
    private TransferTableFieldMpMapper transferTableFieldMpMapper;
    @Autowired
    private TransferTableFieldMpService transferTableFieldMpService;

    @Override
    @MethodMonitor
    public List<TransferTableFieldMp> listByTableName(String tableName) {
        Map<String, Object> map = new HashMap<>();
        map.put("tableName", tableName);
        return transferTableFieldMpMapper.listTransferTableFieldMp(map);
    }

    @Override
    @MethodMonitor
    public Map<String, List<TransferTableFieldMp>> mapByTableName(String tableName) {
        List<TransferTableFieldMp> fieldMps = transferTableFieldMpService.listByTableName(tableName);
        return fieldMps.stream().collect(Collectors.groupingBy(TransferTableFieldMp::getBaseTableField));
    }

    @Override
    @MethodMonitor
    public TransferTableFieldMp save(TransferTableFieldMp transferTableFieldMp) throws Exception {
        transferTableFieldMpMapper.save(transferTableFieldMp);
        return transferTableFieldMp;
    }

    @Override
    @MethodMonitor
    public TransferTableFieldMp update(TransferTableFieldMp transferTableFieldMp) throws Exception {
        transferTableFieldMpMapper.update(transferTableFieldMp);
        return transferTableFieldMp;
    }

    @Override
    @MethodMonitor
    public void remove(String id) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        transferTableFieldMpMapper.delete(map);
    }
}

package com.jy.service.impl;

import com.jy.bean.dto.SendStateDto;
import com.jy.mapper.SendStateMapper;
import com.jy.service.SendStateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/9/12
 */
@Service
public class SendStateServiceImpl implements SendStateService {

    @Autowired
    private SendStateMapper sendStateMapper;

    @Override
    public List<SendStateDto> listBySendTableIds(String sendTableIds) {
        Map<String, Object> map = new HashMap<>();
        map.put("sendTableIds", sendTableIds);
        return sendStateMapper.listSendState(map);
    }

    @Override
    public List<SendStateDto> listCompareData(String compareBatchNo) {
        Map<String, Object> map = new HashMap<>();
        map.put("compareBatchNo", compareBatchNo);
        return sendStateMapper.listCompareData(map);
    }
}

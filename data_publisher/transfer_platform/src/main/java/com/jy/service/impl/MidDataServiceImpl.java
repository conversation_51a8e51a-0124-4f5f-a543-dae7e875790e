package com.jy.service.impl;

import com.jy.ann.MethodMonitor;
import com.jy.bean.dto.BaseDataDTO;
import com.jy.bean.po.ConversionTable;
import com.jy.mapper.SqlMapper;
import com.jy.service.ConversionTableService;
import com.jy.service.MidDataService;
import com.jy.util.EmptyUtils;
import com.jy.util.SqlMapperProvider;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/5/19
 */
@Service
public class MidDataServiceImpl implements MidDataService {

    @Autowired
    private SqlMapper sqlMapper;
    @Autowired
    private SqlSessionTemplate sqlSessionTemplate;
    @Autowired
    private SqlMapperProvider sqlMapperProvider;
    @Autowired
    private ConversionTableService conversionTableService;


    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Transactional(rollbackFor = Exception.class)
    @MethodMonitor
    public int doBatch(List<BaseDataDTO> baseDataDTOs) throws Exception {
        SqlSession batchSqlSession  = this.sqlSessionTemplate
                .getSqlSessionFactory()
                .openSession(ExecutorType.BATCH, false);// 获取批量方式的sqlsession
        //通过新的session获取mapper
        SqlMapper mapper = batchSqlSession.getMapper(SqlMapper.class);
        int total = 0;
        try {
            for (BaseDataDTO baseDataDTO : baseDataDTOs) {
                if("insert".equals(baseDataDTO.getOperate())){
                    mapper.insert(baseDataDTO);
                } else if("update".equals(baseDataDTO.getOperate())) {
                    mapper.update(baseDataDTO);
                } else if("delete".equals(baseDataDTO.getOperate())) {
                    mapper.delete(baseDataDTO);
                }
            }
            batchSqlSession.commit();
            batchSqlSession.clearCache();
            total = baseDataDTOs.size();
        } catch (Exception e) {
            total = 0;
            batchSqlSession.rollback();
            throw e;
        } finally{
            batchSqlSession.close();
        }
        return total;
    }

    @Override
    @MethodMonitor
    public int jdbcInsertBatch(List<BaseDataDTO> baseDataDTOs) throws Exception {
        //根据表名获取表中全部字段名
        String tableName = baseDataDTOs.get(0).getTableName();
        List<ConversionTable> conversionTables = conversionTableService.listByTableName(tableName);
        List<String> tableFields = conversionTables.stream().map(ConversionTable::getFieldName).collect(Collectors.toList());
        String sql = sqlMapperProvider.jdbcInsert(tableName, tableFields);

        //组装动态sql的参数
        List<Object[]> datas = listTransferData(tableFields, baseDataDTOs);
        jdbcTemplate.batchUpdate(sql , datas) ;

        return baseDataDTOs.size();
    }

    /**
     * tableFields:表字段名
     * baseDataDTOs： 上游传递数据
     * @param tableFields
     * @param baseDataDTOs
     * @return
     */
    private List<Object[]> listTransferData(List<String> tableFields, List<BaseDataDTO> baseDataDTOs){
        List<Object[]> list = new ArrayList<>();
        for(BaseDataDTO baseDataDTO : baseDataDTOs){
            Map<String, String> map = new HashMap<>();
            if(EmptyUtils.isNotEmpty(baseDataDTO.getFields())){
                map.putAll(baseDataDTO.getFields());
            }
            if(EmptyUtils.isNotEmpty(baseDataDTO.getKeys())){
                map.putAll(baseDataDTO.getKeys());
            }

            Object[] data = new Object[tableFields.size()];
            for(int i=0; i<tableFields.size(); i++){
                data[i] = map.get(tableFields.get(i));
            }
            list.add(data);
        }
        return list;
    }
}

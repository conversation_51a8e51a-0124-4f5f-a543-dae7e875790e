package com.jy.service.impl;

import com.jy.mapper.AutoTestMapper;
import com.jy.service.AutoTestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class AutoTestServiceImpl implements AutoTestService {

    @Autowired
    private AutoTestMapper autoTestMapper;

    @Override
    public void deleteDataQuery(String tableName, Map<String, String> whereData) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("tableName", tableName);
        map.putAll(whereData);
        autoTestMapper.deleteDataQuery(map);
    }
}

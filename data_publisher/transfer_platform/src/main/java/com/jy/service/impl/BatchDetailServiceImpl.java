package com.jy.service.impl;

import com.jy.ann.MethodMonitor;
import com.jy.bean.po.BatchDetail;
import com.jy.mapper.BatchDetailMapper;
import com.jy.service.BatchDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/18
 */
@Service
public class BatchDetailServiceImpl implements BatchDetailService {

    @Autowired
    private BatchDetailMapper batchDetailMapper;

    @Override
    @MethodMonitor
    public List<BatchDetail> listByMainBatchNo(String mainBatchNo) {
        Map<String, Object> map = new HashMap<>();
        map.put("mainBatchNo", mainBatchNo);
        return batchDetailMapper.listBatchDetail(map);
    }

    @Override
    @MethodMonitor
    public BatchDetail save(BatchDetail batchDetail) throws Exception {
        batchDetailMapper.save(batchDetail);
        return batchDetail;
    }

    @Override
    @MethodMonitor
    public BatchDetail update(BatchDetail batchDetail) throws Exception {
        batchDetailMapper.update(batchDetail);
        return batchDetail;
    }
}

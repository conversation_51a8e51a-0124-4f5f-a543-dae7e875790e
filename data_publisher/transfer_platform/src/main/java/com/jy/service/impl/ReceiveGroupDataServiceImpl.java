package com.jy.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jy.bean.common.Constant;
import com.jy.bean.dto.*;
import com.jy.bean.po.CompareErrorData;
import com.jy.bean.result.ResultStatus;
import com.jy.bean.vo.ReceiveGroupDataVo;
import com.jy.exception.CompareException;
import com.jy.mapper.GroupWeightMapper;
import com.jy.mapper.ReceiveGroupDataMapper;
import com.jy.mapper.SendStateMapper;
import com.jy.rabbitMq.MQClientMonitor;
import com.jy.rabbitMq.RabbitConfig;
import com.jy.service.CompareErrorDataService;
import com.jy.service.GroupBrandAssocitedService;
import com.jy.service.ReceiveGroupDataService;
import com.jy.util.Dictionary;
import com.jy.util.*;
import com.jy.util.rabbitmq.DataTraceUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static com.jy.util.HttpClientUtil.doPost;
import static com.jy.util.HttpClientUtil.doc2String;

/**
 * Created by jdd on 2018/12/5.
 */
@Service
public class ReceiveGroupDataServiceImpl implements ReceiveGroupDataService{

    private static final Log log = LogFactory.getLog(ReceiveGroupDataServiceImpl.class);
    @Autowired
    private ReceiveGroupDataMapper receiveGroupDataMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private GroupWeightMapper groupWeightMapper;

    @Autowired
    private SendStateMapper sendStateMapper;

    @Autowired
    private CompareErrorDataService compareErrorDataService;

    @Autowired
    private GroupBrandAssocitedService groupBrandAssocitedService;
    @Autowired
    private DataPublishUtils dataPublishUtils;

    @Value("${statistics.dataBaseExistsMqData}")
    private String dataBaseUrl;

    @Value("${publishConfig.sentFlag}")
    private String sendLog;

    @Value("${publishConfig.sentJson}")
    private String sendJson;

    @Value("${httpUtils.dataPublish.sendDataPath}")
    private String sendDataPath;

    @Value("${gainData.vehicleTypeUrl}")
    private String vehicleTypeUrl;

    @Value("${gainData.vehicleUrl}")
    private String vehicleUrl;

    @Autowired
    private MQClientMonitor mqClientMonitor;

    @Autowired
    private AmqpTemplate template;

    private int sendErrNum = 0;


    @Override
    public int insert(ReceiveGroupDataDto receiveGroupDataDto) {
        return receiveGroupDataMapper.insert(receiveGroupDataDto);
    }

    @Override
    public void insertBatch(List<ReceiveGroupDataDto> list, List<Map<String,Object>> listMap,List<Map<String,Object>> sendTableList) {

        int times = (int) Math.ceil(list.size() / 100.0);
        for (int i = 0; i < times; i++) {
            //保存车组数据
            receiveGroupDataMapper.insertBatch(list.subList(i * 100, Math.min((i + 1) * 100, list.size())));
        }
        int times1 = (int) Math.ceil(listMap.size() / 100.0);
        for (int i = 0; i < times1; i++) {
            //保存需要查询的表数据
            receiveGroupDataMapper.insertTableBatch(listMap.subList(i * 100, Math.min((i + 1) * 100, listMap.size())));
        }

        int times2 = (int) Math.ceil(sendTableList.size() / 100.0);
        for (int i = 0; i < times2; i++) {
            //保存需要发送的表数据
            receiveGroupDataMapper.saveSendTable(sendTableList.subList(i * 100, Math.min((i + 1) * 100, sendTableList.size())));
        }
    }

    @Override
    public List<ReceiveGroupDataDto> getTableListMainVersionCode(String mainVersionCode) {
        return receiveGroupDataMapper.getTableListMainVersionCode(mainVersionCode);
    }

    @Override
    public Integer updateErrorNum(String mainVersionCode) {
        return receiveGroupDataMapper.updateErrorNum(mainVersionCode);
    }

    @Override
    public void insertTableDataBatch(List<Map> list, String tableName,String groupId,String groupCode,String brandId,String dataType,String sourceTable,String brandCode) throws Exception{
        Map<String,Object> map = new HashMap<>();
        map.put("tableName",tableName);
        map.put("groupId",groupId);
        map.put("dataType",dataType);

        //删除表中原来的车组数据
        map.put("brandId",brandId);
        if("2".equals(dataType)){
            map.put("source",sourceTable.substring(sourceTable.lastIndexOf("_")+1,sourceTable.length()).toUpperCase());
        }
        Long startTime = System.currentTimeMillis();
        receiveGroupDataMapper.delOldGroupData(map);
        log.info("删除数据的时间"+(System.currentTimeMillis()-startTime));
        //使用spring中jdbaTemplate新增数据
        jdbcTempBatch(list,tableName,groupId,groupCode,brandId,brandCode);
    }

    public void jdbcTempBatch(List<Map> list,String tableName,String groupId,String groupCode,String brandId,String brandCode){
        Long startTime = System.currentTimeMillis();
        log.info("----------------------开始处理时间:"+startTime);
        int times = (int) Math.ceil(list.size() / 5000.0);
        for (int i = 0; i < times; i++) {
            //保存车组数据
            StringBuilder strs = new StringBuilder("insert into "+tableName+" (");
            StringBuilder str2 = new StringBuilder("");
            Map<String,Object> maps = list.get(0);
            if(tableName.indexOf("part_base") > -1 || tableName.indexOf("part_hot") > -1 || tableName.indexOf("part_mutex") > -1){
                strs.append("GROUP_CODE,GROUP_ID,");
                str2.append("?,?,");
                strs.append("BRAND_CODE,BRAND_ID,");
                str2.append("?,?,");
            }
            for (String str:maps.keySet()){
                strs.append(str+",");
                str2.append("?,");
            }
            String str = strs.toString().substring(0,strs.length()-1)+") values ("+str2.toString().subSequence(0,str2.length()-1)+")";
            List<Map> finalList = list.subList(i * 5000, Math.min((i + 1) * 5000, list.size()));
            int[] updatedCountArray = jdbcTemplate.batchUpdate(str, new BatchPreparedStatementSetter() {
                Map<String,Object> map = null;
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    map = finalList.get(i);
                    int num = 0;
                    if(tableName.indexOf("part_base") > -1 || tableName.indexOf("part_hot") > -1 || tableName.indexOf("part_mutex") > -1){
                        ps.setObject(++num,groupCode);
                        ps.setObject(++num,groupId);
                        ps.setObject(++num,brandCode);
                        ps.setObject(++num,brandId);
                    }
                    for (String str:map.keySet()){
                        if(("XGRQ".equals(str.toUpperCase()) || "JLRQ".equals(str.toUpperCase())) && map.get(str) != null  && StringUtils.isDate1(map.get(str).toString())){
                            ps.setTimestamp(++num, Timestamp.valueOf(map.get(str).toString()));
                        }else{
                            ps.setObject(++num,map.get(str));
                        }
                    }
                }

                @Override
                public int getBatchSize() {
                    return finalList.size();
                }
            });
        }
        log.info("----------------------总的处理时间:"+(System.currentTimeMillis()-startTime));
    }

    public void insertProvider(List<Map> list,String tableName,String groupId,String groupCode){
        Long startTime = System.currentTimeMillis();
        log.info("----------------------开始处理时间:"+startTime);
        List<String> sqlList = new ArrayList<>(1000);
        for (Iterator iterator = list.iterator();iterator.hasNext();){
            StringBuilder sql = new StringBuilder();
            StringBuilder keys = new StringBuilder();
            StringBuilder values = new StringBuilder();
            Map map = (Map) iterator.next();
            iterator.remove();
            sql.append(" into ");
            sql.append(tableName) .append(" (");
            if(tableName.indexOf("part_base_") > -1 || tableName.indexOf("part_hot_") > -1 || tableName.indexOf("part_mutex_") > -1){
                keys.append("GROUP_CODE,GROUP_ID,");
                values.append("'"+groupCode+"','"+groupId+"',");
            }


            map.forEach((key, value) -> {
                keys.append(key);
                if (!StringUtils.isEmptyObj(value) && StringUtils.isDate1(value.toString())) {
                    values.append("to_date('" + value.toString() + "','yyyy-mm-dd hh24:mi:ss'),");
                } else {
                    values.append("'" + (value.toString() == null ? "" : value.toString()) + "',");
                }
            });
            keys.deleteCharAt(keys.length() - 1);
            values.deleteCharAt(values.length() - 1);
            sql.append(keys);
            sql.append(") values (");
            sql.append(values+")");
            sqlList.add(sql.toString());
            if(sqlList.size()==1000){
                Map<String,List> maps = new HashMap<>();
                maps.put("list",sqlList);
                receiveGroupDataMapper.insertDataJsonToSql(maps);
                sqlList.clear();
            }
        }
        log.info("----------------------总的处理时间:"+(System.currentTimeMillis()-startTime));
    }

    @Override
    public void updateTableStateByMianVersionCode(String state,String errMsg,String mainVersionCode,String tableName,String versionCode) {
        Map<String, Object> map = new HashMap<>();
        map.put("state",state);
        map.put("errMsg",errMsg);
        map.put("mainVersionCode",mainVersionCode);
        map.put("tableName",tableName);
        map.put("versionCode",versionCode);
        receiveGroupDataMapper.updateTableStateByMianVersionCode(map);
        //如果数据获取成功,修改关联数据
        if("01".equals(map.get("state").toString())){
            map.put("state","40");
            receiveGroupDataMapper.updateSendTableState(map);
        }
    }

    @Override
    public void updateGroupStateByMainVersionCode(String state,String mainVersionCode,String errMsg) {
        Map<String, Object> map = new HashMap<>();
        map.put("state",state);
        map.put("mainVersionCode",mainVersionCode);
        map.put("errMsg",errMsg);
        receiveGroupDataMapper.updateGroupStateByMainVersionCode(map);
    }


    private Map<String,Object> fitCompareErrorData(CompareErrorData compareErrorData, String sendCode, String clientCode, String clientUrl){
        String tableNameNoSuffix = groupBrandAssocitedService.fitTableName(compareErrorData.getTableName());

        String tableSuffix = "";
        String tableName = compareErrorData.getTableName().toLowerCase();
        if(tableName.contains(tableNameNoSuffix + "_")){
            tableSuffix = tableName.replace(tableNameNoSuffix + "_", "");
        }
        List<Map<String,Object>> dataList = new ArrayList<>();
        Map<String,Object> map = new HashMap<>();
        Map<String,Object> suffixMap = new HashMap<>(1);
        suffixMap.put("tableSuffix",tableSuffix);
        map.put("operate", "delete");
        map.put("keys", JSONObject.parseObject(compareErrorData.getMessage()));
        map.put("suffix", suffixMap);
        dataList.add(map);



        Map<String,Object> resultMap = new HashMap();
        resultMap.put("data", dataList);
        resultMap.put("tableName", tableNameNoSuffix);
        resultMap.put("total", dataList.size());
        resultMap.put("sendNum", dataList.size());
        resultMap.put("batchNo", sendCode);
        resultMap.put("mainCode", compareErrorData.getMainVersionCode());
        resultMap.put("clientCode", clientCode);
        resultMap.put("clientUrl", clientUrl);

        return resultMap;
    }

    public Map<String,Object> getTableDataList(String tableName,String versionCode,String brandCode,String suffix,String mainVersionCode,String sendCode,String handTable,String brandId,String vehicleId,String delFlag,String groupCode,String clientCode,String clientUrl) throws Exception{

        List<Map<String,Object>> list = null;
        String tableName1 = tableName.substring(2);
        //如果是清空的批次
        //查询上一批报错的信息
        String oldMainVersionCode = "";
        if(delFlag != null && "1".equals(delFlag)){
            Map<String,Object> map1 = new HashMap<>(2);
            map1.put("mainVersionCode",mainVersionCode);
            map1.put("groupCode",groupCode);
            map1 = receiveGroupDataMapper.getMainVersionCodeByDelFlag(map1);
            if(map1 != null && map1.size() > 0){
                oldMainVersionCode = map1.get("mainVersionCode").toString();
            }
        }

        String tableNameNoSuffix = groupBrandAssocitedService.fitTableName(tableName1);
        String tableSuffix = "";
        if(tableName1.contains(tableNameNoSuffix + "_")){
            tableSuffix = tableName1.substring(tableName1.lastIndexOf("_") + 1, tableName1.length());
        }

        if("WL".equals(handTable)){
            list = receiveGroupDataMapper.getCompareDataByTableName(tableName,versionCode,brandId,vehicleId,oldMainVersionCode);
            tableNameNoSuffix = "wl_" + tableNameNoSuffix;
        }else{
            list = receiveGroupDataMapper.getTableDataList(tableName,versionCode,vehicleId,oldMainVersionCode);
        }

        Map<String,Object> resultMap = new HashMap(8);
        if(list != null && !list.isEmpty()){
            //如果承保理赔对应表有变动,则像整车平台推送变更信息
            if("c_pj_zc_cxdyb".equals(tableName) && Dictionary.IS_COMPARE){
                vehicleChangePush(list,mainVersionCode,versionCode);
            }
            Map<String,Object> fieldsMap = null;
            Map<String,String> keysMap = null;
            Map<String,Object> dataMap = null;
            Map<String,Object> suffixMap = new HashMap<>(1);
            Map<String,Object> mustMap = new HashMap<>(1);
            suffixMap.put("tableSuffix",tableSuffix);
            List<Map<String,Object>> dataList = new ArrayList<>();
            resultMap.put("total",list.size());
            resultMap.put("batchNo",sendCode);
            resultMap.put("mainBatchNo",mainVersionCode);
            for (Map<String,Object> map:list) {
                keysMap = new HashMap<>(2);
                dataMap = new HashMap<>(4);
                //主轴字段
                if(StringUtils.isEmptyObj(map.get("id"))){
                    return null;
                }
                keysMap.put("id",map.get("id").toString());
                //facade后缀
                keysMap.put("partTableSuffix",suffix);
                //获取对比后的字段和获取主轴字段的值
                fieldsMap = contrastFields(map);
                //状态
                dataMap.put("operate",fieldsMap.get("state").toString());
                if(!"delete".equals(fieldsMap.get("state").toString())){
                    fieldsMap = removeMapKey(fieldsMap);
                    dataMap.put("fields",fieldsMap);
                }else {
                    dataMap.put("fields",new JSONObject());
                }
                dataMap.put("must",mustMap);
                dataMap.put("suffix",suffixMap);
                dataMap.put("keys",keysMap);
                dataList.add(dataMap);
            }
            if(delFlag != null && "1".equals(delFlag)){
                //发送的数据去重
                HashSet h = new HashSet(dataList);
                dataList.clear();
                dataList.addAll(h);
            }
            resultMap.put("data",dataList);
            resultMap.put("sendNum",dataList.size());
            resultMap.put("clientCode", clientCode);
            resultMap.put("clientUrl", clientUrl);
            resultMap.put("tableName",tableNameNoSuffix);
        }
        return resultMap;

    }

    /**
     * 向整车平台推送变更信息
     * @param list 需要推送的数据
     */
    @Override
    public void vehicleChangePush(List<Map<String,Object>> list,String mainVersionCode,String versionCode){
        try {
            List<Map<String,Object>> vehList = new ArrayList<>(list.size());
            list.forEach((Map<String,Object> map) -> {
                Map<String,Object> veh = new HashMap<>(3);
                veh.put("vehicleCode",map.get("zccxbmRb"));
                veh.put("claimVehicleId",map.get("pjcxid"));
                veh.put("changeFlag",map.get("state"));
                vehList.add(veh);
            });
            Map<String,Object> result = new HashMap<>(1);
            result.put("vehicle",vehList);
            String data = HttpClientUtil.doPost(vehicleUrl,JSONObject.toJSONString(result));
            log.info("整车推送结果:"+data);
            Map<String,String> sendTable = new HashMap<>(6);
            if(!StringUtils.isEmpty(data)){
                JSONObject jsonObject = JSONObject.parseObject(data);
                if(ResultStatus.SUCCESS.getStatus().equals(jsonObject.get("code"))){
                    if(!StringUtils.isEmpty(mainVersionCode)){
                        sendTable.put("mainVersionCode",mainVersionCode);
                        sendTable.put("versionCode",versionCode);
                        sendTable.put("sendTable","c_pj_zc_cxdyb");
                        sendTable.put("state","2");
                        sendTable.put("handleTable","ZC");
                        sendTable.put("sendCode",versionCode);
                        receiveGroupDataMapper.insertSendTable(sendTable);
                    }else{
                        sendTable.put("state","2");
                    }
                }else{
                    sendTable.put("state","3");
                    sendTable.put("errMsg",jsonObject.get("message") == null ? "" : jsonObject.getString("message"));
                }
            }else{
                sendTable.put("state","3");
                sendTable.put("errMsg","未知异常");
            }
            receiveGroupDataMapper.updateSendTable(sendTable);
        }catch (Exception e){
            log.error("整车数据推送失败,错误信息:"+e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public boolean isVerification(String mqNames) {

        try {
            //查询是否有等待处理的数据或正在处理的数据
            int num = receiveGroupDataMapper.getConductCount();
            if(num == 0){
                //判断基础发布平台是否有堆积
                Map<String,String> querys = new HashMap<>(1);
                querys.put("mqNames",mqNames);
                String data = HttpUtils.doPost(dataBaseUrl,"",null,querys,"");
                JSONObject json = JSONObject.parseObject(data);
                if(!ResultStatus.SUCCESS.getStatus().equals(json.getString("code"))){
                    return false;
                }
            }else{
                return false;
            }
        }catch (Exception e){
            log.error("判断是否能校验数据出错,错误信息:"+e.getMessage());
            e.printStackTrace();
            return false;
        }
        //暂停处理转换平台mq数据
    //    Dictionary.setIsContinue(false);
        return true;
    }

    @Override
    public List<Map<String, Object>> getTest() {
        return receiveGroupDataMapper.getTest();
    }

    @Override
    public void saveConversionTables(List<Map> list) {
        receiveGroupDataMapper.saveConversionTables(list);
    }

    @Override
    public void saveAssociated(List<Map<String,Object>> list) {
        //创建数据库
        Map<String,Object> parameter  = new HashMap<>();
        Map<String,Object> map1 = null;
        for (Map<String,Object> map:list) {
            //if("1".equals(map.get("isSeparate").toString())){
                parameter.put("tableName",map.get("tableName"));
                parameter.put("brandCode",map.get("brandCode"));
                List<Map<String,Object>> lists = receiveGroupDataMapper.createTables(parameter);
                System.out.print(JSONArray.toJSON(list).toString()+"================");
            //}
        }
        //保存数据
        receiveGroupDataMapper.saveAssociated(list);
    }

    @Override
    public List<Map<String,Object>> getAssociatedTableName(String tableName) {
        Map<String,Object> map = new HashMap<>();
        map.put("tableName",tableName);
        return receiveGroupDataMapper.getAssociatedTableNameList(map);
    }

    @Override
    public int isExistsTable() {
        return receiveGroupDataMapper.isExistsTable();
    }

    public String isExists(String groupId) {
        Map<String, Object> map = receiveGroupDataMapper.getClfzxxbByGroupCode(groupId);
        if(map == null || map.isEmpty() || StringUtils.isEmptyObj(map.get("partsSuffix"))){
            return "";
        }else{
            String suffix = map.get("partsSuffix").toString();
            Map<String,Object> parameter = new HashMap<>(3);
            parameter.put("partsSuffix",suffix);
            parameter.put("hoursSuffix",Integer.parseInt(suffix)/10);
            parameter.put("groupId",groupId);
            receiveGroupDataMapper.updateSuffix(parameter);
            return suffix;
        }
    }

    @Override
    public void updateSuffix(Map<String,Object> map) {
        receiveGroupDataMapper.updateSuffix(map);
    }

    @Override
    public Map<String, Object> getPartNum(String groupCode) {
        return receiveGroupDataMapper.getPartNum(groupCode);
    }

    @Override
    public List<Map> getTest1(String groupId) {
        return receiveGroupDataMapper.getTest1(groupId);
    }

    //对比数据
    public Map<String,Object> contrastFields(Map<String,Object> fieldsMap){

        int index = -1;
        StringBuilder delKeys = new StringBuilder();
        Map<String,Object> resultMap = new HashMap<>();
        //循环遍历比对新旧值是否相等
        for (Map.Entry<String, Object> m : fieldsMap.entrySet()) {
            index = m.getKey().indexOf("Old");
            if(index > -1){
                if(m.getValue() != null && fieldsMap.get(m.getKey().substring(0,index)+"New") != null){
                    if(!m.getValue().equals(fieldsMap.get(m.getKey().substring(0,index)+"New"))){
                        delKeys.append(m.getKey()+",");
                    }else{
                        delKeys.append(m.getKey()+",");
                        delKeys.append(m.getKey().substring(0,index)+"New,");
                    }
                }else if(m.getValue() == null && fieldsMap.get(m.getKey().substring(0,index)+"New") == null){
                    delKeys.append(m.getKey()+",");
                    delKeys.append(m.getKey().substring(0,index)+"New,");
                }else{
                    delKeys.append(m.getKey()+",");
                }
            }
        }
        if(delKeys.length() > 0){
            for (String key:delKeys.toString().split(",")) {
                fieldsMap.remove(key);
            }
        }
        for (Map.Entry<String, Object> m : fieldsMap.entrySet()) {
            resultMap.put(m.getKey().replace("New",""),m.getValue() == null ? "" : m.getValue());
        }
        return resultMap;
    }

    public Map<String, Object> removeMapKey(Map<String, Object> fieldsMap) {
        fieldsMap.remove("id");
        fieldsMap.remove("state");
        fieldsMap.remove("versionCode");
        fieldsMap.remove("mainCode");
        fieldsMap.put("scbz","0");
        return fieldsMap;
    }

    /**
     * 组装数据并发送到及时更新
     * @param brandCode 品牌编码
     * @param mainVersionCode 主批次号
     */
    @Override
    public void dataAssembly(String mainVersionCode,String brandCode,String groupCode,String suffix,boolean isFirst,String brandId,String delFlag,String clientCode,String clientUrl){

        //修改当前批次为发送中
        Map<String,Object> para = new HashMap<>(2);
        para.put("mainVersionCode",mainVersionCode);
        para.put("state","002");
        receiveGroupDataMapper.updateGroupStateByMainVersionCode(para);
        para = new HashMap<>(2);
        para.put("mainVersionCode",mainVersionCode);
        para.put("state","1");
        List<Map<String,Object>> list = receiveGroupDataMapper.getSendTableListByMainVersionCode(para);
        if(list != null && list.size() > 0) {
            boolean flag = true;
            for (Map<String, Object> dto : list) {
                String stateId = "";
                try{
                    String versionCode = dto.get("versionCode").toString();
                    String sendCode = dto.get("sendCode").toString();
                    String tableName = dto.get("sendTable").toString();
                    String suffix1 = "";
                    List<Map<String,Object>> sendList = new ArrayList<>();
                    if (tableName.contains("pj_clljtxdyb") || tableName.contains("pj_cllbjdyb") || tableName.contains("pj_cltxdyb")) {
                        //查询当前表的数据总量,如果数据量大于2W,则分车型发送
                        para = new HashMap<>(2);
                        para.put("versionCode",versionCode);
                        para.put("tableName",tableName);
                        int dataCount = receiveGroupDataMapper.getTableDataCount(para);
                        suffix1 = suffix;
                        if(dataCount > 20000){
                            //如果是车型零件表或者是车型零件图片表,为防止数据量过大,导致内存溢出,则分车型查询数据
                            //查询车型信息
                            sendList = receiveGroupDataMapper.getVehicleIdByC(para);
                        }
                    }else if(!StringUtils.isEmpty(suffix) && tableName.indexOf("pj_czlbjdyb") > -1){
                        suffix1 = (Integer.parseInt(suffix)/10)+"";
                    }
                    Map<String, Object> map;
                    String sendTableState = "4";
                    if(sendList != null && !sendList.isEmpty()){
                        String createCode = "";
                        for (int i = 0;i < sendList.size();i++){
                            Map<String,Object> vehicleIds = sendList.get(i);
                            int begin = 100;
                            if(tableName.indexOf("c_wl_") == 0){
                                createCode  = sendCode+ (begin + i) +"b";
                            }else{
                                createCode  = sendCode+ (begin + i) +"a";
                            }
                            map = saveSendState(vehicleIds.get("clzlid").toString(),dto.get("id").toString(),createCode,tableName);
                            stateId = map.get("stateId").toString();
                            if("3".equals(map.get("sendState").toString()) || "1".equals(map.get("sendState").toString())){
                                map = getTableDataList(tableName, versionCode, brandCode, suffix1,mainVersionCode,createCode,dto.get("handleTable").toString(),brandId,vehicleIds.get("clzlid").toString(),delFlag,groupCode,clientCode,clientUrl);
                                if(map != null && map.size() > 0){
                                    sendTableState = "2";
                                }
                                flag = send(map,createCode,mainVersionCode,versionCode,dto.get("id").toString(),isFirst,brandCode,groupCode,suffix,brandId,flag,tableName,stateId,delFlag,sendTableState,clientCode,clientUrl);
                            }
                        }
                    }else{
                        map = saveSendState("",dto.get("id").toString(),sendCode,tableName);
                        stateId = map.get("stateId").toString();
                        if("3".equals(map.get("sendState").toString()) || "1".equals(map.get("sendState").toString())){
                            if(Constant.ERROR_RECEIVE_TABLE.equals(tableName)){
                                CompareErrorData compareErrorData = compareErrorDataService.getByVersionCode(versionCode);
                                map = fitCompareErrorData(compareErrorData, sendCode, clientCode, clientUrl);

                            } else {
                                map = getTableDataList(tableName, versionCode, brandCode, suffix1,mainVersionCode,sendCode,dto.get("handleTable").toString(),brandId,"",delFlag,groupCode,clientCode,clientUrl);
                            }
                            if(map != null && map.size() > 0){
                                sendTableState = "2";
                            }
                            flag = send(map,sendCode,mainVersionCode,versionCode,dto.get("id").toString(),isFirst,brandCode,groupCode,suffix,brandId,flag,tableName,stateId,delFlag,sendTableState,clientCode,clientUrl);
                        }
                    }

                }catch (Exception e){
                    flag = false;
                    if(sendErrNum < 2 && isFirst){
                        sendErrNum++;
                        //重新发送
                        dataAssembly(mainVersionCode,brandCode,groupCode,suffix,isFirst,brandId,delFlag,clientCode,clientUrl);
                    }else{
                        updateSendTableState("3",dto.get("id").toString(),e.getMessage(),stateId,"0","3");
                        //记录错误信息
                        String errMsg = e.getMessage();
                        updateGroupStateByMainVersionCode("901",mainVersionCode,errMsg.length() > 2000 ? errMsg.substring(0,2000) : errMsg);
                    }
                    e.printStackTrace();
                }
            }
            if(flag){
                updateGroupStateByMainVersionCode("000", mainVersionCode,"");
                //如果队列是关闭状态,发送成功后重启队列
                if(!Dictionary.IS_CONTINUE){
                    Dictionary.setIsContinue(true);
                }
            }

        }
    }

    /**
     * 数据发送
     * @param map 发送的数据
     * @param sendCode
     * @param mainVersionCode
     * @param versionCode
     * @param id
     * @param isFirst
     * @param brandCode
     * @param groupCode
     * @param suffix
     * @param brandId
     * @param flag
     */
    public boolean send(Map<String,Object> map,String sendCode,String mainVersionCode,String versionCode,String id,boolean isFirst,String brandCode,String groupCode,String suffix,String brandId,boolean flag,String tableName,String stateId,String delFlag,String sendTableState,String clientCode,String clientUrl) throws Exception{

        if (map != null && map.size() > 0) {
            Timestamp time = DateUtil.crunttime();
            String sendNum = map.get("sendNum").toString();
            map.remove("sendNum");

            String data = JSON.toJSONStringWithDateFormat(map,"yyyy-MM-dd HH:mm:ss", SerializerFeature.DisableCircularReferenceDetect,SerializerFeature.WriteMapNullValue);
            //发送到及时更新
            Map<String, String> querys = new HashMap<>();
            querys.put("receive", "receive");
            JSONObject json = dataPublishUtils.doPost(sendDataPath, querys, data);
            String status = json.getString("status");
            String message = json.getString("message");
            if("200".equals(json.get("status"))){
                updateTableStateByMianVersionCode("03", "", mainVersionCode, "",versionCode);
                updateSendTableState("2",id,"",stateId,sendNum,sendTableState);
            } else {
                flag = false;
                updateSendTableState("3",id,"发送小批次日志失败",stateId,sendNum,"3");
                //记录错误信息
                updateGroupStateByMainVersionCode("901",mainVersionCode,"数据发送失败");
            }



           /* String sendResult = HttpUtils.doPost(sendJson, "", new HashMap(0), new HashMap(0), json);
            if(EmptyUtils.isNotEmpty(sendResult) && "1".equals(sendResult)){
                status = "200";
                message = "success";
            }
            if(!StringUtils.isEmpty(resultJson)){

            }else{
                flag = false;
                if(sendErrNum < 2 && isFirst){
                    sendErrNum++;
                    //重新发送
                    dataAssembly(mainVersionCode,brandCode,groupCode,suffix,isFirst,brandId,delFlag,clientCode,clientUrl);
                }else{
                    updateSendTableState("3",id,"发送小批次日志失败",stateId,sendNum,"3");
                    //记录错误信息
                    updateGroupStateByMainVersionCode("901",mainVersionCode,"数据发送失败");
                }
            }*/
            DataTraceUtils.sendTrace(new JSONObject(map), "数据推送", status, message, time);
        }else{
            updateSendTableState("4",id,"",stateId,"0",sendTableState);
        }
        return flag;
    }

    public Map<String,Object> saveSendState(String vehicleId,String id,String sendCode,String tableName){
        Map<String,Object> para = new HashMap<>(2);
        para.put("vehicleId",vehicleId);
        para.put("id",id);
        Map<String,Object> sendStateId = sendStateMapper.getSendListByTableId(para);
        String stateId = "";
        if(sendStateId == null || sendStateId.isEmpty()){
            SendStateDto sendStateDto = new SendStateDto();
            stateId = StringUtils.getGUID();
            sendStateDto.setId(stateId);
            sendStateDto.setSendCode(sendCode);
            sendStateDto.setSendState("1");
            sendStateDto.setSendTable(tableName);
            sendStateDto.setSendTime(new Date());
            sendStateDto.setSendTableId(id);
            sendStateDto.setVehicleId(vehicleId);
            sendStateMapper.SaveSendState(sendStateDto);
            sendStateId = new HashMap<>();
            sendStateId.put("stateId",stateId);
            sendStateId.put("sendState","1");
        }
        return  sendStateId;
    }

    public void updateSendTableState(String state,String id,String errMsg,String stateId,String sendNum,String sendTableState){
        //如果当前队列是运行状态,则停止该队列
        if("3".equals(state) && Dictionary.IS_CONTINUE){
            Dictionary.setIsContinue(false);
        }
        Map<String,Object> para = new HashMap<>(6);
        para.put("state",state);
        para.put("id",id);
        para.put("errMsg",errMsg);
        para.put("stateId",stateId);
        para.put("sendNum",sendNum);
        sendStateMapper.updateSendState(para);
        para.put("state",sendTableState);
        receiveGroupDataMapper.updateSendTableState(para);
    }

    @Override
    public Map<String, Object> getSendTable(String tableName) {
        return receiveGroupDataMapper.getSendTable(tableName);
    }

    @Override
    public int updateBrandLevel(String brandCode) throws Exception{
        Map<String,Object> map = receiveGroupDataMapper.getPartNumAndHotFlag(brandCode);
        String brandLevel = "10000";
        if(map != null){
            if(map.get("partNum") != null && "1".equals(map.get("brandHotFlag").toString())){
                brandLevel = "1";
            }else if(map.get("partNum") != null && "0".equals(map.get("brandHotFlag").toString())){
                brandLevel = "100";
            }
        }
        String brandId = map.get("id") == null ? "" : map.get("id").toString();
        map = new HashMap<>();
        map.put("brandId",brandId);
        map.put("brandLevel",brandLevel);
        int num = receiveGroupDataMapper.updateBrandLevel(map);
        return num;
    }

    //发送数据日志
    public static String sendBatchNo(String mainBatchNo, String strNo,String url,String resultType){

        String json = "";
        //日志请求地址
        JSONObject sendLog = new JSONObject();
        JSONObject sendLogData = new JSONObject();
        //请求产品发布平台获取最新的版本号
        sendLog.put("RESULT_TYPE",resultType);
        sendLog.put("INTERFACE_CODE","001101");
        sendLog.put("DATA_TYPE","2");
        sendLogData.put("PLATFORM_CODE", "PP8569");
        sendLogData.put("COMPANY_CODE", "");
        sendLogData.put("MAIN_VERSION_CODE", mainBatchNo);
        sendLogData.put("VERSION_CODE", strNo);
        sendLogData.put("DEAL_TIME", TimestampTool.getCurrentDateTime());
        sendLogData.put("DATA_CODE", "");
        sendLogData.put("STATE", "000" );
        sendLogData.put("DEAL_NAME", "配件转换平台发布数据");
        sendLog.put("DATA",sendLogData);
        try {
            System.out.println("请求数据服务开始："+ TimestampTool.crunttime());
            //json = HttpUtils.doPost(url, "", new HashMap<String, String>(), new HashMap<String, String>(), JSON.toJSONString(sendLog));
            //json = doPost(url, JSON.toJSONString(sendLogData));
            json = doPost(url,doc2String(sendLog.toString()));
            JSONObject obj = JSONObject.parseObject(json);
            if(obj.getString("code").equals("400")){
                return "";
            }
            System.out.print("请求数据服务结束:"+TimestampTool.crunttime()+json);
            return json;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    //判断当前车组是否是新增数组,如果是新增,则返回表后缀
    @Override
    public String groupIsExists(String groupId) throws Exception{
        String suffix = isExists(groupId);
        if(StringUtils.isEmpty(suffix)){
            try {
                //获取partNum
                Map<String,Object> map1 = receiveGroupDataMapper.getPartNum(groupId);
                int partNum = (map1 == null || map1.get("partNum") == null) ? 0 : Integer.parseInt(map1.get("partNum").toString());

                //获取表后缀
                suffix = getMinPartTableSuffix(partNum);
                //修改表后缀
                Map<String,Object> map = new HashMap<>(3);
                map.put("groupId",groupId);
                map.put("partsSuffix",suffix);
                map.put("hoursSuffix",Integer.parseInt(suffix)/10);
                receiveGroupDataMapper.updateSuffix(map);
            } catch (Exception e) {
                e.printStackTrace();
                return "";
            }
        }

        //p库车组表存在其他车组尾缀字段为空时，从f库更新至p库
        List<Map<String,Object>>  nullSuffixList = receiveGroupDataMapper.listClfzxxbByNullSuffix();
        if(EmptyUtils.isNotEmpty(nullSuffixList)){
            List<String> groupIds = nullSuffixList.stream().map(str->str.get("id").toString()).collect(Collectors.toList());
            for(String str : groupIds){
                isExists(str);
            }
        }

        return suffix;
    }

    public String getMinPartTableSuffix(int partNum) throws Exception {
        GroupWeight groupWeight = groupWeightMapper.getMinPartTableSuffix();
        if(groupWeight == null){
            return null;
        }
        groupWeight.setGroupNum(groupWeight.getGroupNum() + 1);
        groupWeight.setPartNum(groupWeight.getPartNum() + partNum);
        groupWeightMapper.update(groupWeight);
        return groupWeight.getPartTableSuffix();
    }

    @Override
    public boolean againSend(String mainVersionCode){

        try {
            if(!Dictionary.IS_CONTINUE ){
                //查询发送失败的批次信息
                List<ReceiveGroupDataDto> list = receiveGroupDataMapper.getSendFailMainVersionCode(mainVersionCode);
                if(list != null && list.size() > 0){
                    //重新处理
                    for (ReceiveGroupDataDto dto : list){
                        String suffix = "";
                        if (dto.getMainVersionCode().indexOf("GroupData") > -1) {
                            //获取表后缀
                            suffix = groupIsExists(dto.getGroupId());
                        }
                        dataAssembly(dto.getMainVersionCode(),dto.getBrandCode(),dto.getGroupCode(),suffix,false,dto.getBrandId(),"1",dto.getClientCode(),dto.getClientUrl());
                    }
                }else{
                    //如果没有发送失败的数据,则重启队列
                    Dictionary.setIsContinue(true);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return Dictionary.IS_CONTINUE;
    }

    @Override
    public String getReceiceGroupStateByMainVersionCode(String mainVersionCode) {
        return receiveGroupDataMapper.getReceiceGroupStateByMainVersionCode(mainVersionCode);
    }

    @Override
    public List<Map<String, Object>> groupTransformation(String mainVersionCode,String groupId) {
        Map<String,Object> map = new HashMap<>();
        map.put("mainVersionCode",mainVersionCode);
        map.put("groupId",groupId);
        return receiveGroupDataMapper.groupTransformation(map);
    }

    @Override
    public String dataContrast(String mainVersionCode,String versionCode,String certain) {
        Map<String,Object> map = new HashMap<>();
        map.put("mainVersionCode",mainVersionCode);
        map.put("versionCode",versionCode);
        map.put("certain",certain);
        map.put("state","");
        receiveGroupDataMapper.dataContrast(map);
        return map.get("state").toString();
    }

    @Override
    public List<Map<String, Object>> brandTransformation(String mainVersionCode) {
        return null;
    }

    @Override
    public List<Map<String, Object>> repOutTransformation(String mainVersionCode) {
        return null;
    }

    @Override
    public List<Map<String, Object>> pzshTransformation(String mainVersionCode) {
        return null;
    }

    @Override
    public List<Map<String, Object>> stdPartTransformation(String mainVersionCode) {
        return null;
    }

    @Override
    public boolean transformation(String mainVersionCode, String groupId, String dataType, String tableName,boolean flag,boolean isWL) {
        boolean result = true;
        String status = "200";
        Map<String,Object> map = new HashMap<>();
        if(flag){
            map.put("groupRelFlag",1);
        }else{
            map.put("groupRelFlag",0);
        }
        map.put("mainVersionCode",mainVersionCode);

        map.put("state","");
        if(!flag){
            if ("1".equals(dataType)) {
                map.put("groupId",groupId);
                //product库
                receiveGroupDataMapper.groupTransformation(map);
                if("success".equals(map.get("state"))){
                    map.put("state","");
                    //车型换车组
                    receiveGroupDataMapper.clgroupTransformation(map);
                }
                //判断是否是蔚来的品牌,如果是,则调用蔚来的转换存储过程
                if(isWL && "success".equals(map.get("state"))){
                    //调用存储过程
                    receiveGroupDataMapper.groupTransformationByWL(map);
                }
            }else if("3".equals(dataType)){
                receiveGroupDataMapper.repOutTransformation(map);
            }else if("4".equals(dataType)){
                receiveGroupDataMapper.brandTransformation(map);
            }else if("5".equals(dataType)){
                if("pj_pzshljdyb".equals(tableName)){
                    receiveGroupDataMapper.pzshTransformation(map);
                }else if("part_dic_std_part".equals(tableName)){
                    receiveGroupDataMapper.stdPartTransformation(map);
                }
            }else if("6".equals(dataType)){
                map.put("groupId",groupId);
                receiveGroupDataMapper.oneGroup(map);
            }else if("7".equals(dataType)){
                map.put("tableName",tableName);
                receiveGroupDataMapper.oneTable(map);
            }else if("8".equals(dataType)){
                receiveGroupDataMapper.truckVehicle(map);
            }
        }else{
            receiveGroupDataMapper.clgroupTransformation(map);
        }
        log.error("数据转换结果:" + map.get("state"));
        if("success".equals(map.get("state").toString())){
            result = true;
        }else{
            result = false;
        }
        DataTraceUtils.sendTrace(new JSONObject(map), "数据转换", status, map.get("state").toString(), null);
        return result;
    }

    @Override
    public List<ReceiveGroupDataDto> getByPMainVersionCode(String mainVersionCode) {
        return receiveGroupDataMapper.getByPMainVersionCode(mainVersionCode);
    }

    @Override
    public Map<String, Object> getModifyByMainVersionCode(String mainVersionCode) {
        return receiveGroupDataMapper.getModifyByMainVersionCode(mainVersionCode);
    }

    @Override
    public List<Map<String, Object>> getMaxHzNumber(String groupId) {
        return receiveGroupDataMapper.getMaxHzNumber(groupId);
    }

    @Override
    public List<Map<String, Object>> getNullPartNameByGroupCode(String groupId) {
        return receiveGroupDataMapper.getNullPartNameByGroupCode(groupId);
    }

    @Override
    public void updateHzNumber(Map<String, Object> map) {
        receiveGroupDataMapper.updateHzNumber(map);
    }

    @Override
    public void updateCllbjdyb() {
        receiveGroupDataMapper.updateCllbjdyb();
    }

    @Override
    public Map<String, Object> getClfzxxbByGroupCode(String groupId) {
        return receiveGroupDataMapper.getClfzxxbByGroupCode(groupId);
    }

    @Override
    public Map<String, Object> getBrandAndTableSuffixByGroupId(String groupId) {
        return receiveGroupDataMapper.getBrandAndTableSuffixByGroupId(groupId);
    }

    @Override
    public void updateBrandByMainVersionCode(String brandId,String brandCode,String groupCode,String mainVersionCode,String tableSuffix) {
        Map<String,Object> map = new HashMap<>();
        map.put("brandId",brandId);
        map.put("brandCode",brandCode);
        map.put("groupCode",groupCode);
        map.put("mainVersionCode",mainVersionCode);
        map.put("tableSuffix",tableSuffix);
        receiveGroupDataMapper.updateBrandByMainVersionCode(map);
    }

    @Override
    public int selUserTableNumByBrandCode(String brandCode) {
        return receiveGroupDataMapper.selUserTableNumByBrandCode(brandCode);
    }

    @Override
    public String createFCTable(String brandCode) {
        Map<String,Object> map = new HashMap<>();
        map.put("brandCode",brandCode);
        map.put("errMsg","");
        receiveGroupDataMapper.createFCTable(map);
        return map.get("errMsg") == null ? "":map.get("errMsg").toString();
    }

    @Override
    public Map<String,Object> createFTable(String brandCode) {
        Map<String,Object> map = new HashMap<>();
        map.put("brandCode",brandCode);
        map.put("errMsg","");
        map.put("createNum","0");
        receiveGroupDataMapper.createFTable(map);
        return map;
    }

    @Override
    public Map<String,Object> createCTable(String brandCode) {
        Map<String,Object> map = new HashMap<>();
        map.put("brandCode",brandCode);
        map.put("errMsg","");
        map.put("createNum","0");
        receiveGroupDataMapper.createCTable(map);
        return map;
    }

    @Override
    public void createTYC(List<String> list) {
        receiveGroupDataMapper.createTYC(list);
    }

    @Override
    public int updateTableNameByMainVersionCode(Map<String, Object> map) {
        return receiveGroupDataMapper.updateTableNameByMainVersionCode(map);
    }

    @Override
    public String pUpdateData(String mainVersionCode,String versionCode,String certain) {
        Map<String,Object> map = new HashMap<>();
        map.put("mainVersionCode",mainVersionCode);
        map.put("versionCode",versionCode);
        map.put("certain",certain);
        map.put("state","");
        receiveGroupDataMapper.pUpdateData(map);
        return map.get("state").toString();
    }

    @Override
    public PageCustom<List<Map<String,Object>>> selDataByTableName(String tableName, String mainVersionCode, String versionCode, int page) {
        Map<String,Object> map = new HashMap<>(3);
        PageCustom<List<Map<String,Object>>> listPageCustom = new PageCustom<>();
        PageCustom pages = new PageCustom();
        pages.setPage(page);
        pages.setLimit(20);
        map.put("tableName",tableName);
        map.put("mainVersionCode",mainVersionCode);
        map.put("versionCode",versionCode);
        map.putAll(pages.resuest());
        listPageCustom.setData(receiveGroupDataMapper.selDataByTableName(map));
        listPageCustom.setTotal(receiveGroupDataMapper.selDataByTableNameCount(map));
        return listPageCustom;
    }

    @Override
    public Map<String, Object> getWLTableByBrandCode(String brandCode) {
        return receiveGroupDataMapper.getWLTableByBrandCode(brandCode);
    }

    @Override
    public boolean judgeWL(String brandCode,Map<String,Object> wlTables,String mainVersionCode) {
        boolean flag = true;
        //如果当前的品牌是蔚来的
        String tableNameSuffix = wlTables.get("tableNameSuffix") == null ? "" : wlTables.get("tableNameSuffix").toString();
        String tableName = wlTables.get("tableName") == null ? "" : wlTables.get("tableName").toString();
        String tableNames = tableNameSuffix + "," + tableName;
        String[] tables = tableNames.split(",");
        List<Map<String,Object>> list  = new ArrayList<>();
        int num = 0;
        Map<String,Object> parameter = null;
        String table = "";
        Map<String,Object> map;
        for (String str : tables){
            if(!StringUtils.isEmpty(str)){
                if(str.indexOf("pj_cllbjdyb") > -1 || str.indexOf("pj_clljtxdyb") > -1 || str.indexOf("pj_czlbjdyb") > -1){
                    table = str + "_" + brandCode;
                }
                //判断表是否存在
                num = receiveGroupDataMapper.getWLTableCount(table);
                if(num <= 0){
                    flag = false;
                    parameter = new HashMap<>(3);
                    parameter.put("brandCode",brandCode);
                    parameter.put("tableName",str);
                    parameter.put("tableName1",table);
                    parameter.put("errMsg","");
                    receiveGroupDataMapper.createWLPTable(parameter);
                    if(StringUtils.isEmptyObj(parameter.get("errMsg"))){
                        receiveGroupDataMapper.createWLFTable(parameter);
                        if(StringUtils.isEmptyObj(parameter.get("errMsg"))){
                            receiveGroupDataMapper.createWLCTable(parameter);
                            if(StringUtils.isEmptyObj(parameter.get("errMsg"))){
                                map = new HashMap<>();
                                map.put("pTable",str);
                                map.put("fTable",table);
                                list.add(map);
                                flag = true;
                            }
                        }
                    }
                    if(!flag){
                        map = new HashMap<>(2);
                        map.put("mainVersionCode",mainVersionCode);
                        updateGroupStateByMainVersionCode("700",mainVersionCode,parameter.get("errMsg").toString());
                        break;
                    }
                    log.info("---------新建蔚来数据表返回信息:"+parameter);
                }
            }
        }
        if(flag){
            if(list.size() > 0){
                receiveGroupDataMapper.createWLTYC(list);
            }
        }
        return flag;
    }

    @Override
    public PageCustom<List<ReceiveGroupDataVo>> getReceiveListByPage(String mainVersionCode, String versionCode, int page, String state, String groupCode) {
        PageCustom<List<ReceiveGroupDataVo>> listPageCustom = new PageCustom<>();
        PageCustom pages = new PageCustom();
        pages.setPage(page);
        pages.setLimit(20);
        Map<String,Object> map = new HashMap<>();
        map.put("mainVersionCode",mainVersionCode);
        map.put("versionCode",versionCode);
        map.put("state",state);
        map.put("groupCode",groupCode);
        map.putAll(pages.resuest());
        listPageCustom.setData(receiveGroupDataMapper.getReceiveListByPage(map));
        listPageCustom.setTotal(receiveGroupDataMapper.getListCount(map));
        return listPageCustom;
    }

    @Override
    public Map<String,Object> judgeExistsGroupCodeByDate(String mainVersionCode, String receiveDate,String groupCode) {
        Map<String,Object> parameter = new HashMap<>(2);
        if(Dictionary.IS_CONTINUE){
            parameter.put("groupCode",groupCode);
            parameter.put("date",receiveDate);
            //查询当前车组是否重发过,如果没有,则重新获取/转换/对比/发送当前数据
            int num = receiveGroupDataMapper.judgeExistsGroupCodeByDate(parameter);
            parameter = new HashMap<>(2);
            if(num <= 0){
                //将获取数据状态改为初始状态
                receiveGroupDataMapper.updateGainTableByMainVersionCode(mainVersionCode);
                template.convertAndSend(RabbitConfig.MQ_NAME,mainVersionCode);
            }else{
                parameter.put("msg","当前车组已经重发过,不能重新操作");
                parameter.put("code","1001");
            }
        }else{
            parameter.put("msg","由于数据发送失败,mq处于停止状态");
            parameter.put("code","1002");
        }
        return parameter;
    }

    @Override
    public List<Map<String, Object>> getSendTableByWlTable(String mainVersionCode) {
        Map<String,Object> para = new HashMap<>(1);
        para.put("mainVersionCode",mainVersionCode);
        return receiveGroupDataMapper.getSendTableByWlTable(para);
    }

    @Override
    public List<String> getSendTableHXErrDataByMainVersionCode(String mainVersionCode) {
        return receiveGroupDataMapper.getSendTableHXErrDataByMainVersionCode(mainVersionCode);
    }

    @Override
    public void saveSendTable(List<Map<String, Object>> list) {
        receiveGroupDataMapper.saveSendTable(list);
    }

    @Override
    public List<Map<String, Object>> getStatisticsData(String date,String isStatistics,String groupId) {
        Map<String,Object> map = new HashMap<>(1);
        map.put("date",date);
        map.put("isStatistics",isStatistics);
        map.put("groupId",groupId);
        return receiveGroupDataMapper.getStatisticsData(map);
    }

    @Override
    public String vehTypeData(String groupId,String seriesId,String brandId,String mainVersionCode) throws Exception{

        //获取承保车型id
        List<Map<String,Object>> subList = receiveGroupDataMapper.getSubVehicleIdByGroupId(groupId);
        if(subList != null && !subList.isEmpty()){

            //从整车平台获取承保车型分类信息
            saveSubVehicleType(subList,mainVersionCode,groupId,"");

            if(!StringUtils.isEmpty(groupId)){
                //查询当前车组的vehicleTypeName和vehicleTypeCode
                Map<String,Object> groupMap = receiveGroupDataMapper.getVehicleTypeData(groupId);
                log.info("车组的原始数据:"+groupMap);
                //修改车组标识表vehicleTypeName和vehicleTypeCode
                if(groupMap != null && groupMap.size() > 0){
                    receiveGroupDataMapper.updateClfFlagVehicleType(removeRepeat(groupId,groupMap,"group"));
                }

                groupMap = receiveGroupDataMapper.getCxxVehicleTypeData(seriesId);
                log.info("车车系的原始数据:"+groupMap);
                //获取原来的车系vehicleTypeName和vehicleTypeCode
                if(groupMap != null && groupMap.size() > 0){
                    //修改车系标识表
                    receiveGroupDataMapper.updateCxxFlagVehicleType(removeRepeat(seriesId,groupMap,"ser"));
                }

                //获取品牌原来的vehicleTypeName和vehicleTypeCode
                groupMap = receiveGroupDataMapper.getClpFlagVehicleTypeData(brandId);
                log.info("品牌的原始数据:"+groupMap);
                if(groupMap != null && groupMap.size() > 0){
                    //修改品牌标识表
                    receiveGroupDataMapper.updateClpFlagVehicleType(removeRepeat(brandId,groupMap,"brand"));
                }
            }
        }
        return null;
    }

    /**
     * 返回最新的类型名称和编码
     * @param id
     * @param vehMap
     * @param type 区分类型,方便打印日志,定位问题
     * @return
     */
    public Map<String,Object> removeRepeat(String id,Map<String,Object> vehMap,String type){

        String newVehicleTypeName = (vehMap == null || vehMap.get("vehicleTypeName") == null) ? "" : vehMap.get("vehicleTypeName").toString();
        String newVehicleTypeCode = (vehMap == null || vehMap.get("vehicleTypeCode") == null) ? "" : vehMap.get("vehicleTypeCode").toString();

        Map<String,Object> map = new HashMap<>(3);
        List<String> nameList = new ArrayList<>(Arrays.asList(newVehicleTypeName.split(",")));
        List<String> codeList = new ArrayList<>(Arrays.asList(newVehicleTypeCode.split(",")));

        map.put("vehicleTypeName",String.join(",", removeRepeatData(nameList)));
        map.put("vehicleTypeCode",String.join(",", removeRepeatData(codeList)));
        map.put("id",id);
        log.info("初始值:"+vehMap+",处理类型:"+type+",返回数据:"+map);
        return map;
    }

    /**
     * 删除list重复数据
     * @return
     */
    public List<String> removeRepeatData(List<String> list){

        Set set = new HashSet(list);
        list.clear();
        list.addAll(set);
        return list;
    }

    @Override
    public void testTransUpdate() {
        receiveGroupDataMapper.testTransUpdate();
    }

    @Override
    public void updateReStatistics(List<Map<String,Object>> list) {
        receiveGroupDataMapper.updateReStatistics(list);
    }

    @Override
    public void updateWLCllbjdyb() {
        receiveGroupDataMapper.updateWLCllbjdyb();
    }

    @Override
    public boolean judgeVehicleType() {
        List<Map<String,Object>> list = receiveGroupDataMapper.judgeVehicleType();
        if(list != null){
            for (int i = 0,len = list.size();i < len;i++){
                if(list.get(i).get("subVehicleId").toString().indexOf(",") > -1){
                    return false;
                }
            }
        }
        return true;
    }

    @Async("taskExecutor")
    @Override
    public void test(int i){
        try {
            if(i*2 == 0){
                Thread.sleep(3000);
            }
            log.info(i+"-------------------"+Thread.currentThread().getName());
        }catch (Exception e){}

    }

    @Override
    public ReceiveGroupDataDto getReceiveGroupDataModel(String mainVersionCode) {
        return receiveGroupDataMapper.getReceiveGroupDataModel(mainVersionCode);
    }

    @Override
    public void truckVehicleType(String mainVersionCode) throws Exception{
        saveSubVehicleType(receiveGroupDataMapper.getTruckVehicleId(),mainVersionCode,"","8");
    }

    /**
     * 保存从整车获取的车型类型和等级信息
     * 并修改车型表和车型标识表的相关信息
     */
    public void saveSubVehicleType(List<Map<String,Object>> list,String mainVersionCode,String groupId,String dataType) throws Exception{
        Map<String,List<Map<String,Object>>> cxVehicle = list.stream().collect(Collectors.groupingBy((e -> e.get("cxid").toString())));
        Map<String,Object> parameter = new HashMap<>(2);
        JSONArray saveArray = new JSONArray();
        cxVehicle.forEach((cxid,vehicleIdList) -> {
            if(cxid.contains("AUTO_TEST")){
                List<SubVehicleTypeDto> subList = new ArrayList<>();
                for(Map<String,Object> map : vehicleIdList){
                    SubVehicleTypeDto subVehicleTypeDto = new SubVehicleTypeDto();
                    subVehicleTypeDto.setGradeCode("level_test");
                    subVehicleTypeDto.setGradeId("AUTO_TEST_123456");
                    subVehicleTypeDto.setGradeName("level_test");
                    subVehicleTypeDto.setSubVehicleId(map.get("subVehicleId").toString());
                    subVehicleTypeDto.setVehCateCodes("vehCateCode");
                    subVehicleTypeDto.setVehCateNames("vehCateName");
                    subVehicleTypeDto.setVehCateOneCodes("oneCode");
                    subVehicleTypeDto.setVehCateOneNames("oneName");
                    subVehicleTypeDto.setVehCateTwoCodes("twoCode");
                    subVehicleTypeDto.setVehicleTypeCode("test");
                    subVehicleTypeDto.setVehicleTypeName("testName");
                    subList.add(subVehicleTypeDto);
                }
                //保存信息
                saveVehicleType(subList,mainVersionCode);
            } else {
                //1000条请求一次,避免sql语句超长
                int times = (int) Math.ceil(vehicleIdList.size() / 1000.0);
                for (int i = 0; i < times; i++) {
                    String data = HttpClientUtil.doPost(vehicleTypeUrl,JSONArray.toJSONString(vehicleIdList.subList(i * 1000, Math.min((i + 1) * 1000, vehicleIdList.size()))));
                    JSONObject jsonObject = JSONObject.parseObject(data);
                    if("000000".equals(jsonObject.getString("code"))) {
                        saveArray.addAll(jsonObject.getJSONArray("data"));
                        if(saveArray.size() > 4500){
                            List<SubVehicleTypeDto> subList = JSONArray.parseArray(saveArray.toJSONString(), SubVehicleTypeDto.class);
                            //保存信息
                            saveVehicleType(subList,mainVersionCode);
                            saveArray.clear();
                        }
                    }else{
                        log.error("整车返回:"+jsonObject);
                        throw new CompareException("从整车获取数据失败");
                    }
                }
            }
        });
        if(saveArray.size() > 0){
            List<SubVehicleTypeDto> subList = JSONArray.parseArray(saveArray.toJSONString(), SubVehicleTypeDto.class);
            //保存信息
            saveVehicleType(subList,mainVersionCode);
        }
        parameter.put("mainVersionCode",mainVersionCode);
        parameter.put("groupId",groupId);
        if("8".equals(dataType)){
            //精细化商用车
            receiveGroupDataMapper.updateTruckClzVehicleType(parameter);
        }else{
            //修改车型表车型类型信息(由于单表发送时数据量比较大,为了提高效率,使用临时表)
            receiveGroupDataMapper.deleteZhPartTemp(parameter);
            receiveGroupDataMapper.insertZhPartTemp(parameter);
            receiveGroupDataMapper.updateClzVehicleType(parameter);
        }
        //修改车型标识表车型类型信息
        receiveGroupDataMapper.updateClzFlagVehicleType(parameter);
        //修改车型表车型id
        receiveGroupDataMapper.updateClzVehicleTypeId(parameter);
    }

    @Override
    public void truckGroupSuffix() throws Exception{

        List<Map<String,Object>> truckGroupNullSuffix = receiveGroupDataMapper.commercialVehicleList();
        String suffix;
        Map<String,Object> parameter;
        for(Map<String,Object> map : truckGroupNullSuffix) {
            int partNum = (StringUtils.isEmptyObj(map.get("partNum"))) ? 0 : Integer.parseInt(map.get("partNum").toString());

            //获取表后缀
            suffix = getMinPartTableSuffix(partNum);
            //修改表后缀
            parameter = new HashMap<>(3);
            parameter.put("groupId",map.get("id").toString());
            parameter.put("partsSuffix",Integer.parseInt(suffix)/100);
            parameter.put("hoursSuffix",Integer.parseInt(suffix)/10);
            receiveGroupDataMapper.updateSuffix(parameter);
        }
    }

    @Override
    public boolean getOneGroupDelCount(String groupId) {
        if(receiveGroupDataMapper.getOneGroupDelCount(groupId) > 0){
            return true;
        }
        return false;
    }


    @Override
    public List<ReceiveGroupDataDto> listByCompareBatchNo(String compareBatchNo) {
        Map<String, Object> map = new HashMap<>();
        map.put("compareBatchNo", compareBatchNo);
        return receiveGroupDataMapper.listReceiveGroupDataDto(map);
    }

    @Override
    public void saveBrandRelation(ReceiveGroupDataDto dto) throws Exception {

        Map<String,Object> para = new HashMap<>(5);
        para.put("brandId",dto.getBrandId());
        para.put("brandCode",dto.getBrandCode());
        para.put("brandName",dto.getBrandName());
        para.put("tableName","pj_cllbjdyb_"+dto.getTableSuffix());
        para.put("groupTableName","pj_czlbjdyb_"+dto.getTableSuffix());
        receiveGroupDataMapper.saveBrandRelation(para);
    }

    public void saveVehicleType(List<SubVehicleTypeDto> list,String mainVersionCode){

        Long startTime = System.currentTimeMillis();
        log.info("开始时间------------"+startTime);
        String sql = "insert into SUB_VEHICLE_TYPE(SUB_VEHICLE_ID,VEHICLE_TYPE_NAME,VEHICLE_TYPE_CODE,VEH_CATE_NAMES,VEH_CATE_CODES,VEH_CATE_ONE_NAMES,VEH_CATE_ONE_CODES,VEH_CATE_TWO_CODES,ID,MAIN_VERSION_CODE,ADD_TIME,GRADE_ID,GRADE_NAME,GRADE_CODE)" +
                "values (?, ?, ?, ?, ?, ?, ?,?,sys_guid(),?,sysdate, ?, ?, ?)";

        int[] updatedCountArray = jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
            SubVehicleTypeDto sub;
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                sub = list.get(i);
                ps.setObject(1,sub.getSubVehicleId());
                ps.setObject(2,sub.getVehicleTypeName());
                ps.setObject(3,sub.getVehicleTypeCode());
                ps.setObject(4,sub.getVehCateNames());
                ps.setObject(5,sub.getVehCateCodes());
                ps.setObject(6,sub.getVehCateOneNames());
                ps.setObject(7,sub.getVehCateOneCodes());
                ps.setObject(8,sub.getVehCateTwoCodes());
                ps.setObject(9,mainVersionCode);
                ps.setObject(10,sub.getGradeId());
                ps.setObject(11,sub.getGradeName());
                ps.setObject(12,sub.getGradeCode());
            }

            @Override
            public int getBatchSize() {
                return list.size();
            }
        });
        log.info("结束时间------------"+(System.currentTimeMillis()-startTime));
    }
}

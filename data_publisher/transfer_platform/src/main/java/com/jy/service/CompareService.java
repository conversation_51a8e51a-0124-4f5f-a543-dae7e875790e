package com.jy.service;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/8/20
 */
public interface CompareService {

    Integer resendInsert(String mainVersionCode, String versionCode, String state, String tableName, String cKey, String fKey, BigInteger maxVersionId, BigInteger minVersionId);

    List<Map<String, Object>> query(String tableName, BigInteger maxVersionId, BigInteger minVersionId);

    Integer getCountByTableName(String tableName, String layerKey, String layerValue);

    Integer getCountByTableNameAndVersionId(String tableName, String layerKey, String layerValue, BigInteger maxVersionId, BigInteger minVersionId);

    List<String> listTableName(String tableName);

    List<String> listCompareLayer(String tableName, String compareLayerKey);

 //   List<CompareDataLayer> listPageRangeByTableName(String tableName, Integer pageNumber, Integer layerSize, Integer batchSize, Integer total);

    //List<CompareDataLayer> listPageRangeByTableName(String tableName, Integer pageNumber, Integer layerSize, Integer batchSize, Integer total);

    BigInteger listPageRangeByTableName(String tableName, String layerKey, String layerValue, BigInteger minVersionId, Integer count);

    // List<CompareDataLayer> listPageRangeByTableName(String tableName, long minVersionId, Integer layerSize, Integer batchSize);
}

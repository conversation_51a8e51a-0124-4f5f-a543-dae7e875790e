package com.jy.service.impl;

import com.jy.bean.dto.PageCustom;
import com.jy.bean.dto.UserDTO;
import com.jy.bean.po.SysPowerPo;
import com.jy.bean.po.UserPo;
import com.jy.mapper.PowerMapper;
import com.jy.mapper.UserMapper;
import com.jy.service.PowerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class PowerServiceImpl implements PowerService {

    @Autowired
    private PowerMapper powerMapper;

    @Autowired
    private UserMapper userMapper;

    /**
     * 删除菜单(多删)
     * @param ids
     * @return
     */
    @Override
    public boolean delPowers(String[] ids, String roleCode) {
        return powerMapper.delPowers(ids,roleCode);
    }

    /**
     * 为角色添加权限
     * @param userPo
     * @return
     */
    public boolean addRolePower(UserPo userPo){
        return powerMapper.addRolePower(userPo);
    }

    /**
     * 根据用户id得到用户对应的菜单
     * @param userId
     * @return
     */
    public List<SysPowerPo> showMenu(String userId){
        List<SysPowerPo> list = userMapper.selectPowerByUserId(userId);
        return list;
    }

    /**
     * 根据角色编码查找权限菜单
     * @param roleCode
     * @return
     */
    public List<SysPowerPo> getHavePowerList(String roleCode){
        return powerMapper.getHavePowerList(roleCode);
    }

    /**
     * 查询未拥有的权限菜单
     * @param roleCode
     * @return
     */
    public List<SysPowerPo> getNonePowerList(String roleCode){
        return powerMapper.getNonePowerList(roleCode);
    }

    /**
     * 添加菜单
     * @param power
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addMenuPower(SysPowerPo power) {
        Long num = powerMapper.addMenu(power);
        if(num > 0 ){
            return "添加成功！";
        }
        return "添加失败！";
    }

    @Override
    public PageCustom<List<SysPowerPo>> findMenuByList(UserDTO dto, PageCustom<List<UserDTO>> pages) {
        Map map = pages.resuest();
        map.put("dto",dto);
        PageCustom<List<SysPowerPo>> pageCustom = new PageCustom<List<SysPowerPo>>();
        Long count = powerMapper.findMenuCount(map);
        if(count > 0){
            List<SysPowerPo> list = powerMapper.findMenuByList(map);
            pageCustom.setData(list);
        }
        pageCustom.setTotal(count);

        return pageCustom;
    }

    @Override
    public String delMenuPower(SysPowerPo power) {
        Long num = powerMapper.delMenu(power.getId());
        if (num > 0 ) {
            return "删除成功！";
        }
        return "删除失败";
    }

    @Override
    public List<SysPowerPo> findMenuList(List list) {
        List<SysPowerPo>  munuList = new ArrayList<SysPowerPo>();
        if(list != null && !list.isEmpty()){
            for(Object id : list){
                munuList = powerMapper.findMenuList(id.toString());
            }
        }else{
            munuList = powerMapper.findMenuList(null);
        }
        return munuList;
    }

    @Override
    public String updateMenuPower(SysPowerPo power) {

        Long num = powerMapper.updateMenu(power);
        if(num > 0 ){
            return "修改成功！";
        }
        return "修改失败！";
    }
}

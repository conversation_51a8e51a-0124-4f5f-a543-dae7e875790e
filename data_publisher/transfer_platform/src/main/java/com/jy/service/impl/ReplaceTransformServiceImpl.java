package com.jy.service.impl;

import com.jy.bean.po.RepOutRelation;
import com.jy.mapper.ReplaceTransformMapper;
import com.jy.service.ReplaceTransformService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: caolt
 * @Description:
 * @Version:
 * @Date: Created in  2020/12/14
 */
@Service
public class ReplaceTransformServiceImpl implements ReplaceTransformService {

    @Autowired
    private ReplaceTransformMapper replaceTransformMapper;

    @Override
    public String getMaxRelGroupId(String brandId) {
        Map<String, Object> map = new HashMap<>();
        map.put("brandId", brandId);
        return replaceTransformMapper.getMaxRelGroupId(map);
    }

    @Override
    public List<RepOutRelation> getTableDataByNoRelGroupId(String brandId) {
        Map<String, Object> map = new HashMap<>();
        map.put("brandId", brandId);
        return replaceTransformMapper.ListByNoRelGroupId(map);
    }

    @Override
    public void updateByRelReplace() {
        replaceTransformMapper.updateByRelReplace();
    }
}

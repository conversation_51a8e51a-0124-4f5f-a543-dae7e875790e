package com.jy.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jy.ann.MethodMonitor;
import com.jy.bean.common.BatchNoStatus;
import com.jy.bean.common.Constant;
import com.jy.bean.common.DataTraceMenu;
import com.jy.bean.dto.BaseDataDTO;
import com.jy.bean.dto.BaseDataDTOs;
import com.jy.bean.po.ReceiveBatch;
import com.jy.bean.po.SendDetail;
import com.jy.bean.result.ResultStatus;
import com.jy.service.CommonService;
import com.jy.service.SendDataService;
import com.jy.service.SendDetailService;
import com.jy.util.DataPublishUtils;
import com.jy.util.DateUtil;
import com.jy.util.EmptyUtils;
import com.jy.util.ToolUtils;
import com.jy.util.rabbitmq.DataTraceUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

/**
 * @Author: caolt
 * @Description:
 * @Version:
 * @Date: Created in  2021/08/31
 */
@Service
public class SendDataServiceImpl implements SendDataService {
    private static final Logger logger = LogManager.getLogger(SendDataServiceImpl.class);

    @Value("${transform.batchLimit}")
    private int batchToplimit = 500;
    @Value("${transform.pushLimit}")
    private int pushToplimit = 10000;
    @Value("${httpUtils.dataPublish.sendDataPath}")
    private String sendDataPath;

    @Autowired
    private CommonService commonService;
    @Autowired
    private SendDetailService sendDetailService;
    @Autowired
    private DataPublishUtils dataPublishUtils;

    @Override
    @Async("asyncTransferExecutor")
    @MethodMonitor
    public void sendData(ReceiveBatch receiveBatch, SendDetail sendDetail, String clientCode, CountDownLatch countDownLatch) {
        try {
            String status = "";
            String message = "";
            Timestamp time = DateUtil.crunttime();
            try {
                //数据查询
                int pageIndex = Integer.parseInt(sendDetail.getBatchNo().substring(sendDetail.getBatchNo().lastIndexOf("-") + 1));
                int endNum = pageIndex * pushToplimit;
                int startNum = endNum - pushToplimit;
                List<Map<String,Object>> list = commonService.getTableDataByTableNameAndBatchNo(sendDetail.getCTableName(), sendDetail.getMainBatchNo(), startNum, endNum);
                //数据组装
                BaseDataDTOs baseDataDTOs = new BaseDataDTOs(sendDetail.getMainBatchNo(), sendDetail.getBatchNo(), sendDetail.getTableName(), list.size());
                baseDataDTOs.setData(fitBaseDataDTO(sendDetail.getTableName(), sendDetail.getCTableName(), list));
                if(EmptyUtils.isNotEmpty(clientCode)){
                    baseDataDTOs.setClientCode(clientCode);
                }
                //数据推送
                String data = JSON.toJSONStringWithDateFormat(baseDataDTOs,"yyyy-MM-dd HH:mm:ss", SerializerFeature.DisableCircularReferenceDetect,SerializerFeature.WriteMapNullValue);
                //发送到及时更新
                Map<String, String> querys = new HashMap<>();
                querys.put("receive", "receive");
                JSONObject json = dataPublishUtils.doPost(sendDataPath, querys, data);
                status = !ResultStatus.SUCCESS.getStatus().equals(json.getString("status")) ? ResultStatus.INTERNAL_SERVER_ERROR.getStatus() : json.getString("status");
                message = json.getString("status") + ":" + json.getString("message");
                message = message.length() > 100 ? message.substring(0,100) : message;
            } catch (Exception e) {
                status = ResultStatus.INTERNAL_SERVER_ERROR.getStatus();
                message = e.getMessage().length() > 100 ? e.getMessage().substring(0,100) : e.getMessage();
                logger.error("数据推送失败:batchNo：{}, message: {}", sendDetail.getBatchNo(), ToolUtils.getExceptionMsg(e));
            }
            sendDetail.setResult(status, message);
            sendDetailService.update(sendDetail);
            DataTraceUtils.sendTrace((JSONObject)JSON.toJSON(sendDetail), DataTraceMenu.SRC_PUSH_DESC.getName(), status, BatchNoStatus.messageOf(status), time);

        } catch (Exception e) {
            logger.error("数据推送失败:batchNo：{}, message: {}", sendDetail.getBatchNo(), ToolUtils.getExceptionMsg(e));
            DataTraceUtils.sendTrace((JSONObject)JSON.toJSON(sendDetail), DataTraceMenu.SRC_PUSH_DESC.getName(), ResultStatus.INTERNAL_SERVER_ERROR.getStatus(), "数据推送失败"+ToolUtils.getExceptionMsg(e));
        }finally {
            countDownLatch.countDown();
        }

    }

    @Override
    @Async("asyncTransferExecutor")
    @MethodMonitor
    public void sendTransData(SendDetail sendDetail, String clientCode) {
        try {
            String status = "";
            String message = "";
            try {
                //数据组装
                //BaseDataDTOs baseDataDTOs = new BaseDataDTOs(sendDetail.getMainBatchNo(), sendDetail.getBatchNo(), sendDetail.getTableName(), 0);
                //if (EmptyUtils.isNotEmpty(clientCode)) {
                //    baseDataDTOs.setClientCode(clientCode);
                //}
                //baseDataDTOs.setStatus(sendDetail.getStatus());
                //数据推送
                sendDetail.setClientCode(clientCode);
                String data = JSON.toJSONStringWithDateFormat(sendDetail, "yyyy-MM-dd HH:mm:ss", SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.WriteMapNullValue);
                //发送到及时更新
                Map<String, String> querys = new HashMap<>();
                querys.put("receiveTransData", "receiveTransData");
                JSONObject json = dataPublishUtils.doPost(sendDataPath, querys, data);
                status = json.getString("status");
                message = json.getString("status") + ":" + json.getString("message");
                if(!ResultStatus.SUCCESS.getStatus().equals(status)){
                    logger.info("向发布平台推送轨迹信息失败：{}",message);
                }
            } catch (Exception e) {
                logger.error("轨迹记录数据推送失败:batchNo：{}, message: {}", sendDetail.getBatchNo(), ToolUtils.getExceptionMsg(e));
            }
        } catch (Exception e) {
            logger.error("轨迹记录数据推送失败:batchNo：{}, message: {}", sendDetail.getBatchNo(), ToolUtils.getExceptionMsg(e));
        }

    }

    @Override
    @Async("asyncTransferExecutor")
    @MethodMonitor
    public void sendExpData(ReceiveBatch receiveBatch, String clientCode, String exceptionMsg) {
        String batchNo = receiveBatch.getMainBatchNo() + "-exp";
        try {
            String status = "";
            Timestamp time = DateUtil.crunttime();
            try {
                //数据组装
                BaseDataDTOs baseDataDTOs = new BaseDataDTOs(receiveBatch.getMainBatchNo(), batchNo, "", 0);
                if (EmptyUtils.isNotEmpty(clientCode)) {
                    baseDataDTOs.setClientCode(clientCode);
                }
                //数据推送
                String data = JSON.toJSONStringWithDateFormat(baseDataDTOs, "yyyy-MM-dd HH:mm:ss", SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.WriteMapNullValue);
                //发送到及时更新
                Map<String, String> querys = new HashMap<>();
                querys.put("receiveTransData", "receiveTransData");
                JSONObject json = dataPublishUtils.doPost(sendDataPath, querys, data);
            } catch (Exception e) {
                logger.error("异常轨迹记录数据推送失败:batchNo：{}, message: {}", "", ToolUtils.getExceptionMsg(e));
            }
            JSONObject receiveBatchJO = (JSONObject) JSON.toJSON(receiveBatch);
            receiveBatchJO.put("batchNo", batchNo);
            DataTraceUtils.sendTrace(receiveBatchJO, DataTraceMenu.SRC_NO_PUSH_DESC.getName(), ResultStatus.INTERNAL_SERVER_ERROR.getStatus(), exceptionMsg, time);

        } catch (Exception e) {
            logger.error("异常轨迹记录数据推送失败:batchNo：{}, message: {}", batchNo, ToolUtils.getExceptionMsg(e));
        }

    }


    private List<BaseDataDTO> fitBaseDataDTO(String tableName, String cTableName, List<Map<String,Object>> list){
        List<BaseDataDTO> baseDataDTOs = new ArrayList<>();
        for(Map map : list){
            BaseDataDTO baseDataDTO = new BaseDataDTO();
            String operate = map.get("state").toString();
            if("item_detail".equals(tableName)){
                String stdItemType = EmptyUtils.isNotEmpty(map.get("stdItemTypeNew")) ? map.get("stdItemTypeNew").toString() : map.get("stdItemTypeOld").toString();
                if("part".equals(stdItemType)){
                    map.remove("stdItemTypeNew");
                    map.remove("stdItemTypeOld");
                    operate = "part".equals(stdItemType) ? "update" : map.get("state").toString();
                }
            }

            if("zc_clzlb".equals(tableName)){
                String ppbm = EmptyUtils.isNotEmpty(map.get("ppbmNew")) ? map.get("ppbmNew").toString() : map.get("ppbmOld").toString();
                map.remove("ppbmNew");
                map.remove("ppbmOld");
                if(!map.containsKey("ppbm") || EmptyUtils.isEmpty(map.get("ppbm"))){
                    map.put("ppbm", ppbm);
                }
            }

            baseDataDTO.setOperate(operate);
            Map<String, String> keys = new HashMap<>();
            keys.put("id", map.get("id").toString());
            baseDataDTO.setKeys(keys);
            Map<String, String> suffix = new HashMap<>();

            String tableSuffix =  cTableName.replace(Constant.C_DB_SUFFIX + tableName, "");
            suffix.put("tableSuffix", tableSuffix.replace("_", ""));
            baseDataDTO.setSuffix(suffix);
            baseDataDTO.setFields(fitFields(baseDataDTO.getOperate(), map));
            baseDataDTOs.add(baseDataDTO);
        }
        return baseDataDTOs;
    }



    private Map<String, Object> fitFields(String operate, Map<String,Object> map){
        map.remove("id");
        Map<String, Object> fields = new HashMap<>();
        if("delete".equals(operate)){
            map.forEach((k,v)->{
                if(k.lastIndexOf("New")>0){
                    String fileName = k.replace("New", "Old");
                    Object value = EmptyUtils.isEmpty(v) ? "" : v;
                    Object oldValue = EmptyUtils.isEmpty(map.get(fileName)) ? "" :map.get(fileName);
                    if(!value.equals(oldValue)){
                        fields.put(k.replace("New", ""), oldValue);
                    }
                }
                //主轴字段
                if(k.indexOf("New")== -1 && k.indexOf("Old") == -1){
                    fields.put(k, EmptyUtils.isEmpty(v) ? "" : v);
                }
            });
            return fields;
        }
        if("insert".equals(operate)){
            map.forEach((k,v)->{
                if(k.lastIndexOf("New")>0 || k.indexOf("Old") == -1){
                    fields.put(k.replace("New", ""), v);
                }
            });
            fields.put("scbz", "0");
            fields.put("jlrq", map.get("createdTime"));
            fields.put("xgrq", map.get("createdTime"));
        }
        if("update".equals(operate)){
            map.forEach((k,v)->{
                if(k.lastIndexOf("New")>0){
                    String fileName = k.replace("New", "Old");
                    Object value = EmptyUtils.isEmpty(v) ? "" : v;
                    Object oldValue = EmptyUtils.isEmpty(map.get(fileName)) ? "" :map.get(fileName);
                    if(!value.equals(oldValue)){
                        fields.put(k.replace("New", ""), value);
                    }
                }
                //主轴字段
                if(k.indexOf("New")== -1 && k.indexOf("Old") == -1){
                    fields.put(k, EmptyUtils.isEmpty(v) ? "" : v);
                }
            });
            fields.put("xgrq", map.get("createdTime"));
        }
        return fields;
    }

}

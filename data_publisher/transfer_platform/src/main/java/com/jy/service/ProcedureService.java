package com.jy.service;

import java.util.Map;

/**
 * 存储过程
 * <AUTHOR>
 * @date 2020/5/20
 */
public interface ProcedureService {

    Map<String,Object> tAnalyzeData();

    Map<String,Object> partProcedure(String mainBatchNo, String groupId);

    Map<String,Object> wlPartProcedure(String mainBatchNo, String groupId);

    Map<String,Object> compareFlagProcedure(String mainBatchNo);

    Map<String,Object> compareProcedure(String mainBatchNo, String tableName, String suffixFlag, String endFlag);

    Map<String,Object> compareSyncProcedure(String mainBatchNo, String tableName, String ppbm, String certainId);

    Map<String,Object> updateProcedure(String mainBatchNo, String tableName, String suffixFlag, String endFlag);

    Map<String,Object> createFCTable(String groupId);

    Map<String,Object> replaceProcedure(String mainBatchNo);

    Map<String,Object> stdPartProcedure(String mainBatchNo);

    Map<String,Object> truncateMData(String tableName);

    Map<String,Object> truncatePData(String tableName);
}

package com.jy.service;

import com.jy.bean.dto.PageCustom;
import com.jy.bean.dto.UserDTO;
import com.jy.bean.po.UserPo;

import java.util.List;

public interface UserService {

	/**
	 * 忘记密码/重置密码/修改密码
	 * @param user
	 * @return
	 */
	String updatePwd(UserPo user, String oldPass, String newPass);

	/**
	 * 查看用户名是否存在
	 * @param userName
	 * @return
	 */
	int selectUserPoByName(String userName);

	/**
	 * 根据用户名密码验证用户
	 */
	UserPo getUserPoByUp(String userName, String passWord);


	/**
	 * 查询上级领导
	 * @param upLeaderId
	 * @return
	 */
	UserPo getUserPoById(String upLeaderId);

	UserDTO getUserDtoById(String id);

	/**
	 * 用户列表
	 * @param dto
	 * @param pageCustom
	 * @return
	 */
	PageCustom<List<UserDTO>> getUserList(UserDTO dto, PageCustom pageCustom);

	/**
	 * 新增
	 * @param user
	 */
	int insertUserPo(UserPo user);


	/**
	 * 保存用户信息
	 * @param dto
	 * @return
	 */
    int saveUser(UserDTO dto);
}

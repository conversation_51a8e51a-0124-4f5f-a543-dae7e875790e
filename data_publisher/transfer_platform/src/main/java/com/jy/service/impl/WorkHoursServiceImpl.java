package com.jy.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jy.bean.dto.ReceiveGroupDataDto;
import com.jy.bean.dto.WorkHoursDto;
import com.jy.mapper.ReceiveGroupDataMapper;
import com.jy.mapper.WorkHoursMapper;
import com.jy.rabbitMq.RabbitConfig;
import com.jy.service.ReceiveGroupDataService;
import com.jy.service.WorkHoursService;
import com.jy.util.EmptyUtils;
import com.jy.util.StringUtils;
import com.jy.util.TimestampTool;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/6/5.
 */
@Service
public class WorkHoursServiceImpl implements WorkHoursService {

    private static final Log log = LogFactory.getLog(WorkHoursServiceImpl.class);

    @Autowired
    private WorkHoursMapper workHoursMapper;

    @Autowired
    private ReceiveGroupDataMapper receiveGroupDataMapper;

    @Autowired
    private ReceiveGroupDataService receiveGroupDataService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private AmqpTemplate template;

    @Override
    public void saveWorkHours(JSONArray data) throws Exception{

        List<ReceiveGroupDataDto> reList;
        Map<String,Object> map = new HashMap<>(data.size());
        JSONObject jsonObject;
        JSONArray jsonArray;
        Map<String,Object> sendTable;
        List<Map<String,Object>> sendList;
        String versionCode;

        for (int i = 0,j = data.size();i < j;i++){
            jsonObject = data.getJSONObject(i);
            reList = new ArrayList<>(data.size());
            sendList = new ArrayList<>(data.size());
            jsonArray = jsonObject.getJSONArray("data");
            List<WorkHoursDto> list = jsonArray.toJavaList(WorkHoursDto.class);

            jsonObject.remove("data");
            //批次号信息
            ReceiveGroupDataDto dto = jsonObject.toJavaObject(ReceiveGroupDataDto.class);
            log.info("每批处理的工时数量:"+jsonArray.size()+",批次号:"+dto.getMainVersionCode());
            reList.add(dto);
            //新增工时批次
            receiveGroupDataMapper.insertBatchByWorkHours(reList);
            //发送的表信息
            sendTable = new HashMap<>(11);
            versionCode = "WorkHours" + TimestampTool.yyyymmddhhmmss()+ StringUtils.randomStr(5);
            sendTable.put("mainVersionCode",dto.getMainVersionCode());
            sendTable.put("versionCode",versionCode);
            sendTable.put("sendTable","c_pj_cllbjdyb_"+dto.getBrandCode());
            sendTable.put("state","71");
            sendTable.put("handleTable","pj_cllbjdyb_"+dto.getBrandCode());
            sendTable.put("sendCode",versionCode);
            sendList.add(sendTable);
            //新增修改的表信息
            receiveGroupDataMapper.saveSendTable(sendList);

            //工时信息
            map.put("brandCode",dto.getTableSuffix());
            map.put("groupId",dto.getGroupId());
            map.put("brandId",dto.getBrandId());
            map.put("mainVersionCode",dto.getMainVersionCode());
            map.put("list",list);
            //新增车组工时信息
            saveWorkHours(list,dto.getGroupId(),dto.getBrandId(),dto.getMainVersionCode());

            template.convertAndSend(RabbitConfig.WORK_MQ_NAME,dto.getMainVersionCode());
        }
    }

    @Override
    public void handleWorkHours(String mainVersionCode) throws  Exception{

        ReceiveGroupDataDto dto = receiveGroupDataService.getReceiveGroupDataModel(mainVersionCode);
        if(dto != null){
            //获取主轴字段
            List<Map<String,Object>> mainFieldList = receiveGroupDataMapper.getConverMainField();
            //获取当前批次下的工时信息
            List<WorkHoursDto> list = workHoursMapper.getWorkHoursListByMainVersionCode(mainVersionCode);

            //新增C库需要发送的数据
            cllbjDybC(dto.getTableSuffix(),dto.getMainVersionCode(),dto.getGroupId(),mainFieldList,list,dto.getVersionCode(),dto.getBrandId());

            //回写到F库的车型零件表
            partF(dto.getMainVersionCode(),dto.getBrandCode());

            //回写F库工时表
            workHoursF(dto.getBrandId(),dto.getGroupId());

            //查询表后缀
            String suffix = receiveGroupDataService.groupIsExists(dto.getGroupId());
            //发送数据
            receiveGroupDataService.dataAssembly(dto.getMainVersionCode(),dto.getBrandCode(),dto.getGroupCode(),suffix,true,dto.getBrandId(),"0",dto.getClientCode(),dto.getClientUrl());
        }
    }

    public void saveWorkHours(List<WorkHoursDto> list, String groupId, String brandId, String mainVersionCode){
        Long startTime = System.currentTimeMillis();
        int times = (int) Math.ceil(list.size() / 5000.0);
        for (int i = 0;i < times;i++){
            List<WorkHoursDto> finalList = list.subList(i * 5000, Math.min((i + 1) * 5000, list.size()));
            jdbcTemplate.batchUpdate("insert into work_hours (ID,MAIN_VERSION_CODE,GROUP_ID,STD_PART_ID,OPERATE_CODE,CHANGE_CODE,BRAND_ID,VEHICLE_ID) values(sys_guid(),?,?,?,?,?,?,?)", new BatchPreparedStatementSetter() {

                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    WorkHoursDto w = finalList.get(i);
                    ps.setString(1,mainVersionCode);
                    ps.setString(2,groupId);
                    ps.setString(3,w.getStdPartId());
                    ps.setString(4,w.getOperateCode());
                    ps.setString(5,w.getChangeCode());
                    ps.setString(6,brandId);
                    ps.setString(7,w.getVehicleId());
                }

                @Override
                public int getBatchSize() {
                    return finalList.size();
                }
            });
        }
        log.info("新增workHours时间:"+(System.currentTimeMillis() - startTime));
    }

    public void partF(String mainVersionCode,String brandCode){
        Long startTime = System.currentTimeMillis();
        List<Object[]> list = new ArrayList<>(2);
        list.add(new String[]{mainVersionCode,mainVersionCode});
        jdbcTemplate.batchUpdate("UPDATE f_pj_cllbjdyb_"+brandCode+" f set REPAIR_RELATION = (SELECT REPAIR_RELATION_NEW FROM c_pj_cllbjdyb_"+brandCode+" c WHERE main_code = ?" +
                "and f.id = c.id) WHERE EXISTS " +
                "(SELECT 1 FROM c_pj_cllbjdyb_"+brandCode+" c1 WHERE f.id = c1.id AND main_code= ?)",list);
        log.info("回写F库车型零件表时间:"+(System.currentTimeMillis() - startTime));
    }

    /**
     * 生成发送的数据
     * @param brandCode 品牌编码
     * @param mainVersionCode 主批次号
     * @param groupId 车组id
     * @param mainList 主轴字段集合
     * @param whList 工时信息集合
     * @param versionCode 小批次号
     */
    public void cllbjDybC(String brandCode, String mainVersionCode, String groupId, List<Map<String,Object>> mainList, List<WorkHoursDto> whList, String versionCode, String brandId){

        //分车型处理数据
        Map<String,List<WorkHoursDto>> wClzlbList = whList.stream().collect(Collectors.groupingBy(WorkHoursDto::getVehicleId));
        //新增到C库中的数据
        List<Map<String,Object>> cList = new ArrayList<>(whList.size());
        wClzlbList.forEach((vkey, vValue) -> {
            Map<String,Object> para = new HashMap<>(3);
            para.put("mainVersionCode",mainVersionCode);
            para.put("groupId",groupId);
            para.put("brandCode",brandCode);
            para.put("list",mainList);
            para.put("vehicleId",vkey);
            List<Map<String,Object>> fList = receiveGroupDataMapper.getFCLLByLjbzid(para);
            log.info("查询的F库原始数据:"+ JSON.toJSONString(fList));
            //将结果根据ljbzbid(零件标准id)分组
            Map<String,List<Map<String,Object>>> fMap = fList.stream().collect(Collectors.groupingBy(e -> e.get("ljbzbid").toString()));
            Map<String, Object> repairRelationMap = fList.stream().collect(Collectors.toMap(s1->s1.get("ljbzbid").toString(), s2->EmptyUtils.isNotEmpty(s2.get("repairRelation")) ? s2.get("repairRelation") : "", (k1, k2)->k1));
            //将工时信息根据std_part_id(零件标准id)分组
            Map<String,List<WorkHoursDto>> wMap = vValue.stream().collect(Collectors.groupingBy(WorkHoursDto::getStdPartId));

            wMap.forEach((key,value) -> {
                String localrRepairRelation = repairRelationMap.get(key).toString();
                List<String> repairRelationList =  EmptyUtils.isNotEmpty(localrRepairRelation) ? Arrays.asList(localrRepairRelation.split(",")) : new ArrayList<>();
                Set<String> set = new HashSet<>(repairRelationList);
                for (WorkHoursDto dto : value){
                    if("A".equals(dto.getChangeCode())){
                        set.add(dto.getOperateCode());
                    }else if("D".equals(dto.getChangeCode())){
                        set.remove(dto.getOperateCode());
                    }
                }
                String repairRelation = set.stream().collect(Collectors.joining(","));
                if(!localrRepairRelation.equals(repairRelation)){
                    Map<String,Object> cMap = new HashMap<>(3);
                    cMap.put("repairRelation",repairRelation);
                    cMap.put("ljbzbid",key);
                    cMap.put("clzlid",vkey);
                    cMap.put("list",fMap.get(key));
                    cList.add(cMap);
                }
                log.info("每次处理的原始数据:"+ fMap.get(key));
            });
        });

        //新增到c库
        log.info("最后的集合长度:"+cList.size()+"最后需要新增的集合:"+cList);
        saveCVehiclePart(cList,brandCode,mainList,mainVersionCode,versionCode);

        //新增工时临时表
        saveWorkHoursTemp(cList);
    }

    /**
     * 将组装好的工时数据存储到临时表
     */
    public void saveWorkHoursTemp(List<Map<String,Object>> list){

        jdbcTemplate.execute("truncate table WORK_HOURS_TEMP");
        Long startTime = System.currentTimeMillis();
        //新增
        String sql = "INSERT INTO WORK_HOURS_TEMP(LJBZBID,REPAIR_RELATION,CLZLID) values (?,?,?)";
        int times = (int) Math.ceil(list.size() / 5000.0);
        for (int i = 0;i < times;i++) {
            List<Map<String, Object>> finalList = list.subList(i * 5000, Math.min((i + 1) * 5000, list.size()));
            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    Map<String, Object> map = finalList.get(i);
                    ps.setObject(1, map.get("ljbzbid"));
                    ps.setObject(2, map.get("repairRelation"));
                    ps.setObject(3,map.get("clzlid"));
                }

                @Override
                public int getBatchSize() {
                    return finalList.size();
                }
            });
        }
        log.info("新增工时临时表时间:"+(System.currentTimeMillis() - startTime));
    }
    public void saveCVehiclePart(List<Map<String,Object>> list,String brandCode,List<Map<String,Object>> mainList,String mainVersionCode,String versionCode){

        Long startTime = System.currentTimeMillis();
        StringBuilder sql = new StringBuilder("insert into c_pj_cllbjdyb_"+brandCode+" (");
        StringBuilder para = new StringBuilder(mainList.size());
        mainList.forEach((Map<String,Object> map) -> {
            sql.append(map.get("fieldName")+",");
            para.append("?,");
        });
        sql.append("id,main_code,version_code,REPAIR_RELATION_NEW,REPAIR_RELATION_OLD,created_time,state,LJBZBID_NEW,version_id) values(");
        sql.append(para);
        sql.append("?,?,?,?,?,sysdate,'update',?,FUN_GETSEQNO())");
        log.info("最终的sql:"+sql);
        for (Map<String,Object> oldMap : list){
            List<Map<String,Object>> finalList = (List<Map<String,Object>>)oldMap.get("list");
            jdbcTemplate.batchUpdate(sql.toString(), new BatchPreparedStatementSetter() {

                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    int num = 0;
                    Map<String,Object> newMap = finalList.get(i);
                    //List
                    for (Map<String,Object> map : mainList){
                        ps.setObject(++num,newMap.get(map.get("fieldName").toString().toLowerCase()));
                    }
                    ps.setObject(++num,newMap.get("id"));
                    ps.setObject(++num,mainVersionCode);
                    ps.setObject(++num,versionCode);
                    ps.setObject(++num,oldMap.get("repairRelation"));
                    ps.setObject(++num,newMap.get("repairRelation"));
                    ps.setObject(++num,oldMap.get("ljbzbid"));
                }

                @Override
                public int getBatchSize() {
                    return finalList.size();
                }
            });
        }
        log.info("新增C库的执行时间:"+(System.currentTimeMillis() - startTime));
    }

    public void workHoursF(String brandId,String groupId){

        Long startTime = System.currentTimeMillis();
        List<Object[]> lists = new ArrayList<>(1);
        lists.add(new String[]{groupId});
        //修改
        jdbcTemplate.batchUpdate("update f_work_hours f set (OPERATE_CODE,REPAIR_FLAG,PAINT_FLAG) = (" +
                "select REPAIR_RELATION,CASE WHEN instr(REPAIR_RELATION,'R01') > 0 THEN '1' ELSE '0' END,CASE WHEN instr(REPAIR_RELATION,'R02') > 0 THEN '1' ELSE '0' END from work_hours_temp w where f.STD_PART_ID = w.ljbzbid and f.vehicle_id = w.clzlid)" +
                " where f.GROUP_ID = ? and exists(select 1 from work_hours_temp w1 where f.STD_PART_ID = w1.ljbzbid and f.vehicle_id = w1.clzlid)",lists);
        Long start1 = System.currentTimeMillis();
        log.info("修改F库workHours时间:"+(start1 - startTime));
        //新增
        jdbcTemplate.batchUpdate("insert into f_work_hours(id,BRAND_ID,GROUP_ID,STD_PART_ID,OPERATE_CODE,REPAIR_FLAG,PAINT_FLAG,VEHICLE_ID)" +
                "select sys_guid(),'"+brandId+"','"+groupId+"',LJBZBID,REPAIR_RELATION,CASE WHEN instr(REPAIR_RELATION,'R01') > 0 THEN '1' ELSE '0' END,CASE WHEN instr(REPAIR_RELATION,'R02') > 0 THEN '1' ELSE '0' END,CLZLID from work_hours_temp w " +
                " where not exists(select 1 from f_work_hours f1 where f1.GROUP_ID = ? and f1.STD_PART_ID = w.ljbzbid and f1.vehicle_id = w.clzlid)",lists);
        log.info("新增F库workHours时间:"+(System.currentTimeMillis() - start1));
    }

    /**
     * 修改F库的车型零件表和工时表
     * @param brandCode 品牌编码(拼接表名)
     * @param list 当前工时信息
     * @param groupId 车组id
     * @param type 数据类型:1修改车型零件,2修改工时表
     */
    public void cllbjDybF(String brandCode,String groupId,String type,List<Map<String,Object>> list){

        String sql;
        int times = (int) Math.ceil(list.size() / 1000.0);
        for (int i = 0;i < times;i++){
            List<Map<String,Object>> finalList = list.subList(i * 1000, Math.min((i + 1) * 1000, list.size()));
            Long startTime = System.currentTimeMillis();

            if("1".equals(type)){
                sql = "update f_pj_cllbjdyb_" + brandCode + " set REPAIR_RELATION = ? " +
                        " where czid = ? and ljbzbid = ?";
            }else{
                sql = "update f_work_hours set OPERATE_CODE = ?" +
                        " where GROUP_ID = ? and STD_PART_ID = ?";
            }

            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {

                    String repairRelation;
                    Map<String,Object> map = finalList.get(i);
                    ps.setObject(1, map.get("repairRelation"));
                    ps.setString(2, groupId);
                    ps.setObject(3, map.get("ljbzbid"));
                }

                @Override
                public int getBatchSize() {
                    return finalList.size();
                }
            });
            log.info("执行数据类型:"+type+",执行数据量:"+finalList.size()+",一次update时间:"+(System.currentTimeMillis() - startTime));
        }

    }
}

package com.jy.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.jy.ann.MethodMonitor;
import com.jy.mapper.CommonMapper;
import com.jy.service.CommonService;
import com.jy.util.EmptyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公共调用方法
 */
@Service
public class CommonServiceImpl implements CommonService{

    @Autowired
    private CommonMapper commonMapper;


    @Override
    public List<String> listCertain(String tableName, String certain) {
        Map<String, Object> map = new HashMap<>();
        map.put("tableName", tableName);
        map.put("certain", certain);
        return commonMapper.listCertain(map);
    }

    @Override
    @MethodMonitor
    public List<Map<String,Object>> listCertainByWhere(String tableName, String certain, String whereKey, String whereValue) {
        Map<String, Object> map = new HashMap<>();
        map.put("tableName", tableName);
        map.put("certain", certain);
        if(EmptyUtils.isNotEmpty(whereKey) && EmptyUtils.isNotEmpty(whereValue)){
            map.put("whereKey", whereKey);
            map.put("whereValue", whereValue);
        }
        return commonMapper.listCertainByWhere(map);
    }

    @Override
    @MethodMonitor
    public int getTableCountByTableNameAndBatchNo(String tableName, String mainBatchNo) {
        Map<String, Object> map = new HashMap<>();
        map.put("tableName", tableName);
        map.put("mainBatchNo", mainBatchNo);
        return commonMapper.getTableCount(map);
    }

    @Override
    @MethodMonitor
    public List<Map<String, Object>> getTableDataByTableNameAndBatchNo(String tableName, String mainBatchNo, int startNum, int endNum) {
        Map<String, Object> map = new HashMap<>();
        map.put("tableName", tableName);
        map.put("mainBatchNo", mainBatchNo);
        map.put("startNum", startNum);
        map.put("endNum", endNum);
        return commonMapper.getTableData(map);
    }

    @Override
    @MethodMonitor
    public List<Map<String, Object>> getMaxHzNumber(String groupId) {
        return commonMapper.getMaxHzNumber(groupId);
    }

    @Override
    @MethodMonitor
    public List<Map<String, Object>> getNullPartNameByGroupCode(String groupId) {
        return commonMapper.getNullPartNameByGroupCode(groupId);
    }

    @Override
    @MethodMonitor
    public void updateHzNumber(Map<String, Object> map) {
        commonMapper.updateHzNumber(map);
    }

    @Override
    @MethodMonitor
    public void updateCllbjdyb() {
        commonMapper.updateCllbjdyb();
    }

    @Override
    @MethodMonitor
    public void updateWLCllbjdyb() {
        commonMapper.updateWLCllbjdyb();
    }

    @Override
    @MethodMonitor
    public List getOnlyJson(String id) {
        JSONObject jsonObject = JSONObject.parseObject(id);
        id = jsonObject.getString("id");
        List vinHbzjList = new ArrayList();
        vinHbzjList.add(id);
        return vinHbzjList;
    }

    @Override
    @MethodMonitor
    public String getPartSuffix(String groupId) {
        String result = "";
        Map<String, Object> map = new HashMap<>();
        map.put("groupId", groupId);
        List<String> list = commonMapper.getPartSuffix(map);
        if(EmptyUtils.isNotEmpty(list)){
            result = list.get(0);
        }
        return result;
    }

    @Override
    @MethodMonitor
    public void updateIncGraphGroup(String clVehicleId) {
        Map<String, Object> map = new HashMap<>();
        map.put("clVehicleId", clVehicleId);
        commonMapper.updateIncGraphGroup(map);
    }
}

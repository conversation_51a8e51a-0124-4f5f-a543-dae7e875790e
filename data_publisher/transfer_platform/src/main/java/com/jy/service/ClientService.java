package com.jy.service;

import com.jy.bean.dto.ClientTableDTO;
import com.jy.bean.dto.ClientTableFieldMpDTO;
import com.jy.bean.dto.ClientUrlDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/8/21
 */
public interface ClientService {

    List<ClientUrlDTO> listCompareClient(String baseClientCode, String clientCode) throws Exception;

    Map<String, ClientUrlDTO> mapClientUrlByClientCode(String clientCode) throws Exception;

    Map<String, ClientTableDTO> mapClientTableByClientCode(String clientCode) throws Exception;

    Map<String, ClientTableFieldMpDTO> mapClientTableFieldByTableName(String clientCode, String tableName) throws Exception;

}

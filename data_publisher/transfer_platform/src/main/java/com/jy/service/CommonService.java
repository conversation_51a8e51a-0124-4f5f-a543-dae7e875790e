package com.jy.service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/22
 */
public interface CommonService {

    List<String> listCertain(String tableName, String certainm);

    List<Map<String,Object>> listCertainByWhere(String tableName, String certain, String whereKey, String whereValue);

    int getTableCountByTableNameAndBatchNo(String tableName, String mainBatchNo);

    List<Map<String,Object>> getTableDataByTableNameAndBatchNo(String tableName, String mainBatchNo, int startNum, int endNum);

    List<Map<String,Object>> getMaxHzNumber(String groupId);

    List<Map<String,Object>> getNullPartNameByGroupCode(String groupId);

    void updateHzNumber(Map<String,Object> map);

    /**
     * 给同零件名称追加数字
     */
    void updateCllbjdyb();

    /**
     * 给蔚来的同零件名称追加数字
     */
    void updateWLCllbjdyb();

    List getOnlyJson(String id);

    String getPartSuffix(String groupId);

    void updateIncGraphGroup(String clVehicleId);
}

package com.jy.service;

import com.jy.bean.dto.DataStatisticsDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface DataStatisticsService {

	/**
	 * 查询加工平台-转换平台-facade的统计信息
	 * @param groupId 车组id
	 * @param date 日期
	 * @return
	 */
	List<DataStatisticsDto> listDataStatistics(String groupId,String date);

	/**
	 * 批量新增统计数据
	 * @param dataJson
	 * @param dataType
	 * @param receiveDate 数据发送日期
	 */
	void insertDataStatisticsBatch(String dataJson, String dataType,String receiveDate);

	/**
	 * 根据车组统计转换整体数据
	 * @param brandCode 品牌编号
	 * @param groupId 车组id
	 * @param seriesId 车系id
	 * @param brandId 品牌id
	 * @param list 主批次列表
	 */
	void transformationData(String brandCode, String groupId, String seriesId, String brandId, List<Map<String,Object>> list,String date);

	/**
	 * 导出统计数据
	 * @param date 导出数据的日期
	 * @return
	 */
	List<Map<String,Object>> exportData(String date);

	void getCTableDataStaticticsList(List<Map<String,Object>> mainList,String brandCode,String groupId,String brandId,String date);

	/**
	 * 获取统计数据进行对比校验
	 * @return
	 */
	List<Map<String,Object>> getStisticsDataContrast(String date,String filePath);
}

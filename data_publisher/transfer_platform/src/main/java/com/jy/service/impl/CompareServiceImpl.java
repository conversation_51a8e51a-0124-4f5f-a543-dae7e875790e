package com.jy.service.impl;

import com.jy.bean.dto.ClientUrlDTO;
import com.jy.bean.po.CompareDataLayer;
import com.jy.bean.result.JsonResult;
import com.jy.exception.CompareException;
import com.jy.mapper.CompareMapper;
import com.jy.service.*;
import com.jy.util.Dictionary;
import com.jy.util.EmptyUtils;
import com.jy.util.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/8/20
 */
@Service
public class CompareServiceImpl implements CompareService {

    @Autowired
    private CompareMapper compareMapper;

    @Override
    public Integer resendInsert(String mainVersionCode, String versionCode, String state, String tableName, String cKey, String fKey, BigInteger maxVersionId, BigInteger minVersionId) {
        Map<String, Object> map = new HashMap<>();
        map.put("mainVersionCode", mainVersionCode);
        map.put("versionCode", versionCode);
        map.put("state", state);
        map.put("fKey", fKey);
        map.put("cKey", cKey);
        map.put("tableName", tableName);
        map.put("maxVersionId", maxVersionId);
        map.put("minVersionId", minVersionId);
        return compareMapper.resendInsert(map);
    }

    @Override
    public Integer getCountByTableName(String tableName, String layerKey, String layerValue) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("tableName", tableName);
        map.put("layerKey", layerKey);
        map.put("layerValue", layerValue);
        return compareMapper.getCountByTableName(map);
    }

    @Override
    public Integer getCountByTableNameAndVersionId(String tableName, String layerKey, String layerValue, BigInteger maxVersionId, BigInteger minVersionId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("tableName", tableName);
        map.put("layerKey", layerKey);
        map.put("layerValue", layerValue);
        map.put("maxVersionId", maxVersionId);
        map.put("minVersionId", minVersionId);
        return compareMapper.getCountByTableName(map);
    }

    @Override
    public List<String> listTableName(String tableName) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("tableName", tableName);
        return compareMapper.listTableName(map);
    }

    @Override
    public List<String> listCompareLayer(String tableName, String compareLayerKey) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("tableName", tableName);
        map.put("compareLayerKey", compareLayerKey);
        return compareMapper.listCompareLayer(map);
    }

    /*    @Override
        public List<CompareDataLayer> listPageRangeByTableName(String tableName, Integer pageNumber, Integer layerSize, Integer batchSize, Integer total) {
            List<CompareDataLayer> compareDataLayers = new ArrayList<>();
            int begin = layerSize * batchSize * pageNumber;
            for(int j=0; j< batchSize; j++){
                int pageStart =  begin + (layerSize * j);
                if(pageStart > total){
                    break;
                }
                CompareDataLayer temp = new CompareDataLayer(tableName, begin + (layerSize * j), layerSize);
                compareDataLayers.add(temp);
            }
            return compareMapper.listRangeByTableName(compareDataLayers);
        }*/
/*@Override
public List<CompareDataLayer> listPageRangeByTableName(String tableName, Integer pageNumber, Integer layerSize, Integer batchSize, Integer total) {
    List<CompareDataLayer> compareDataLayers = new ArrayList<>();
    int begin = layerSize * batchSize * pageNumber;
    for(int j=0; j< batchSize; j++){
        int pageStart =  begin + (layerSize * j);
        if(pageStart > total){
            break;
        }
        CompareDataLayer temp = new CompareDataLayer(tableName, begin + (layerSize * j), layerSize);
        compareDataLayers.add(temp);
    }
    return compareMapper.listRangeByTableName(compareDataLayers);
}*/
    @Override
    public BigInteger listPageRangeByTableName(String tableName, String layerKey, String layerValue, BigInteger minVersionId, Integer count) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("tableName", tableName);
        map.put("layerKey", layerKey);
        map.put("layerValue", layerValue);
        map.put("minVersionId", minVersionId);
        map.put("count", count);
        List<BigInteger> list = compareMapper.listRangeByTableName(map);
        if(EmptyUtils.isEmpty(list)){
            return null;
        }
        return list.get(0);
    }
/*    @Override
    public List<CompareDataLayer> listPageRangeByTableName(String tableName, BigInteger minVersionId, Integer layerSize, Integer batchSize) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("tableName", tableName);
        map.put("minVersionId", minVersionId);
        map.put("count", layerSize * batchSize);
        List<BigInteger> list = compareMapper.listRangeByTableName(map);
        List<CompareDataLayer> compareDataLayers = new ArrayList<>();
        if(EmptyUtils.isNotEmpty(list)){
            int pageSize = (int)Math.ceil(list.size() / (layerSize * 1.0));
            for(int j=0; j< pageSize; j++){
                int maxIndex = (layerSize * (j +1) - 1);
                if(maxIndex > (list.size() - 1)){
                    maxIndex = list.size() - 1;
                }
                BigInteger maxVersionId = list.get(maxIndex);
                CompareDataLayer compareDataLayer = new CompareDataLayer(tableName, maxVersionId, minVersionId, layerSize);
                compareDataLayers.add(compareDataLayer);
                minVersionId = maxVersionId;
            }
        }
        return compareDataLayers;
    }*/

    @Override
    public List<Map<String, Object>> query(String tableName, BigInteger maxVersionId, BigInteger minVersionId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("tableName", tableName);
        map.put("maxVersionId", maxVersionId);
        map.put("minVersionId", minVersionId);
        return compareMapper.query(map);
    }
}

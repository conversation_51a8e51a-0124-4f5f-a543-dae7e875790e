bash-4.2$ cat /usr/share/logstash/pipeline/logstash.conf
input {

	# facade 路由轨迹
	file{
		path=>["/data/facade_log/zuul-trail*.*.log","/data/facade_log/zuul-trail*.log"]
                type=>"zuul-trail"
		start_position=>"beginning"
		discover_interval=>15
		stat_interval=>1
		codec=>plain{
		      charset=>"UTF-8"
		}
	}
	# 及时更新 开发环境
        rabbitmq {
                host => "**************"
                port=> 5672
                user => "guest"
                password => "guest"
                queue => "data-trace-queue"
                durable => true
                codec => "json"
                type => "dataTrace"
        }



}

filter {
  date {
     match => ["logdate", "yyyy-MM-dd HH:mm:ss.SSS"]
     target => "@timestamp"
  }
}

output {
        if [type] == "dataTrace"{
        	elasticsearch {
            		hosts => ["**************:9400"]
            		index => "data-trace-alias"
        	}
    	}
	#stdout{
	#	codec=>rubydebug
	#}
}

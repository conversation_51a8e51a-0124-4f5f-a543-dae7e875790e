package com.jy.util;

import com.jy.bean.dto.DataCompareDTO;
import com.jy.equator.Equator;
import com.jy.equator.FieldBaseEquator;
import com.jy.equator.FieldInfo;
import com.jy.equator.User;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class test {


    public static void main(String[] args) throws Exception {

        long time1 = System.currentTimeMillis();
        List<User> list1 = new ArrayList<User>();
        for(int i=0;i< 1000000;i++){
          //  System.out.println(i);
            User user11 = new User(i+"","测试" + i,i + "");
            list1.add(user11);
        }

       // User user25 = new User("6","测试6","6", DateUtil.convertStringToDate(DateUtil.datePattern, "2020-01-01"), 13);
        List<User> list2 = new ArrayList<User>();
        for(int i=0;i< 1;i++){
            User user21 = new User((i+1)+"","测试" + (i+1),(i+1) + "");
            list2.add(user21);
        }
        long time2 = System.currentTimeMillis();
        System.out.println("生成数据------" + (time2 -time1));
        List<DataCompareDTO>  dataCompareDTOS = DataCompareUtils.arrayCompare(list1, list2);
        long time3 = System.currentTimeMillis();
        System.out.println("对比数据------" + (time3 -time2));
        System.out.println(dataCompareDTOS.size());
        long time4 = System.currentTimeMillis();
        System.out.println("对比结果------" + (time4 -time3));


       /* User user1 = new User("1234","测试1","111");
        User2 user2 = new User2("1234","测试1");
        User user3 = new User("1234","测试1");
        List<User> list1 = new ArrayList<User>();
        list1.add(user1);
        List<User2> list2 = new ArrayList<User2>();
        list2.add(user2);
        List<User> list3 = new ArrayList<User>();
        list3.add(user3);
        System.out.println(DataCompareUtils.arrayCompare(list1, list3));*/
    }


}

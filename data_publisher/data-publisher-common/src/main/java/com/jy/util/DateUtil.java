package com.jy.util;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018/4/26
 */
public class DateUtil {
    public static String datePattern = "yyyy-MM-dd";
    public static String timePattern = "yyyy-MM-dd HH:mm:ss";
    public static String milliPattern = "yyyy-MM-dd HH:mm:ss SSS";

    public static final Date convertStringToDate(String aMask, String strDate) {
        SimpleDateFormat df =  new SimpleDateFormat(aMask);
        Date date = null;
        try {
            date = df.parse(strDate);
        } catch (ParseException pe) {
            System.out.println(pe.getMessage());
        }
        return (date);
    }

    public static final String convertDateToString(String aMask, Date date) {
        SimpleDateFormat df =  new SimpleDateFormat(aMask);
        String dateStr = null;
        try {
            dateStr = df.format(date);
        } catch (Exception pe) {
            System.out.println(pe.getMessage());
        }
        return (dateStr);
    }

    public static final String convertDateToString(Date aDate) {
        return convertDateToString(datePattern, aDate);
    }

    public static Date convertStringToDate(String strDate) {
        return convertStringToDate(datePattern, strDate);
    }

    // 当前时间
    public static Timestamp crunttime() {
        return new Timestamp(System.currentTimeMillis());
    }

    public static String getTime(int num){
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, num);
        Date date = cal.getTime();
        SimpleDateFormat df = new SimpleDateFormat(datePattern);
        return df.format(date);
    }

    public static long getBetweenDay(String date) {
        LocalDate dateOfFeb = LocalDate.parse(date);
        LocalDate today = LocalDate.now();
        return today.toEpochDay() - dateOfFeb.toEpochDay();
    }


}

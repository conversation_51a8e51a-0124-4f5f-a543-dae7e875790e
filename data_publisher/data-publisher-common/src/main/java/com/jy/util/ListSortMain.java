package com.jy.util;

/**
 * @Author: caolt
 * @Description:
 * @Version:
 * @Date: Created in  2020/01/13
 */

import com.jy.equator.User;
import com.jy.equator.User2;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


public class ListSortMain {

    public static void main(String[] args) throws Exception {

        User user1 = new User("1234","测试1","111");
        User2 user2 = new User2("1234","测试1");
        User user3 = new User("1234","测试1");
        List<User> list1 = new ArrayList<User>();
        list1.add(user1);
        List<User2> list2 = new ArrayList<User2>();
        list2.add(user2);
        List<User> list3 = new ArrayList<User>();
        list3.add(user3);
        System.out.println(DataCompareUtils.arrayCompare(list1, list3));

        List<Dog> dogList = new ArrayList<Dog>();

        System.out.println(ListSortUtil.arrayCompare(dogList, dogList));
        for (int i = 0; i < 5; i++) {
            Dog d = new Dog();
            d.setName("dog" + i);
            d.setTime(new Date());
            dogList.add(d);
            Thread.sleep(1000);
        }
        System.out.println("排序前:" + dogList.toString());

        ListSortUtil.sort(dogList, "name", true);
        System.out.println("按name正序排：" + dogList.toString());

        ListSortUtil.sort(dogList, "name", false);
        System.out.println("按name逆序排：" + dogList.toString());

        ListSortUtil.sort(dogList, "time", true);
        System.out.println("按time正序排：" + dogList.toString());

        ListSortUtil.sort(dogList, "time", false);
        System.out.println("按time逆序排：" + dogList.toString());

    }

}

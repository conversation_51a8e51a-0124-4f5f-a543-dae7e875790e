package com.jy.util;

import com.jy.bean.dto.DataCompareDTO;
import java.lang.reflect.Method;
import java.util.*;

/**
 * @Author: caolt
 * @Description:
 * @Version:
 * @Date: Created in  2020/01/10
 */
public class DataCompareUtils<T> {
    private static final String DEFAULT_KEY = "id";

    public static <T> List<DataCompareDTO> arrayCompare(List<T> first, List<T> second) throws Exception {
       return arrayCompare(first, second, DEFAULT_KEY);
    }

    /**
     * 对比两个集合的差异数据
     * @value first  旧集合
     * @value second  新集合
     * @value keyFields   主键（唯一标识）可以是联合主键
     * @return
     */
    public static <T> List<DataCompareDTO> arrayCompare(List<T> first, List<T> second, String keyFields) throws Exception {
        List<DataCompareDTO> result = new ArrayList<>();
        if(EmptyUtils.isEmpty(first) || EmptyUtils.isEmpty(second) || EmptyUtils.isEmpty(keyFields)){
            return result;
        }

        if(ClassUtils.isSimpleField(first.get(0)) || ClassUtils.isSimpleField(second.get(0))){
            throw new Exception("参数错误，不支持简单数据类型对比");
        }
      /*  if(!ClassUtils.isEqualClass(data1, data2)){
            throw new Exception("参数错误，不支持不同对象间对比");
        }*/
        //first second 变成以keys作为key的map Map<String, DataCompareDTO>
        Map<String, DataCompareDTO> map1 = mapByKey(first, keyFields);
        Map<String, DataCompareDTO> map2 = mapByKey(second, keyFields);
        //first有 second没有 delete
        for (Map.Entry<String, DataCompareDTO> entry : map1.entrySet()){
            if(!map2.containsKey(entry.getKey())){
                DataCompareDTO dataCompareDTO = entry.getValue();
                dataCompareDTO.setOperate("delete");
                result.add(dataCompareDTO);
            }
        }
        //first没有 second有 insert
        for (Map.Entry<String, DataCompareDTO> entry : map2.entrySet()){
            if(!map1.containsKey(entry.getKey())){
                DataCompareDTO dataCompareDTO = entry.getValue();
                dataCompareDTO.setOperate("insert");
                result.add(dataCompareDTO);
            }
        }
        //first = second 过滤  first != second update
        for (Map.Entry<String, DataCompareDTO> entry : map1.entrySet()){
            if(map2.containsKey(entry.getKey())){
                Map<String, String> fields1 = entry.getValue().getFields();
                Map<String, String> fields2 = map2.get(entry.getKey()).getFields();
                Map<String, String> fields = compareByMap(fields1, fields2);
                if(EmptyUtils.isNotEmpty(fields)){
                    DataCompareDTO dataCompareDTO = new DataCompareDTO(entry.getValue().getKeys(), fields);
                    dataCompareDTO.setOperate("update");
                    result.add(dataCompareDTO);
                }
            }
        }
        return result;
    }
    private static Map<String, String> compareByMap(Map<String, String> fields1, Map<String, String> fields2){
        Map<String, String> result = new HashMap<>();
        for (Map.Entry<String, String> entry : fields1.entrySet()){
            String value2 = fields2.get(entry.getKey());
            if(!entry.getValue().equals(value2)){
                result.put(entry.getKey(), value2);
            }
        }
        return result;
    }

    private static <T> Map<String, DataCompareDTO> mapByKey(List<T> first, String keyFields) throws Exception {
        List<String> array = Arrays.asList(keyFields.split(","));
        Map<String, Method> getters = ClassUtils.getAllGetters(first.get(0).getClass());
        Map<String, DataCompareDTO> map = new HashMap<>(first.size());
        for(Object o : first){
            String key = "";
            Map<String, String> keys = new HashMap<>();
            Map<String, String> fields = new HashMap<>();
            for (Map.Entry<String, Method> entry : getters.entrySet()) {
                String value = entry.getValue().invoke(o) == null ? "" : localToString(entry.getValue().invoke(o));
                if(array.contains(entry.getKey())){
                    key += value;
                    keys.put(entry.getKey(), value);
                } else {
                    fields.put(entry.getKey(), value);
                }
            }
            DataCompareDTO dataCompareDTO = new DataCompareDTO(keys, fields);
            map.put(key, dataCompareDTO);
        }
        return map;
    }

    private static String localToString(Object value){
        if (value instanceof Date) {
            Date date = (Date) value;
            return DateUtil.convertDateToString(DateUtil.timePattern, date);
        } else {
            return value.toString();
        }
    }


}

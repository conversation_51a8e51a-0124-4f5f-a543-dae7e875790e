package com.jy.bean.dto;

import lombok.Data;

import java.util.Map;

/**
 * @Author: caolt
 * @Description:
 * @Version:
 * @Date: Created in  2020/01/10
 */
@Data
public class DataCompareDTO {

    /**  insert, update, delete */
    private String operate;
    /**  主键 */
    private Map<String, String> keys;
    /**  属性信息 */
    private Map<String, String> fields;

    public DataCompareDTO(){}

    public DataCompareDTO(Map<String, String> keys, Map<String, String> fields){
        this.keys = keys;
        this.fields = fields;
    }
}

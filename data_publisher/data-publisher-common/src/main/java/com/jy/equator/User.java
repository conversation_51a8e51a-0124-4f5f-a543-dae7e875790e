package com.jy.equator;

import java.util.Date;

public class User {
    private String id;
    private String name;
    private String code;
    private Date stime;
    private Integer age;

    public User(){}
    public User(String id , String name){
        this.id = id ;
        this.name = name ;
    }
    public User(String id , String name, String code){
        this.id = id ;
        this.name = name ;
        this.code = code;
    }

    public User(String id , String name, String code, Date stime, Integer age){
        this.id = id ;
        this.name = name ;
        this.code = code;
        this.stime = stime;
        this.age = age;
    }
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Date getStime() {
        return stime;
    }

    public void setStime(Date stime) {
        this.stime = stime;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }
}

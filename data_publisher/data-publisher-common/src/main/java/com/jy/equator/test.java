package com.jy.equator;

import com.alibaba.fastjson.JSON;
import com.jy.equator.Equator;
import com.jy.equator.FieldBaseEquator;
import com.jy.equator.FieldInfo;
import com.jy.equator.User;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class test {

    public static void main(String[] args){
        Equator equator = new FieldBaseEquator();
        User user1 = new User("1234","测试1","111");
        User user2 = new User("1234","测试1");
        Map<String, String> map1 = new HashMap<>();
        map1.put("id", "1234");
        map1.put("name", "测试1");
        map1.put("code", "111");

        Map<String, String> map2 = new HashMap<>();
        map2.put("id", "1234");
        map2.put("name", "测试1");
// 判断属性是否完全相等
      //  boolean data = equator.isEquals(user1, user2);
    //    System.out.println("---------------" + data);

// 获取不同的属性
        List<FieldInfo> diff = equator.getDiffFields(map1, map2);
        System.out.println("---------------" + JSON.toJSONString(diff));

      //  Map<String,Object> map = CompareUtils.getModifyContent(user1, user2);
      //  System.out.println(JSON.toJSONString(map));
    }
}

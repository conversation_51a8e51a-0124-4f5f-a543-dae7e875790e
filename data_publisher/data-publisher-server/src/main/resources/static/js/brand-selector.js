/**
 * 品牌选择器组件
 * 支持瀑布流加载、搜索、多选功能
 */
class BrandSelector {
    constructor(options) {
        this.options = {
            container: null,           // 容器元素
            placeholder: '请选择品牌',  // 占位符
            multiple: true,           // 是否多选
            searchable: true,         // 是否可搜索
            pageSize: 20,            // 每页数量
            apiUrl: '/brandDict/search', // API地址
            onSelect: null,          // 选择回调
            onChange: null,          // 变化回调
            ...options
        };

        this.selectedItems = [];     // 已选择的项目
        this.allItems = [];         // 所有加载的项目
        this.currentPage = 0;       // 当前页码
        this.isLoading = false;     // 是否正在加载
        this.hasMore = true;        // 是否还有更多数据
        this.searchKeyword = '';    // 搜索关键词
        this.searchTimeout = null;  // 搜索防抖定时器

        this.init();
    }

    init() {
        this.createElements();
        this.bindEvents();
        this.loadData();
    }

    createElements() {
        const container = typeof this.options.container === 'string'
            ? document.querySelector(this.options.container)
            : this.options.container;

        if (!container) {
            throw new Error('Container not found');
        }

        container.innerHTML = `
            <div class="brand-selector">
                <div class="brand-selector-main">
                    <div class="brand-selector-input" tabindex="0">
                        <span class="brand-selector-placeholder">${this.options.placeholder}</span>
                    </div>
                    <div class="brand-selector-arrow"></div>
                    <div class="brand-selector-selected-tags"></div>
                </div>
                <div class="brand-selector-dropdown" style="display: none;">
                    ${this.options.searchable ? `
                        <div class="brand-selector-search">
                            <input type="text" placeholder="搜索品牌编码或名称..." />
                        </div>
                    ` : ''}
                    <div class="brand-selector-list"></div>
                </div>
            </div>
        `;

        this.elements = {
            container: container.querySelector('.brand-selector'),
            main: container.querySelector('.brand-selector-main'),
            input: container.querySelector('.brand-selector-input'),
            placeholder: container.querySelector('.brand-selector-placeholder'),
            dropdown: container.querySelector('.brand-selector-dropdown'),
            searchInput: container.querySelector('.brand-selector-search input'),
            list: container.querySelector('.brand-selector-list'),
            tagsContainer: container.querySelector('.brand-selector-selected-tags')
        };
    }

    bindEvents() {
        // 点击整个选择器区域显示/隐藏下拉框（包括输入框和箭头）
        this.elements.container.addEventListener('click', (e) => {
            // 防止点击下拉框内容时触发
            if (!this.elements.dropdown.contains(e.target)) {
                e.preventDefault();
                e.stopPropagation();
                this.toggle();
            }
        });

        // 搜索输入事件
        if (this.elements.searchInput) {
            this.elements.searchInput.addEventListener('input', (e) => {
                this.handleSearch(e.target.value);
            });

            // 防止搜索框点击时关闭下拉框
            this.elements.searchInput.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }

        // 滚动加载更多
        this.elements.list.addEventListener('scroll', () => {
            this.handleScroll();
        });

        // 点击外部关闭下拉框
        document.addEventListener('click', (e) => {
            if (!this.elements.container.contains(e.target)) {
                this.close();
            }
        });

        // 防止下拉框内容点击时关闭
        this.elements.dropdown.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        // 窗口大小变化时重新定位下拉框
        window.addEventListener('resize', () => {
            if (this.elements.dropdown.style.display === 'block') {
                this.positionDropdown();
            }
        });

        // 页面滚动时重新定位下拉框
        window.addEventListener('scroll', () => {
            if (this.elements.dropdown.style.display === 'block') {
                this.positionDropdown();
            }
        });
    }

    toggle() {
        if (this.elements.dropdown.style.display === 'none') {
            this.open();
        } else {
            this.close();
        }
    }

    open() {
        // 确保下拉框正确定位
        this.positionDropdown();
        this.elements.dropdown.style.display = 'block';
        this.elements.container.classList.add('open');
        if (this.elements.searchInput) {
            setTimeout(() => {
                this.elements.searchInput.focus();
            }, 10);
        }
    }

    positionDropdown() {
        // 重新计算下拉框位置，确保对齐
        const mainRect = this.elements.main.getBoundingClientRect();
        const inputRect = this.elements.input.getBoundingClientRect();

        // 确保下拉框宽度与输入框一致
        this.elements.dropdown.style.width = inputRect.width + 'px';
        this.elements.dropdown.style.left = '0px';

        // 计算主容器的实际高度（包括标签）
        const mainHeight = this.elements.main.offsetHeight;
        this.elements.dropdown.style.top = mainHeight + 'px';
    }

    close() {
        this.elements.dropdown.style.display = 'none';
        this.elements.container.classList.remove('open');
    }

    handleSearch(keyword) {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.searchKeyword = keyword;
            this.currentPage = 0;
            this.allItems = [];
            this.hasMore = true;
            this.loadData();
        }, 300);
    }

    handleScroll() {
        const { scrollTop, scrollHeight, clientHeight } = this.elements.list;
        if (scrollTop + clientHeight >= scrollHeight - 10 && this.hasMore && !this.isLoading) {
            this.loadMore();
        }
    }

    loadData() {
        if (this.isLoading) return;

        this.isLoading = true;
        this.showLoading();

        const params = new URLSearchParams({
            page: this.currentPage,
            size: this.options.pageSize
        });

        if (this.searchKeyword) {
            params.append('keyword', this.searchKeyword);
        }

        // 添加认证头
        const headers = {
            'Content-Type': 'application/json'
        };

        // 从localStorage获取token
        const token = localStorage.getItem('token');
        if (token) {
            headers['Authorization'] = 'Bearer ' + token;
        }

        fetch(`${this.options.apiUrl}?${params}`, {
            method: 'GET',
            headers: headers
        })
            .then(response => response.json())
            .then(data => {
                this.isLoading = false;
                this.hideLoading();

                if (data.status === '200' && data.result) {
                    const newItems = data.result;

                    if (this.currentPage === 0) {
                        this.allItems = newItems;
                    } else {
                        this.allItems = this.allItems.concat(newItems);
                    }

                    this.hasMore = newItems.length === this.options.pageSize;
                    this.renderList();
                } else {
                    this.showError(data.message || '加载失败');
                }
            })
            .catch(error => {
                this.isLoading = false;
                this.hideLoading();
                this.showError('网络错误');
                console.error('Brand selector load error:', error);
            });
    }

    loadMore() {
        this.currentPage++;
        this.loadData();
    }

    renderList() {
        if (this.currentPage === 0) {
            this.elements.list.innerHTML = '';
        }

        if (this.allItems.length === 0) {
            this.elements.list.innerHTML = '<div class="brand-selector-empty">暂无数据</div>';
            return;
        }

        const startIndex = this.currentPage * this.options.pageSize;
        const endIndex = Math.min(startIndex + this.options.pageSize, this.allItems.length);

        for (let i = startIndex; i < endIndex; i++) {
            const item = this.allItems[i];
            const itemElement = this.createItemElement(item);
            this.elements.list.appendChild(itemElement);
        }

        // 无感瀑布流，不显示"加载更多"按钮
    }

    createItemElement(item) {
        const div = document.createElement('div');
        div.className = 'brand-selector-item';
        div.dataset.value = item.brandCode;

        const isSelected = this.selectedItems.some(selected => selected.brandCode === item.brandCode);
        if (isSelected) {
            div.classList.add('selected');
        }

        div.innerHTML = `
            ${this.options.multiple ? `<input type="checkbox" class="brand-selector-checkbox" ${isSelected ? 'checked' : ''} />` : ''}
            <span class="brand-selector-text">${item.brandCode} - ${item.brandName}</span>
        `;

        div.addEventListener('click', () => {
            this.selectItem(item, div);
        });

        return div;
    }

    selectItem(item, element) {
        const isSelected = this.selectedItems.some(selected => selected.brandCode === item.brandCode);

        if (this.options.multiple) {
            if (isSelected) {
                this.removeSelectedItem(item.brandCode);
            } else {
                this.addSelectedItem(item);
            }
        } else {
            this.selectedItems = [item];
            this.close();
        }

        this.updateUI();
        this.triggerChange();
    }

    addSelectedItem(item) {
        if (!this.selectedItems.some(selected => selected.brandCode === item.brandCode)) {
            this.selectedItems.push(item);
        }
    }

    removeSelectedItem(brandCode) {
        this.selectedItems = this.selectedItems.filter(item => item.brandCode !== brandCode);
    }

    updateUI() {
        this.updateInput();
        this.updateTags();
        this.updateListSelection();

        // 如果下拉框是打开的，重新定位
        if (this.elements.dropdown.style.display === 'block') {
            // 延迟一点时间让DOM更新完成
            setTimeout(() => {
                this.positionDropdown();
            }, 10);
        }
    }

    updateInput() {
        if (this.selectedItems.length === 0) {
            this.elements.placeholder.textContent = this.options.placeholder;
            this.elements.placeholder.style.color = '#999';
        } else if (this.options.multiple) {
            this.elements.placeholder.textContent = `已选择 ${this.selectedItems.length} 个品牌`;
            this.elements.placeholder.style.color = '#333';
        } else {
            const item = this.selectedItems[0];
            this.elements.placeholder.textContent = `${item.brandCode} - ${item.brandName}`;
            this.elements.placeholder.style.color = '#333';
        }
    }

    updateTags() {
        if (!this.options.multiple) {
            this.elements.tagsContainer.style.display = 'none';
            return;
        }

        this.elements.tagsContainer.innerHTML = '';

        this.selectedItems.forEach(item => {
            const tag = document.createElement('div');
            tag.className = 'brand-selector-tag';
            tag.innerHTML = `
                <span class="brand-selector-tag-text">${item.brandCode}</span>
                <span class="brand-selector-tag-close" data-brand-code="${item.brandCode}">×</span>
            `;

            tag.querySelector('.brand-selector-tag-close').addEventListener('click', (e) => {
                e.stopPropagation();
                this.removeSelectedItem(item.brandCode);
                this.updateUI();
                this.triggerChange();
            });

            this.elements.tagsContainer.appendChild(tag);
        });
    }

    updateListSelection() {
        const items = this.elements.list.querySelectorAll('.brand-selector-item');
        items.forEach(item => {
            const brandCode = item.dataset.value;
            const isSelected = this.selectedItems.some(selected => selected.brandCode === brandCode);
            const checkbox = item.querySelector('.brand-selector-checkbox');

            if (isSelected) {
                item.classList.add('selected');
                if (checkbox) checkbox.checked = true;
            } else {
                item.classList.remove('selected');
                if (checkbox) checkbox.checked = false;
            }
        });
    }

    showLoading() {
        const loading = this.elements.list.querySelector('.brand-selector-loading');
        if (!loading) {
            const div = document.createElement('div');
            div.className = 'brand-selector-loading';
            div.textContent = '加载中...';
            this.elements.list.appendChild(div);
        }
    }

    hideLoading() {
        const loading = this.elements.list.querySelector('.brand-selector-loading');
        if (loading) {
            loading.remove();
        }
    }



    showError(message) {
        this.elements.list.innerHTML = `<div class="brand-selector-empty">${message}</div>`;
    }

    triggerChange() {
        if (this.options.onChange) {
            this.options.onChange(this.selectedItems);
        }
    }

    // 公共方法
    getSelectedItems() {
        return this.selectedItems;
    }

    getSelectedCodes() {
        return this.selectedItems.map(item => item.brandCode);
    }

    setSelectedItems(items) {
        this.selectedItems = items || [];
        this.updateUI();
    }

    clear() {
        this.selectedItems = [];
        this.updateUI();
        this.triggerChange();
    }

    destroy() {
        clearTimeout(this.searchTimeout);
        // 移除事件监听器等清理工作
    }
}

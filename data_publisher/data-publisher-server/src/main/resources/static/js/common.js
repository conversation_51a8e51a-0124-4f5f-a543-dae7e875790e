var StringUtil = {
	/**
	 * 截取字符串
	 * @param str
	 * @param first
	 * @param last
	 */
	ipRegular: /\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/,
	substringForLong : function(str, first, last){
		if(str != undefined && str != null){
			if(str.length > 0 && str.length > last && first < last){
				return str.substring(first, last) + "...";
			}else{
				return str;
			}
		}
		return "";
	},
	show : function(str, defaultStr){
		if(str != undefined && str != null && (str + "") != ""){
			return str;
		}
		
		if(defaultStr != undefined && defaultStr != null && defaultStr != ""){
			return defaultStr;
		}
		return "";
	},
	appends : function(str, appendStr, splitStr){
		if (str.length > 0) {
			str += splitStr + appendStr;
		} else {
			str = appendStr;
		}
		return str;
	},
	deletes : function(str, deleteStr, splitStr){
		if (str.length > 0) {
			var ret = '';
			var st = str.split(splitStr);
			for (var i=0; i<st.length; i++) {
				if (st[i] != deleteStr) {
					ret += st[i];
					ret += splitStr;
				}
			}
			return ret.substring(0, ret.length-1);
		}
		return str;
	},
	getUUID : function (len, radix) {
		if (!len) {
			len = 32;
		}
		if(!radix){
			radix = 1;
		}
		var chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
		var uuid = [], i;
		radix = radix || chars.length;

		for (i = 0; i < len; i++) uuid[i] = chars[0 | Math.random()*radix];

		return uuid.join('');
	},
    getIPFromStr: function (ipAddress) {
        var ip = this.ipRegular.exec(ipAddress);
        if(ip){
            return ip[0];
        }
        return null;
    }
};

var NumberUtil = {
	
	/**
	 * 保留2位小数
	 */
	get2Scale : function(num){
		
		if(num != undefined && num != null){
			return num.toFixed(2);
		}
		
		return '';
		
	},
	/**
	 * 整数
	 * @param obj
	 * @returns {Boolean}
	 */
	isInteger : function (obj){   
	    reg=/^[-+]?\d+$/;    
	    if(reg.test(obj)){   
	        return true;   
	    }else{   
	    	return false;   
	    }   
	},
	/**
	 * 非负数
	 * @param obj
	 * @returns {Boolean}
	 */
	isNonNegative : function (obj){   
		reg=/^\d+(\.\d+)?$/;    
		if(reg.test(obj)){   
			return true;   
		}else{   
			return false;   
		}   
	}
	
};

var ArrayUtil = {
		arrayClone : function (array){ 
			var a = []; 
			for(var i=0,l=array.length;i<l;i++) {
				if(array[i]){
					a.push(array[i]); 
				}
			}
			return a; 
		},
		/**
		 * 两个集合差集
		 * @param a
		 * @param b
		 * @returns {Array}
		 */
		minus : function(a, b){  
			var r = [];
			if(typeof(a) == "undefined"){
				
			} else if (typeof(b) == "undefined"){
				r = a;
			} else if(typeof(a) != "undefined" && typeof(b) != "undefined" ) { 
				 $.each(a, function(i, o){
					if($.inArray(o, b) == -1){
						r.push(o);
					}
				 });
			}
			return r;
		},

		/**
		 * 两个集合交集
		 * @param a
		 * @param b
		 * @returns {Array}
		 */
		intersect : function(a, b){  
			var r = [];
			if(typeof(a) != "undefined" && typeof(b) != "undefined" ) { 
				 $.each(a, function(i, o){
					if($.inArray(o, b) != -1){
						r.push(o);
					}
				 });
			}
			return r;
		}
};

var ObjectUtil = {
		clone : function (obj){  
			  if(typeof(obj) != 'object') return obj;  
			  if(obj == null) return obj;  
			    
			  var newObj = new Object();  
			    
			  for(var i in obj){
			     newObj[i] = clone(obj[i]);  
			  }  
			  return newObj;  
		} 
};

Date.prototype.format=function(fmt) {           
    var o = {           
    "M+" : this.getMonth()+1, //月份           
    "d+" : this.getDate(), //日           
    "h+" : this.getHours()%12 == 0 ? 12 : this.getHours()%12, //小时           
    "H+" : this.getHours(), //小时           
    "m+" : this.getMinutes(), //分           
    "s+" : this.getSeconds(), //秒           
    "q+" : Math.floor((this.getMonth()+3)/3), //季度           
    "S" : this.getMilliseconds() //毫秒           
    };           
    var week = {           
    "0" : "/u65e5",           
    "1" : "/u4e00",           
    "2" : "/u4e8c",           
    "3" : "/u4e09",           
    "4" : "/u56db",           
    "5" : "/u4e94",           
    "6" : "/u516d"          
    };           
    if(/(y+)/.test(fmt)){           
        fmt=fmt.replace(RegExp.$1, (this.getFullYear()+"").substr(4 - RegExp.$1.length));           
    }           
    if(/(E+)/.test(fmt)){           
        fmt=fmt.replace(RegExp.$1, ((RegExp.$1.length>1) ? (RegExp.$1.length>2 ? "/u661f/u671f" : "/u5468") : "")+week[this.getDay()+""]);           
    }           
    for(var k in o){           
        if(new RegExp("("+ k +")").test(fmt)){           
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length==1) ? (o[k]) : (("00"+ o[k]).substr((""+ o[k]).length)));           
        }           
    }           
    return fmt;           
} ;        

var DateUtil = {
	
	/**
	 * 获取当前时间
	 * 返回格式：yyyy-MM-dd hh:mm:ss
	 */
	getCurrentlyDate : function(){
		return DateUtil.format(new Date());
	},
	/**
	 * 获取当天零点
	 */
	getCurrentlyZero : function(){
		var date = new Date();
		var year = date.getFullYear();
		var month = date.getMonth() + 1;
		var day = date.getDate();
		return year + "-" + month + "-" + day + " 00:00:00";
	},
	/**
	 * 获取当天最后一刻
	 */
	getCurrentlyUltimate : function(){
		var date = new Date();
		var year = date.getFullYear();
		var month = date.getMonth() + 1;
		var day = date.getDate();
		return year + "-" + month + "-" + day + " 23:59:59";
	},
	/**
	 * 添加一段时间
	 * strDate : yyyy-MM-dd hh:mm:ss 格式的字符串
	 * millisecond : 增加的毫秒数（正负数）
	 */
	addSpacingDate : function(strDate, millisecond){
		strDate = strDate.replace(/-/g,"/");
		var date = new Date(strDate);
		
		date.setTime(date.getTime() + (millisecond));
		
		var year = date.getFullYear();
		var month = date.getMonth() + 1;
		var day = date.getDate();
		
		var hours = date.getHours();
		var minutes = date.getMinutes();
		var seconds = date.getSeconds();
		
		month = month < 10 ? ("0" + month) : month;
		day = day < 10 ? ("0" + day) : day;
		
		hours = hours < 10 ? ("0" + hours) : hours;
		minutes = minutes < 10 ? ("0" + minutes) : minutes;
		seconds = seconds < 10 ? ("0" + seconds) : seconds;
		
		return year + "-" + month + "-" + day + " " + hours + ":" + minutes + ":" + seconds;
		
	},
	/**
	 * 加减月份
	 * strDate : yyyy-MM-dd hh:mm:ss 格式的字符串
	 * month : 增加的月份数（正负数）
	 */
	addMonth : function(strDate, toAddMonth){
		strDate = strDate.replace(/-/g,"/");
		var date = new Date(strDate);

		var year = date.getFullYear();
		var month = date.getMonth() + 1 + toAddMonth;
		var day = date.getDate();

		var hours = date.getHours();
		var minutes = date.getMinutes();
		var seconds = date.getSeconds();

		while (month > 12) {
			year++;
			month -= 12;
		}

		while (month < 1){
			year--;
			month += 12;
		}

		month = month < 10 ? ("0" + month) : month;
		day = day < 10 ? ("0" + day) : day;

		hours = hours < 10 ? ("0" + hours) : hours;
		minutes = minutes < 10 ? ("0" + minutes) : minutes;
		seconds = seconds < 10 ? ("0" + seconds) : seconds;

		return year + "-" + month + "-" + day + " " + hours + ":" + minutes + ":" + seconds;
	},
	formatLongToDate : function(time){
        var datetime = new Date();
        datetime.setTime(time);
        return DateUtil.format(datetime);
	},
	/**
	 * 格式时间
	 * date : 数据类型为Date
	 */
	format : function(date){
		var year = date.getFullYear();
		var month = date.getMonth() + 1;
		var day = date.getDate();
		
		var hours = date.getHours();
		var minutes = date.getMinutes();
		var seconds = date.getSeconds();
		
		month = month < 10 ? ("0" + month) : month;
		day = day < 10 ? ("0" + day) : day;
		
		hours = hours < 10 ? ("0" + hours) : hours;
		minutes = minutes < 10 ? ("0" + minutes) : minutes;
		seconds = seconds < 10 ? ("0" + seconds) : seconds;
		
		return year + "-" + month + "-" + day + " " + hours + ":" + minutes + ":" + seconds;
	},
	/**
	 * 获取毫秒数
	 */
	getStrDateTime : function(strDate) {
		if (strDate != undefined && strDate != null && strDate != '') {
			strDate = strDate.replace(/-/g,"/");
			var date = new Date(strDate);
			return date.getTime();
		}
		return null; 
	},
    /**
     * 获取毫秒数
     */
    strDateToLong : function(strDate) {
        if (strDate != undefined && strDate != null && strDate != '') {
            strDate = strDate.replace(/-/g,"/");
            var date = new Date(strDate);
            return date.getTime();
        }
        return null;
    },
	formatLayDate : function(layDate){
		return layDate.year + "-" + layDate.month + "-" + layDate.date + " "
			+ layDate.hours + ":" + layDate.minutes + ":" + layDate.seconds;
	},
	/**
	 * 日期比较大小
	 * compareDateString大于dateString，返回1；
	 * 等于返回0；
	 * compareDateString小于dateString，返回-1
	 * @param dateString 日期
	 * @param compareDateString 比较的日期
	 */
	dateCompare : function(compareDateString, dateString){
		if(!dateString){
			dateString = DateUtil.format(new Date());
		}
		if(!compareDateString){
			return;
		}
		var dateTime = DateUtil.getStrDateTime(dateString);
		var compareDateTime = DateUtil.getStrDateTime(compareDateString);
		if(compareDateTime > dateTime){
			return 1;
		}else if(compareDateTime == dateTime){
			return 0;
		}else{
			return -1;
		}
	},
	/**
	 * 判断日期是否在区间内，在区间内返回true，否返回false
	 * @param dateString 日期字符串
	 * @param startDateString 区间开始日期字符串
	 * @param endDateString 区间结束日期字符串
	 * @returns {Number}
	 */
	isDateBetween: function (startDateString, endDateString, dateString){
		if(!dateString){
			dateString = DateUtil.format(new Date());
		}
		if(!startDateString){
			return;
		}
		if(!endDateString){
			return;
		}
		var flag = false;
		var startFlag = (DateUtil.dateCompare(startDateString, dateString) <= 0);
		var endFlag = (DateUtil.dateCompare(endDateString, dateString) >= 0);
		if(startFlag && endFlag){
			flag = true;
		}
		return flag;
	}
};


var CheckBox = {
		checkAllTh : function (th){
			$(th).find('#checkAll').click();
		},
		checkAll : function (input){  
			var $tbr = $('table tbody tr');
			$tbr.find('#check').prop('checked',$(input).prop('checked'));  
		    if ($(input).prop('checked')) {  
		        $tbr.find('input').parent().parent().addClass('itemChecked');  
		    } else{  
		        $tbr.find('input').parent().parent().removeClass('itemChecked');  
		    }
		    /*阻止向上冒泡，以防再次触发点击操作*/  
		    event.stopPropagation();
		},
		checkItem : function(input){
			 $(input).parent().parent().toggleClass('itemChecked');
			 var $checkAll = $('#checkAll');
			 var $tbr = $('#table tbody tr');
		     $checkAll.prop('checked',$tbr.find('#check:checked').length == $tbr.find('#check').length ? true : false);
		     /*阻止向上冒泡，以防再次触发点击操作*/  
		     event.stopPropagation();
		},    
		checkTd : function (td){
			$(td).find('input').click();
		}    
}

Array.prototype.remove=function(dx) { 
  if(isNaN(dx)||dx>this.length){return false;} 
  for(var i=0,n=0;i<this.length;i++) { 
    if(this[i]!=this[dx]) { 
      this[n++]=this[i] 
    } 
  } 
  this.length-=1 
} 


<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>产品数据发布平台</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="plugin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="plugin/layui/css/admin.css" media="all">
  <link rel="stylesheet" href="plugin/layui/css/login.css" media="all">
  <style type="text/css">
    .body-background {
      background:  url(img/bg.png) no-repeat center,linear-gradient(#3571af, #c3c9d0);

    }
    .layadmin-user-login-body {
      background-color: #f3f2f2;
    }
  </style>
</head>
<body class="body-background" >

  <div class="layadmin-user-login layadmin-user-display-show" id="LAY-user-login" style="display: none;">

    <div class="layadmin-user-login-main" id="login">
      <div class="layadmin-user-login-box layadmin-user-login-header">
        <h2>产品数据发布平台</h2>
        
      </div>
      <div class="layadmin-user-login-box layadmin-user-login-body layui-form">
        <div class="layui-form-item">
          <label class="layadmin-user-login-icon layui-icon layui-icon-username" for="LAY-user-login-username"></label>
          <input type="text" name="username" id="LAY-user-login-username" lay-verify="required" placeholder="用户名" class="layui-input" v-model="username">
        </div>
        <div class="layui-form-item">
          <label class="layadmin-user-login-icon layui-icon layui-icon-password" for="LAY-user-login-password"></label>
          <input type="password" name="password" id="LAY-user-login-password" lay-verify="required" placeholder="密码" class="layui-input" v-model="password">
        </div>
        
        <div class="layui-form-item">
          <button @click="userLogin()" class="layui-btn layui-btn-fluid" lay-submit lay-filter="LAY-user-login-submit">登 入</button>
        </div>
        
      </div>
    </div>
    
    <div class="layui-trans layadmin-user-login-footer">
      <p>© 2018 产品数据发布平台</p>
    </div>
  </div>
  <script src="plugin/layui/layui.js"></script>
  <script src="plugin/vue/vue.min.js"></script>
  <script src="plugin/vue/vue-resource.js"></script>
  <script>
      Vue.http.headers.common['Authorization'] = 'Basic YW5kcm9pZDphbmRyb2lk';

      var login = new Vue({
          el: '#login',
          data: {
              username:"",
              password:""
          },
          mounted: function () {
          },
          methods:{
              userLogin: function () {
                  var _this = this;
                  var url = "/oauth/token?grant_type=password&username="
                      + _this.username
                      + '&password='
                      + _this.password;

                  this.$http.post(url).then(function(res){
                      var access_token = res.data.access_token;
                      if(access_token){
                          localStorage.removeItem("token");
                          window.location.href = "page/index";
                          localStorage.setItem("token", access_token);
                      } else {
                          alert('账号或密码错误');
                      }
                  },function(){
                      alert('账号或密码错误');
                  });
              }
          }
      });
  </script>
</body>
</html>
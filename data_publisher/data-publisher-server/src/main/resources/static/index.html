<!DOCTYPE html>
<html >
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <title>产品数据发布平台</title>
  <link rel="stylesheet" href="../plugin/layui/css/layui.css">
</head>
<body class="layui-layout-body">
<div class="layui-layout layui-layout-admin">
  <div class="layui-header">
    <div class="layui-logo"><img class="headLogo" src="../img/logo.png"/></div>
    <!-- 头部区域（可配合layui已有的水平导航） -->
    <ul class="layui-nav layui-layout-right">
      <li class="layui-nav-item">
        <a href="javascript:;">
          <img src="../img/admin.png" class="layui-nav-img">
          admin
        </a>
      </li>
      <li class="layui-nav-item"><a href="../login" >退出</a></li>
    </ul>
  </div>

  <div class="layui-side layui-bg-black">
    <div class="layui-side-scroll" id="leftMenu" v-cloak>
      <!-- 左侧导航区域（可配合layui已有的垂直导航） -->
      <ul class="layui-nav layui-nav-tree"  lay-filter="test">
        <li v-for="(parent, index) in menuList" v-bind:class="[index==0 ? 'layui-nav-item layui-nav-itemed' : 'layui-nav-item', '']">
          <a href="javascript:;" v-cloak>{{parent.parentName}}</a>
          <dl v-if="parent.parentSide" class="layui-nav-child">
            <dd v-for="child in parent.parentSide"><a :href="child.childPath" target="main-frame" v-cloak>{{child.childName}}</a></dd>
          </dl>
        </li>
      </ul>
    </div>
  </div>

  <div class="layui-body" style="bottom:0px;">
    <!-- 内容主体区域 -->
    <iframe src="/page/batchInfo" width="100%" height="99%" name="main-frame" class="layadmin-iframe"></iframe>
  </div>
</div>
<script src="../plugin/layui/layui.js"></script>
<script type="text/javascript" src="../plugin/vue/vue.min.js"></script>
<script type="text/javascript" src="../plugin/vue/vue-resource.js"></script>
<script>
  //JavaScript代码区域
  layui.use('element', function(){
    var element = layui.element;

  });

  Vue.http.headers.common['Authorization'] = 'Bearer ' + localStorage.token;
  var leftMenu = new Vue({
    el: '#leftMenu',
    data: {
      menuList: []
    },
    mounted: function () {
      this.listMenu();
    },
    methods:{
      listMenu: function () {
        var _this = this;
        this.$http.get('/menu?menu').then(function(res){
          var data = res.data;
          if(data.status == "200"){
            _this.menuList = res.data.result.menu;
          } else {
            layer.msg(res.data.message);
          }
        });
        /*  this.menuList =
         [{
         "parentName": "客户端管理",
         "parentSide": [{
         "childName": "用户管理",
         "childPath": "/page/user"
         }, {
         "childName": "客户端信息管理",
         "childPath": "/page/client"
         }, {
         "childName": "客户端表管理",
         "childPath": "/page/clientTable"
         }, {
         "childName": "表字段管理",
         "childPath": "/page/clientTableField"
         }, {
         "childName": "客户端地址管理",
         "childPath": "/page/clientUrlMp"
         }, {
         "childName": "数据发布管理",
         "childPath": "/page/sendData"
         }]
         },
         {
         "parentName": "机构管理",
         "parentSide": [{
         "childName": "机构管理",
         "childPath": "/page/org"
         },{
         "childName": "客户端机构管理",
         "childPath": "/page/clientOrg"
         }, {
         "childName": "客户端机构字段对应管理",
         "childPath": "/page/clientTableFieldOrg"
         }]
         }];*/
      }
    }
  });
</script>
</body>
</html>
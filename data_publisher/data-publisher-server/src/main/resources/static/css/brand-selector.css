/* 品牌选择器样式 */
.brand-selector {
    position: relative;
    width: 100%;
}

.brand-selector-main {
    position: relative;
    cursor: pointer;
}

.brand-selector-input {
    width: 100%;
    padding: 8px 30px 8px 10px;
    border: 1px solid #e6e6e6;
    border-radius: 2px;
    font-size: 14px;
    line-height: 20px;
    background-color: #fff;
    cursor: pointer;
    transition: border-color 0.3s;
    box-sizing: border-box;
    position: relative;
    z-index: 1;
}

.brand-selector-input:focus {
    border-color: #009688;
    outline: none;
}

.brand-selector-arrow {
    position: absolute;
    right: 10px;
    top: 12px;
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #c2c2c2;
    transition: transform 0.3s;
    pointer-events: none;
    z-index: 2;
}

.brand-selector.open .brand-selector-arrow {
    transform: rotate(180deg);
}

.brand-selector-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    border: 1px solid #e6e6e6;
    border-top: none;
    max-height: 300px;
    overflow: hidden;
    z-index: 1001;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    border-radius: 0 0 2px 2px;
    box-sizing: border-box;
}

.brand-selector-search {
    padding: 8px;
    border-bottom: 1px solid #e6e6e6;
}

.brand-selector-search input {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #e6e6e6;
    border-radius: 2px;
    font-size: 12px;
}

.brand-selector-list {
    max-height: 200px;
    overflow-y: auto;
}

.brand-selector-item {
    padding: 8px 10px;
    cursor: pointer;
    border-bottom: 1px solid #f5f5f5;
    font-size: 12px;
    display: flex;
    align-items: center;
}

.brand-selector-item:hover {
    background-color: #f5f5f5;
}

.brand-selector-item.selected {
    background-color: #e8f4f8;
    color: #009688;
}

.brand-selector-checkbox {
    margin-right: 8px;
    width: 14px;
    height: 14px;
}

.brand-selector-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.brand-selector-loading {
    padding: 20px;
    text-align: center;
    color: #999;
    font-size: 12px;
}

.brand-selector-empty {
    padding: 20px;
    text-align: center;
    color: #999;
    font-size: 12px;
}



.brand-selector-selected-tags {
    margin-top: 5px;
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    min-height: 0;
}

.brand-selector-tag {
    display: inline-flex;
    align-items: center;
    padding: 2px 6px;
    background-color: #e8f4f8;
    color: #009688;
    border-radius: 2px;
    font-size: 11px;
    max-width: 120px;
}

.brand-selector-tag-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.brand-selector-tag-close {
    margin-left: 4px;
    cursor: pointer;
    font-weight: bold;
    color: #666;
}

.brand-selector-tag-close:hover {
    color: #009688;
}

/* 滚动条样式 */
.brand-selector-list::-webkit-scrollbar {
    width: 6px;
}

.brand-selector-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.brand-selector-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.brand-selector-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

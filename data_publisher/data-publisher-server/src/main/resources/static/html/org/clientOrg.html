<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<title>layui</title>
	<meta name="renderer" content="webkit">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<link rel="stylesheet" href="../../plugin/layui/css/layui.css">
	<link rel="stylesheet" href="../../plugin/layui/css/admin.css">
	<link rel="stylesheet" href="../../plugin/layui/css/admin.css">

	<!-- 注意：如果你直接复制所有代码到本地，上述css路径需要改成你本地的 -->
</head>
<body>
<div class="layui-fluid" id="clientOrg">
	<div class="layui-card">
		<div class="layui-card-header">客户端机构管理</div>
		<div class="layui-card-body layui-row layui-col-space10">
			<div class="layui-col-xs3">
				<label class="layui-form-label">客户端名称</label>
				<div class="layui-input-block search-select" id="user-select">
					<select  v-model="searchData.clientCode">
						<option value="" >请选择</option>
						<option v-cloak v-for="client  in clientList"  :value="client.code" >{{client.name}}</option>
					</select>
				</div>
			</div>
			<div class="layui-col-xs3">
				<label class="layui-form-label">源机构名称</label>
				<div class="layui-input-block search-select" id="user-select">
					<select  v-model="searchData.baseOrgCode">
						<option value="" >请选择</option>
						<option v-cloak v-for="org  in orgList"  :value="org.code" >{{org.name}}</option>
					</select>
				</div>
			</div>
			<div class="layui-col-xs3">
				<label class="layui-form-label" style="width: 90px">目标机构编码</label>
				<div class="layui-input-block " style="margin-left: 120px;">
					<input type="text" name="title" lay-verify="title" autocomplete="off" placeholder="目标机构编码" class="layui-input" v-model="searchData.orgCode">
				</div>
			</div>
			<div class="layui-col-xs3">
				<button class="layui-btn layui-btn-normal" @Click="search()">查询</button>
			</div>
		</div>
	</div>

	<div class="layui-card">
		<div class="layui-card-body layui-row layui-col-space10">
			<table class="layui-table" lay-even="" lay-skin="row">
				<colgroup>
					<col width="60">
					<col width="120">
					<col width="120">
					<col width="120">
					<col width="120">
					<col width="150">
				</colgroup>
				<thead>
				<tr>
					<th>序号</th>
					<th>客户端名称</th>
					<th>源机构名称</th>
					<th>源机构编码</th>
					<th>目标机构编码</th>

					<th>操作</th>
				</tr>
				</thead>
				<tbody>
				<tr v-for="(clientOrg, index) in clientOrgList">
					<td v-cloak>{{index + 1}}</td>
					<td v-cloak>
						<div v-if="!clientOrg.edit" class="layui-table-cell" v-cloak>{{getClientName(clientOrg.clientCode)}}</div>
						<div v-else id="user-select">
							<select  v-model="clientOrg.clientCode">
								<option v-cloak v-for="client  in clientList"  :value="client.code" >{{client.name}}</option>
							</select>
						</div>
					</td>
					<td v-cloak>
						<div v-if="!clientOrg.edit" class="layui-table-cell" v-cloak>{{getOrgName(clientOrg.baseOrgCode)}}</div>
						<div v-else id="user-select">
							<select  v-model="clientOrg.baseOrgCode">
								<option v-cloak v-for="org  in orgList"  :value="org.code" >{{org.name}}</option>
							</select>
						</div>
					</td>
					<td v-cloak>
						<div class="layui-table-cell" v-cloak>{{clientOrg.baseOrgCode}}</div>
					</td>
					<td v-cloak>
						<div v-if="!clientOrg.edit" class="layui-table-cell" v-cloak>{{clientOrg.orgCode}}</div>
						<input v-else type="text" class="layui-input layui-table-edit" v-model="clientOrg.orgCode">
					</td>

					<td v-cloak>
						<div class="layui-table-cell laytable-cell-1-10">
							<a v-if="!clientOrg.edit" class="layui-btn layui-btn-xs" lay-event="edit" @Click="edit(clientOrg, index)">编辑</a>
							<a v-else class="layui-btn layui-btn-xs layui-btn-warm" lay-event="edit" @Click="save(clientOrg, index)">保存</a>
							<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del" @Click="del(clientOrg, index)">删除</a>
						</div>
					</td>
				</tr>
				<tr >
					<td v-cloak>{{clientOrgList.length + 1}}</td>
					<td v-cloak>
						<div id="user-select">
							<select  v-model="toAddClientOrg.clientCode">
								<option v-for="client  in clientList"  :value="client.code" >{{client.name}}</option>
							</select>
						</div>
					</td>
					<td v-cloak>
						<div id="user-select">
							<select  v-model="toAddClientOrg.baseOrgCode" >
								<option v-for="org  in orgList"  :value="org.code" >{{org.name}}</option>
							</select>
						</div>
					</td>
					<td v-cloak>
						<div class="layui-table-cell" >{{toAddClientOrg.baseOrgCode}}</div>
					</td>
					<td v-cloak>

						<input type="text" class="layui-input layui-table-edit" v-model="toAddClientOrg.orgCode">
					</td>
					<td v-cloak>
						<div class="layui-table-cell laytable-cell-1-10">
							<a  class="layui-btn layui-btn-xs" lay-event="edit" @Click="add(toAddClientOrg)">添加</a>
						</div>
					</td>
				</tr>
				</tbody>
			</table>
		</div>
	</div>
</div>
<script src="../../plugin/jquery/jquery-1.10.1.min.js"></script>
<script src="../../plugin/layer/layer.js"></script>
<script src="../../plugin/layui/layui.js"></script>
<script type="text/javascript" src="../../plugin/vue/vue.min.js"></script>
<script type="text/javascript" src="../../plugin/vue/vue-resource.js"></script>

<script type="text/javascript">

	Vue.http.headers.common['Authorization'] = 'Bearer ' + localStorage.token;
	var clientOrgVue = new Vue({
		el: '#clientOrg',
		data: {
			clientList: [],
			orgList: [],
			clientOrgList: [],
			searchData:{
				clientCode:"",
				baseOrgCode:"",
				orgCode:""
			},
			toAddClientOrg: {
				baseOrgCode: "",
				orgCode: "",
				clientCode:""
			},
			newClientOrg: {
				baseOrgCode: "",
				orgCode: "",
				clientCode:""
			}
		},
		mounted: function () {
			this.getClientList();
			this.getOrgList();
		},
		watch: {
			searchData: {
				handler(newValue, oldValue) {
					this.toAddClientOrg.clientCode = this.searchData.clientCode;
					this.toAddClientOrg.baseOrgCode = this.searchData.baseOrgCode;
				},
				deep: true
			}
		},
		methods:{
			getClientList: function(){
				//成功后回调
				//this.clientList = _clientList;
				var _this = this;
				_this.clientList = [];
				var loadIndex = layerLoad();
				this.$http.get('/client/query').then(function(res){
					layer.close(loadIndex);
					if(res.data.status == "200"){
						_this.clientList = res.data.result;
						if(_this.clientList && _this.clientList.length > 0){
							_this.searchData.clientCode = _this.clientList[0].code;
						}
					} else {
						layer.msg(res.data.message);
					}
				});

			},
			getOrgList: function(){
				//this.orgList = _orgList;
				var _this = this;
				_this.orgList = [];
				var loadIndex = layerLoad();
				this.$http.get('/dict?query').then(function(res){
					layer.close(loadIndex);
					if(res.data.status == "200"){
						_this.orgList = res.data.result;
						if(_this.orgList && _this.orgList.length > 0){
							_this.searchData.baseOrgCode = _this.orgList[0].code;
						}
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			search: function () {
				//成功后回调
				var _this = this;
				_this.clientOrgList = [];
				var loadIndex = layerLoad();
				this.$http.get('/orgMp/query', this.searchData).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						_this.clientOrgList = res.data.result;
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			del: function (clientOrg, index) {
				var _this = this;
				//询问框
				var layerIndex = layer.confirm('删除此客户端后，不会再更新此客户端数据', {
					btn: ['确认删除','取消'] //按钮
				}, function(){
					layer.close(layerIndex);
					var loadIndex = layerLoad();
					_this.$http.delete('/orgMp?id=' + clientOrg.id).then(function(res){
						layer.close(loadIndex);
						var data = res.data;
						if(data.status == "200"){
							_this.clientOrgList.splice(index, 1);
						} else {
							layer.msg(res.data.message);
						}
					});
				}, function(){
					layer.close(layerIndex);
				});
			},
			edit: function (clientOrg, index) {
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.put('/orgMp', clientOrg).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						clientOrg.edit = true;
						Vue.set(_this.clientOrgList, index, clientOrg);
					} else {
						layer.msg(res.data.message);
					}
				});

			},
			save: function(clientOrg, index){
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.put('/orgMp', this.trim(clientOrg)).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						clientOrg.edit = false;
						Vue.set(_this.clientOrgList, index, clientOrg);
					} else {
						layer.msg(res.data.message);
					}
				});

			},
			add: function(toAddClientOrg){
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.post('/orgMp', this.trim(toAddClientOrg)).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						toAddClientOrg = res.data.result;
						Vue.set(_this.clientOrgList, _this.clientOrgList.length, JSON.parse(JSON.stringify(toAddClientOrg)));
						_this.toAddClientOrg = JSON.parse(JSON.stringify(_this.newClientOrg));
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			getClientName: function(clientCode){
				var index = -1;
				this.clientList.forEach(function (_client, _index) {
					if (_client.code == clientCode) {
						index = _index;
						return false;
					}
				});
				if(index != -1){
					return this.clientList[index].name;
				} else {
					return clientCode;
				}
			},
			getOrgName: function(orgCode){
				var index = -1;
				this.orgList.forEach(function (_org, _index) {
					if (_org.code == orgCode) {
						index = _index;
						return false;
					}
				});
				if(index != -1){
					return this.orgList[index].name;
				} else {
					return orgCode;
				}
			},
			trim: function (data) {
				for(var key in data) {
					if(typeof data[key] == "string" && data[key] != null && data[key] != ""){
						var val = data[key].replace(/(^\s*)|(\s*$)/g, "");
						data[key]= val;
					}
				}
				return data;
			}
		}
	});

</script>

</body>
</html>
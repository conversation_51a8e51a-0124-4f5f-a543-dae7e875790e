<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<title>layui</title>
	<meta name="renderer" content="webkit">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<link rel="stylesheet" href="../../plugin/layui/css/layui.css">
	<link rel="stylesheet" href="../../plugin/layui/css/admin.css">

	<!-- 注意：如果你直接复制所有代码到本地，上述css路径需要改成你本地的 -->
</head>
<body>
<div class="layui-fluid" id="clientTableFieldOrg">
	<div class="layui-card">
		<div class="layui-card-header">客户端机构字段对应管理</div>
		<div class="layui-card-body layui-row layui-col-space10">
			<div class="layui-col-xs3">
				<label class="layui-form-label">客户端名称</label>
				<div class="layui-input-block search-select" id="user-select" >
					<select  v-model="searchData.clientCode" @change="getClientTableAndOrg()" >
						<option value="" >请选择</option>
						<option v-cloak v-for="client  in clientList"  :value="client.code" >{{client.name}}</option>
					</select>
				</div>
			</div>
			<div class="layui-col-xs3">
				<label class="layui-form-label">源表名</label>
				<div class="layui-input-block search-select" id="user-select" >
					<select  v-model="searchData.baseTableName" @change="getClientTableFieldList()" >
						<option value="" >请选择</option>
						<option v-cloak v-for="clientTable  in clientTableList"  :value="clientTable.baseTableName" >{{clientTable.baseTableName}}</option>
					</select>
				</div>
			</div>
			<div class="layui-col-xs3">
				<label class="layui-form-label">源字段名</label>
				<div class="layui-input-block search-select" id="user-select" >
					<select  v-model="searchData.baseTableField" >
						<option value="" >请选择</option>
						<option v-cloak v-for="tableField  in clientTableFieldList"  :value="tableField.baseTableField" >{{tableField.baseTableField}}</option>
					</select>
				</div>
			</div>
			<div class="layui-col-xs3">
				<button class="layui-btn layui-btn-normal" @Click="search()">查询</button>
			</div>
		</div>
		<div class="layui-card-body layui-row layui-col-space10">
			<div class="layui-col-xs3">
				<label class="layui-form-label">源机构名</label>
				<div class="layui-input-block search-select" id="user-select" >
					<select  v-model="searchData.baseOrgCode" >
						<option value="" >请选择</option>
						<option v-cloak v-for="org  in orgList"  :value="org.baseOrgCode" >{{org.orgName}}</option>
					</select>
				</div>
			</div>
			<div class="layui-col-xs3">
				<label class="layui-form-label">目标机构名</label>
				<div class="layui-input-block search-select" id="user-select" >
					<select  v-model="searchData.toBaseOrgCode" >
						<option value="" >请选择</option>
						<option v-cloak v-for="org  in orgList"  :value="org.baseOrgCode" >{{org.orgName}}</option>
					</select>
				</div>
			</div>
		</div>
	</div>

	<div class="layui-card">
		<div class="layui-card-body layui-row layui-col-space10">
			<table class="layui-table" lay-even="" lay-skin="row">
				<colgroup>
					<col width="60">
					<col width="150">
					<col width="150">
					<col width="150">
					<col width="150">
					<col width="150">
					<col width="100">
				</colgroup>
				<thead>
				<tr>
					<th>序号</th>
					<th>客户端名称</th>
					<th>源表名</th>
					<th>源字段名</th>
					<th>源机构编码</th>
					<th>目标机构编码</th>
					<th>操作</th>
				</tr>
				</thead>
				<tbody>
				<tr v-for="(clientTableFieldOrg, index) in clientTableFieldOrgList">
					<td v-cloak>{{index + 1}}</td>
					<td v-cloak>
						<div v-if="!clientTableFieldOrg.edit" class="layui-table-cell" v-cloak>{{getClientName(clientTableFieldOrg.clientCode)}}</div>
						<div v-else id="user-select">
							<select  v-model="clientTableFieldOrg.clientCode" @change="changeClientCode(clientTableFieldOrg, index)">
								<option v-cloak v-for="client  in clientList"  :value="client.code" >{{client.name}}</option>
							</select>
						</div>
					</td>
					<td v-cloak>
						<div v-if="!clientTableFieldOrg.edit" class="layui-table-cell" v-cloak>{{clientTableFieldOrg.baseTableName}}</div>
						<div v-else id="user-select">
							<select  v-model="clientTableFieldOrg.baseTableName" @change="changeClientTable(clientTableFieldOrg, index)">
								<option v-cloak v-for="clientTable  in clientTableFieldOrg.clientTableList"  :value="clientTable.baseTableName" >{{clientTable.baseTableName}}</option>
							</select>
						</div>
					</td>
					<td v-cloak>
						<div v-if="!clientTableFieldOrg.edit" class="layui-table-cell" v-cloak>{{clientTableFieldOrg.baseTableField}}</div>
						<div v-else id="user-select">
							<select  v-model="clientTableFieldOrg.baseTableField">
								<option v-cloak v-for="clientTableField  in clientTableFieldOrg.clientTableFieldList"  :value="clientTableField.baseTableField" >{{clientTableField.baseTableField}}</option>
							</select>
						</div>
					</td>
					<td v-cloak>
						<div v-if="!clientTableFieldOrg.edit" class="layui-table-cell" v-cloak>{{getOrgName(clientTableFieldOrg.baseOrgCode)}}</div>
						<div v-else id="user-select">
							<select  v-model="clientTableFieldOrg.baseOrgCode">
								<option v-cloak v-for="org  in orgList"  :value="org.baseOrgCode" >{{org.orgName}}</option>
							</select>
						</div>
					</td>
					<td v-cloak>
						<div v-if="!clientTableFieldOrg.edit" class="layui-table-cell" v-cloak>{{getOrgName(clientTableFieldOrg.toBaseOrgCode)}}</div>
						<div v-else id="user-select">
							<select  v-model="clientTableFieldOrg.toBaseOrgCode">
								<option v-cloak v-for="org  in orgList"  :value="org.baseOrgCode" >{{org.orgName}}</option>
							</select>
						</div>
					</td>

					<td v-cloak>
						<div class="layui-table-cell laytable-cell-1-10">
							<a v-if="!clientTableFieldOrg.edit" class="layui-btn layui-btn-xs" lay-event="edit" @Click="edit(clientTableFieldOrg, index)">编辑</a>
							<a v-else class="layui-btn layui-btn-xs layui-btn-warm" lay-event="edit" @Click="save(clientTableFieldOrg, index)">保存</a>
							<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del" @Click="del(clientTableFieldOrg, index)">删除</a>
						</div>
					</td>
				</tr>
				<tr >
					<td v-cloak>{{clientTableFieldOrgList.length + 1}}</td>
					<td v-cloak>
						<div id="user-select">
							<select  v-model="toAddclientTableFieldOrg.clientCode" @change="changeClientCode(toAddclientTableFieldOrg)">
								<option v-cloak v-for="client  in clientList"  :value="client.code" >{{client.name}}</option>
							</select>
						</div>
					</td>
					<td v-cloak>
						<div id="user-select">
							<select  v-model="toAddclientTableFieldOrg.baseTableName" @change="changeClientTable(toAddclientTableFieldOrg)">
								<option v-cloak v-for="clientTable  in toAddclientTableFieldOrg.clientTableList"  :value="clientTable.baseTableName" >{{clientTable.baseTableName}}</option>
							</select>
						</div>
					</td>
					<td v-cloak>
						<div id="user-select">
							<select  v-model="toAddclientTableFieldOrg.baseTableField" >
								<option v-cloak v-for="clientTableField  in toAddclientTableFieldOrg.clientTableFieldList"  :value="clientTableField.baseTableField" >{{clientTableField.baseTableField}}</option>
							</select>
						</div>
					</td>
					<td v-cloak>
						<div id="user-select">
							<select  v-model="toAddclientTableFieldOrg.baseOrgCode">
								<option v-cloak v-for="org  in orgList"  :value="org.baseOrgCode" >{{org.orgName}}</option>
							</select>
						</div>
					</td>
					<td v-cloak>
						<div id="user-select">
							<select  v-model="toAddclientTableFieldOrg.toBaseOrgCode">
								<option v-cloak v-for="org  in orgList"  :value="org.baseOrgCode" >{{org.orgName}}</option>
							</select>
						</div>
					</td>
					<td v-cloak>
						<div class="layui-table-cell laytable-cell-1-10">
							<a  class="layui-btn layui-btn-xs" lay-event="edit" @Click="add(toAddclientTableFieldOrg)">添加</a>
						</div>
					</td>
				</tr>
				</tbody>
			</table>
		</div>
	</div>
	<input class="input-box" type="hidden" ref="clientCode" th:value="${clientCode}">
	<input class="input-box" type="hidden" ref="baseTableName" th:value="${baseTableName}">
	<input class="input-box" type="hidden" ref="baseTableField" th:value="${baseTableField}">
</div>
<script src="../../plugin/jquery/jquery-1.10.1.min.js"></script>
<script src="../../plugin/layer/layer.js"></script>
<script src="../../plugin/layui/layui.js"></script>
<script type="text/javascript" src="../../plugin/vue/vue.min.js"></script>
<script type="text/javascript" src="../../plugin/vue/vue-resource.js"></script>

<script type="text/javascript">
	Vue.http.headers.common['Authorization'] = 'Bearer ' + localStorage.token;
	var clientTableFieldOrgVue = new Vue({
		el: '#clientTableFieldOrg',
		data: {
			clientList: [],
			clientTableList: [],
			clientTableFieldList: [],
			clientTableFieldOrgList: [],
			orgList: [],
			searchData:{
				clientCode:"",
				baseTableName:"",
				baseTableField:"",
				baseOrgCode:"",
				toBaseOrgCode:""
			},
			toAddclientTableFieldOrg: {
				clientCode: "",
				baseTableName: "",
				baseTableField:"",
				baseOrgCode:"",
				toBaseOrgCode:""
			},
			newclientTableFieldOrg: {
				clientCode: "",
				baseTableName: "",
				baseTableField: "",
				baseOrgCode: "",
				toBaseOrgCode: ""
			},
			linkData: {
				code: "",
				baseTableName: "",
				baseTableField:"",
			}
		},
		mounted: function () {
			this.linkData.code = this.getQueryString("code") != null ? this.getQueryString("code") : "";
			this.linkData.baseTableName = this.getQueryString("baseTableName") != null ? this.getQueryString("baseTableName") : "";
			this.linkData.baseTableField = this.getQueryString("baseTableField") != null ? this.getQueryString("baseTableField") : "";
			this.getClientList();
			if(this.$refs.clientCode.value || this.$refs.baseTableName.value || this.$refs.baseTableField.value ){
				this.searchData.clientCode = this.$refs.clientCode.value;
				this.searchData.baseTableName = this.$refs.baseTableName.value;
				this.searchData.baseTableField = this.$refs.baseTableField.value;
				this.search();
			}
		},
		/*    watch: {
		 searchData: {
		 handler(newValue, oldValue) {
		 　　　　　　this.toAddclientTableFieldOrg.clientCode = this.searchData.clientCode;
		 this.toAddclientTableFieldOrg.baseTableName = this.searchData.baseTableName;
		 this.toAddclientTableFieldOrg.baseTableField = this.searchData.baseTableField;
		 this.toAddclientTableFieldOrg.baseOrgCode = this.searchData.baseOrgCode;
		 　　　　},
		 　　　　deep: true
		 }
		 },*/
		methods:{
			getClientList: function(){
				this.clientList = [];
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.get('/client/query', this.linkData).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						_this.clientList = res.data.result;
						if(_this.clientList && _this.clientList.length > 0){
							//	if(_this.linkData.){}
							_this.searchData.clientCode = _this.clientList[0].code;
							_this.searchData.baseTableName = _this.linkData.baseTableName;
							_this.searchData.baseTableField = _this.linkData.baseTableField;

						}
						_this.getClientTableList();
						_this.getOrgList();
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			getClientTableAndOrg: function () {
				var clientCode = this.searchData.clientCode;
				this.searchData = JSON.parse(JSON.stringify(this.newclientTableFieldOrg));
				this.searchData.clientCode = clientCode;
				this.clientTableFieldOrgList = [];
				this.getClientTableList();
				this.getOrgList();
			},
			getClientTableList: function(){
				this.clientTableList = [];
				this.clientTableFieldList = [];
				var _this = this;
				if(this.searchData.clientCode) {
					var loadIndex = layerLoad();
					this.$http.get('/clientTable/query', this.searchData).then(function (res) {
						layer.close(loadIndex);
						var data = res.data;
						if (data.status == "200") {
							_this.clientTableList = res.data.result;
							if(_this.clientTableList && _this.clientTableList.length > 0){
								_this.searchData.baseTableName = _this.clientTableList[0].baseTableName;
							}
							_this.getClientTableFieldList();
						} else {
							layer.msg(res.data.message);
						}
					});
				} else {
					layer.close(loadIndex);
				}
			},
			getClientTableFieldList: function(){
				this.clientTableFieldList = [];
				var _this = this;
				if(this.searchData.baseTableName) {
					var loadIndex = layerLoad();
					this.$http.get('/clientTableFieldMp/query', this.searchData).then(function (res) {
						layer.close(loadIndex);
						if (res.data.status == "200") {
							_this.clientTableFieldList = res.data.result;
							if(_this.clientTableFieldList && _this.clientTableFieldList.length > 0){
								_this.searchData.baseTableField = _this.clientTableFieldList[0].baseTableField;
							}
						} else {
							layer.msg(res.data.message);
						}
					});
				}
			},
			getOrgList: function(){
				this.orgList = [];
				var _this = this;
				if(this.searchData.clientCode) {
					var loadIndex = layerLoad();
					this.$http.get('/orgMp?clientCode=' + this.searchData.clientCode).then(function (res) {
						layer.close(loadIndex);
						if (res.data.status == "200") {
							_this.orgList = res.data.result;
						} else {
							layer.msg(res.data.message);
						}
					});
				}
			},
			search: function () {
				//成功后回调
				this.clientTableFieldOrgList = [];
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.get('/fieldOrgMp/query', this.searchData).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						_this.clientTableFieldOrgList = res.data.result;
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			del: function (clientTableFieldOrg, index) {
				//询问框
				_this = this;
				var layerIndex = layer.confirm('确认要删除信息么？', {
					btn: ['确认删除','取消'] //按钮
				}, function(){
					layer.close(layerIndex);
					var loadIndex = layerLoad();
					_this.$http.delete('/fieldOrgMp?id=' + clientTableFieldOrg.id).then(function(res){
						layer.close(loadIndex);
						var data = res.data;
						if(data.status == "200"){
							_this.clientTableFieldOrgList.splice(index, 1);
						} else {
							layer.msg(res.data.message);
						}
					});
				}, function(){
					layer.close(layerIndex);
				});

			},
			edit: function (clientTableFieldOrg, index) {
				clientTableFieldOrg.edit = true;
				this.changeClientCode(clientTableFieldOrg, index);
				this.changeClientTable(clientTableFieldOrg, index);
				Vue.set(this.clientTableFieldOrgList, index, clientTableFieldOrg);
			},
			changeClientTable: function (clientTableFieldOrg, index) {
				clientTableFieldOrg.clientTableFieldList = [];
				var _this = this;
				var loadIndex = layerLoad();
				var queryClientTableField = {clientCode : clientTableFieldOrg.clientCode, baseTableName : clientTableFieldOrg.baseTableName };
				if(clientTableFieldOrg.baseTableName){
					this.$http.get('/clientTableFieldMp/query', queryClientTableField).then(function(res){
						layer.close(loadIndex);
						var data = res.data;
						if(data.status == "200"){
							if(typeof(index) != "undefined"){
								clientTableFieldOrg.clientTableFieldList = res.data.result;
								Vue.set(_this.clientTableFieldOrgList, index, clientTableFieldOrg);
							} else {
								_this.toAddclientTableFieldOrg.clientTableFieldList = res.data.result;
								if(_this.toAddclientTableFieldOrg.clientTableFieldList.length > 0){
									_this.toAddclientTableFieldOrg.baseTableField = _this.toAddclientTableFieldOrg.clientTableFieldList[0].baseTableField;
								}
								var _tmp = _this.toAddclientTableFieldOrg;
								_this.toAddclientTableFieldOrg = {};
								_this.toAddclientTableFieldOrg = _tmp;
							}
						} else {
							layer.msg(res.data.message);
						}
					});
				} else {
					layer.close(loadIndex);
				}

			},
			changeClientCode: function (clientTableFieldOrg, index) {
				clientTableFieldOrg.clientTableList = [];
				clientTableFieldOrg.clientTableFieldList = [];
				var _this = this;
				var loadIndex = layerLoad();
				var queryClientTable = {clientCode : clientTableFieldOrg.clientCode };
				if(clientTableFieldOrg.clientCode){
					this.$http.get('/clientTable/query', queryClientTable).then(function(res){
						layer.close(loadIndex);
						var data = res.data;
						if(data.status == "200"){
							if(typeof(index) != "undefined"){

								clientTableFieldOrg.clientTableList = res.data.result;
								Vue.set(_this.clientTableFieldOrgList, index, clientTableFieldOrg);
							} else {
								_this.toAddclientTableFieldOrg.clientTableList = res.data.result;
								/*	if(_this.toAddclientTableFieldOrg.clientTableList.length > 0){
								 console.log(_this.toAddclientTableFieldOrg.clientTableList[0]);
								 _this.toAddclientTableFieldOrg.baseTableName = _this.toAddclientTableFieldOrg.clientTableList[0].baseTableName;
								 }*/
								var _tmp = _this.toAddclientTableFieldOrg;
								_this.toAddclientTableFieldOrg = {};
								_this.toAddclientTableFieldOrg = _tmp;
							}
						} else {
							layer.msg(res.data.message);
						}
					});
				} else {
					layer.close(loadIndex);
				}

			},
			save: function(clientTableFieldOrg, index){
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.put('/fieldOrgMp', this.trim(clientTableFieldOrg)).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						clientTableFieldOrg.edit = false;
						Vue.set(this.clientTableFieldOrgList, index, clientTableFieldOrg);
					} else {
						layer.msg(res.data.message);
					}
				});

			},
			add: function(toAddclientTableFieldOrg){
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.post('/fieldOrgMp', this.trim(toAddclientTableFieldOrg)).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						toAddclientTableFieldOrg = res.data.result;
						Vue.set(this.clientTableFieldOrgList, this.clientTableFieldOrgList.length, JSON.parse(JSON.stringify(toAddclientTableFieldOrg)));
						_this.toAddclientTableFieldOrg = JSON.parse(JSON.stringify(this.newclientTableFieldOrg));
					} else {
						layer.msg(res.data.message);
					}
				});

			},
			getClientName: function(clientCode){
				var index = -1;
				this.clientList.forEach(function (_client, _index) {
					if (_client.code == clientCode) {
						index = _index;
						return false;
					}
				});
				if(index != -1){
					return this.clientList[index].name;
				} else {
					return clientCode;
				}
			},
			getOrgName: function(orgCode){
				var index = -1;
				this.orgList.forEach(function (_org, _index) {
					if (_org.code == orgCode) {
						index = _index;
						return false;
					}
				});
				if(index != -1){
					return this.orgList[index].name;
				} else {
					return orgCode;
				}
			},
			getQueryString: function  (name){
				var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
				var data = window.location.href.split("?");
				var r ;
				if(data.length > 1){
					r = data[1].match(reg);
				}
				if(r!=null) return unescape(r[2]); return null;
			},
			trim: function (data) {
				for(var key in data) {
					if(typeof data[key] == "string" && data[key] != null && data[key] != ""){
						var val = data[key].replace(/(^\s*)|(\s*$)/g, "");
						data[key]= val;
					}
				}
				return data;
			}
		}
	});

</script>

</body>
</html>
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>layui</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link rel="stylesheet" href="../../plugin/layui/css/layui.css">
  <link rel="stylesheet" href="../../plugin/layui/css/admin.css">
  <link rel="stylesheet" href="../../plugin/layui/css/admin.css">

  <!-- 注意：如果你直接复制所有代码到本地，上述css路径需要改成你本地的 -->
</head>
<body>
<div class="layui-fluid" id="org">
	<div class="layui-card layui-form">
		<div class="layui-card-header">机构管理</div>
		<div class="layui-card-body layui-row layui-col-space10">
		    <div class="layui-col-xs3">
		        <label class="layui-form-label">机构名称</label>
			    <div class="layui-input-block">
			      <input type="text" name="title" lay-verify="title" autocomplete="off" placeholder="机构名称" class="layui-input" v-model="searchData.name">
			    </div>
	  		</div>
		    <div class="layui-col-xs3">
		      	<label class="layui-form-label">机构编码</label>
			    <div class="layui-input-block">
			      <input type="text" name="title" lay-verify="title" autocomplete="off" placeholder="机构编码" class="layui-input" v-model="searchData.code">
			    </div>
		    </div>
		    <div class="layui-col-xs1">
		    	
		    </div>
		    <div class="layui-col-xs3">
		    	<button class="layui-btn layui-btn-normal" @Click="search()">查询</button>
		    </div>
		</div>
    </div>

	<div class="layui-card layui-form">	
		<div class="layui-card-body layui-row layui-col-space10">	 
			<table class="layui-table" lay-even="" lay-skin="row">
			  <colgroup>
				<col width="60">
				<col width="120">
				<col width="120">
				<col width="150">
			  </colgroup>
			  <thead>
			    <tr>
					<th>序号</th>
					<th>机构名称</th>
					<th>机构编码</th>
					<th>操作</th>
			    </tr> 
			  </thead>
			  <tbody>
			    <tr v-for="(org, index) in orgList">
				  	<td v-cloak>{{index + 1}}</td>
					<td v-cloak>
						<div v-if="!org.edit" class="layui-table-cell">{{org.name}}</div>
						<input v-else type="text" class="layui-input layui-table-edit" v-model="org.name">
					</td>
					<td v-cloak>
						<div v-if="!org.edit" class="layui-table-cell">{{org.code}}</div>
						<input v-else type="text" class="layui-input layui-table-edit" v-model="org.code">
					</td>
					<td v-cloak>
						<div class="layui-table-cell laytable-cell-1-10"> 
							<a v-if="!org.edit" class="layui-btn layui-btn-xs" lay-event="edit" @Click="edit(org, index)">编辑</a>
							<a v-else class="layui-btn layui-btn-xs layui-btn-warm" lay-event="edit" @Click="save(org, index)">保存</a> 
							<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del" @Click="del(org, index)">删除</a> 
						</div>
					  </td>		
			    </tr>
			    <tr>
				  	<td v-cloak>{{orgList.length + 1}}</td>
					<td v-cloak> 
					  	
					  	<input type="text" class="layui-input layui-table-edit" v-model="toAddOrg.name">
					</td>
					<td v-cloak>
						
						<input type="text" class="layui-input layui-table-edit" v-model="toAddOrg.code">
					</td>
					
					<td v-cloak>
						<div class="layui-table-cell laytable-cell-1-10"> 
							<a  class="layui-btn layui-btn-xs" lay-event="edit" @Click="add(toAddOrg)">添加</a> 
						</div>
					  </td>		
			    </tr>
			  </tbody>
			</table>  
		</div>
	</div>	
</div> 
<script src="../../plugin/jquery/jquery-1.10.1.min.js"></script>
<script src="../../plugin/layer/layer.js"></script>
<script src="../../plugin/layui/layui.js"></script>
<script type="text/javascript" src="../../plugin/vue/vue.min.js"></script>
<script type="text/javascript" src="../../plugin/vue/vue-resource.js"></script>
 
<script type="text/javascript">

Vue.http.headers.common['Authorization'] = 'Bearer ' + localStorage.token;
var orgVue = new Vue({
    el: '#org',
    data: {
        orgList: [],
        searchData:{
            name:"",
            code:""
        },
        toAddOrg: {},
        newOrg: {
			"code": "",
			"name": ""
        }
    },
    mounted: function () {
        this.search();
    },
    methods:{
        search: function () {
        	//成功后回调
			var _this = this;
			_this.orgList = [];
			var loadIndex = layerLoad();
			this.$http.get('/dict?query', this.searchData).then(function(res){
				layer.close(loadIndex);
				if(res.data.status == "200"){
					_this.orgList = res.data.result;
				} else {
					layer.msg(res.data.message);
				}
			});
        },
        del: function (org, index) {
			var _this = this;
        	//询问框
			var layerIndex = layer.confirm('删除此省份编码后，不会再更新此省份数据', {
			  btn: ['确认删除','取消'] //按钮
			}, function(){
				layer.close(layerIndex);
				var loadIndex = layerLoad();
				_this.$http.delete('/dict?id=' + org.id).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						_this.orgList.splice(index, 1);
					} else {
						layer.msg(res.data.message);
					}
				});
			}, function(){
			  	layer.close(layerIndex);
			});
        	
        },
        edit: function (org, index) {
			var _this = this;
			var loadIndex = layerLoad();
			this.$http.put('/dict', org).then(function(res){
				layer.close(loadIndex);
				var data = res.data;
				if(data.status == "200"){
					org.edit = true;
					Vue.set(this.orgList, index, org);
				} else {
					layer.msg(res.data.message);
				}
			});

        },
        save: function(org, index){
			var _this = this;
			var loadIndex = layerLoad();
			this.$http.put('/dict', this.trim(org)).then(function(res){
				layer.close(loadIndex);
				var data = res.data;
				if(data.status == "200"){
					org.edit = false;
					Vue.set(_this.orgList, index, org);
				} else {
					layer.msg(res.data.message);
				}
			});
        },
        add: function(toAddOrg){
			var _this = this;
			var loadIndex = layerLoad();
			toAddOrg.type = 'orgCode';
			this.$http.post('/dict', this.trim(toAddOrg)).then(function(res){
				layer.close(loadIndex);
				var data = res.data;
				if(data.status == "200"){
					toAddOrg = res.data.result;
					Vue.set(_this.orgList, _this.orgList.length, JSON.parse(JSON.stringify(toAddOrg)));
					_this.toAddOrg = JSON.parse(JSON.stringify(_this.newOrg));
				} else {
					layer.msg(res.data.message);
				}
			});
        },
		trim: function (data) {
			for(var key in data) {
				if(typeof data[key] == "string" && data[key] != null && data[key] != ""){
					var val = data[key].replace(/(^\s*)|(\s*$)/g, "");
					data[key]= val;
				}
			}
			return data;
		}
    }
});

</script>

</body>
</html>
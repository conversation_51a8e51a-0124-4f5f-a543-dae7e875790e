<!DOCTYPE html>
<html>
<head>
	<base href="../../../" />
	<meta charset="utf-8">
	<title>layui</title>
	<meta name="renderer" content="webkit">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<link rel="stylesheet" href="/plugin/layui/css/layui.css">
	<link rel="stylesheet" href="/plugin/layui/css/admin.css">
	<link rel="stylesheet" href="/plugin/layui/css/admin.css">
	<style type="text/css">
		.red {
			color : red
		}
		.yellow {
			color : yellow
		}
		.green {
			color : green
		}
	</style>
	<!-- 注意：如果你直接复制所有代码到本地，上述css路径需要改成你本地的 -->
</head>
<body>
<div class="layui-fluid" id="user">
	<div class="layui-card layui-form">
		<div class="layui-card-header">客户端用户管理</div>
		<div class="layui-card-body layui-row layui-col-space10">
			<div class="layui-col-xs3">
				<label class="layui-form-label">用户名称</label>
				<div class="layui-input-block">
					<input type="text" name="title" lay-verify="title" autocomplete="off" placeholder="用户名称" class="layui-input" v-model="searchData.username">
				</div>
			</div>
			<!--    <div class="layui-col-xs3">
                      <label class="layui-form-label">客户端编码</label>
                    <div class="layui-input-block">
                      <input type="text" name="title" lay-verify="title" autocomplete="off" placeholder="客户端编码" class="layui-input" v-model="searchData.code">
                    </div>
                </div>-->
			<!--	<div class="layui-col-xs3">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-block">
                        <select  v-model="searchData.status" lay-filter="searchDataStatus">
                            <option value="" >请选择</option>
                            <option v-cloak v-for="status in statusList"  :value="status.code" >{{status.code}}</option>
                        </select>
                    </div>
                </div>-->
			<div class="layui-col-xs1">
			</div>
			<div class="layui-col-xs2">
				<button class="layui-btn layui-btn-normal" @Click="search()">查询</button>
			</div>
		</div>
	</div>

	<div class="layui-card">
		<div class="layui-card-body layui-row layui-col-space10 ">
			<table class="layui-table" lay-even="" lay-skin="row">
				<colgroup>
					<col width="60">
					<col width="100">
					<col width="120">
					<col width="120">
					<col width="120">
					<col width="150">
				</colgroup>
				<thead>
				<tr>
					<th>序号</th>
					<th>用户名称</th>
					<th>用户密码</th>
					<th>email</th>
					<!--<th>imageUrl</th>-->
					<th>操作</th>
				</tr>
				</thead>
				<tbody>
				<tr v-for="(user, index) in userList">
					<td v-cloak>{{index + 1}}</td>
					<td v-cloak>
						<div v-cloak  class="layui-table-cell">{{user.username}}</div>
						<!--<input v-else type="text" class="layui-input layui-table-edit" v-model="user.username">  v-if="!user.edit"-->
					</td>
					<td v-cloak>
						<div v-cloak v-if="!user.edit" class="layui-table-cell">******</div>
						<input v-else type="text" class="layui-input layui-table-edit" v-model="user.password">
					</td>
					<td v-cloak>
						<div v-cloak v-if="!user.edit" class="layui-table-cell">{{user.email}}</div>
						<input v-else type="text" class="layui-input layui-table-edit" v-model="user.email">
					</td>
					<!--	<td v-cloak>
                            <div v-cloak v-if="!user.edit" class="layui-table-cell">******</div>
                            <input v-else type="text" class="layui-input layui-table-edit" v-model="user.imageUrl">
                        </td>-->
					<td v-cloak>
						<div class="layui-table-cell laytable-cell-1-10">
							<a v-if="!user.edit" class="layui-btn layui-btn-xs" lay-event="edit" @Click="edit(user, index)">编辑</a>
							<a v-else class="layui-btn layui-btn-xs layui-btn-warm" lay-event="edit" @Click="save(user, index)">保存</a>
							<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del" @Click="del(user, index)">删除</a>
						</div>
					</td>
				</tr>
				<tr>
					<td v-cloak>{{userList.length + 1}}</td>
					<td v-cloak>

						<input type="text" class="layui-input layui-table-edit" v-model="toAddUser.username">
					</td>
					<td v-cloak>

						<input type="text" class="layui-input layui-table-edit" v-model="toAddUser.password">
					</td>
					<td v-cloak>

						<input type="text" class="layui-input layui-table-edit" v-model="toAddUser.email">
					</td>
					<!--<td v-cloak>
						
						<input type="text" class="layui-input layui-table-edit" v-model="toAddUser.imageUrl">
					</td>-->
					<td v-cloak>
						<div class="layui-table-cell laytable-cell-1-10">
							<a  class="layui-btn layui-btn-xs" lay-event="edit" @Click="add(toAddUser)">添加</a>
						</div>
					</td>
				</tr>
				</tbody>
			</table>
		</div>
	</div>
</div>
<script src="/plugin/jquery/jquery-1.10.1.min.js"></script>
<script src="/plugin/layer/layer.js"></script>
<script src="/plugin/layui/layui.js"></script>
<script type="text/javascript" src="/plugin/vue/vue.min.js"></script>
<script type="text/javascript" src="/plugin/vue/vue-resource.js"></script>

<script type="text/javascript">
	layui.use('form', function(){
		var form = layui.form;
		form.on('select(searchDataStatus)', function(data){
			clientVue.searchData.status = data.value;
		});
	});
	Vue.http.headers.common['Authorization'] = 'Bearer ' + localStorage.token;
	var userVue = new Vue({
		el: '#user',
		data: {
			userList: [],
			searchData:{
				username: ""
			},
			toAddUser: {
				"username": "",
				"password": "",
				"email": ""/*,
				 "imageUrl": ""*/
			},
			newUser: {
				"username": "",
				"password": "",
				"email": "",/*
				 "imageUrl": ""*/
			}
		},
		mounted: function () {
			this.search();
			this.toAddUser = JSON.parse(JSON.stringify(this.newUser));
		},
		methods:{
			search: function () {
				this.userList = [];
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.get('/user/query', this.searchData).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						_this.userList = res.data.result;
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			del: function (user, index) {
				var _this = this;
				//询问框
				var layerIndex = layer.confirm('删除此用戶后，不会再更新此用戶数据', {
					btn: ['确认删除','取消'] //按钮
				}, function(){
					layer.close(layerIndex);
					var loadIndex = layerLoad();
					_this.$http.delete('/user?id=' + user.id).then(function(res){
						layer.close(loadIndex);
						var data = res.data;
						if(data.status == "200"){
							_this.userList.splice(index, 1);
						} else {
							layer.msg(res.data.message);
						}
					});
				}, function(){
					layer.close(layerIndex);
				});
			},
			edit: function (user, index) {
				user.edit = true;
				Vue.set(this.userList, index, user);
			},
			save: function(user, index){
				user = this.trim(user);
				var _this = this;
				if(user.password == null || user.password == ""){
					layer.msg("请填写新密码！");
				}
				var layerIndex = layer.confirm('修改密码后，不会再更新对应用户数据', {
					btn: ['确认修改','取消'] //按钮
				}, function(){
					layer.close(layerIndex);
					var loadIndex = layerLoad();
					_this.$http.put('/user', user).then(function(res){
						layer.close(loadIndex);
						var data = res.data;
						if(data.status == "200"){
							user.edit = false;
							user.password = data.result.password;
							Vue.set(_this.userList, index, user);
						} else {
							layer.msg(res.data.message);
						}
					});
				}, function(){
					layer.close(layerIndex);
				});



			},
			add: function(toAddUser){
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.post('/user', this.trim(toAddUser)).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						toAddUser = res.data.result;
						Vue.set(_this.userList, _this.userList.length, JSON.parse(JSON.stringify(toAddUser)));
						_this.toAddUser = JSON.parse(JSON.stringify(_this.newUser));
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			goSendedData: function(client){
				window.location.href = "/page/sendData?clientCode=" + client.code;
			},
			trim: function (data) {
				for(var key in data) {
					if(typeof data[key] == "string" && data[key] != null && data[key] != ""){
						var val = data[key].replace(/(^\s*)|(\s*$)/g, "");
						data[key]= val;
					}
				}
				return data;
			}
		}
	});
</script>

</body>
</html>
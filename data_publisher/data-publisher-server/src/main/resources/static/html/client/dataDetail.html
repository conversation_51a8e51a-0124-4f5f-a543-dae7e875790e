<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>layui</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link rel="stylesheet" href="../../plugin/layui/css/layui.css">
  <link rel="stylesheet" href="../../plugin/layui/css/admin.css">
  <style type="text/css">
    .red {
  		color : red
  	}
  	.yellow {
  		color : yellow
  	}
  	.green {
  		color : green
  	}
    </style>
  <!-- 注意：如果你直接复制所有代码到本地，上述css路径需要改成你本地的 -->
</head>
<body>
<div class="layui-fluid" id="dataDetail">

	<div class="layui-card" >
		<div class="layui-card-body layui-row layui-col-space10 layui-form">
			<table class="layui-table" lay-even="" lay-skin="row">
			  <colgroup>
				<col width="60">
				<col width="400">
				<col width="120">
				<col width="150">
				<col width="150">
			  </colgroup>
			  <thead>
			    <tr>
					<th>序号</th>
					<th>发送数据</th>
					<th>发送次数</th>
					<th>信息</th>
			    </tr>
			  </thead>
			  <tbody>
			    <tr v-for="(detail, index) in detailList">
				  	<td v-cloak>{{index + 1}}</td>
					<td v-cloak>
						<div class="layui-table-cell":title="JSON.stringify(detail.data)">{{JSON.stringify(detail)}}</div>
					</td>
					<td v-cloak>
						<div class="layui-table-cell">{{detail.sendTimes}}</div>
					</td>
					<td v-cloak>
						<div class="layui-table-cell" :title="detail.message">{{detail.message != null?detail.message.substring(0,50):''}}</div>
					</td>
			    </tr>
			  </tbody>
			</table>
		</div>
	</div>	
</div> 
<script src="../../plugin/jquery/jquery-1.10.1.min.js"></script>
<script src="../../plugin/layer/layer.js"></script>
<script src="../../plugin/layui/layui.js"></script>
<script type="text/javascript" src="../../plugin/vue/vue.min.js"></script>
<script type="text/javascript" src="../../plugin/vue/vue-resource.js"></script>
 
<script type="text/javascript">

layui.use('form', function(){
  var form = layui.form;
});

Vue.http.headers.common['Authorization'] = 'Bearer ' + localStorage.token;
var dataDetail = new Vue({
    el: '#dataDetail',
    data: {
        detailList: []
    },
    mounted: function () {
		var batchNo = this.getQueryString("batchNo");
		var clientCode = this.getQueryString("clientCode");
		var status = this.getQueryString("status");
		if(batchNo != null && clientCode != null && status != null){
			this.search(batchNo, clientCode, status);
		}
    },
    methods:{
        search: function (batchNo, clientCode, status) {
        	//成功后回调
			var _this = this;
			_this.detailList = [];
			var loadIndex = layerLoad();
			this.$http.get('/failData?batchNo=' + batchNo + "&clientCode=" + clientCode + "&status=" + status).then(function(res){
				layer.close(loadIndex);
				if(res.data.status == "200"){
					_this.detailList = res.data.result;
					console.log(_this.detailList);

				} else {
					layer.msg(res.data.message);
				}
			});
        },
		getQueryString: function  (name){
			var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
			var r = window.location.href.split("?")[1].match(reg);
			if(r!=null) return unescape(r[2]); return null;
		}
    }
});

</script>

</body>
</html>
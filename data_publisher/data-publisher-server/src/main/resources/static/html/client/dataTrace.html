<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>layui</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link rel="stylesheet" href="../../plugin/layui/css/layui.css">
  <link rel="stylesheet" href="../../plugin/layui/css/admin.css">
  <style type="text/css">
	  .red {
		  color: rgb(255, 87, 34) !important;
	  }
	  .red.layui-elem-quote {
		  border-left: 5px solid #FF5722;
	  }
	  .yellow {
		  color: rgb(255, 184, 0) !important;
	  }
	  .yellow.layui-elem-quote {
		  border-left: 5px solid #FFB800;
	  }
	  .green {
		  color: rgb(0, 150, 136) !important;
	  }
	  .green.layui-elem-quote {
		  border-left: 5px solid #009688;
	  }
    </style>
  <!-- 注意：如果你直接复制所有代码到本地，上述css路径需要改成你本地的 -->
</head>
<body>
<div class="layui-card" id="dataTrace">
	<div >
		<blockquote class="layui-elem-quote" v-bind:class="getStatsClass(status)">
			<div >{{getStatsStr(status)}}： {{batchNo}}</div>
		</blockquote>
		<ul class="layui-timeline" style="padding: 30px 100px;" >
			<li class="layui-timeline-item" v-for="dataTraceAgg in dataTraceAggList" >
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text"  v-bind:class="getStatsClass(dataTraceAgg.status)">
					<h3 class="layui-timeline-title" v-bind:class="getStatsClass(dataTraceAgg.status)">{{getDateString(dataTraceAgg.startTime, dataTraceAgg.endTime)}}</h3>
					<p>
						来源：{{dataTraceAgg.dataSource}}
					</p>
					<p>
						服务：{{dataTraceAgg.serviceName}}
					</p>
					<p v-if="dataTraceAgg.sendTimes>1">
						第 {{dataTraceAgg.sendTimes}} 次
					</p>
					<ul>
						<li v-if="dataTraceAgg.clientCode">客户端名称： {{dataTraceAgg.clientCode}}</li>
						<li>节点： {{dataTraceAgg.nodeName}}</li>
						<li>状态： {{getStatsStr(dataTraceAgg.status)}}</li>
						<li>数量： {{dataTraceAgg.dataNum}}条</li>
						<pre v-if="dataTraceAgg.errorMsg" class="layui-code">{{dataTraceAgg.errorMsg}}</pre>
					</ul>
				</div>
			</li>
		</ul>
	</div>
</div>
<script src="../../plugin/jquery/jquery-1.10.1.min.js"></script>
<script src="../../plugin/layer/layer.js"></script>
<script src="../../plugin/layui/layui.js"></script>
<script src="../../js/common.js"></script>
<script type="text/javascript" src="../../plugin/vue/vue.min.js"></script>
<script type="text/javascript" src="../../plugin/vue/vue-resource.js"></script>

<script type="text/javascript">

Vue.http.headers.common['Authorization'] = 'Bearer ' + localStorage.token;
var dataTrace = new Vue({
    el: '#dataTrace',
    data: {
        dataTraceAggList: [],
		batchNo: "",
		status: "",
		dataSource: "",
    },
    mounted: function () {
        this.batchNo = this.getQueryString("batchNo");
        var clientCode = this.getQueryString("clientCode");
        this.status = this.getQueryString("status");
        this.dataSource = this.getQueryString("dataSource");
        if(this.batchNo != null){
            this.search(this.batchNo, clientCode, this.dataSource);
        }
    },
    methods:{
        search: function (batchNo, clientCode, dataSource) {
            //成功后回调
            var _this = this;
            _this.dataTraceAggList = [];
            var loadIndex = layerLoad();
            this.$http.get('/dataTraceAggs?batchNo=' + batchNo + "&clientCode=" + clientCode + "&dataSource="+dataSource).then(function(res){
                layer.close(loadIndex);
                if(res.data.status == "200"){
                    _this.dataTraceAggList = res.data.result;
                    _this.dataTraceAggList.forEach(function (dataTraceAgg, _index) {
                        if (dataTraceAgg.status == '500') {
                        	console.log("--------------" + _index);
                            _this.listTraceData(dataTraceAgg, _index)
                            return false;
                        }
                    });
                } else {
                    layer.msg(res.data.message);
                }
            });
        },
        listTraceData: function (dataTraceAgg, index) {
            //成功后回调
            var _this = this;
            var searchDataTrace = {
                batchNo : dataTraceAgg.batchNo,
                clientCode : dataTraceAgg.clientCode,
                status : dataTraceAgg.status,
                nodeName : dataTraceAgg.nodeName,
				sendTimes: dataTraceAgg.sendTimes
			}

            var loadIndex = layerLoad();
            this.$http.get('/dataTraces/query', searchDataTrace).then(function(res){
                layer.close(loadIndex);
                if(res.data.status == "200"){
                    var dataTraceList = res.data.result;
                    var errorMsg = "";
                    dataTraceList.forEach(function (dataTrace, _index) {
                        errorMsg += (1+_index) + "." + dataTrace.message + "\n"
                    });
                    dataTraceAgg.errorMsg = errorMsg;
                    Vue.set(_this.dataTraceAggList, index, dataTraceAgg);
                } else {
                    layer.msg(res.data.message);
                }
            });
        },
        getDateString: function  (startTime, endTime){
            if(startTime == endTime){
                return DateUtil.formatLongToDate(startTime);
			}
            return DateUtil.formatLongToDate(startTime) + " - " + DateUtil.formatLongToDate(endTime);
        },
        getQueryString: function  (name){
            var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
            var r = window.location.href.split("?")[1].match(reg);
            if(r!=null) return unescape(r[2]); return null;
        },
        getStatsClass:function (status) {
            if(status == "500"){
                return "red";
            } else if (status == "200"){
                return "green";
            } else if (status == "101"){
                return "yellow";
            }
        },
        getStatsStr:function (status) {
            if(status == "500"){
                return "失败";
            } else if (status == "200"){
                return "成功";
            } else if (status == "101"){
                return "处理中";
            }
        }
    }
});

</script>

</body>
</html>

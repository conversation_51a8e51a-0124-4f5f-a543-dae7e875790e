<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:v-bind="http://www.w3.org/1999/xhtml"
	  xmlns:v-on="http://www.w3.org/1999/xhtml"
	  xmlns:v-on="http://www.w3.org/1999/xhtml" th:fragment="footer-pages">
<head>
	<meta charset="utf-8">
	<title>layui</title>
	<meta name="renderer" content="webkit">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<link rel="stylesheet" href="../../plugin/layui/css/layui.css">
	<link rel="stylesheet" href="../../plugin/layui/css/admin.css">
	<style type="text/css">
		.red {
			color : red
		}
		.yellow {
			color : yellow
		}
		.green {
			color : green
		}
		ul,li{
			/*margin-left: 3px;*/
			padding: 0px;
			font-size: 12px;
		}
		li{
			list-style: none
		}
		.page-bar li:first-child>a {
			margin-left: 0px
		}
		.page-bar a{
			border: 1px solid #ddd;
			text-decoration: none;
			position: relative;
			float: left;
			padding: 6px 12px;
			margin-left: -1px;
			line-height: 1.42857143;
			color: #337ab7;
			cursor: pointer
		}
		.page-bar a:hover{
			background-color: #eee;
		}
		.page-bar .active a{
			color: #fff;
			cursor: default;
			background-color: #337ab7;
			border-color: #337ab7;
		}
		.page-bar i{
			font-style:normal;
			color: #d44950;
			margin: 0px 4px;
			font-size: 12px;
		}
	</style>
	<!-- 注意：如果你直接复制所有代码到本地，上述css路径需要改成你本地的 -->
</head>
<body>
<div class="layui-fluid" id="sendData">
	<div class="layui-card">
		<div class="layui-card-header">客户端发布管理</div>
		<div class="layui-card-body layui-row layui-col-space10">
			<div class="layui-col-xs3">
				<label class="layui-form-label">批次号</label>
				<div class="layui-input-block">
					<input type="text" name="title" lay-verify="title" autocomplete="off" placeholder="批次号" class="layui-input" v-model="searchData.batchNo">
				</div>
			</div>
			<div class="layui-col-xs3">
				<div class="layui-input-block search-select" id="user-select">
					<select  v-model="searchData.queueName" >
						<option value="" >请选择客户端</option>
						<option v-cloak v-for="client  in clientList"  :value="client.code" >{{client.name}}</option>
					</select>
				</div>
			</div>
			<div class="layui-col-xs3">
				<div class="layui-input-block search-select" id="user-select">
					<select  v-model="searchData.status" >
						<option value="" >请选择数据状态</option>
						<option value="500">失败</option>
						<option value="200">成功</option>
						<option value="100">未处理</option>
						<option value="202">进行中</option>
					</select>
				</div>
			</div>
			<div class="layui-col-xs2">
				<button class="layui-btn layui-btn-normal" @Click="search()">查询</button>
			</div>
			<div class="layui-col-xs3">
				<label class="layui-form-label">开始时间</label>
				<div class="layui-input-block">
					<input type="date" name="title" lay-verify="title" autocomplete="off" class="layui-input" v-model="searchData.createTime">
				</div>
			</div>
			<div class="layui-col-xs3">
				<label class="layui-form-label">结束时间</label>
				<div class="layui-input-block">
					<input type="date" name="title" lay-verify="title" autocomplete="off" class="layui-input" v-model="searchData.updateTime">
				</div>
			</div>
			<div class="layui-col-xs3">
				<div class="layui-input-block search-select" id="user-select">
					<select  v-model="sendStatus" >
						<option value="" >请选择重新发送数据</option>
						<option value="500">失败</option>
						<option value="202">进行中</option>
						<option value="100">未处理</option>
					</select>
				</div>
			</div>
			<div class="layui-col-xs2">
				<button class="layui-btn layui-btn-normal" @Click="againSend()">重新发送</button>
			</div>
		</div>
	</div>

	<div class="insurance_table">
		<table class="layui-table" lay-even="" lay-skin="primary">
			<colgroup>
				<col width="70">
				<col width="60">
				<col width="120">
				<col width="100">
				<col width="60">
				<col width="150">
				<col width="60">
				<col width="150">
				<col width="130">
			</colgroup>
			<thead>
			<tr>
				<th>
					<input v-bind:checked="checked" @click="checkAll()" type="checkbox"  style="display: inline-block;">
				</th>
				<th>序号</th>
				<th>批次号</th>
				<th>客户端名称</th>
				<th>成功（条）</th>
				<th>失败（条）</th>
				<th>未处理（条）</th>
				<th>进行中（条）</th>
				<th>发送时间</th>
				<th>处理时间</th>
				<!--	<th>操作</th>-->
			</tr>
			</thead>
			<tbody>
			<tr v-for="(data, index) in limitList">
				<th>
					<input  type="checkbox" v-model='checkboxList' :value="index">
				</th>
				<td v-cloak>{{index + 1}}</td>

				<td v-cloak>
					<div class="layui-table-cell">{{data.batchNo}}</div>
				</td>
				<td v-cloak>
					<div class="layui-table-cell">{{data.clientName}}</div>
				</td>
				<td v-cloak >
					<div v-if="data.successCount" class="layui-table-cell"><a class="layui-btn layui-btn-xs" lay-event="detail" @Click="popup(data,'200')">{{data.successCount}}</a></div>
				</td>
				<td v-cloak>
					<div v-if="data.errorCount" class="layui-table-cell">
						<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="detail" @Click="popup(data,'500')">{{data.errorCount}}</a>
						<a class="layui-btn layui-btn-xs" lay-event="detail" @Click="repush(data, '500')">发送</a>
					</div>
				</td>
				<td v-cloak>
					<div v-if="data.unsendCount" class="layui-table-cell"><a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="detail" @Click="popup(data, '100')">
						{{data.unsendCount}}</a></div>
				</td>
				<td v-cloak>
					<div v-if="data.sendingCount" class="layui-table-cell"><a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="detail" @Click="popup(data, '202')">
						{{data.sendingCount}}</a>
						<a class="layui-btn layui-btn-xs" lay-event="detail" @Click="repush(data, '202')">发送</a>
					</div>

				</td>
				<td v-cloak>
					<div class="layui-table-cell">{{data.createTime}}</div>
				</td>
				<td v-cloak>
					<div class="layui-table-cell">{{data.updateTime}}</div>
				</td>
				<!--	<td v-cloak>
						<div class="layui-table-cell laytable-cell-1-10">
							<a v-if="data.errorCount>0 || data.sendingCount>0" class="layui-btn layui-btn-danger layui-btn-xs" lay-event="detail" @Click="repush(data)">重新发送</a>
						</div>
					  </td>	-->
			</tr>

			</tbody>
		</table>
	</div>
	<input class="input-box" type="hidden" ref="clientCode" th:value="${clientCode}">
	<div class="page-bar">
		<ul>
			<li><a>总<i>{{totalCount}}</i>条数</a></li>
		</ul>
		<div>
			<ul>
				<li ><a @click="btnClick(1)">首页</a></li>
				<li ><a @click="btnClick(cur-1)">上一页</a></li>
				<li v-for="index in indexs"  v-bind:class="{ 'active': cur == index}">
					<a v-on:click="btnClick(index)">{{ index }}</a>
				</li>
				<li ><a @click="btnClick(cur+1)">下一页</a></li>
				<li ><a @click="btnClick(all)">尾页</a></li>
				<li><a>共<i>{{all}}</i>页</a></li>
			</ul>
		</div>
	</div>
</div>
<script src="../../plugin/jquery/jquery-1.10.1.min.js"></script>
<script src="../../plugin/jquery/moment.min.js"></script>
<script src="../../plugin/layer/layer.js"></script>
<script src="../../plugin/layui/layui.js"></script>
<script src="../../plugin/layui/lay/modules/form.js"></script>
<script type="text/javascript" src="../../plugin/vue/vue.min.js"></script>
<script type="text/javascript" src="../../plugin/vue/vue-resource.js"></script>

<script type="text/javascript">
	Vue.http.headers.common['Authorization'] = 'Bearer ' + localStorage.token;
	var sendData = new Vue({
		el: '#sendData',
		data: {
			dataList: [],
			clientList: [],
			arr: [],
			searchData:{
				batchNo: "",
				clientCode: "",
				status: "",
				createTime: "",
				updateTime: "",
				page:0
			},
			isAllChecked:false,
			checkboxList:[],
			checked:false,
			mainVersionLogList: [],
			totalCount: 0,//总数
			all: 0, //总页数
			cur: 1,//当前页码
			limitList:[],
			dtoLog: {
				batchNo: "",
				clientCode:"",
				status:""
			},
			sendStatus:""
		},
		mounted: function () {
			var end = new Date();
			var begin = new Date();
			begin = new Date(begin.setUTCDate((end.getUTCDate()-1)));
			this.searchData.updateTime = moment(end.toLocaleDateString()).format('YYYY-MM-DD');
			this.searchData.createTime = moment(begin.toLocaleDateString()).format('YYYY-MM-DD');
			this.searchData.clientCode = this.$refs.clientCode.value;
			this.getClientList();
			//this.search();
		},
		methods:{
			checkAll: function () {
				if(!this.checked){//全选
					var _this = this;
					this.checkboxList = [];
					this.checked = true;
					this.dataList.forEach(function(item,index){
						_this.checkboxList.push(index);
					});
				}else{//反选
					this.checkboxList = [];
					this.checked = false;
				}
			},
			getClientList: function(){
				this.clientList = [];
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.get('/client/query', {code: this.searchData.clientCode}).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						_this.clientList = res.data.result;
						if(_this.clientList && _this.clientList.length > 0){
							//_this.searchData.queueName = _this.clientList[0].code;
						}
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			search: function (type) {
				//需要把page置为1
				if("1" == type){
					this.clearPage();
				}
				this.arr = [];
				this.dataList = [];
				var _this = this;
				var loadIndex = layerLoad();
				this.limitList = [];
				this.checkboxList = [];
				this.checked = false;
				this.searchData.page = this.cur;
				this.$http.get('/failData/all', this.searchData).then(function(res){
					layer.close(loadIndex);
					if(res.data.status == "200"){
						_this.arr = res.data.result;
						_this.dataList = [];
						_this.getData();
						_this.limit();
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			repush:function(data, status){
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.post('/failData/rePush?batchNo=' + data.batchNo + "&clientCode=" + data.clientCode + "&status=" + status).then(function(res){
					layer.close(loadIndex);
					if(res.data.status == "200" || res.data.status == "204"){
						_this.search();
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			_old:function( val ,data){
				var dataList = this.dataList;
				var _data = dataList[data.idx];
				if(val.status*1 == 500){//失败
					_data.errorCount?(_data.errorCount += val.errorCount): (_data.errorCount = val.errorCount);
				} else if(val.status*1 == 200){//成功
					_data.successCount?(_data.successCount += val.errorCount): (_data.successCount = val.errorCount);
				} else if(val.status*1 == 100){//进行中
					_data.unsendCount?(_data.unsendCount += val.errorCount): (_data.unsendCount = val.errorCount);
				} else {
					_data.sendingCount?(_data.sendingCount += val.errorCount): (_data.sendingCount = val.errorCount);
				}
			},
			_new:function(_val){
				var dataList = this.dataList;
				var _o = {};
				_o.batchNo = _val.batchNo;
				_o.clientName = _val.clientName;
				_o.clientCode = _val.clientCode;
				_o.createTime = _val.createTime;
				_o.updateTime = _val.updateTime;
				_o.status = _val.status;
				if(_val.status*1 == 500){
					_o.errorCount = _val.errorCount;
				} else if(_val.status*1 == 200){
					_o.successCount = _val.errorCount;
				} else if(_val.status*1 == 100){
					_o.unsendCount = _val.errorCount;
				} else {
					_o.sendingCount = _val.errorCount;
				}
				dataList.push(_o);
			},
			gettt:function(arrs , it){
				var idx ;
				var k = arrs.map(function( item , i ){
					if (item.batchNo == it.batchNo && item.clientCode == it.clientCode) {
						idx = i
						return {
							idx: i,
							value: item
						}
					}
				})
				return k[idx]
			},
			getData:function(){
				var _this = this;
				var dataList = this.dataList;
				if(this.arr != null){
					this.arr.forEach( function( val , idx ){
						if(dataList.length == 0 ){
							_this._new(val);
						}else{
							var _d = _this.gettt(dataList, val);
							if( _d ){
								_this._old( val ,_d );
							}else{
								_this. _new(val)
							}
						}
					})
				}
			},
			limit:function(){
				this.limitList = [];
				var _this = this;
				var page = this.cur;
				var page1 = (page - 1) * 10;
				var result = this.dataList;
				if(result != null && result.length > 0){
					for (var i = page1;i < (page*10);i++) {
						if(result[i] != null){
							_this.limitList.push(result[i]);
							_this.totalCount = result.length;
							_this.all = Math.ceil(result.length/10);
						}
					}
				}
			},
			popup:function(data, status){
				//var url = "dataDetail?batchNo=" + data.batchNo + "&queueName=" + data.queueName + "&staus=" + data.status
				layer.open({
					type: 2,
					skin: 'layui-layer-rim', //加上边框
					area: ['1000px', '400px'], //宽高
					content: 'dataDetail?batchNo=' + data.batchNo + '&clientCode=' + data.clientCode + '&status=' + status

				});
			},
			//重新发送失败信息
			againSend:function(){
				if(this.sendStatus == ''){
					layer.msg("请选择重新发送的数据状态");
					return false;
				}
				var checkboxList = this.checkboxList;
				console.log(checkboxList);
				if(checkboxList == null || checkboxList.length <= 0){
					layer.msg("请选择重新发送的数据批次");
					return false;
				}
				var dtoLogList = [];
				var _this = this;

				for(var i = 0;i<checkboxList.length;i++){
					_this.dtoLog = {};
					var s = _this.limitList[checkboxList[i]];
					if(s != undefined && this.sendStatus == '500'&& s.errorCount != undefined && s.errorCount > 0){
						_this.dtoLog.batchNo = _this.limitList[checkboxList[i]].batchNo;
						_this.dtoLog.clientCode = _this.limitList[checkboxList[i]].clientCode;
						_this.dtoLog.status = this.sendStatus;
						dtoLogList.push(_this.dtoLog);
					}
					if(s != undefined && this.sendStatus == '100' && s.unsendCount > 0){
						_this.dtoLog.batchNo = _this.limitList[checkboxList[i]].batchNo;
						_this.dtoLog.clientCode = _this.limitList[checkboxList[i]].clientCode;
						_this.dtoLog.status = this.sendStatus;
						dtoLogList.push(_this.dtoLog);
					}
					if(s != undefined && this.sendStatus == '202' && s.sendingCount > 0){
						_this.dtoLog.batchNo = _this.limitList[checkboxList[i]].batchNo;
						_this.dtoLog.clientCode = _this.limitList[checkboxList[i]].clientCode;
						_this.dtoLog.status = this.sendStatus;
						dtoLogList.push(_this.dtoLog);
					}
				}
				if(dtoLogList != null && dtoLogList.length > 0){
					var loadIndex = layerLoad();
					this.$http.post('/failData/againSend', {"dtoLogJson":JSON.stringify(dtoLogList)},{emulateJSON:true}).then(function(res){
						layer.close(loadIndex);
						if(res.data.status == "200"){
							layer.msg("发送成功")
							_this.search();
						} else {
							layer.msg(res.data.message);
						}
					}).catch(function(reason) {
						console.log('catch:', reason);
					});
				}else{
					layer.msg("没有发送失败数据！");
				}
			},
			clearPage: function(){
				this.totalCount = 0, //当前页的页码
						this.cur = 1
			},
			btnClick: function(data){//页码点击事件
				if(data != this.cur && this.all !=0 && data <= this.all && data > 0){
					this.cur = data;
					this.limit();
				}
			}
		},
		computed:{
			indexs: function () {
				var left = 1
				var right = this.all
				var ar = []
				if (this.all >= 11) {
					if (this.cur > 5 && this.cur < this.all - 4) {
						left = this.cur - 5
						right = this.cur + 4
					} else {
						if (this.cur <= 5) {
							left = 1
							right = 10
						} else {
							right = this.all
							left = this.all - 9
						}
					}
				}
				while (left <= right) {
					ar.push(left)
					left++
				}
				return ar
			},
			showLast: function () {
				if (this.cur == this.all) {
					return false
				}
				return true
			},
			showFirst: function () {
				if (this.cur == 1) {
					return false
				}
				return true
			},

		}
	});

</script>

</body>
</html>
<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:v-bind="http://www.w3.org/1999/xhtml"
	  xmlns:v-on="http://www.w3.org/1999/xhtml"
	  xmlns:v-on="http://www.w3.org/1999/xhtml" th:fragment="footer-pages">
<head>
	<meta charset="utf-8">
	<title>layui</title>
	<meta name="renderer" content="webkit">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<link rel="stylesheet" href="../../plugin/layui/css/layui.css">
	<link rel="stylesheet" href="../../plugin/layui/css/admin.css">
	<link rel="stylesheet" href="../../css/page-bar.css">

	<style type="text/css">
		.red {
			color: rgb(255, 87, 34) !important;
		}
		.yellow {
			color: rgb(255, 184, 0) !important;
		}
		.green {
			color: rgb(0, 150, 136) !important;
		}
		ul,li{
			/*margin-left: 3px;*/
			padding: 0px;
			font-size: 12px;
		}
		li{
			list-style: none
		}

	</style>
	<!-- 注意：如果你直接复制所有代码到本地，上述css路径需要改成你本地的 -->
</head>
<body>
<div class="layui-body" style="left:0px">
	<div class="layui-fluid">
		<div class="layui-card" id="nav">
			<div class="layui-card-body layui-row layui-col-space10">

				<div class="layui-col-md3">
					<label class="layui-form-label">时间范围</label>
					<div class="layui-input-block">
						<input type="text" class="layui-input" id="timeRange" v-model="searchData.timeRange">
					</div>
				</div>

				<div class="layui-col-md3">
					<label class="layui-form-label">主批次号</label>
					<div class="layui-input-block">
						<input type="text" class="layui-input" id="mainBatchNo" v-model="searchData.mainBatchNo">
					</div>
				</div>

				<div class="layui-col-md3">
					<label class="layui-form-label">数据类型</label>
					<div class="layui-input-block search-select" id="user-select">
						<select v-model="searchData.dataType" :disabled="filter">
							<option value="0" >全部</option>
							<option v-cloak v-for="batch in dataTypeList"  :value="batch.code" >{{batch.name}}</option>
						</select>
					</div>
				</div>

				<div class="layui-col-md1">

				</div>
				<div class="layui-col-md3">
					<button class="layui-btn layui-btn-normal" @Click="search()">查询</button>
					<button class="layui-btn layui-btn-normal" @Click="repushAll()">重新发送</button>
					<button class="layui-btn layui-btn-normal" @click="exportData()">导出</button>
				</div>
			<!--	<div class="layui-col-md2">

				</div>-->

				<div class="layui-col-md3">
					<label class="layui-form-label"></label>
					<button class="layui-btn layui-btn-normal layui-btn-xs time-range-button" @Click="setTimeRange(-2, this)">2小时</button>
					<button class="layui-btn layui-btn-primary layui-btn-xs time-range-button" @Click="setTimeRange(-6, this)">6小时</button>
					<button class="layui-btn layui-btn-primary layui-btn-xs time-range-button" @Click="setTimeRange(-24, this)">1天</button>
					<button class="layui-btn layui-btn-primary layui-btn-xs time-range-button" @Click="setTimeRange(-24 * 7, this)">7天</button>
					<button class="layui-btn layui-btn-primary layui-btn-xs time-range-button" @Click="setTimeRange(-24 * 30, this)">30天</button>
				</div>
				<div class="layui-col-md3">
					<label class="layui-form-label">小批次号</label>
					<div class="layui-input-block">
						<input type="text" class="layui-input" id="batchNo" v-model="searchData.batchNo">
					</div>
				</div>


				<div class="layui-col-md3">
					<label class="layui-form-label">客户端</label>
					<div class="layui-input-block search-select" id="user-select">
						<select v-model="searchData.clientCode" :disabled="filter">
							<option value="0" >全部</option>
							<option v-cloak v-for="client in clientList"  :value="client.code" >{{client.name}}</option>
						</select>
					</div>
				</div>

				<div class="layui-col-md3">
					<label class="layui-form-label">发送状态</label>
					<div class="layui-input-block search-select" id="user-select">
						<select name="status" v-model="searchData.status" :disabled="filter">
							<option value="0">全部</option>
							<option v-cloak value="200">成功</option>
						<!--	<option v-cloak value="101">处理中</option>-->
							<option v-cloak value="500">未成功</option>
						</select>
					</div>
				</div>
			</div>
		</div>


		<div id="batch">
			<div class="layui-card" >
				<div class="layui-card-header">批次信息</div>
				<div class="layui-card-body layui-row layui-col-space10 ">
					<table class="layui-table" lay-even="" lay-skin="row">
						<colgroup>
							<col width="10">
							<col width="10">
							<col width="70">
							<col width="150">
							<col width="90">
							<col width="90">
							<col width="100">
							<col width="70">
							<col width="80">
							<!--<col width="110">-->
							<col width="110">
							<col width="80">
						</colgroup>
						<thead>
						<tr>
							<th>
								<input v-bind:checked="checked" @click="checkAll()" type="checkbox"  style="display: inline-block;">
							</th>
							<th>序号</th>
							<th>批次号（小）</th>
							<th>时间段</th>
							<th>当前节点</th>
							<th>来源</th>
							<th>客戶端</th>
							<th>状态</th>
							<th>成功（条）</th>
							<th>未成功（条）</th>
						<!--	<th>进行中（条）</th>-->
							<th>更新详情</th>
						</tr>
						</thead>
						<tbody>
							<tr v-for="(batch, index) in batchList" :key="batch.id" v-bind:class="getStatsClass(batch.status)">
								<td>
									<input  type="checkbox" v-model='checkboxList' :value="index">
								</td>
								<td v-cloak>{{index + 1}}</td>
								<td v-cloak>
									{{batch.batchNo}}
								</td>
								<td v-cloak>
									{{batch.startTimeStr}} - {{batch.endTimeStr}}
								</td>
								<td v-cloak>
									{{batch.serviceName}} - {{batch.nodeName}}
								</td>
								<td v-cloak>
									<template v-if="batch.dataSource == 'transfer'">
											转换平台
									</template>
									<template v-if="batch.dataSource == 'publisher'">
											发布平台
									</template>
								</td>
								<td v-cloak>
									{{batch.clientName}}
								</td>
								<td v-cloak>
									{{(batch.status == '200') ? '成功' : '未成功'}}
								</td>
								<td v-cloak>
									<a class="layui-btn layui-btn-green layui-btn-xs"  lay-event="detail" @Click="popup(batch,'200')">{{batch.successNum}}</a>
								</td>
								<td v-cloak>
									<a class="layui-btn layui-btn-danger layui-btn-xs"  lay-event="detail" @Click="popup(batch,'500')">{{batch.unsuccessNum}}</a>
									<a  v-if="batch.unsuccessNum > 0 && batch.dataSource == 'publisher'" class="layui-btn layui-btn-xs" lay-event="detail" @Click="repush(batch, '500')">发送</a>
								</td>
							<!--	<td v-cloak>
									<a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="detail" @Click="popup(batch,'101')">{{batch.processingNum}}</a>
									<a  v-if="batch.processingNum > 0" class="layui-btn layui-btn-xs" lay-event="detail" @Click="repush(batch, '101')">发送</a>
								</td>-->
								<td v-cloak>
									<a class="layui-btn layui-btn-normal layui-btn-xs" @Click="dataTrace(batch.batchNo, batch.clientCode, batch.status, batch.dataSource,batch.mainBatchNo,batch.nodeName)">详情</a>
									<a class="layui-btn layui-btn-normal layui-btn-xs" @Click="refreshData(index,batch.id, batch.mainBatchNo, batch.batchNo, batch.clientCode,batch.nodeName, batch.dataSource)">刷新</a>
								</td>
							</tr>
							</tbody>
					</table>
				</div>
				<div class="page-bar" style="height: 40px;margin-left: 10px;">
					<ul>
						<li><a>总<i>{{pageInfo.total}}</i>条数</a></li>
					</ul>
					<div>
						<ul>
							<li ><a @click="setPageNo(0)">首页</a></li>
							<li ><a @click="setPageNo(pageInfo.page - 1)">上一页</a></li>
							<li v-for="page in pageArray"  v-bind:class="{ 'active': pageInfo.page == page}">
								<a v-on:click="setPageNo(page)">{{ page + 1 }}</a>
							</li>
							<li ><a @click="setPageNo(pageInfo.page + 1)">下一页</a></li>
							<li ><a @click="setPageNo(pageInfo.pageTotal - 1)">尾页</a></li>
							<li><a>共<i>{{pageInfo.pageTotal}}</i>页</a></li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<script src="../../plugin/jquery/jquery-1.10.1.min.js"></script>
<script src="../../plugin/jquery/moment.min.js"></script>
<script src="../../js/common.js"></script>
<script src="../../plugin/layer/layer.js"></script>
<script src="../../plugin/layui/layui.js"></script>
<script src="../../plugin/layui/lay/modules/form.js"></script>
<script type="text/javascript" src="../../plugin/vue/vue.min.js"></script>
<script type="text/javascript" src="../../plugin/vue/vue-resource.js"></script>

<script type="text/javascript">
	Vue.http.headers.common['Authorization'] = 'Bearer ' + localStorage.token;
	/*初始的page信息*/
    var pageJson = {
        page: 0,
        size: 100,
        total: 0
    };

    var batchVue = new Vue({
        el: '#batch',
        data: {
            batchList: [],
			checkboxList:[],
            pageInfo:{
                page:0,
                size:100,
                total:0,
                pageTotal:0
            },
            pageArray: []
        },
     /*   http: {
            headers: {
                isPage: "1"
            }
        },*/
        mounted: function () {
        },
        methods:{
            search: function () {
                var _this = this;
                var loadIndex = layerLoad();
                var searchData = _this.getSearchData();
                this.$http.get('/flBatchInfo/query', searchData, {
					headers: {
						isPage: "1"
					}}).then(function (res) {
                    layer.close(loadIndex);
                    var data = res.data;
                    if (data.status == "200") {
                        _this.batchList = res.data.result;
                        _this.setPage(res.data.pageInfo);

                    } else {
                        layer.msg("查询：" + res.data.message);
                        _this.batchList = [];
                        _this.setPage(pageJson);
                    }
                });
                // this.setPage(pageJson);
            },
			getSearchData: function(){
				var searchData = JSON.parse(JSON.stringify(navVue.searchData));
                if(searchData.clientCode == "0"){
                    delete searchData["clientCode"];
                }
                if(searchData.dataType == "0"){
                    delete searchData["dataType"];
                }
                if(searchData.status == "0"){
                    delete searchData["status"];
                }
                searchData.startTime = DateUtil.formatLongToDate(searchData.startTime);
                searchData.endTime = DateUtil.formatLongToDate(searchData.endTime);
                searchData["page"] = this.pageInfo.page;
                searchData["size"] = this.pageInfo.size;
                return searchData;
			},
            setPage: function(pageJson){
                this.pageInfo.pageTotal = Math.ceil(pageJson.total / pageJson.size);
                this.pageInfo.page = pageJson.page;
                this.pageInfo.size = pageJson.size;
                this.pageInfo.total = pageJson.total;
                this.pageArray = this.getPageArray();
            },
            getPageArray: function () {
                totalPage = this.pageInfo.pageTotal;
                page = this.pageInfo.page;
                var left = 0;
                var right = totalPage - 1;
                var pageArray = [];
                if (totalPage >= 11) {
                    if (page > 5 && page < totalPage - 4) {
                        left = page - 5;
                        right = page + 4;
                    } else {
                        if (page <= 5) {
                            left = 1;
                            right = 10;
                        } else {
                            right = totalPage;
                            left = totalPage - 9;
                        }
                    }
                }
                while (left <= right) {
                    pageArray.push(left);
                    left++;
                }
                return pageArray
            },
            setPageNo: function(pageNo){
                if(pageNo > this.pageInfo.pageTotal - 1){
                    return;
                } else if (pageNo < 0){
                    return;
                }
                this.pageInfo.page = pageNo;
                this.pageArray = this.getPageArray()
				this.search();
            },
            dataTrace:function(batchNo, clientCode, status, dataSource, mainBatchNo, nodeName){
                //var url = "dataDetail?batchNo=" + data.batchNo + "&clientCode=" + data.clientCode + "&staus=" + data.status
                layer.open({
                    type: 2,
                    skin: 'layui-layer-rim', //加上边框
                    area: ['700px', '800px'], //宽高
					title: '批次信息',
                    content: 'dataTrace?batchNo=' + batchNo + '&clientCode=' + clientCode + '&status=' + status + '&dataSource='+dataSource
                });
            },
			refreshData:function(index, id, mainBatchNo, batchNo, clientCode, nodeName, dataSource){
				var _this = this;
				var query = {
					"id":id,
					"mainBatchNo":mainBatchNo,
					"batchNo":batchNo,
					"clientCode":clientCode,
					"nodeName":nodeName,
					"dataSource":dataSource
				}
				this.$http.post('/flBatchInfo/refreshData', query).then(function(res){
					var data = res.data;
					if(data.status == "200"){
						this.$set(this.batchList, index, data.result);
						layer.msg("状态刷新成功");
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			popup:function(data, status){
				//var url = "dataDetail?batchNo=" + data.batchNo + "&clientCode=" + data.clientCode + "&staus=" + data.status
				if((status == '200' && data.successNum > 0) || (status == '500' && data.unsuccessNum > 0) ){
					layer.open({
						type: 2,
						skin: 'layui-layer-rim', //加上边框
						area: ['1000px', '400px'], //宽高
						content: 'dataDetail?batchNo=' + data.batchNo + '&clientCode=' + data.clientCode + '&status=' + status

					});
				}
			},
			getStatsClass:function (status) {
				if(status == "500"){
				    return "red";
				} else if (status == "200"){
				    return "green";
				} else if (status == "101"){
				    return "yellow";
				}
            },
			repush:function(data, status){
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.post('/failData/rePush?batchNo=' + data.batchNo + "&clientCode=" + data.clientCode + "&status=" + status).then(function(res){
					layer.close(loadIndex);
					if(res.data.status == "200" || res.data.status == "204"){
						_this.search();
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			//重新发送失败信息
			repushAll:function(){
				var _this = this;
				var searchData = this.getSearchData();
				if(searchData.status == undefined || searchData.status == '' || searchData.status == '0'){
					layer.msg("请选择重新发送的数据状态");
					return false;
				}
				if(searchData.status == '200'){
					layer.msg("推送成功的数据无需再次重新发送");
					return false;
				}
				var checkboxList = this.checkboxList;
				var batchList = this.batchList;
				if(checkboxList == null || checkboxList.length <= 0 || batchList == null || batchList.length <= 0 ){
					layer.msg("请选择重新发送的数据批次");
					return false;
				}

				var list = [];
				for(var i = 0;i<checkboxList.length;i++){
					var batch = batchList[checkboxList[i]];
					batch.clientCode = batch.clientCode;
					batch.status = searchData.status;
					list.push(batch);
				}
				var loadIndex = layerLoad();
				this.checkboxList = [];
				this.checked = false;
				this.$http.post('/failData?rePushBatch', {"data":list}).then(function(res){
					layer.close(loadIndex);
					if(res.data.status == "200"){
						layer.msg("发送成功")
						_this.search();
					} else {
						layer.msg(res.data.message);
					}
				}).catch(function(reason) {
					console.log('catch:', reason);
				});
			},
			/*前端生成excel*/
			exportData: function () {
				let _this = this;
				var searchData = _this.getSearchData();
				this.$http.get('/flBatchInfo/query', searchData, {
					headers: {
						isPage: "0"
					}}).then(function (res) {
					var data = res.data;
					if (data.status == "200") {
						var historys = res.data.result;
						var excel = '<table>';
						var row = "<tr >";
						//设置表头

						var keys = ["日期", "保险公司名称", "品牌", "批次号", "成功数据（条）", "未成功数据（条）"]
						keys.forEach(function (item) {
							row += "<td width='150'  style='border:1px solid;font-size:18px;background: yellow'>" + item + '</td>';
						});
						//换行
						excel += row + "</tr>";
						//设置数据
						for (let history of historys) {
							time = moment(history.startTimeStr).format('YYYY-MM-DD');
							var row = "<tr style='font-size:15px;'>";
							row += '<td >' + time + '</td>';
							row += '<td>' + history.clientName + '</td>';
							row += '<td>' + (history.hasOwnProperty("brandName") ? history.brandName : "-") + '</td>';
							row += '<td>' + history.batchNo + '</td>';
							row += '<td>' + history.successNum + '</td>';
							row += '<td>' + history.unsuccessNum + '</td>';
							/*row += '<td>' + history.processingNum + '</td>';*/
							excel += row + "</tr>";
						}
						excel += "</table>";
						var excelFile = "<html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:x='urn:schemas-microsoft-com:office:excel' xmlns='http://www.w3.org/TR/REC-html40'>";
						excelFile += '<meta http-equiv="content-type" content="application/vnd.ms-excel; charset=UTF-8">';
						excelFile += '<meta http-equiv="content-type" content="application/vnd.ms-excel';
						excelFile += '; charset=UTF-8">';
						excelFile += "<head>";
						excelFile += "<!--[if gte mso 9]>";
						excelFile += "<xml>";
						excelFile += "<x:ExcelWorkbook>";
						excelFile += "<x:ExcelWorksheets>";
						excelFile += "<x:ExcelWorksheet>";
						excelFile += "<x:Name>";
						excelFile += "即时更新推送历史导出";
						excelFile += "</x:Name>";
						excelFile += "<x:WorksheetOptions>";
						excelFile += "<x:DisplayGridlines/>";
						excelFile += "</x:WorksheetOptions>";
						excelFile += "</x:ExcelWorksheet>";
						excelFile += "</x:ExcelWorksheets>";
						excelFile += "</x:ExcelWorkbook>";
						excelFile += "</xml>";
						excelFile += "<![endif]-->";
						excelFile += "</head>";
						excelFile += "<body>";
						excelFile += excel;
						excelFile += "</body>";
						excelFile += "</html>";

						var uri = 'data:application/vnd.ms-excel;charset=utf-8,' + encodeURIComponent(excelFile);

						var link = document.createElement("a");
						link.href = uri;

						link.style = "visibility:hidden";
						link.download = "即时更新推送历史导出"+ searchData.startTime + "-"  + searchData.endTime + ".xls";

						document.body.appendChild(link);
						link.click();
						document.body.removeChild(link);
					} else {
						layer.msg(res.data.message);
					}
				});

			},
			checkAll: function () {
				if(!this.checked){//全选
					var _this = this;
					this.checkboxList = [];
					this.checked = true;
					this.batchList.forEach(function(item,index){
						_this.checkboxList.push(index);
					});
				}else{//反选
					this.checkboxList = [];
					this.checked = false;
				}
			}

        }
    });

    var navVue = new Vue({
        el: '#nav',
        data: {
			dataTypeList: [],
            clientList: [],
            searchData: {
                clientName: "",
				clientCode:"0",
				dataType: "0",
                batchNo: "",
                mainBatchNo: "",
                status: "0",
                startTime: "",
                endTime: "",
                timeRange: ""
            }
        },
        mounted: function () {
            this.setTimeRange(-2);
            this.getClientList();
			this.getDataType();
        },
        methods:{
            search: function () {
				batchVue.batchList = [];
                batchVue.search(this.searchData);
            },
			repushAll:  function () {
				batchVue.repushAll();
			},
            getClientList: function(){
                var _this = this;
                var loadIndex = layerLoad();
                this.$http.get('/client/query').then(function (res) {
                    layer.close(loadIndex);
                    var data = res.data;
                    if (data.status == "200") {
                        _this.clientList = res.data.result;
                        // _this.setPage(res.data.pageInfo);
                    } else {
                        layer.msg("获取客户端列表：" + res.data.message);
                    }
                });
            },
			getDataType: function () {
				//成功后回调
				var _this = this;
				_this.dataTypeList = [];
				var loadIndex = layerLoad();
				this.$http.get('/dict?type=dataType').then(function(res){
					layer.close(loadIndex);
					if(res.data.status == "200"){
						_this.dataTypeList = res.data.result;
					} else {
						layer.msg(res.data.message);
					}
				});
			},
            setTimeRange(hours, button) {
                var endDate = DateUtil.getCurrentlyDate();
                var startDate = DateUtil.addSpacingDate(endDate, hours * 60 * 60 * 1000);
                this.searchData.timeRange = startDate + " - " + endDate;
                this.searchData.startTime = DateUtil.strDateToLong(startDate);
                this.searchData.endTime = DateUtil.strDateToLong(endDate);
                if (button) {
                    $(".time-range-button").removeClass("layui-btn-normal").addClass("layui-btn-primary")
                    button.document.activeElement.classList.remove("layui-btn-primary");
                    button.document.activeElement.classList.add("layui-btn-normal");
                }
            },
			/*前端生成excel*/
			exportData: function () {
				batchVue.exportData(this.searchData);
			}
        }
    });



    layui.use('laydate', function(){
        var laydate = layui.laydate;

        //执行一个laydate实例
    /*    laydate.render({
            elem: '#timeRange' //指定元素
            ,type: 'datetime'
            ,range: true
            ,min: DateUtil.addSpacingDate(DateUtil.getCurrentlyDate(), -7 * 24 * 60 * 60 * 1000)
            ,max: DateUtil.getCurrentlyUltimate()
            ,value: navVue.setTimeRange
        });*/
		//执行一个laydate实例
		laydate.render({
			elem: '#timeRange' //指定元素
			, type: 'datetime'
			, range: true
			, done: function (value, date, endDate) {//控件选择完毕后的回调---点击日期、清空、现在、确定均会触发。
				navVue.searchData.timeRange = value;
				navVue.searchData.startTime = DateUtil.strDateToLong(DateUtil.formatLayDate(date));
				navVue.searchData.endTime = DateUtil.strDateToLong(DateUtil.formatLayDate(endDate));

				$(".time-range-button").removeClass("layui-btn-normal").addClass("layui-btn-primary")
			}
		});
    });

</script>

</body>
</html>

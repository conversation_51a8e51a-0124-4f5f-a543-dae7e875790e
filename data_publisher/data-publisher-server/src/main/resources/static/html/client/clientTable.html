<!DOCTYPE html>
<html>
<head>
	<base href="../../../" />
	<meta charset="utf-8">
	<title>layui</title>
	<meta name="renderer" content="webkit">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<link rel="stylesheet" href="/plugin/layui/css/layui.css">
	<link rel="stylesheet" href="/plugin/layui/css/admin.css">

	<!-- 注意：如果你直接复制所有代码到本地，上述css路径需要改成你本地的 -->
</head>
<body>
<div class="layui-fluid" id="clientTable">
	<div class="layui-card">
		<div class="layui-card-header">客户端表管理</div>
		<div class="layui-card-body layui-row layui-col-space10">
			<div class="layui-col-xs3">
				<label class="layui-form-label">客户端名称</label>
				<div class="layui-input-block search-select" id="user-select">
					<select  v-model="searchData.clientCode" >
						<option value="" >请选择</option>
						<option v-cloak v-for="client  in clientList"  :value="client.code" >{{client.name}}</option>
					</select>
				</div>
			</div>
			<div class="layui-col-xs3">
				<label class="layui-form-label">目标表名</label>
				<div class="layui-input-block">
					<input type="text" name="title" lay-verify="title" autocomplete="off" placeholder="表名" class="layui-input" v-model="searchData.tableName">
				</div>
			</div>
			<div class="layui-col-xs1">

			</div>
			<div class="layui-col-xs3">
				<button class="layui-btn layui-btn-normal" @Click="search()">查询</button>
			</div>
		</div>
	</div>

	<div class="layui-card" >
		<div class="layui-card-body layui-row layui-col-space10">
			<table class="layui-table" lay-even="" lay-skin="row">
				<colgroup>
					<col width="60">
					<col width="120">
					<col width="120">
					<col width="150">
					<col width="150">
				</colgroup>
				<thead>
				<tr>
					<th>序号</th>
					<th>客户端名称</th>
					<th>源表名</th>
					<th>目标表名</th>
					<th>操作</th>
				</tr>
				</thead>
				<tbody>
				<tr v-for="(clientTable, index) in clientTableList">
					<td v-cloak>{{index + 1}}</td>
					<td v-cloak>
						<div v-if="!clientTable.edit" v-cloak class="layui-table-cell">{{getClientName(clientTable.clientCode)}}</div>
						<div v-else id="user-select">
							<select  v-model="clientTable.clientCode">
								<option v-cloak v-for="client  in clientList"  :value="client.code" >{{client.name}}</option>
							</select>
						</div>
					</td>

					<td v-cloak>
						<div v-cloak v-if="!clientTable.edit" class="layui-table-cell">{{clientTable.baseTableName}}</div>
						<input v-else type="text" class="layui-input layui-table-edit" v-model="clientTable.baseTableName">
					</td>
					<td v-cloak>
						<div v-cloak v-if="!clientTable.edit" class="layui-table-cell">{{clientTable.tableName}}</div>
						<input v-else type="text" class="layui-input layui-table-edit" v-model="clientTable.tableName">
					</td>
					<td v-cloak>
						<div class="layui-table-cell laytable-cell-1-10">
							<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="detail" @Click="goTableFiled(clientTable)">查看表字段</a>
							<a v-if="!clientTable.edit" class="layui-btn layui-btn-xs" lay-event="edit" @Click="edit(clientTable, index)">编辑</a>
							<a v-else class="layui-btn layui-btn-xs layui-btn-warm" lay-event="edit" @Click="save(clientTable, index)">保存</a>
							<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del" @Click="del(clientTable, index)">删除</a>
						</div>
					</td>
				</tr>
				<tr>
					<td v-cloak>{{clientTableList.length + 1}}</td>
					<td v-cloak>
						<div id="user-select">
							<select  v-model="toAddClientTable.clientCode">
								<option v-cloak v-for="client  in clientList"  :value="client.code" >{{client.name}}</option>
							</select>
						</div>
					</td>
					<td v-cloak>
						<input type="text" class="layui-input layui-table-edit" v-model="toAddClientTable.baseTableName">
					</td>
					<td v-cloak>
						<input type="text" class="layui-input layui-table-edit" v-model="toAddClientTable.tableName">
					</td>

					<td v-cloak>
						<div class="layui-table-cell laytable-cell-1-10">
							<a  class="layui-btn layui-btn-xs" lay-event="edit" @Click="add(toAddClientTable)">添加</a>
						</div>
					</td>
				</tr>
				</tbody>
			</table>
		</div>
	</div>
</div>
<script src="/plugin/jquery/jquery-1.10.1.min.js"></script>
<script src="/plugin/layer/layer.js"></script>
<script src="/plugin/layui/layui.js"></script>
<script type="text/javascript" src="/plugin/vue/vue.min.js"></script>
<script type="text/javascript" src="/plugin/vue/vue-resource.js"></script>

<script type="text/javascript">

	Vue.http.headers.common['Authorization'] = 'Bearer ' + localStorage.token;
	var clientTableVue = new Vue({
		el: '#clientTable',
		data: {
			clientTableList: [],
			clientList: [],
			searchData:{
				clientCode:"",
				tableName:""
			},
			toAddClientTable: {},
			newClientTable: {
				"clientCode": "",
				"tableName": "",
				"baseTableName": ""
			}
		},
		mounted: function () {
			this.getClientList();
			this.search();
			this.toAddClientTable = JSON.parse(JSON.stringify(this.newClientTable));
		},
		watch: {
			searchData: {
				handler(newValue, oldValue) {
					this.toAddClientTable.clientCode = this.searchData.clientCode;
				},
				deep: true
			}
		},
		methods:{
			getClientList: function(){
				this.clientList = [];
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.get('/client/query', this.searchData).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						_this.clientList = res.data.result;
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			search: function () {
				var _this = this;
				_this.clientTableList = [];
				var loadIndex = layerLoad();
				this.$http.get('/clientTable/query', this.searchData).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						_this.clientTableList = res.data.result;
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			del: function (clientTable, index) {
				var _this = this;
				var layerIndex = layer.confirm('删除此表后，不会再更新此表数据', {
					btn: ['确认删除','取消'] //按钮
				}, function(){
					var loadIndex = layerLoad();
					_this.$http.delete('/clientTable?id=' + clientTable.id).then(function(res){
						layer.close(loadIndex);
						var data = res.data;
						if(data.status == "200"){
							_this.clientTableList.splice(index, 1);
						} else {
							layer.msg(res.data.message);
						}
					});
					layer.close(layerIndex);
				}, function(){
					layer.close(layerIndex);
				});

			},
			edit: function (clientTable, index) {
				//成功后回调
				clientTable.edit = true;
				Vue.set(this.clientTableList, index, clientTable);
				layui.use('form', function(){
					var form = layui.form;
					form.render('select')
				});
			},
			save: function(clientTable, index){
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.put('/clientTable', this.trim(clientTable)).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						clientTable.edit = false;
						Vue.set(_this.clientTableList, index, clientTable);
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			add: function(toAddClientTable){
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.post('/clientTable', this.trim(toAddClientTable)).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						toAddClientTable = res.data.result;
						Vue.set(_this.clientTableList, _this.clientTableList.length, JSON.parse(JSON.stringify(toAddClientTable)));
						_this.toAddClientTable = JSON.parse(JSON.stringify(_this.newClientTable));
					} else {
						layer.msg(res.data.message);
					}
				});

			},
			getClientName: function(clientCode){
				var index = -1;
				this.clientList.forEach(function (_client, _index) {
					if (_client.code == clientCode) {
						index = _index;
						return false;
					}
				});
				if(index != -1){
					return this.clientList[index].name;
				} else {
					return clientCode;
				}

			},
			goTableFiled: function(clientTable){
				window.location.href = "/page/clientTableField?clientCode=" + clientTable.clientCode + "&tableName=" + clientTable.tableName;
			},
			trim: function (data) {
				for(var key in data) {
					if(typeof data[key] == "string" && data[key] != null && data[key] != ""){
						var val = data[key].replace(/(^\s*)|(\s*$)/g, "");
						data[key]= val;
					}
				}
				return data;
			}
		}
	});
</script>

</body>
</html>
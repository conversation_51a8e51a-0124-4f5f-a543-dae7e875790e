<!DOCTYPE html>
<html>
<head>
	<base href="../../../" />
	<meta charset="utf-8">
	<title>layui</title>
	<meta name="renderer" content="webkit">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<link rel="stylesheet" href="/plugin/layui/css/layui.css">
	<link rel="stylesheet" href="/plugin/layui/css/admin.css">
	<style type="text/css">
		.red {
			color : red
		}
		.yellow {
			color : yellow
		}
		.green {
			color : green
		}
	</style>
</head>
<body>
<div class="layui-fluid" id="client">
	<div class="layui-card"><!-- layui-form-->
		<div class="layui-card-header">客户端信息管理</div>
		<div class="layui-card-body layui-row layui-col-space10">
			<div class="layui-col-xs3">
				<label class="layui-form-label">客户端名称</label>
				<div class="layui-input-block">
					<input type="text" name="title" lay-verify="title" autocomplete="off" placeholder="客户端名称" class="layui-input" v-model="searchData.name">
				</div>
			</div>
			<div class="layui-col-xs3">
				<label class="layui-form-label">客户端编码</label>
				<div class="layui-input-block">
					<input type="text" name="title" lay-verify="title" autocomplete="off" placeholder="客户端编码" class="layui-input" v-model="searchData.code">
				</div>
			</div>
			<div class="layui-col-xs3">
				<label class="layui-form-label">状态</label>
				<div class="layui-input-block search-select" id="user-select">
					<select  v-model="searchData.status" ><!--lay-filter="searchDataStatus"-->
						<option value="" >请选择</option>
						<option v-cloak v-for="status in statusList"  :value="status.code" >{{status.code}}</option>
					</select>
				</div>
			</div>
			<div class="layui-col-xs1">
			</div>
			<div class="layui-col-xs2">
				<button class="layui-btn layui-btn-normal" @Click="search()">查询</button>
				<button class="layui-btn layui-btn-normal" @Click="syncData()">同步</button>
			</div>
		</div>
	</div>

	<div class="layui-card">
		<div class="layui-card-body layui-row layui-col-space10 ">
			<table class="layui-table" lay-even="" lay-skin="row">
				<colgroup>
					<col width="60">
					<col width="110">
					<col width="110">
					<col width="150">
					<col width="120">
					<col width="120">
					<col width="150">
					<col width="60">
					<col width="150">
				</colgroup>
				<thead>
				<tr>
					<th>序号</th>
					<th>客户端名称</th>
					<th>客户端编码</th>
					<th>mq数量上限</th>
					<th>接收数量上限</th>
					<th>ip</th>
					<th>描述</th>
					<th>状态</th>
					<th>操作</th>
				</tr>
				</thead>
				<tbody>
				<tr v-for="(client, index) in clientList">
					<td v-cloak>{{index + 1}}</td>
					<td v-cloak>
						<div v-cloak v-if="!client.edit" class="layui-table-cell">{{client.name}}</div>
						<input v-else type="text" class="layui-input layui-table-edit" v-model="client.name">
					</td>
					<td v-cloak>
						<div v-cloak v-if="client!=null" class="layui-table-cell">{{client.code}}</div>
						<input v-else type="text" class="layui-input layui-table-edit" v-model="client.code">
					</td>
					<td v-cloak>
						<div v-cloak v-if="!client.edit" class="layui-table-cell">{{client.topLimit}}</div>
						<input v-else type="text" class="layui-input layui-table-edit" v-model="client.topLimit">
					</td>
					<td v-cloak>
						<div v-cloak v-if="!client.edit" class="layui-table-cell">{{client.receiveLimit}}</div>
						<input v-else type="text" class="layui-input layui-table-edit" v-model="client.receiveLimit">
					</td>
					<td v-cloak>
						<div v-cloak v-if="!client.edit" class="layui-table-cell">{{client.ip}}</div>
						<input v-else type="text" class="layui-input layui-table-edit" v-model="client.ip">
					</td>
					<td v-cloak>
						<div v-cloak v-if="!client.edit" class="layui-table-cell">{{client.path}}</div>
						<input v-else type="text" class="layui-input layui-table-edit" v-model="client.path">
					</td>
					<td v-cloak>
						<span v-cloak v-if="!client.edit" class="layui-badge" v-bind:class="getColor(client)">{{client.status}}</span>
						<div v-else id="user-select">
							<select  v-model="client.status">
								<option v-cloak v-for="status  in statusList"  :value="status.code" >{{status.code}}</option>
							</select>
						</div>
					</td>
					<td v-cloak>
						<div class="layui-table-cell laytable-cell-1-10">
							<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="detail" @Click="goSendedData(client)">发布状态查看</a>
							<a v-if="!client.edit" class="layui-btn layui-btn-xs" lay-event="edit" @Click="edit(client, index)">编辑</a>
							<a v-else class="layui-btn layui-btn-xs layui-btn-warm" lay-event="edit" @Click="save(client, index)">保存</a>
							<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del" @Click="del(client, index)">删除</a>
						</div>
					</td>
				</tr>
				<tr>
					<td v-cloak>{{clientList.length + 1}}</td>
					<td v-cloak>

						<input type="text" class="layui-input layui-table-edit" v-model="toAddClient.name">
					</td>
					<td v-cloak>

						<input type="text" class="layui-input layui-table-edit" v-model="toAddClient.code">
					</td>
					<td v-cloak>

						<input type="text" class="layui-input layui-table-edit" v-model="toAddClient.topLimit">
					</td>
					<td v-cloak>

						<input type="text" class="layui-input layui-table-edit" v-model="toAddClient.receiveLimit">
					</td>
					<td v-cloak>

						<input type="text" class="layui-input layui-table-edit" v-model="toAddClient.ip">
					</td>
					<td v-cloak>

						<input type="text" class="layui-input layui-table-edit" v-model="toAddClient.path">
					</td>
					<td v-cloak>
						<div  v-cloak class="layui-table-cell">{{toAddClient.status}}</div>
					</td>
					<td v-cloak>
						<div class="layui-table-cell laytable-cell-1-10">
							<a  class="layui-btn layui-btn-xs" lay-event="edit" @Click="add(toAddClient)">添加</a>
						</div>
					</td>
				</tr>
				</tbody>
			</table>
		</div>
	</div>
</div>
<script src="/plugin/jquery/jquery-1.10.1.min.js"></script>
<script src="/plugin/layer/layer.js"></script>
<script src="/plugin/layui/layui.js"></script>
<script type="text/javascript" src="/plugin/vue/vue.min.js"></script>
<script type="text/javascript" src="/plugin/vue/vue-resource.js"></script>

<script type="text/javascript">
	/*layui.use('form', function(){
	 var form = layui.form;
	 form.on('select(searchDataStatus)', function(data){
	 clientVue.searchData.status = data.value;
	 });
	 });*/
	Vue.http.headers.common['Authorization'] = 'Bearer ' + localStorage.token;
	var clientVue = new Vue({
		el: '#client',
		data: {
			clientList: [],
			statusList: [{
				code: "DOWN"
			},{
				code: "UP"
			},{
				code: "OFFLINE"
			}],
			searchData:{
				name: "",
				code: "",
				status: ""
			},
			toAddClient: {},
			newClient: {
				"code": "",
				"name": "",
				"topLimit": "",
				"receiveLimit":"",
				"ip":"",
				"path": "",
				"status": "OFFLINE"
			}
		},
		mounted: function () {
			this.search();
			this.toAddClient = JSON.parse(JSON.stringify(this.newClient));
		},
		methods:{
			syncData :function () {
				var loadIndex = layerLoad();
				this.$http.get('/client/syncData', this.searchData).then(function(res){
					layer.close(loadIndex);
					if(data.status == "200"){
						layer.msg(res.data.message);
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			search: function () {
				this.clientList = [];
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.get('/client/query', this.searchData).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						_this.clientList = res.data.result;
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			del: function (client, index) {
				var _this = this;
				//询问框
				var layerIndex = layer.confirm('删除此客户端后，不会再更新此客户端数据', {
					btn: ['确认删除','取消'] //按钮
				}, function(){
					layer.close(layerIndex);
					var loadIndex = layerLoad();
					_this.$http.delete('/client?id=' + client.id).then(function(res){
						layer.close(loadIndex);
						var data = res.data;
						if(data.status == "200"){
							_this.clientList.splice(index, 1);
						} else {
							layer.msg(res.data.message);
						}
					});
				}, function(){
					layer.close(layerIndex);
				});

			},
			edit: function (client, index) {
				client.edit = true;
				Vue.set(this.clientList, index, client);
			},
			save: function(client, index){
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.put('/client', this.trim(client)).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						client.edit = false;
						Vue.set(_this.clientList, index, client);
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			add: function(toAddClient){
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.post('/client', this.trim(toAddClient)).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						toAddClient = res.data.result;
						Vue.set(_this.clientList, _this.clientList.length, JSON.parse(JSON.stringify(toAddClient)));
						_this.toAddClient = JSON.parse(JSON.stringify(_this.newClient));
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			getColor: function(client){
				if(client.status == "UP"){
					return "layui-bg-green";
				} else if (client.status == "DOWN" ) {
					return "layui-bg-cyan";
				} else {
					return "";
				}
			},
			goSendedData: function(client){
				window.location.href = "/page/sendData?clientCode=" + client.code;
			},
			trim: function (data) {
				for(var key in data) {
					if(typeof data[key] == "string" && data[key] != null && data[key] != ""){
						var val = data[key].replace(/(^\s*)|(\s*$)/g, "");
						data[key]= val;
					}
				}
				return data;
			}

		}
	});
</script>

</body>
</html>
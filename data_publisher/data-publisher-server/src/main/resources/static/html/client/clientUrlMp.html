<!DOCTYPE html>
<html>
<head>
	<base href="../../../" />
	<meta charset="utf-8">
	<title>layui</title>
	<meta name="renderer" content="webkit">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<link rel="stylesheet" href="/plugin/layui/css/layui.css">
	<link rel="stylesheet" href="/plugin/layui/css/admin.css">

	<!-- 注意：如果你直接复制所有代码到本地，上述css路径需要改成你本地的 -->
</head>
<body>
<div class="layui-fluid" id="clientUrlMp">
	<div class="layui-card">
		<div class="layui-card-header">客户端地址管理</div>
		<div class="layui-card-body layui-row layui-col-space10">
			<div class="layui-col-xs3">
				<label class="layui-form-label">客户端名称</label>
				<div class="layui-input-block search-select" id="user-select">
					<select  v-model="searchData.clientCode" >
						<option value="" >请选择</option>
						<option v-cloak v-for="client  in clientList"  :value="client.code" >{{client.name}}</option>
					</select>
				</div>
			</div>
			<div class="layui-col-xs3">
				<label class="layui-form-label">目标表名</label>
				<div class="layui-input-block">
					<input type="text" name="title" lay-verify="title" autocomplete="off" placeholder="表名" class="layui-input" v-model="searchData.tableName">
				</div>
			</div>
			<div class="layui-col-xs1">

			</div>
			<div class="layui-col-xs3">
				<button class="layui-btn layui-btn-normal" @Click="search()">查询</button>
			</div>
		</div>
	</div>

	<div class="layui-card" >
		<div class="layui-card-body layui-row layui-col-space10">
			<table class="layui-table" lay-even="" lay-skin="row">
				<colgroup>
					<col width="60">
					<col width="120">
					<col width="120">
					<col width="150">
					<col width="150">
				</colgroup>
				<thead>
				<tr>
					<th>序号</th>
					<th>客户端名称</th>
					<th>目标表名</th>
					<th>访问地址</th>
					<th>访问路径</th>
					<th>操作</th>
				</tr>
				</thead>
				<tbody>
				<tr v-for="(clientUrlMp, index) in clientUrlMpList">
					<td v-cloak>{{index + 1}}</td>
					<td v-cloak>
						<div v-if="!clientUrlMp.edit" v-cloak class="layui-table-cell">{{getClientName(clientUrlMp.clientCode)}}</div>
						<div v-else id="user-select">
							<select  v-model="clientUrlMp.clientCode">
								<option v-cloak v-for="client  in clientList"  :value="client.code" >{{client.name}}</option>
							</select>
						</div>
					</td>
					<td v-cloak>
						<div v-cloak v-if="!clientUrlMp.edit" class="layui-table-cell">{{clientUrlMp.tableName}}</div>
						<input v-else type="text" class="layui-input layui-table-edit" v-model="clientUrlMp.tableName">
					</td>
					<td v-cloak>
						<div v-cloak v-if="!clientUrlMp.edit" class="layui-table-cell">{{clientUrlMp.url}}</div>
						<input v-else type="text" class="layui-input layui-table-edit" v-model="clientUrlMp.url">
					</td>
					<td v-cloak>
						<div v-cloak v-if="!clientUrlMp.edit" class="layui-table-cell">{{clientUrlMp.path}}</div>
						<input v-else type="text" class="layui-input layui-table-edit" v-model="clientUrlMp.path">
					</td>

					<td v-cloak>
						<div class="layui-table-cell laytable-cell-1-10">
							<a v-if="!clientUrlMp.edit" class="layui-btn layui-btn-xs" lay-event="edit" @Click="edit(clientUrlMp, index)">编辑</a>
							<a v-else class="layui-btn layui-btn-xs layui-btn-warm" lay-event="edit" @Click="save(clientUrlMp, index)">保存</a>
							<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del" @Click="del(clientUrlMp, index)">删除</a>
						</div>
					</td>
				</tr>
				<tr>
					<td v-cloak>{{clientUrlMpList.length + 1}}</td>
					<td v-cloak>
						<div id="user-select">
							<select  v-model="toAddClientUrlMp.clientCode">
								<option v-cloak v-for="client  in clientList"  :value="client.code" >{{client.name}}</option>
							</select>
						</div>
					</td>
					<td v-cloak>
						<input type="text" class="layui-input layui-table-edit" v-model="toAddClientUrlMp.tableName">
					</td>
					<td v-cloak>
						<input type="text" class="layui-input layui-table-edit" v-model="toAddClientUrlMp.url">
					</td>
					<td v-cloak>
						<input type="text" class="layui-input layui-table-edit" v-model="toAddClientUrlMp.path">
					</td>
					<td v-cloak>
						<div class="layui-table-cell laytable-cell-1-10">
							<a  class="layui-btn layui-btn-xs" lay-event="edit" @Click="add(toAddClientUrlMp)">添加</a>
						</div>
					</td>
				</tr>
				</tbody>
			</table>
		</div>
	</div>
</div>
<script src="/plugin/jquery/jquery-1.10.1.min.js"></script>
<script src="/plugin/layer/layer.js"></script>
<script src="/plugin/layui/layui.js"></script>
<script type="text/javascript" src="/plugin/vue/vue.min.js"></script>
<script type="text/javascript" src="/plugin/vue/vue-resource.js"></script>

<script type="text/javascript">

	Vue.http.headers.common['Authorization'] = 'Bearer ' + localStorage.token;
	var clientUrlMpVue = new Vue({
		el: '#clientUrlMp',
		data: {
			clientUrlMpList: [],
			clientList: [],
			searchData:{
				clientCode:"",
				tableName:""
			},
			toAddClientUrlMp: {},
			newClientUrlMp: {
				"clientCode": "",
				"tableName": "",
				"url": "",
				"path": ""
			}
		},
		mounted: function () {
			this.getClientList();
			this.search();
			this.toAddClientUrlMp = JSON.parse(JSON.stringify(this.newClientUrlMp));
		},
		watch: {
			searchData: {
				handler(newValue, oldValue) {
					this.toAddClientUrlMp.clientCode = this.searchData.clientCode;
				},
				deep: true
			}
		},
		methods:{
			getClientList: function(){
				this.clientList = [];
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.get('/client/query', this.searchData).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						_this.clientList = res.data.result;
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			search: function () {
				var _this = this;
				_this.clientUrlMpList = [];
				var loadIndex = layerLoad();
				this.$http.get('/clientUrlMp/query', this.searchData).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						_this.clientUrlMpList = res.data.result;
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			del: function (clientUrlMp, index) {
				var _this = this;
				var layerIndex = layer.confirm('删除此表后，不会再更新此表数据', {
					btn: ['确认删除','取消'] //按钮
				}, function(){
					var loadIndex = layerLoad();
					_this.$http.delete('/clientUrlMp?id=' + clientUrlMp.id).then(function(res){
						layer.close(loadIndex);
						var data = res.data;
						if(data.status == "200"){
							_this.clientUrlMpList.splice(index, 1);
						} else {
							layer.msg(res.data.message);
						}
					});
					layer.close(layerIndex);
				}, function(){
					layer.close(layerIndex);
				});

			},
			edit: function (clientUrlMp, index) {
				//成功后回调
				clientUrlMp.edit = true;
				Vue.set(this.clientUrlMpList, index, clientUrlMp);
				layui.use('form', function(){
					var form = layui.form;
					form.render('select')
				});
			},
			save: function(clientUrlMp, index){
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.put('/clientUrlMp', this.trim(clientUrlMp)).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						clientUrlMp.edit = false;
						Vue.set(_this.clientUrlMpList, index, clientUrlMp);
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			add: function(toAddClientUrlMp){
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.post('/clientUrlMp', this.trim(toAddClientUrlMp)).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						toAddClientUrlMp = res.data.result;
						Vue.set(_this.clientUrlMpList, _this.clientUrlMpList.length, JSON.parse(JSON.stringify(toAddClientUrlMp)));
						_this.toAddClientUrlMp = JSON.parse(JSON.stringify(_this.newClientUrlMp));
					} else {
						layer.msg(res.data.message);
					}
				});

			},
			getClientName: function(clientCode){
				var index = -1;
				this.clientList.forEach(function (_client, _index) {
					if (_client.code == clientCode) {
						index = _index;
						return false;
					}
				});
				if(index != -1){
					return this.clientList[index].name;
				} else {
					return clientCode;
				}

			},
			goTableFiled: function(clientUrlMp){
				window.location.href = "/page/clientUrlMpField?clientCode=" + clientUrlMp.clientCode + "&tableName=" + clientUrlMp.tableName;
			},
			trim: function (data) {
				for(var key in data) {
					if(typeof data[key] == "string" && data[key] != null && data[key] != ""){
						var val = data[key].replace(/(^\s*)|(\s*$)/g, "");
						data[key]= val;
					}
				}
				return data;
			}
		}
	});
</script>

</body>
</html>
<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
	<meta charset="utf-8">
	<title>layui</title>
	<meta name="renderer" content="webkit">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<link rel="stylesheet" href="../../plugin/layui/css/layui.css">
	<link rel="stylesheet" href="../../plugin/layui/css/admin.css">

	<!-- 注意：如果你直接复制所有代码到本地，上述css路径需要改成你本地的 -->
</head>
<body>
<div class="layui-fluid" id="clientTableField">
	<div class="layui-card" >
		<div class="layui-card-header">表字段管理</div>
		<div class="layui-card-body layui-row layui-col-space10">
			<div class="layui-col-xs3">
				<label class="layui-form-label">客户端名称</label>
				<div class="layui-input-block search-select" id="user-select" >
					<select  v-model="searchData.clientCode" @change="changeClientTable()" >
						<option value="" >请选择</option>
						<option v-cloak v-for="client  in clientList"  :value="client.code" >{{client.name}}</option>
					</select>
				</div>

			</div>
			<div class="layui-col-xs3" >
				<label class="layui-form-label">目标表名</label>
				<div class="layui-input-block search-select" id="user-select">
					<select  v-model="searchData.tableName" >
						<option value="" >请选择</option>
						<option v-cloak v-for="clientTable  in clientTableList"  :value="clientTable.tableName" >{{clientTable.tableName}}</option>
					</select>
				</div>

			</div>
			<div class="layui-col-xs3">
				<label class="layui-form-label">源字段名</label>
				<div class="layui-input-block">
					<input type="text" name="title" lay-verify="title" autocomplete="off" placeholder="源字段名" class="layui-input" v-model="searchData.baseTableField">
				</div>
			</div>
			<div class="layui-col-xs3">
				<button class="layui-btn layui-btn-normal" @Click="search()">查询</button>
			</div>
		</div>
	</div>

	<div class="layui-card" >
		<div class="layui-card-body layui-row layui-col-space10">
			<table class="layui-table" lay-even="" lay-skin="row">
				<colgroup>
					<col width="60">
					<col width="150">
					<col width="150">
					<col width="150">
					<col width="150">
					<col width="150">
				</colgroup>
				<thead>
				<tr>
					<th>序号</th>
					<th>客户端名称</th>
					<th>目标表名</th>
					<th>源字段名</th>
					<th>目标字段名</th>
					<th>操作</th>
				</tr>
				</thead>
				<tbody>
				<tr v-for="(clientTableField, index) in clientTableFieldList">
					<td v-cloak>{{index + 1}}</td>
					<td v-cloak>
						<div v-if="!clientTableField.edit" v-cloak  class="layui-table-cell">{{getClientName(clientTableField.clientCode)}}</div>
						<div v-else id="user-select">
							<select  v-model="clientTableField.clientCode" @change="changeClientCode(clientTableField, index)">
								<option v-cloak v-for="client  in clientList"  :value="client.code" >{{client.name}}</option>
							</select>
						</div>
					</td>
					<td v-cloak>
						<div v-if="!clientTableField.edit" v-cloak  class="layui-table-cell">{{clientTableField.tableName}}</div>
						<div v-else id="user-select">
							<select  v-model="clientTableField.tableName">
								<option v-cloak v-for="clientTable  in clientTableField.clientTableList"  :value="clientTable.tableName" >{{clientTable.tableName}}</option>
							</select>
						</div>
					</td>
					<td v-cloak>
						<div v-if="!clientTableField.edit" class="layui-table-cell">{{clientTableField.baseTableField}}</div>
						<input v-else type="text" class="layui-input layui-table-edit" v-model="clientTableField.baseTableField">
					</td>
					<td v-cloak>
						<div v-if="!clientTableField.edit" class="layui-table-cell">{{clientTableField.tableField}}</div>
						<input v-else type="text" class="layui-input layui-table-edit" v-model="clientTableField.tableField">
					</td>
					<td v-cloak>
						<div class="layui-table-cell laytable-cell-1-10">
							<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="detail" @Click="goClientTableFieldOrg(clientTableField)" >查看字段对应机构</a>
							<a v-if="!clientTableField.edit" class="layui-btn layui-btn-xs" lay-event="edit" @Click="edit(clientTableField, index)">编辑</a>
							<a v-else class="layui-btn layui-btn-xs layui-btn-warm" lay-event="edit" @Click="save(clientTableField, index)">保存</a>
							<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del" @Click="del(clientTableField, index)">删除</a>
						</div>
					</td>
				</tr>
				<tr>
					<td v-cloak>{{clientTableFieldList.length + 1}}</td>
					<td v-cloak>
						<div id="user-select">
							<select  v-model="toAddClientTableField.clientCode" @change="changeClientCode(toAddClientTableField)">
								<option v-cloak v-for="client  in clientList"  :value="client.code" >{{client.name}}</option>
							</select>
						</div>
					</td>
					<td v-cloak>
						<div id="user-select">
							<select  v-model="toAddClientTableField.tableName">
								<option v-cloak v-for="clientTable  in toAddClientTableField.clientTableList"  :value="clientTable.tableName" >{{clientTable.tableName}}</option>
							</select>
						</div>
					</td>
					<td v-cloak>

						<input type="text" class="layui-input layui-table-edit" v-model="toAddClientTableField.baseTableField">
					</td>
					<td v-cloak>

						<input type="text" class="layui-input layui-table-edit" v-model="toAddClientTableField.tableField">
					</td>
					<td v-cloak>
						<div class="layui-table-cell laytable-cell-1-10">
							<a  class="layui-btn layui-btn-xs" lay-event="edit" @Click="add(toAddClientTableField)">添加</a>
						</div>
					</td>
				</tr>
				</tbody>
			</table>
		</div>
	</div>
	<input class="input-box" type="hidden" ref="clientCode" th:value="${clientCode}">
	<input class="input-box" type="hidden" ref="tableName" th:value="${tableName}">
</div>
<script src="../../plugin/jquery/jquery-1.10.1.min.js"></script>
<script src="../../plugin/layer/layer.js"></script>
<script src="../../plugin/layui/layui.js"></script>
<script type="text/javascript" src="../../plugin/vue/vue.min.js"></script>
<script type="text/javascript" src="../../plugin/vue/vue-resource.js"></script>

<script type="text/javascript">
	Vue.http.headers.common['Authorization'] = 'Bearer ' + localStorage.token;
	var clientTableFieldVue = new Vue({
		el: '#clientTableField',
		data: {
			clientTableFieldList: [],
			clientTableList: [],
			clientList: [],
			searchData:{
				clientCode:"",
				tableName:"",
				baseTableField:""
			},
			toAddClientTableField: {},
			newClientTableField: {
				"clientCode": "",
				"tableName": "",
				"tableField": "",
				"baseTableField": ""
			},
			linkData: {
				code: "",
				tableName: ""
			}
		},
		mounted: function () {
			this.toAddClientTableField = JSON.parse(JSON.stringify(this.newClientTableField));
			if(this.$refs.clientCode.value || this.$refs.tableName.value){
				this.linkData.code = this.$refs.clientCode.value;
				this.linkData.tableName = this.$refs.tableName.value;
			}
			this.getClientList();
			if(this.$refs.clientCode.value || this.$refs.tableName.value){
				this.searchData.clientCode = this.$refs.clientCode.value;
				this.searchData.tableName = this.$refs.tableName.value;
				this.search();
			}
		},
//    watch: {
//    	searchData: {
//    	   handler(newValue, oldValue) {
//	　　　　　　  this.toAddClientTableField.clientCode = JSON.parse(JSON.stringify(this.searchData.clientCode));
//				this.toAddClientTableField.tableName = JSON.parse(JSON.stringify(this.searchData.tableName));
//	　　　　},
//	　　　　deep: true
//		}
//    },
		methods:{
			getClientList: function(){
				this.clientList = [];
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.get('/client/query', this.linkData).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						_this.clientList = res.data.result;
						if(_this.clientList && _this.clientList.length > 0){
							_this.searchData.clientCode = _this.clientList[0].code;
							_this.searchData.tableName = _this.linkData.tableName;
						}
						_this.getClientTableList();
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			changeClientTable: function () {
				var clientCode = this.searchData.clientCode;
				this.searchData = JSON.parse(JSON.stringify(this.newClientTableField));
				this.searchData.clientCode = clientCode;
				this.getClientTableList();
			},
			getClientTableList: function(){
				this.clientTableList = [];
				var _this = this;
				var loadIndex = layerLoad();
				if(this.searchData.clientCode) {
					this.$http.get('/clientTable/query', this.searchData).then(function (res) {
						layer.close(loadIndex);
						var data = res.data;
						if (data.status == "200") {
							_this.clientTableList = res.data.result;
							if(_this.clientTableList && _this.clientTableList.length > 0){
								_this.searchData.tableName = _this.clientTableList[0].tableName;
							}
						} else {
							layer.msg(res.data.message);
						}
					});
				} else {
					layer.close(loadIndex);
				}
			},
			search: function () {
				this.clientTableFieldList = [];
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.get('/clientTableFieldMp/query', this.searchData).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						_this.clientTableFieldList = res.data.result;
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			del: function (client, index) {
				var cName = "";
				if(this.clientTableFieldList.length==1){
					cName = "确认删除整张表数据？";
				}else{
					cName = "确认删除此数据？";
				}
				var _this = this;
				var layerIndex = layer.confirm(cName, {
					btn: ['确认删除','取消'] //按钮
				}, function(){
					if(_this.clientTableFieldList.length==1){
						layer.close(layerIndex);
						var loadIndex = layerLoad();
						_this.$http.post('/clientTableFieldMp/selectField' , _this.trim(client)).then(function(res){
							layer.close(loadIndex);
							var data = res.data;
							if(data.status == "200"){
								_this.clientTableFieldList.splice(index, 1);
							} else {
								layer.msg(res.data.message);
							}
						});
					}else{
						layer.close(layerIndex);
						var loadIndex = layerLoad();
						_this.$http.delete('/clientTableFieldMp?id=' + client.id).then(function(res){
							layer.close(loadIndex);
							var data = res.data;
							if(data.status == "200"){
								_this.clientTableFieldList.splice(index, 1);
							} else {
								layer.msg(res.data.message);
							}
						});
					}

				}, function(){
					layer.close(layerIndex);
				});
			},
			edit: function (clientTableField, index) {
				clientTableField.edit = true;
				this.changeClientCode(clientTableField, index);
				Vue.set(this.clientTableFieldList, index, clientTableField);
			},
			changeClientCode: function (clientTableField, index) {
				clientTableField.clientTableList = [];
				var _this = this;
				var loadIndex = layerLoad();
				var queryClientTable = {clientCode : clientTableField.clientCode };
				if(clientTableField.clientCode){
					this.$http.get('/clientTable/query', queryClientTable).then(function(res){
						layer.close(loadIndex);
						var data = res.data;
						if(data.status == "200"){
							if(typeof(index) != "undefined"){
								clientTableField.clientTableList = res.data.result;
								Vue.set(_this.clientTableFieldList, index, clientTableField);
							} else {
								_this.toAddClientTableField.clientTableList = res.data.result;
								if(_this.toAddClientTableField.clientTableList.length > 0){
									_this.toAddClientTableField.tableName = _this.toAddClientTableField.clientTableList[0].tableName;
								}
								var _tmp = _this.toAddClientTableField;
								_this.toAddClientTableField = {};
								_this.toAddClientTableField = _tmp;
							}
						} else {
							layer.msg(res.data.message);
						}
					});
				} else {
					layer.close(loadIndex);
				}

			},
			save: function(clientTableField, index){
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.put('/clientTableFieldMp', this.trim(clientTableField)).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						clientTableField.edit = false;
						Vue.set(_this.clientTableFieldList, index, clientTableField);
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			add: function(toAddClientTableField){
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.post('/clientTableFieldMp', this.trim(toAddClientTableField)).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						var _toAddClientTableField = res.data.result;
						Vue.set(_this.clientTableFieldList, _this.clientTableFieldList.length, _toAddClientTableField);
						_this.toAddClientTableField = JSON.parse(JSON.stringify(_this.newClientTableField));
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			getClientName: function(clientCode){
				var index = -1;
				this.clientList.forEach(function (_client, _index) {
					if (_client.code == clientCode) {
						index = _index;
						return false;
					}
				});
				if(index != -1){
					return this.clientList[index].name;
				} else {
					return clientCode;
				}

			},
			getBaseTableName: function(clientCode){
				var index = -1;
				this.clientList.forEach(function (_client, _index) {
					if (_client.code == clientCode) {
						index = _index;
						return false;
					}
				});
				if(index != -1){
					return this.clientList[index].name;
				} else {
					return clientCode;
				}

			},
			goClientTableFieldOrg: function(clientTableField){
				var searchData = {
					clientCode: clientTableField.clientCode,
					tableName: clientTableField.tableName
				};
				var loadIndex = layerLoad();
				this.$http.get('/clientTable/query', searchData).then(function (res) {
					layer.close(loadIndex);
					var data = res.data;
					if (data.status == "200") {
						var baseTableName = res.data.result[0].baseTableName;
						window.location.href = "/page/clientTableFieldOrg?"
								+ "code=" + clientTableField.clientCode
								+ "&baseTableName=" + baseTableName
								+ "&baseTableField=" + clientTableField.baseTableField;
					} else {
						layer.msg(res.data.message);
					}
				});

			},
			trim: function (data) {
				for(var key in data) {
					if(typeof data[key] == "string"  && data[key] != null && data[key] != ""){
						var val = data[key].replace(/(^\s*)|(\s*$)/g, "");
						data[key]= val;
					}
				}
				return data;
			}
		}
	});

</script>

</body>
</html>
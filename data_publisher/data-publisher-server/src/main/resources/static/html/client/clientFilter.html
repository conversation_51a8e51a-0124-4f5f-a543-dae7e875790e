<!DOCTYPE html>
<html>
<head>
	<base href="../../../" />
	<meta charset="utf-8">
	<title>layui</title>
	<meta name="renderer" content="webkit">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<link rel="stylesheet" href="/plugin/layui/css/layui.css">
	<link rel="stylesheet" href="/plugin/layui/css/admin.css">

	<!-- 注意：如果你直接复制所有代码到本地，上述css路径需要改成你本地的 -->
</head>
<body>
<div class="layui-fluid" id="clientFilter">
	<div class="layui-card">
		<div class="layui-card-header">客户端数据过滤管理</div>
		<div class="layui-card-body layui-row layui-col-space10">
			<div class="layui-col-xs3">
				<label class="layui-form-label">客户端名称</label>
				<div class="layui-input-block search-select" id="user-select">
					<select  v-model="searchData.clientCode" >
						<option value="" >请选择</option>
						<option v-cloak v-for="client  in clientList"  :value="client.code" >{{client.name}}</option>
					</select>
				</div>
			</div>
			<div class="layui-col-xs3">
				<label class="layui-form-label">表名</label>
				<div class="layui-input-block">
					<input type="text" name="title" lay-verify="title" autocomplete="off" placeholder="表名" class="layui-input" v-model="searchData.tableName">
				</div>
			</div>
			<div class="layui-col-xs1">

			</div>
			<div class="layui-col-xs3">
				<button class="layui-btn layui-btn-normal" @Click="search()">查询</button>
			</div>
		</div>
	</div>

	<div class="layui-card" >
		<div class="layui-card-body layui-row layui-col-space10">
			<table class="layui-table" lay-even="" lay-skin="row">
				<colgroup>
					<col width="60">
					<col width="120">
					<col width="120">
					<col width="150">
					<col width="150">
					<col width="150">
					<col width="150">
				</colgroup>
				<thead>
				<tr>
					<th>序号</th>
					<th>客户端名称</th>
					<th>源表名</th>
					<th>过滤节点</th>
					<th>过滤字段</th>
					<th>过滤值</th>
					<th>过滤规则</th>
					<th>操作</th>
				</tr>
				</thead>
				<tbody>
				<tr v-for="(clientFilter, index) in clientFilterList">
					<td v-cloak>{{index + 1}}</td>
					<td v-cloak>
						<div v-if="!clientFilter.edit" v-cloak class="layui-table-cell">{{getClientName(clientFilter.clientCode)}}</div>
						<div v-else id="user-select">
							<select  v-model="clientFilter.clientCode">
								<option v-cloak v-for="client  in clientList"  :value="client.code" >{{client.name}}</option>
							</select>
						</div>
					</td>

					<td v-cloak>
						<div v-cloak v-if="!clientFilter.edit" class="layui-table-cell">{{clientFilter.tableName}}</div>
						<input v-else type="text" class="layui-input layui-table-edit" v-model="clientFilter.tableName">
					</td>
					<td v-cloak>
						<div v-cloak v-if="!clientFilter.edit" class="layui-table-cell">{{clientFilter.filterNode}}</div>
						<input v-else type="text" class="layui-input layui-table-edit" v-model="clientFilter.filterNode">
					</td>
					<td v-cloak>
						<div v-cloak v-if="!clientFilter.edit" class="layui-table-cell">{{clientFilter.filterKey}}</div>
						<input v-else type="text" class="layui-input layui-table-edit" v-model="clientFilter.filterKey">
					</td>
					<td v-cloak>
						<div v-cloak v-if="!clientFilter.edit" class="layui-table-cell">{{clientFilter.filterValue}}</div>
						<input v-else type="text" class="layui-input layui-table-edit" v-model="clientFilter.filterValue">
					</td>
					<td v-cloak>
						<div v-cloak v-if="!clientFilter.edit" class="layui-table-cell">{{clientFilter.filterRule == 'approve'? '通过' : (clientFilter.filterRule == 'intercept' ? '拦截': '--')}}</div>
						<div v-else id="user-select">
						<select  v-model="clientFilter.filterRule">
							<option v-cloak v-for="rule  in ruleList"  :value="rule.code" >{{rule.name}}</option>
						</select>
						</div>
						<!--<input v-else type="text" class="layui-input layui-table-edit" v-model="clientFilter.filterRule">-->
					</td>
					<td v-cloak>
						<div class="layui-table-cell laytable-cell-1-10">
							<a v-if="!clientFilter.edit" class="layui-btn layui-btn-xs" lay-event="edit" @Click="edit(clientFilter, index)">编辑</a>
							<a v-else class="layui-btn layui-btn-xs layui-btn-warm" lay-event="edit" @Click="save(clientFilter, index)">保存</a>
							<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del" @Click="del(clientFilter, index)">删除</a>
						</div>
					</td>
				</tr>
				<tr>
					<td v-cloak>{{clientFilterList.length + 1}}</td>
					<td v-cloak>
						<div id="user-select">
							<select  v-model="toAddclientFilter.clientCode">
								<option v-cloak v-for="client  in clientList"  :value="client.code" >{{client.name}}</option>
							</select>
						</div>
					</td>
					<td v-cloak>
						<input type="text" class="layui-input layui-table-edit" v-model="toAddclientFilter.tableName">
					</td>
					<td v-cloak>
						<input type="text" class="layui-input layui-table-edit" v-model="toAddclientFilter.filterNode">
					</td>
					<td v-cloak>
						<input type="text" class="layui-input layui-table-edit" v-model="toAddclientFilter.filterKey">
					</td>
					<td v-cloak>
						<input type="text" class="layui-input layui-table-edit" v-model="toAddclientFilter.filterValue">
					</td>
					<td v-cloak>
					<!--	<select  v-model="toAddclientFilter.filterRule" >
							<option  value="-" >请选择</option>
							<option v-cloak  value="intercept" >拦截</option>
							<option v-cloak  value="approve" >通过</option>
						</select>-->
						<select  v-model="toAddclientFilter.filterRule">
							<option v-cloak v-for="rule  in ruleList"  :value="rule.code" >{{rule.name}}</option>
						</select>
						<!--<input type="text" class="layui-input layui-table-edit" v-model="toAddclientFilter.filterRule">-->
					</td>
					<td v-cloak>
						<div class="layui-table-cell laytable-cell-1-10">
							<a  class="layui-btn layui-btn-xs" lay-event="edit" @Click="add(toAddclientFilter)">添加</a>
						</div>
					</td>
				</tr>
				</tbody>
			</table>
		</div>
	</div>
</div>
<script src="/plugin/jquery/jquery-1.10.1.min.js"></script>
<script src="/plugin/layer/layer.js"></script>
<script src="/plugin/layui/layui.js"></script>
<script type="text/javascript" src="/plugin/vue/vue.min.js"></script>
<script type="text/javascript" src="/plugin/vue/vue-resource.js"></script>

<script type="text/javascript">

	Vue.http.headers.common['Authorization'] = 'Bearer ' + localStorage.token;
	var clientFilterVue = new Vue({
		el: '#clientFilter',
		data: {
			clientFilterList: [],
			clientList: [],
			searchData:{
				clientCode:"",
				tableName:""
			},
			ruleList: [{
				code: "intercept",
				name:"拦截"
			},{
				code: "approve",
				name:"通过"
			}],
			toAddclientFilter: {},
			newclientFilter: {
				"clientCode": "",
				"tableName": "",
				"baseTableName": ""
			}
		},
		mounted: function () {
			this.getClientList();
			this.search();
			this.toAddclientFilter = JSON.parse(JSON.stringify(this.newclientFilter));
		},
		watch: {
			searchData: {
				handler(newValue, oldValue) {
					this.toAddclientFilter.clientCode = this.searchData.clientCode;
				},
				deep: true
			}
		},
		methods:{
			getClientList: function(){
				this.clientList = [];
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.get('/client/query', this.searchData).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						_this.clientList = res.data.result;
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			search: function () {
				var _this = this;
				_this.clientFilterList = [];
				var loadIndex = layerLoad();
				this.$http.get('/clientFilter/query', this.searchData).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						_this.clientFilterList = res.data.result;
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			del: function (clientFilter, index) {
				var _this = this;
				var layerIndex = layer.confirm('删除此表后，不会再更新此表数据', {
					btn: ['确认删除','取消'] //按钮
				}, function(){
					var loadIndex = layerLoad();
					_this.$http.delete('/clientFilter?id=' + clientFilter.id).then(function(res){
						layer.close(loadIndex);
						var data = res.data;
						if(data.status == "200"){
							_this.clientFilterList.splice(index, 1);
						} else {
							layer.msg(res.data.message);
						}
					});
					layer.close(layerIndex);
				}, function(){
					layer.close(layerIndex);
				});

			},
			edit: function (clientFilter, index) {
				//成功后回调
				clientFilter.edit = true;
				Vue.set(this.clientFilterList, index, clientFilter);
				layui.use('form', function(){
					var form = layui.form;
					form.render('select')
				});
			},
			save: function(clientFilter, index){
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.put('/clientFilter', this.trim(clientFilter)).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						clientFilter.edit = false;
						Vue.set(_this.clientFilterList, index, clientFilter);
					} else {
						layer.msg(res.data.message);
					}
				});
			},
			add: function(toAddclientFilter){
				var _this = this;
				var loadIndex = layerLoad();
				this.$http.post('/clientFilter', this.trim(toAddclientFilter)).then(function(res){
					layer.close(loadIndex);
					var data = res.data;
					if(data.status == "200"){
						toAddclientFilter = res.data.result;
						Vue.set(_this.clientFilterList, _this.clientFilterList.length, JSON.parse(JSON.stringify(toAddclientFilter)));
						_this.toAddclientFilter = JSON.parse(JSON.stringify(_this.newclientFilter));
					} else {
						layer.msg(res.data.message);
					}
				});

			},
			getClientName: function(clientCode){
				var index = -1;
				this.clientList.forEach(function (_client, _index) {
					if (_client.code == clientCode) {
						index = _index;
						return false;
					}
				});
				if(index != -1){
					return this.clientList[index].name;
				} else {
					return clientCode;
				}

			},
			trim: function (data) {
				for(var key in data) {
					if(typeof data[key] == "string" && data[key] != null && data[key] != ""){
						var val = data[key].replace(/(^\s*)|(\s*$)/g, "");
						data[key]= val;
					}
				}
				return data;
			}
		}
	});
</script>

</body>
</html>
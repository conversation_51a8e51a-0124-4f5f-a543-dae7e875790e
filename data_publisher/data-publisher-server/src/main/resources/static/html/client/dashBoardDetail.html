<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>layui</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link rel="stylesheet" href="../../plugin/layui/css/layui.css">
  <link rel="stylesheet" href="../../plugin/layui/css/admin.css">
  <style type="text/css">
	  .red {
		  color: rgb(255, 87, 34) !important;
	  }
	  .red.layui-elem-quote {
		  border-left: 5px solid #FF5722;
	  }
	  .yellow {
		  color: rgb(255, 184, 0) !important;
	  }
	  .yellow.layui-elem-quote {
		  border-left: 5px solid #FFB800;
	  }
	  .green {
		  color: rgb(0, 150, 136) !important;
	  }
	  .green.layui-elem-quote {
		  border-left: 5px solid #009688;
	  }
    </style>
  <!-- 注意：如果你直接复制所有代码到本地，上述css路径需要改成你本地的 -->
</head>
<body>
<div id="detailBatch">
	<div class="layui-card" >
		<div class="layui-card-header">主批次号：<span v-cloak>{{mainBatchNo}}</span><a class="layui-btn layui-btn-xs" lay-event="edit" @Click="search(mainBatchNo)">刷新</a></div>

		<div class="layui-card-header">处理优先级：
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">转换平台: </label>
			<div class="layui-input-block">
				<input type="text" name="title" required lay-verify="required" placeholder="数字越小优先级越高" v-model="transOrder" class="layui-input layui-input-inline">
				<a class="layui-btn layui-btn-xs" lay-event="editTrans" @Click="editTransOrder()">保存</a>
			</div>
		</div>
		<div v-if="publishOrderShow" class="layui-form-item">
			<label class="layui-form-label">发布平台: </label>
			<div class="layui-input-block">
				<input type="text" name="title" required lay-verify="required" placeholder="数字越小优先级越高" v-model="publishOrder" class="layui-input layui-input-inline">
				<a class="layui-btn layui-btn-xs" lay-event="editTrans" @Click="editPublishOrder()">保存</a>
			</div>
		</div>


		<div class="layui-card-body layui-row layui-col-space10 ">
			<table class="layui-table" lay-even="" lay-skin="row">
				<colgroup>
					<col width="10">
					<col width="50">
					<col width="150">
					<col width="100">
					<col width="100">
					<col width="100">
					<col width="100">
					<col width="100">
					<col width="50">
				</colgroup>
				<thead>
				<tr>
					<th>序号</th>
					<th>批次号</th>
					<th>时间段</th>
					<th>来源</th>
					<th>客戶端</th>
					<th>服务名</th>
					<th>节点名</th>
					<th>当前状态</th>
					<!--	<th>进行中（条）</th>-->
					<th>详情</th>
				</tr>
				</thead>
				<tbody>
				<tr v-for="(batch, index) in detailBatchList" :key="batch.id" v-bind:class="getStatsClass(batch.status)">
					<td v-cloak>{{index + 1}}</td>
					<td v-cloak>
						{{batch.batchNo}}
					</td>
					<td v-cloak>
						{{batch.startTimeStr}} - {{batch.endTimeStr}}
					</td>
					<td v-cloak>
						{{batch.dataSource=='transfer'?'转换平台':'发布平台'}}
					</td>
					<td v-cloak>
						{{batch.clientCode}}
					</td>
					<td v-cloak>
						{{batch.serviceName}}
					</td>
					<td v-cloak>
						{{batch.nodeName}}
					</td>
					<td v-cloak>
						{{getStatsStr(batch.status)}}
					</td>
					<!--	<td v-cloak>
                            <a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="detail" @Click="popup(batch,'101')">{{batch.processingNum}}</a>
                            <a  v-if="batch.processingNum > 0" class="layui-btn layui-btn-xs" lay-event="detail" @Click="repush(batch, '101')">发送</a>
                        </td>-->
					<td v-cloak>
						<a class="layui-btn layui-btn-normal layui-btn-xs" @Click="dataTrace(batch.batchNo, batch.clientCode, batch.status, batch.dataSource)">详情</a>
					</td>
				</tr>
				</tbody>
			</table>
		</div>
	</div>
</div>
<script src="../../plugin/jquery/jquery-1.10.1.min.js"></script>
<script src="../../plugin/layer/layer.js"></script>
<script src="../../plugin/layui/layui.js"></script>
<script src="../../js/common.js"></script>
<script type="text/javascript" src="../../plugin/vue/vue.min.js"></script>
<script type="text/javascript" src="../../plugin/vue/vue-resource.js"></script>

<script type="text/javascript">

Vue.http.headers.common['Authorization'] = 'Bearer ' + localStorage.token;
var detailBatch = new Vue({
    el: '#detailBatch',
    data: {
		transOrder : 0,
		publishOrder : 0,
		publishOrderShow : false,
		detailBatchList: [],
		mainBatchNo: "",
		status: "",
		mainOrder: 1,
    },
    mounted: function () {
        this.mainBatchNo = this.getQueryString("mainBatchNo");
        this.status = this.getQueryString("status");
        if(this.mainBatchNo != null){
            this.search(this.mainBatchNo);
            this.getOrder(this.mainBatchNo,this.status);
        }
    },
    methods:{
        search: function (mainBatchNo) {
            //成功后回调
            var _this = this;
            _this.dataTraceAggList = [];
            var loadIndex = layerLoad();
            this.$http.get('/flBatchInfo/detailBatch?mainBatchNo=' + mainBatchNo).then(function(res){
                layer.close(loadIndex);
                if(res.data.status == "200"){
                    _this.detailBatchList = res.data.result;
                } else {
                    layer.msg(res.data.message);
                }
            });
        },
		getOrder: function (mainBatchNo, status) {
            //成功后回调
            var _this = this;
            var loadIndex = layerLoad();
            this.$http.get('/flMainBatchInfo/transferOrder?mainBatchNo=' + mainBatchNo).then(function(res){
                layer.close(loadIndex);
                if(res.data.status == "200"){
                    _this.transOrder = res.data.result;
                } else {
                    layer.msg(res.data.message);
                }
            });
			if (status && status.startsWith("p")){
				this.publishOrderShow = true;
				this.$http.get('/flMainBatchInfo/publishOrder?mainBatchNo=' + mainBatchNo).then(function(res){
					layer.close(loadIndex);
					if(res.data.status == "200"){
						_this.publishOrder = res.data.result;
					} else {
						layer.msg(res.data.message);
					}
				});
			}
        },
		editTransOrder: function () {
			if (!isNaN(parseFloat(this.transOrder)) && isFinite(this.transOrder)){
				var data = {
					"mainBatchNo":this.mainBatchNo,
					"transferOrder":this.transOrder
				}
				this.$http.post('/flMainBatchInfo/updateTransferOrder', data).then(function(res){
					var data = res.data;
					if(data.status == "200"){
						layer.msg(res.data.message);
					} else {
						layer.msg(res.data.message);
					}
				});
			}else{
				layer.msg("请输入数字");
			}

        },
		editPublishOrder: function () {
			if (!isNaN(parseFloat(this.publishOrder)) && isFinite(this.publishOrder)) {
				var data = {
					"mainBatchNo": this.mainBatchNo,
					"publishOrder": this.publishOrder
				}
				this.$http.post('/flMainBatchInfo/updatePublishOrder', data).then(function (res) {
					var data = res.data;
					if (data.status == "200") {
						layer.msg(res.data.message);
					} else {
						layer.msg(res.data.message);
					}
				});
			}else{
				layer.msg("请输入数字");
			}
        },
        getDateString: function  (startTime, endTime){
            if(startTime == endTime){
                return DateUtil.formatLongToDate(startTime);
			}
            return DateUtil.formatLongToDate(startTime) + " - " + DateUtil.formatLongToDate(endTime);
        },
        getQueryString: function  (name){
            var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
            var r = window.location.href.split("?")[1].match(reg);
            if(r!=null) return unescape(r[2]); return null;
        },
        getStatsClass:function (status) {
            if(status == "500"){
                return "red";
            } else if (status == "200"){
                return "green";
            } else if (status == "101"){
                return "yellow";
            }
        },
        getStatsStr:function (status) {
            if(status == "500"){
                return "失败";
            } else if (status == "200"){
                return "成功";
            } else if (status == "101"){
                return "处理中";
            }
        },
		dataTrace:function(batchNo, clientCode, status, dataSource){
			//var url = "dataDetail?batchNo=" + data.batchNo + "&clientCode=" + data.clientCode + "&staus=" + data.status
			layer.open({
				type: 2,
				skin: 'layui-layer-rim', //加上边框
				area: ['700px', '700px'], //宽高
				title: '批次信息',
				content: 'dataTrace?batchNo=' + batchNo + '&clientCode=' + clientCode + '&status=' + status + '&dataSource='+dataSource
			});
		},
    }
});

</script>

</body>
</html>

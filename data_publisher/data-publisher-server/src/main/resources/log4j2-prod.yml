Configuration:
  status: warn

  Properties:
    Property:
      -  name: logFormat
         value: '%d{yyyy-MM-dd HH:mm:ss.SSS}{GMT+8} [%thread]  %-5level  %logger{35}  %msg  %n'

  Appenders:
    Console:  #输出到控制台
      name: Console
      target: SYSTEM_OUT
      ThresholdFilter:
        level: warn
        onMatch: ACCEPT
        onMismatch: DENY
#      JsonLayout:
#        complete: true
#        locationInfo: true
      PatternLayout:
        charset: utf-8
        pattern: ${logFormat}

    RollingFile:
       name: RollingFile
       fileName: /jylog/dataPublisher/dataPublisherServer/dataPublisher.log
       filePattern: '/jylog/dataPublisher/dataPublisherServer/dataPublisher.%d{yyyy-MM-dd_HH_mm_ss}.log'
       PatternLayout:
         charset: utf-8
         Pattern: ${logFormat}
       Policies:
         SizeBasedTriggeringPolicy:
           size: 512 MB
    Async:
      - name: AsyncRollingFile
        AppenderRef:
          ref: RollingFile

  Loggers:
      Root:
       AppenderRef:
         -  ref: Console
         -  ref: AsyncRollingFile
       level: warn


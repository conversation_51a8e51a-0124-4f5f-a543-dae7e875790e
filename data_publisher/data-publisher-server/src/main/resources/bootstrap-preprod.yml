spring:
  rabbitmq:
    host: **********
    port: 5672
    username: admin
    password: admin
    clienttoplimit: 1000000
    dataTraceInfo: data-trace-exchange,data-trace-routing-key,data-trace-queue,direct
  data:
    mongodb:
      host: **********
      port: 27017
      authentication-database: publish_data
      database:  publish_data
      username:  user_prod
      password:  user_prod
      option:
        min-connection-per-host: 100
        max-connection-per-http://*********/host: 500
  datasource:
      type: com.alibaba.druid.pool.DruidDataSource
      #抓取SQL运行时的参数值
      # url: jdbc:log4jdbc:mysql://**************:3306/vehicle?useUnicode=true&characterEncoding=utf-8&autoReconnect=true&failOverReadOnly=false
      url: *****************************************************************************************************************
      username: jy
      password: 123
      #抓取SQL运行时的参数值
      #driver-class-name: net.sf.log4jdbc.DriverSpy
      driver-class-name: com.mysql.jdbc.Driver

      # 初始化大小，最小，最大
      initialSize: 5
      minIdle: 5
      maxActive: 50
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 'x'
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      # 打开PSCache，并且指定每个连接上PSCache的大小
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 50
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,slf4j
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      # 合并多个DruidDataSource的监控数据
      #spring.datasource.useGlobalDataSourceStat=true

  redis:
    host: **********
    password: 123456
    port: 6379
    pool:
      max-idle: 20
      min-idle: 10
      max-active: 100
      max-wait: 2000
# 缓存配置
  cache:
    type: ehcache
    ehcache:
      config: classpath:ehcache.xml
  freemarker:
    # 启用 freemarker 模板
    enabled: true
    # 是否缓存
    cache: false
    # 编码
    charset: utf-8
    # 模板所在位置（目录）
    template-loader-path:
      - classpath:/templates/
    settings:
      datetime_format: yyyy-MM-dd HH:mm:ss      # date 输出格式化
      template_update_delay: 30m                # 模板引擎刷新时间
      default_encoding: utf-8                   # 默认编码
thread:
  corePoolSize: 20
  maxPoolSize: 30
  queueCapacity: 10
  keepAliveSeconds: 3000
  threadName: facadeasync-service-

cache:
  longExpiration: 864000
  expiration: 86400
  shortExpiration: 3600

security:
  oauth2:
    resource:
      filter-order: 3

clientConfig:
  cacheExpiration: 360
  redisSetName: client
  batchToplimit: 100

logging:
  level:
    org.springframework.security: warn
    org.springframework: warn
    com.jy.mapper: warn
  config: classpath:log4j2-preprod.yml

facade:
  authorization: YW5kcm9pZDphbmRyb2lk
  env1:
    url: http://192.168.120.10:8765/
    username: admin
    password: admin2018
  env2:
    url: http://192.168.80.53:8765/
    username: admin
    password: admin2018
  env3:
    url: http://192.168.120.10:8765/
    username: admin
    password: admin2018
  env4:
    url: http://192.168.100.71:30320/
    username: admin
    password: 76b47ec5b90bac30
  email:
    emailPath: common-service/email
    emailTo: <EMAIL>,<EMAIL>,<EMAIL>
  #蔚来请求地址
thePath:
  nioLifeUrl: https://artemis-api.nio.com/api/v2
  nioLifeDataPath: /api/v2/acsData/acsPartInfo
  nioLifePicPath: /api/v2/acsData/findLegend
  nioLifeAppId: 90011001
  nioLifeSecret: secret=8f5cbb89638520b2f9d06ebec4352c4b

httpClient:
  socketTimeOut: 15000
  connectTimeOut: 15000
compare:
  clientComparePath: compares
  clientTablePath: tables
  serverUrl:
  serverPath: compares

serviceName: 产品数据发布平台
srcData:
  filePath: /data/data-publisher/
ElasticsearchConfig:
  host: *********
  port: 9200
feishu:
  proxy:
    host: **************
    port: 80
    userName:
    password:
  bot:
    host: https://open.feishu.cn
    path: /open-apis/bot/v2/hook/c6937e87-60b4-471b-824a-bf8ae675b069
    secret: kaBZCa2G5coMT1mr2K2bX
    hostName: ***********

transfer:
  authorization: YW5kcm9pZDphbmRyb2lk
  username: SRC_PART
  password: src_part
  url: http://*************:8080/
  transferOrder:
    get: ${transfer.url}receiveBatchNos/transferOrder
    update: ${transfer.url}receiveBatchNos/updateOrder


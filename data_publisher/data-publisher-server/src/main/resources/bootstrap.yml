server:
  port: 443
  ssl:
    key-store: classpath:keystore.p12
    key-store-password: 464638
    keyStoreType: PKCS12
    keyAlias: tomcat
  http:
    port: 8091

spring:
  profiles:
    active: dev
#    active: test
#    active: prod
  thymeleaf:
    cache: false
    prefix: classpath:/static/
    check-template-location: true
    suffix: .html
    encoding: UTF-8
    content-type: text/html
    mode: LEGACYHTML5
  application:
    name: dataPublisher

mybatis:
  typeAliasesPackage: com.jy.bean
  mapperLocations: classpath:mapper/*.xml
  configuration:
    #开启驼峰命名转换
    mapUnderscoreToCamelCase: true
info:
  version: 2.7.3

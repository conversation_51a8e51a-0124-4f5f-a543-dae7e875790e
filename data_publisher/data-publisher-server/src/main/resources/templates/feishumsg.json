{
  "timestamp": "${dto.timestamp}",
  "sign": "${dto.sign}",
  "msg_type": "interactive",
  "card": {
    "config": {
      "wide_screen_mode": true
    },
    "elements": [{
      "fields": [{
        "is_short": true,
        "text": {
          "content": "**开始时间**\n${dto.startTime}",
          "tag": "lark_md"
        }
      },{
        "is_short": true,
        "text": {
          "content": "**异常时间**\n${dto.endTime}",
          "tag": "lark_md"
        }
      }, {
        "is_short": true,
        "text": {
          "content": "**服务器**\n${dto.hostName}",
          "tag": "lark_md"
        }
      }, {
        "is_short": true,
        "text": {
          "content": "**方法名**\n${dto.methodName}",
          "tag": "lark_md"
        }
      }],

      "tag": "div"
    }, {
      "tag": "div",
      "text": {
        "content": "**异常信息**\n",
        "tag": "lark_md"
      }
    }, {
      "tag": "div",
      "text": {
        "content": ${dto.msg},
        "tag": "lark_md"
      }
    }, {
      "tag": "hr"
    }, {
      "elements": [{
        "content": "${dto.remark}",
        "tag": "lark_md"
      }],
      "tag": "note"
    }],
    "header": {
      "template": "red",
      "title": {
        "content": "【应急通知】及时更新发生异常",
        "tag": "plain_text"
      }
    }
  }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jy.mapper.ClientMapper">

    <select id="listClient" resultType="com.jy.bean.po.Client">
        select *
        from
        client p
        <where>
            1=1
            <if test="_parameter.containsKey('id') and id != null and id != ''">
                and p.id = #{id}
            </if>
            <if test="_parameter.containsKey('name') and name != null and name != ''">
                and p.name like CONCAT('%','${name}','%')
            </if>
            <if test="_parameter.containsKey('code') and code != null and code != ''">
                and p.code = #{code}
            </if>
            <if test="_parameter.containsKey('status') and status != null and status != ''">
                and p.status = #{status}
            </if>
            <if test="_parameter.containsKey('path') and path != null and path != ''">
                and p.path = #{path}
            </if>
            <if test="_parameter.containsKey('removeStatus') and removeStatus != null and removeStatus != ''">
                and p.status != #{removeStatus}
            </if>
            <if test="_parameter.containsKey('removeCode') and removeCode != null and removeCode != ''">
                and not p.code = #{removeCode}
            </if>
            <if test="_parameter.containsKey('removePath') and removePath != null and removePath != ''">
                and not p.path = #{removePath}
            </if>
        </where>
        order by path,u_time desc
    </select>

    <select id="listClientByBaseTable" resultType="com.jy.bean.po.Client">
        select p.*
        from
        client p, client_table t
        <where>
            p.code = t.client_code
            <if test="_parameter.containsKey('baseTableName') and baseTableName != null and baseTableName != ''">
                and t.base_table_name = #{baseTableName}
            </if>
        </where>
    </select>

    <sql id="Base_Column_List" >
        id,
        code,
        name,
        ip,
        path,
        status,
        top_limit,
        receive_limit,
        c_by,
        c_time,
        u_by,
        u_time,
        del_flag,
        remark
    </sql>

    <insert id="save" parameterType="com.jy.bean.po.Client" >
        INSERT  INTO client (<include refid="Base_Column_List" />)
        values(
        #{id,jdbcType=VARCHAR},
        #{code,jdbcType=VARCHAR},
        #{name,jdbcType=VARCHAR},
        #{ip,jdbcType=VARCHAR},
        #{path,jdbcType=VARCHAR},
        #{status,jdbcType=VARCHAR},
        #{topLimit,jdbcType=INTEGER},
        #{receiveLimit,jdbcType=INTEGER},
        #{cBy,jdbcType=VARCHAR},
        NOW(),
        #{uBy,jdbcType=VARCHAR},
        NOW(),
        #{delFlag,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR}
        )
    </insert>

    <delete id="delete" >
        delete from client
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <update id="update" parameterType="com.jy.bean.po.Client" >
        update client
        <set>
            <if test="code != null">
                code = #{code},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="ip != null">
                ip = #{ip},
            </if>
            <if test="path != null">
                path = #{path},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="topLimit != null">
                top_limit = #{topLimit,jdbcType=INTEGER},
            </if>
            <if test="receiveLimit != null">
                receive_limit = #{receiveLimit,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="uBy != null">
                u_by = #{uBy,jdbcType=VARCHAR},
            </if>
            u_time = now()
        </set>
        <where>
            <choose>
                <when test="id != null">
                    id = #{id,jdbcType=VARCHAR}
                </when>
                <otherwise>
                    code = #{code,jdbcType=VARCHAR}
                </otherwise>
            </choose>
        </where>
    </update>

</mapper>
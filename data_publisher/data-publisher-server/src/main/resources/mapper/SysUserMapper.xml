<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jy.mapper.SysUserMapper">
    <select id="listSysUser" resultType="com.jy.bean.po.SysUser">
    select *
    from
    sys_user p
    <where>
        1 = 1
        <if test="_parameter.containsKey('id') and id != null and id != ''">
            and p.id = #{id}
        </if>
        <if test="_parameter.containsKey('username') and username != null and username != ''">
            and p.username = #{username}
        </if>
    </where>
</select>

    <select id="listSortFieldSysUser" resultType="com.jy.bean.po.SysUser">
        select p.id, p.username, p.email
        from
        sys_user p
        <where>
            1 = 1
            <if test="_parameter.containsKey('id') and id != null and id != ''">
                and p.id = #{id}
            </if>
            <if test="_parameter.containsKey('username') and username != null and username != ''">
                and p.username = #{username}
            </if>
        </where>
    </select>

    <sql id="Base_Column_List" >
        id,username,password,email,imageUrl
    </sql>

    <insert id="save" parameterType="com.jy.bean.po.SysUser" >
        INSERT  INTO sys_user (<include refid="Base_Column_List" />)
        values(
        #{id,jdbcType=VARCHAR},#{username,jdbcType=VARCHAR},#{password,jdbcType=VARCHAR},
        #{email,jdbcType=VARCHAR},#{imageUrl,jdbcType=VARCHAR}
        )
    </insert>

    <delete id="delete" >
        delete from sys_user
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <update id="update" parameterType="com.jy.bean.po.SysUser" >
        update sys_user
        <set>
<!--            <if test="username != null">
                username = #{username},
            </if>-->
            <if test="password != null">
                password = #{password},
            </if>
            <if test="imageUrl != null">
                imageUrl = #{imageUrl},
            </if>
            <if test="email != null">
                email = #{email},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
</mapper>
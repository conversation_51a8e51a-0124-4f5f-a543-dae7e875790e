<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jy.mapper.BatchDetailMapper">

    <select id="listBatchDetail" resultType="com.jy.bean.po.BatchDetail">
        select *
        from
        batch_detail p
        <where>
            1=1
            <if test="_parameter.containsKey('id') and id != null and id != ''">
                and p.id = #{id}
            </if>
            <if test="_parameter.containsKey('clientCode') and clientCode != null and clientCode != ''">
                and p.client_code = #{clientCode}
            </if>
            <if test="_parameter.containsKey('status') and status != null and status != ''">
                and p.status = #{status}
            </if>
            <if test="_parameter.contains<PERSON>ey('batchNo') and batchNo != null and batchNo != ''">
                and p.batch_no = #{batchNo}
            </if>
            <if test="_parameter.containsKey('mainBatchNo') and mainBatchNo != null and mainBatchNo != ''">
                and p.main_batch_no = #{mainBatchNo}
            </if>
        </where>
        order by batch_order asc, c_time asc
    </select>

    <select id="getCount" resultType="java.lang.Integer">
        select ifnull(sum(num),0)
        from
        batch_detail p
        <where>
            1=1
            <if test="_parameter.containsKey('clientCode') and clientCode != null and clientCode != ''">
                and p.client_code = #{clientCode}
            </if>
            <if test="_parameter.containsKey('status') and status != null and status != ''">
                and p.status = #{status}
            </if>
            <if test="_parameter.containsKey('batchNo') and batchNo != null and batchNo != ''">
                and p.batch_no = #{batchNo}
            </if>
        </where>
    </select>

    <sql id="Base_Column_List">
        id,
        main_batch_no,
        batch_no,
        status,
        client_code,
        num,
        table_name,
        file_path,
        batch_order,
        org_code,
        brand_code,
        brand_name,
        c_by,
        c_time,
        u_by,
        u_time,
        del_flag,
        remark
    </sql>

    <delete id="delete" parameterType="java.lang.String">
        delete from batch_detail
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <insert id="save" parameterType="com.jy.bean.po.BatchDetail">
        insert into batch_detail (<include refid="Base_Column_List" />)
        values (
            #{id,jdbcType=VARCHAR},
            #{mainBatchNo,jdbcType=VARCHAR},
            #{batchNo,jdbcType=VARCHAR},
            #{status,jdbcType=VARCHAR},
            #{clientCode,jdbcType=VARCHAR},
            #{num,jdbcType=INTEGER},
            #{tableName,jdbcType=VARCHAR},
            #{filePath,jdbcType=VARCHAR},
            #{batchOrder,jdbcType=INTEGER},
            #{orgCode,jdbcType=VARCHAR},
            #{brandCode,jdbcType=VARCHAR},
            #{brandName,jdbcType=VARCHAR},
            #{cBy,jdbcType=VARCHAR},
            NOW(3),
            #{uBy,jdbcType=VARCHAR},
            NOW(3),
            #{delFlag,jdbcType=VARCHAR},
            #{remark,jdbcType=VARCHAR}
        )
    </insert>

    <update id="update" parameterType="com.jy.bean.po.BatchDetail">
        update batch_detail
        <set>
            <if test="mainBatchNo != null">
                main_batch_no = #{mainBatchNo,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="clientCode != null">
                client_code = #{clientCode,jdbcType=VARCHAR},
            </if>
            <if test="num != null">
                num = #{num,jdbcType=INTEGER},
            </if>
            <if test="tableName != null">
                table_name = #{tableName,jdbcType=VARCHAR},
            </if>
            <if test="filePath != null">
                file_path = #{filePath,jdbcType=VARCHAR},
            </if>
            <if test="batchOrder != null">
                batch_order = #{batchOrder,jdbcType=INTEGER},
            </if>
            <if test="orgCode != null">
                org_code = #{orgCode,jdbcType=VARCHAR},
            </if>
            <if test="brandCode != null">
                brand_code = #{brandCode,jdbcType=VARCHAR},
            </if>
            <if test="brandName != null">
                brand_name = #{brandName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="uBy != null">
                u_by = #{uBy,jdbcType=VARCHAR},
            </if>
            u_time = NOW(3)
        </set>
        where batch_no = #{batchNo,jdbcType=VARCHAR}
    </update>

    <select id="selectMaxOrder" resultType="java.lang.Integer">
        select max(t.batch_order) as batch_order
        from batch_detail t
        where main_batch_no = #{mainBatchNo}
    </select>
    <select id="updateByMainBatchNo" resultType="java.lang.Integer">
        update batch_detail
        <set>
            <if test="mainBatchNo != null">
                main_batch_no = #{mainBatchNo,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="clientCode != null">
                client_code = #{clientCode,jdbcType=VARCHAR},
            </if>
            <if test="num != null">
                num = #{num,jdbcType=INTEGER},
            </if>
            <if test="tableName != null">
                table_name = #{tableName,jdbcType=VARCHAR},
            </if>
            <if test="filePath != null">
                file_path = #{filePath,jdbcType=VARCHAR},
            </if>
            <if test="batchOrder != null">
                batch_order = #{batchOrder,jdbcType=INTEGER},
            </if>
            <if test="orgCode != null">
                org_code = #{orgCode,jdbcType=VARCHAR},
            </if>
            <if test="brandCode != null">
                brand_code = #{brandCode,jdbcType=VARCHAR},
            </if>
            <if test="brandName != null">
                brand_name = #{brandName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="uBy != null">
                u_by = #{uBy,jdbcType=VARCHAR},
            </if>
            u_time = NOW(3)
        </set>
        where main_batch_no = #{mainBatchNo,jdbcType=VARCHAR}
    </select>

    <select id="batchListByMainAndBatchNo" resultType="com.jy.bean.po.BatchDetail">
        select *
        from
        batch_detail p
        <where>
            <foreach collection="list" item="item" separator="or" open="(" close=")">
                <if test="item.mainBatchNo != null and item.mainBatchNo != ''">
                    p.main_batch_no = #{item.mainBatchNo}
                </if>
                <if test="item.mainBatchNo != null and item.mainBatchNo != ''and item.batchNo != null and item.batchNo != ''">
                     and
                </if>
                <if test="item.batchNo != null and item.batchNo != ''">
                    p.batch_no = #{item.batchNo}
                </if>
            </foreach>
        </where>
        order by batch_order asc, c_time asc
    </select>

    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update batch_detail
            <set>
                <if test="item.status != null">
                    status = #{item.status,jdbcType=VARCHAR},
                </if>
                <if test="item.clientCode != null">
                    client_code = #{item.clientCode,jdbcType=VARCHAR},
                </if>
                <if test="item.num != null">
                    num = #{item.num,jdbcType=INTEGER},
                </if>
                <if test="item.tableName != null">
                    table_name = #{item.tableName,jdbcType=VARCHAR},
                </if>
                <if test="item.filePath != null">
                    file_path = #{item.filePath,jdbcType=VARCHAR},
                </if>
                <if test="item.batchOrder != null">
                    batch_order = #{item.batchOrder,jdbcType=INTEGER},
                </if>
                <if test="item.orgCode != null">
                    org_code = #{item.orgCode,jdbcType=VARCHAR},
                </if>
                <if test="item.brandCode != null">
                    brand_code = #{item.brandCode,jdbcType=VARCHAR},
                </if>
                <if test="item.brandName != null">
                    brand_name = #{item.brandName,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.uBy != null">
                    u_by = #{item.uBy,jdbcType=VARCHAR},
                </if>
                u_time = NOW(3)
            </set>
            where batch_no = #{item.batchNo,jdbcType=VARCHAR}
        </foreach>
    </update>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jy.mapper.SysRoleMapper">
    <select id="listSysRoles" resultType="com.jy.bean.po.SysRole">
        select p.id, p.name, p.value
        from
        sys_role p, sys_user_roles m
        <where>
            m.role_id = p.id
            <if test="_parameter.containsKey('userId') and userId != null">
                and m.user_id = #{userId}
            </if>
        </where>
    </select>

</mapper>
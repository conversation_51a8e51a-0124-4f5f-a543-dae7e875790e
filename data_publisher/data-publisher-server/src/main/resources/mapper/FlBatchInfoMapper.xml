<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jy.mapper.FlBatchInfoMapper">

    <select id="listFlBatchInfo" resultType="com.jy.bean.po.FlBatchInfo">
        select
        p.id
        ,f.brand_code
        ,f.brand_name
        ,c.name as clientName
        ,p.client_code
        ,p.batch_no
        ,p.main_batch_no
        ,p.data_type
        ,p.status
        ,p.message
        ,p.service_name
        ,p.node_name
        ,p.success_num
        ,p.fail_num
        ,p.processing_num
        ,p.unsuccess_num
        ,p.c_time
        ,p.u_time
        ,p.start_time
        ,p.end_time
        ,p.data_source
        from
        fl_batch_info p LEFT JOIN  batch_detail f  ON p.batch_no = f.batch_no
        LEFT JOIN client c ON p.client_code = c.code
        <where>
            1=1 and c.status !='OFFLINE'
            <if test="_parameter.containsKey('status') and status != null">
                and p.status = #{status}
            </if>
            <if test="_parameter.containsKey('noStatus') and noStatus != null">
                and p.status != #{noStatus}
            </if>
            <if test="_parameter.containsKey('batchNo') and batchNo != null and batchNo != ''">
                and p.batch_no = #{batchNo}
            </if>
            <if test="_parameter.containsKey('mainBatchNo') and mainBatchNo != null and mainBatchNo != ''">
                and p.main_batch_no = #{mainBatchNo}
            </if>
            <if test="_parameter.containsKey('dataType') and dataType != null and dataType != ''">
                and p.main_batch_no like CONCAT('%','${dataType}','_%')
            </if>
            <if test="_parameter.containsKey('clientCode') and clientCode != null and clientCode != ''">
                and p.client_code = #{clientCode}
            </if>
            <if test="_parameter.containsKey('startTime') and startTime != null and _parameter.containsKey('endTime') and endTime != null">
                <![CDATA[
                    and ((p.start_time > #{startTime} and p.start_time < #{endTime}) or (p.end_time > #{startTime} and  p.end_time < #{endTime}))
                ]]>
            </if>
            <if test="_parameter.containsKey('dataSource') and dataSource != null and dataSource != ''">
                and p.data_source = #{dataSource}
            </if>
        </where>
        order by c_time, data_source desc
    </select>

    <sql id="Base_Column_List" >
         id
        ,batch_no
        ,main_batch_no
        ,data_type
        ,status
        ,message
        ,service_name
        ,node_name
        ,client_code
        ,success_num
        ,unsuccess_num
        ,fail_num
        ,processing_num
        ,c_time
        ,c_by
        ,u_time
        ,u_by
        ,start_time
        ,end_time
        ,data_source
    </sql>

    <insert id="insert" parameterType="com.jy.bean.po.FlBatchInfo" >
        INSERT  INTO fl_batch_info
        (batch_no
        ,main_batch_no
        ,data_type
        ,status
        ,message
        ,service_name
        ,node_name
        ,client_code
        ,success_num
        ,unsuccess_num
        ,fail_num
        ,processing_num
        ,c_time
        ,c_by
        ,u_time
        ,u_by
        ,start_time
        ,end_time
        ,data_source)
        values(
         #{batchNo,jdbcType=VARCHAR}
        ,#{mainBatchNo,jdbcType=VARCHAR}
        ,#{dataType,jdbcType=VARCHAR}
        ,#{status,jdbcType=VARCHAR}
        ,#{message,jdbcType=VARCHAR}
        ,#{serviceName,jdbcType=VARCHAR}
        ,#{nodeName,jdbcType=VARCHAR}
        ,#{clientCode,jdbcType=VARCHAR}
        ,#{successNum,jdbcType=INTEGER}
        ,#{unsuccessNum,jdbcType=INTEGER}
        ,#{failNum,jdbcType=INTEGER}
        ,#{processingNum,jdbcType=INTEGER}
        ,#{cTime,jdbcType=TIMESTAMP}
        ,#{cBy,jdbcType=VARCHAR}
        ,#{uTime,jdbcType=TIMESTAMP}
        ,#{uBy,jdbcType=VARCHAR}
        ,#{startTime,jdbcType=TIMESTAMP}
        ,#{endTime,jdbcType=TIMESTAMP}
        ,#{dataSource,jdbcType=VARCHAR}
        )
    </insert>

    <insert id="insertBatch" parameterType="list">
        INSERT  INTO fl_batch_info
        (batch_no
        ,main_batch_no
        ,data_type
        ,status
        ,message
        ,service_name
        ,node_name
        ,client_code
        ,success_num
        ,unsuccess_num
        ,fail_num
        ,processing_num
        ,c_time
        ,c_by
        ,u_time
        ,u_by
        ,start_time
        ,end_time
        ,data_source)
        <foreach collection="list" item="item" separator="union all">
            SELECT
             #{item.batchNo,jdbcType=VARCHAR}
            ,#{item.mainBatchNo,jdbcType=VARCHAR}
            ,#{item.dataType,jdbcType=VARCHAR}
            ,#{item.status,jdbcType=VARCHAR}
            ,#{item.message,jdbcType=VARCHAR}
            ,#{item.serviceName,jdbcType=VARCHAR}
            ,#{item.nodeName,jdbcType=VARCHAR}
            ,#{item.clientCode,jdbcType=VARCHAR}
            ,#{item.successNum,jdbcType=INTEGER}
            ,#{item.unsuccessNum,jdbcType=INTEGER}
            ,#{item.failNum,jdbcType=INTEGER}
            ,#{item.processingNum,jdbcType=INTEGER}
            ,#{item.cTime,jdbcType=TIMESTAMP}
            ,#{item.cBy,jdbcType=VARCHAR}
            ,#{item.uTime,jdbcType=TIMESTAMP}
            ,#{item.uBy,jdbcType=VARCHAR}
            ,#{item.startTime,jdbcType=TIMESTAMP}
            ,#{item.endTime,jdbcType=TIMESTAMP}
            ,#{item.dataSource,jdbcType=VARCHAR}
            FROM dual
        </foreach>
    </insert>

    <update id="update" parameterType="com.jy.bean.po.FlBatchInfo">
        update fl_batch_info
        <set>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            end_time = #{endTime,jdbcType=TIMESTAMP},
            u_time = now()
        </set>
        where batch_no = #{batchNo,jdbcType=VARCHAR}
        and client_code =  #{clientCode,jdbcType=VARCHAR}
    </update>

    <update id="updateTraceStatus" parameterType="com.jy.bean.po.FlBatchInfo">
        update fl_batch_info
        <set>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="unsuccessNum != null">
                unsuccess_num = #{unsuccessNum,jdbcType=INTEGER},
            </if>
        </set>
        where status ='500' and success_num > #{successNum,jdbcType=INTEGER}
        and main_batch_no like concat( #{mainBatchNo},'%')
    </update>

    <delete id="delete" >
        delete from fl_batch_info
        where batch_no = #{batchNo,jdbcType=VARCHAR}
        <if test="_parameter.containsKey('dataSource') and dataSource != null and dataSource != ''">
            and data_source = #{dataSource}
        </if>
    </delete>

    <update id="updateSelective">
        update fl_batch_info
        <set>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="message != null">
                message = #{message,jdbcType=VARCHAR},
            </if>
            <if test="serviceName != null">
                service_name = #{serviceName,jdbcType=VARCHAR},
            </if>
            <if test="nodeName != null">
                node_name = #{nodeName,jdbcType=VARCHAR},
            </if>
            <if test="clientCode != null">
                client_code = #{clientCode,jdbcType=VARCHAR},
            </if>
            <if test="successNum != null">
                success_num = #{successNum,jdbcType=INTEGER},
            </if>
            <if test="unsuccessNum != null">
                unsuccess_num = #{unsuccessNum,jdbcType=INTEGER},
            </if>
            <if test="failNum != null">
                fail_num = #{failNum,jdbcType=INTEGER},
            </if>
            <if test="processingNum != null">
                processing_num = #{processingNum,jdbcType=INTEGER},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="getById" resultType="com.jy.bean.po.FlBatchInfo">
        select
        p.id
        ,f.brand_code
        ,f.brand_name
        ,c.name as clientName
        ,p.client_code
        ,p.batch_no
        ,p.main_batch_no
        ,p.data_type
        ,p.status
        ,p.message
        ,p.service_name
        ,p.node_name
        ,p.success_num
        ,p.fail_num
        ,p.processing_num
        ,p.unsuccess_num
        ,p.c_time
        ,p.u_time
        ,p.start_time
        ,p.end_time
        ,p.data_source
        from
        fl_batch_info p LEFT JOIN  batch_detail f  ON p.batch_no = f.batch_no
        LEFT JOIN client c ON p.client_code = c.code
        where p.id = #{id}
    </select>

    <select id="countByBatchNo" resultType="java.lang.Integer">
        select count(1) from fl_batch_info where batch_no = #{batchNo} and data_source = #{dataSource} and node_name = #{nodeName}
    </select>

    <update id="updateSelectiveByBatchNo">
        update fl_batch_info
        <set>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="message != null">
                message = #{message,jdbcType=VARCHAR},
            </if>
            <if test="serviceName != null">
                service_name = #{serviceName,jdbcType=VARCHAR},
            </if>
            <if test="nodeName != null">
                node_name = #{nodeName,jdbcType=VARCHAR},
            </if>
            <if test="clientCode != null">
                client_code = #{clientCode,jdbcType=VARCHAR},
            </if>
            <if test="successNum != null">
                success_num = #{successNum,jdbcType=INTEGER},
            </if>
            <if test="unsuccessNum != null">
                unsuccess_num = #{unsuccessNum,jdbcType=INTEGER},
            </if>
            <if test="failNum != null">
                fail_num = #{failNum,jdbcType=INTEGER},
            </if>
            <if test="processingNum != null">
                processing_num = #{processingNum,jdbcType=INTEGER},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where where batch_no = #{batchNo} and data_source = #{dataSource} and node_name = #{nodeName}
    </update>

    <update id="reset">
        update fl_batch_info
        set start_time = #{startTime,jdbcType=TIMESTAMP},
        status = #{status,jdbcType=VARCHAR},
        end_time = null,
        success_num = null,
        fail_num = null,
        processing_num = null,
        unsuccess_num = null
        where batch_no = #{batchNo,jdbcType=VARCHAR}
    </update>

    <update id="resetBatch">
        <foreach collection="list" item="item" separator=";">
            update fl_batch_info
            set start_time = #{startTime,jdbcType=TIMESTAMP},
            status = #{status,jdbcType=VARCHAR},
            end_time = null,
            success_num = null,
            fail_num = null,
            processing_num = null,
            unsuccess_num = null
            where batch_no = #{item.batchNo,jdbcType=VARCHAR}
        </foreach>
    </update>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jy.mapper.FlMainBatchInfoMapper">

    <select id="listFlMainBatchInfo" resultType="com.jy.bean.po.FlMainBatchInfo">
        select
        p.client_code
        ,p.main_batch_no
        ,p.data_remark
        ,p.status
        ,p.message
        ,p.service_name
        ,p.node_name
        ,p.total_time
        ,p.wait_time
        ,p.process_time
        ,p.c_time
        ,p.u_time
        ,p.start_time
        ,p.end_time
        from
        fl_mainBatch_info p
        <where>
            1=1
            <if test="_parameter.containsKey('status') and status != null">
                and p.status = #{status}
            </if>
            <if test="_parameter.containsKey('dbStatus') and dbStatus != null">
                and p.status = #{dbStatus}
            </if>
            <if test="_parameter.containsKey('noStatus') and noStatus != null">
                and p.status != #{noStatus}
            </if>
            <if test="_parameter.containsKey('mainBatchNo') and mainBatchNo != null and mainBatchNo != ''">
                and p.main_batch_no = #{mainBatchNo}
            </if>
            <if test="_parameter.containsKey('clientCode') and clientCode != null and clientCode != ''">
                and p.client_code = #{clientCode}
            </if>
            <if test="_parameter.containsKey('dataType') and dataType != null and dataType != ''">
                and p.main_batch_no like CONCAT('%','${dataType}','_%')
            </if>
            <if test="_parameter.containsKey('startTime') and startTime != null and _parameter.containsKey('endTime') and endTime != null">
                <![CDATA[
                    and ((p.start_time > #{startTime} and p.start_time < #{endTime}) or (p.end_time > #{startTime} and  p.end_time < #{endTime}))
                ]]>
            </if>
        </where>
        order by c_time desc
    </select>

    <select id="selectFlMainBatchInfo" parameterType="String" resultType="com.jy.bean.po.FlMainBatchInfo">
        select
        p.client_code
        ,p.main_batch_no
        ,p.data_remark
        ,p.status
        ,p.message
        ,p.service_name
        ,p.node_name
        ,p.total_time
        ,p.wait_time
        ,p.process_time
        ,p.c_time
        ,p.u_time
        ,p.start_time
        ,p.end_time
        from
        fl_mainBatch_info p
        where
            and p.main_batch_no = #{mainBatchNo}
    </select>

    <sql id="Base_Column_List" >
        main_batch_no
        ,data_remark
        ,status
        ,message
        ,service_name
        ,node_name
        ,client_code
        ,total_time
        ,wait_time
        ,process_time
        ,c_time
        ,c_by
        ,u_time
        ,u_by
        ,start_time
        ,end_time
    </sql>

    <insert id="insert" parameterType="com.jy.bean.po.FlMainBatchInfo" >
        INSERT  INTO fl_mainBatch_info (<include refid="Base_Column_List" />)
        values(
        #{mainBatchNo,jdbcType=VARCHAR}
        ,#{dataRemark,jdbcType=VARCHAR}
        ,#{status,jdbcType=VARCHAR}
        ,#{message,jdbcType=VARCHAR}
        ,#{serviceName,jdbcType=VARCHAR}
        ,#{nodeName,jdbcType=VARCHAR}
        ,#{clientCode,jdbcType=VARCHAR}
        ,#{totalTime,jdbcType=INTEGER}
        ,#{waitTime,jdbcType=INTEGER}
        ,#{processTime,jdbcType=INTEGER}
        ,#{cTime,jdbcType=TIMESTAMP}
        ,#{cBy,jdbcType=VARCHAR}
        ,#{uTime,jdbcType=TIMESTAMP}
        ,#{uBy,jdbcType=VARCHAR}
        ,#{startTime,jdbcType=TIMESTAMP}
        ,#{endTime,jdbcType=TIMESTAMP}
        )
    </insert>

    <insert id="insertBatch" parameterType="list">
        INSERT  INTO fl_mainBatch_info (<include refid="Base_Column_List" />)
        <foreach collection="list" item="item" separator="union all">
            SELECT
            #{item.mainBatchNo,jdbcType=VARCHAR}
            ,#{item.dataRemark,jdbcType=VARCHAR}
            ,#{item.status,jdbcType=VARCHAR}
            ,#{item.message,jdbcType=VARCHAR}
            ,#{item.serviceName,jdbcType=VARCHAR}
            ,#{item.nodeName,jdbcType=VARCHAR}
            ,#{item.clientCode,jdbcType=VARCHAR}
            ,#{item.totalTime,jdbcType=INTEGER}
            ,#{item.waitTime,jdbcType=INTEGER}
            ,#{item.processTime,jdbcType=INTEGER}
            ,#{item.cTime,jdbcType=TIMESTAMP}
            ,#{item.cBy,jdbcType=VARCHAR}
            ,#{item.uTime,jdbcType=TIMESTAMP}
            ,#{item.uBy,jdbcType=VARCHAR}
            ,#{item.startTime,jdbcType=TIMESTAMP}
            ,#{item.endTime,jdbcType=TIMESTAMP}
            FROM dual
        </foreach>
    </insert>

    <update id="update" parameterType="com.jy.bean.po.FlMainBatchInfo">
        update fl_mainBatch_info
        <set>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="totalTime != null">
                total_time = #{totalTime,jdbcType=INTEGER},
            </if>
            <if test="waitTime != null">
                wait_time = #{waitTime,jdbcType=INTEGER},
            </if>
            <if test="processTime != null">
                process_time = #{processTime,jdbcType=INTEGER},
            </if>
            end_time = #{endTime,jdbcType=TIMESTAMP},
            u_time = now()
        </set>
        where main_batch_no = #{mainBatchNo,jdbcType=VARCHAR}
    </update>

    <select id="selectDaily" resultType="com.jy.bean.dto.DashBordDailyDTO">
        select
        count(1) as totalCount,
        COUNT(CASE WHEN status = '101' THEN 1 END) AS totalProcessing,
        COUNT(CASE WHEN status = '101' AND (select count(1) from fl_batch_info b where b.main_batch_no = p.main_batch_no and b.data_source = 'publisher') > 0  THEN 1 END) AS publisherProcessing,
        COUNT(CASE WHEN status = '101' AND (select count(1) from fl_batch_info b where b.main_batch_no = p.main_batch_no and b.data_source = 'publisher') = 0 THEN 1 END) AS transferProcessing,
        COUNT(CASE WHEN status = '101'
                AND (select count(1) from fl_batch_info b where b.main_batch_no = p.main_batch_no and b.data_source = 'transfer') > 0
                AND (select count(1) from fl_batch_info b where b.main_batch_no = p.main_batch_no and b.data_source = 'transfer' and b.node_name = '加工数据开始处理') = 0
                THEN 1
        END) AS transferReceive
        from
        fl_mainBatch_info p
        <where>
            <![CDATA[
                ((p.start_time > #{startOfDay} and p.start_time < #{endOfDay}) or (p.end_time > #{startOfDay} and  p.end_time < #{endOfDay}))
            ]]>
        </where>
        order by c_time desc
    </select>

    <select id="listDailyMainBatchInfo" resultType="com.jy.bean.po.FlMainBatchInfo">
        select t.*
        from (SELECT p.client_code,
                     p.main_batch_no,
                     p.data_remark,
                     p.message,
                     p.service_name,
                     p.node_name,
                     p.total_time,
                     p.wait_time,
                     p.process_time,
                     p.c_time,
                     p.u_time,
                     p.start_time,
                     p.end_time,
                     CASE
                         WHEN p.status = '101'
                             AND IFNULL(t.transfer_cnt, 0) = 0
                             AND IFNULL(pub.publisher_cnt, 0) = 0 THEN 't01'
                         WHEN p.status = '101'
                             AND IFNULL(t.transfer_cnt, 0) > 0
                             AND IFNULL(pub.publisher_cnt, 0) = 0 THEN 't02'
                         WHEN p.status = '101'
                             AND IFNULL(pub.publisher_cnt, 0) > 0 THEN 'p01'
                         WHEN IFNULL(err.publisher_error_cnt, 0) > 0 THEN 'p03'
                         WHEN p.status = '200' THEN 'p02'
                         END AS status,
                     CASE
                         WHEN IFNULL(t.transfer_cnt, 0) = 0 AND IFNULL(pub.publisher_cnt, 0) > 0 THEN 'publisher'
                         ELSE 'transfer'
                         END AS dataSource
              FROM fl_mainBatch_info p
                       LEFT JOIN (SELECT main_batch_no, COUNT(1) AS transfer_cnt
                                  FROM fl_batch_info
                                  WHERE data_source = 'transfer'
                                  GROUP BY main_batch_no) t ON p.main_batch_no = t.main_batch_no
                       LEFT JOIN (SELECT main_batch_no, COUNT(1) AS publisher_cnt
                                  FROM fl_batch_info
                                  WHERE data_source = 'publisher'
                                  GROUP BY main_batch_no) pub ON p.main_batch_no = pub.main_batch_no
                       LEFT JOIN (SELECT main_batch_no, COUNT(1) AS publisher_error_cnt
                                  FROM fl_batch_info
                                  WHERE data_source = 'publisher'
                                    AND status = 500
                                  GROUP BY main_batch_no) err ON p.main_batch_no = err.main_batch_no ) t
        <where>
            1 = 1
            <if test="_parameter.containsKey('status') and status != null">
                and t.status = #{status}
            </if>
            <if test="_parameter.containsKey('mainBatchNo') and mainBatchNo != null and mainBatchNo != ''">
                and t.main_batch_no = #{mainBatchNo}
            </if>
            <if test="_parameter.containsKey('clientCode') and clientCode != null and clientCode != ''">
                and t.client_code = #{clientCode}
            </if>
            <if test="_parameter.containsKey('startTime') and startTime != null and _parameter.containsKey('endTime') and endTime != null">
                <![CDATA[
                and ((t.start_time > #{startTime} and t.start_time < #{endTime}) or
                     (t.end_time > #{startTime} and t.end_time < #{endTime}))
                ]]>
            </if>
        </where>
        order by t.c_time desc
    </select>

    <update id="reset">
        update fl_mainbatch_info
        set start_time = #{startTime,jdbcType=TIMESTAMP},
        status = #{status,jdbcType=VARCHAR},
        end_time = null,
        total_time = null,
        wait_time = null,
        process_time = null
        where main_batch_no = #{mainBatchNo,jdbcType=VARCHAR}
    </update>

    <update id="resetBatch">
        <foreach collection="list" item="item" separator=";">
            update fl_mainbatch_info
            set start_time = #{startTime,jdbcType=TIMESTAMP},
            status = #{status,jdbcType=VARCHAR},
            end_time = null,
            total_time = null,
            wait_time = null,
            process_time = null
            where main_batch_no = #{item.mainBatchNo,jdbcType=VARCHAR}
        </foreach>
    </update>
</mapper>

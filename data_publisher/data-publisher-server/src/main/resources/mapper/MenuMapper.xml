<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jy.mapper.MenuMapper">
    <select id="getOne" resultType="com.jy.bean.po.Menu">
        select *
        from
        menu p
        order by id
       <!-- <where>
            1 = 1
            <if test="_parameter.containsKey('id') and id != null">
                and p.id = #{id}
            </if>
            <if test="_parameter.containsKey('menuType') and menuType != null and menuType != ''">
                and p.menu_type = #{menuType}
            </if>
            <if test="_parameter.containsKey('menuParentId') and menuParentId != null and menuParentId != ''">
                and p.menu_parent_id = #{menuParentId}
            </if>
        </where>-->
    </select>


</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jy.mapper.SysUserRoleMapper">

    <insert id="save" parameterType="com.jy.bean.po.SysUserRole" >
        INSERT  INTO sys_user_roles (user_id,role_id)
        values(#{userId,jdbcType=VARCHAR},#{roleId,jdbcType=VARCHAR} )
    </insert>

    <delete id="delete" >
        delete from sys_user_roles
        where user_id = #{userId,jdbcType=VARCHAR}
    </delete>
</mapper>
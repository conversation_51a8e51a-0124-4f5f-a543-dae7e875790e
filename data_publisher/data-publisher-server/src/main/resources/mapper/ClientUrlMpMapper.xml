<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jy.mapper.ClientUrlMpMapper">

    <select id="listClientUrlMp" resultType="com.jy.bean.po.ClientUrlMp">
        select *
        from
        client_url_mp p
        <where>
            <if test="_parameter.containsKey('id') and id != null and id != ''">
                and p.id = #{id}
            </if>
            <if test="_parameter.containsKey('tableName') and tableName != null and tableName != ''">
                and p.table_name = #{tableName}
            </if>
            <if test="_parameter.containsKey('clientCode') and clientCode != null and clientCode != ''">
                and p.client_code = #{clientCode}
            </if>
            <if test="_parameter.contains<PERSON><PERSON>('baseClientCode') and baseClientCode != null and baseClientCode != ''">
                and p.base_client_code = #{baseClientCode}
            </if>
        </where>
        order by p.client_code asc, p.table_name asc
    </select>

    <select id="listCompareClient" parameterType="map" resultType="com.jy.bean.po.ClientUrlMp">
        select
        base_client_code,
        client_code,
        url
        from
        client_url_mp p
        <where>
            <if test="_parameter.containsKey('tableName') and tableName != null and tableName != ''">
                and p.table_name = #{tableName}
            </if>
            <if test="_parameter.containsKey('clientCode') and clientCode != null and clientCode != ''">
                and p.client_code = #{clientCode}
            </if>
            <if test="_parameter.containsKey('baseClientCode') and baseClientCode != null and baseClientCode != ''">
                and p.base_client_code = #{baseClientCode}
            </if>
        </where>
        GROUP BY base_client_code, client_code,url
    </select>

    <sql id="Base_Column_List" >
        id,base_client_code,client_code,table_name,url,path,c_time,u_time
    </sql>

    <insert id="save" parameterType="com.jy.bean.po.ClientUrlMp" >
        INSERT  INTO client_url_mp (<include refid="Base_Column_List" />)
        values(
        #{id,jdbcType=VARCHAR},#{baseClientCode,jdbcType=VARCHAR},#{clientCode,jdbcType=VARCHAR},#{tableName,jdbcType=VARCHAR},
        #{url,jdbcType=VARCHAR},#{path,jdbcType=VARCHAR},NOW(),NOW()
        )
    </insert>

    <delete id="delete" >
        delete from client_url_mp
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <update id="update" parameterType="com.jy.bean.po.ClientUrlMp" >
        update client_url_mp
        <set>
            <if test="baseClientCode != null">
                base_client_code = #{baseClientCode},
            </if>
            <if test="clientCode != null">
                client_code = #{clientCode},
            </if>
            <if test="tableName != null">
                table_name = #{tableName},
            </if>
            <if test="url != null">
                url = #{url},
            </if>
            <if test="path != null">
                path = #{path},
            </if>
            u_time = now()
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

</mapper>
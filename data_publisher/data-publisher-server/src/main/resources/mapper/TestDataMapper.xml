<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jy.mapper.TestDataMapper">

    <select id="listPrice" resultType="com.jy.bean.po.Price">
        select ppid,ycljid
        from
        jy_pj_qylbjjgxxb_bd p
        <where>
            1=1
            <if test="_parameter.containsKey('ppid') and ppid != null">
                and p.ppid = #{ppid}
            </if>
            <if test="_parameter.containsKey('ycljid') and ycljid != null">
                and p.ycljid = #{ycljid}
            </if>
        </where>
    </select>

    <select id="listVehicleId" resultType="com.jy.bean.po.Price">
        select id
        from
        ap_veh_vehicle p
        <where>
            1=1
            <if test="_parameter.containsKey('ppid') and ppid != null">
                and p.ppid = #{ppid}
            </if>
            <if test="_parameter.containsKey('ycljid') and ycljid != null">
                and p.ycljid = #{ycljid}
            </if>
            <if test="_parameter.containsKey('id') and id != null">
                and p.id = #{id}
            </if>
        </where>
        limit 0, 20000
    </select>

    <select id="listVcBaseApVehicleId" resultType="com.jy.bean.po.Price">
        select ap_vehicle_id
        from
        vc_base p
        <where>
            1=1
            <if test="_parameter.containsKey('ppid') and ppid != null">
                and p.ppid = #{ppid}
            </if>
            <if test="_parameter.containsKey('ycljid') and ycljid != null">
                and p.ycljid = #{ycljid}
            </if>
            <if test="_parameter.containsKey('id') and id != null">
                and p.id = #{id}
            </if>
            <if test="_parameter.containsKey('apVehicleId') and apVehicleId != null">
                and p.ap_vehicle_id = #{apVehicleId}
            </if>
        </where>
        limit 0, 20000
    </select>
</mapper>
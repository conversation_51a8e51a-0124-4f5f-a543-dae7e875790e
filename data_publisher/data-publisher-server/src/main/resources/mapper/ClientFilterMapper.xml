<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jy.mapper.ClientFilterMapper">

    <select id="listClientFilter" resultType="com.jy.bean.po.ClientFilter">
        select *
        from
        client_filter p
        <where>
            1=1
            <if test="_parameter.containsKey('id') and id != null and id != ''">
                and p.id = #{id}
            </if>
            <if test="_parameter.containsKey('clientCode') and clientCode != null and clientCode != ''">
                and p.client_code = #{clientCode}
            </if>
            <if test="_parameter.containsKey('tableName') and tableName != null and tableName != ''">
                and p.table_name = #{tableName}
            </if>
        </where>
        order by client_code,table_name
    </select>

    <sql id="Base_Column_List" >
        id,
        client_code,
        table_name,
        filter_key,
        filter_value,
        filter_node,
        filter_rule,
        c_by,
        c_time,
        u_by,
        u_time,
        del_flag,
        remark
    </sql>

    <insert id="save" parameterType="com.jy.bean.po.ClientFilter" >
        INSERT  INTO client_filter (<include refid="Base_Column_List" />)
        values(
        #{id,jdbcType=VARCHAR},
        #{clientCode,jdbcType=VARCHAR},
        #{tableName,jdbcType=VARCHAR},
        #{filterKey,jdbcType=VARCHAR},
        #{filterValue,jdbcType=VARCHAR},
        #{filterNode,jdbcType=VARCHAR},
        #{filterRule,jdbcType=VARCHAR},
        #{cBy,jdbcType=VARCHAR},
        NOW(),
        #{uBy,jdbcType=VARCHAR},
        NOW(),
        #{delFlag,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR}
        )
    </insert>

    <delete id="delete" >
        delete from client_filter
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <update id="update" parameterType="com.jy.bean.po.ClientFilter" >
        update client_filter
        <set>
            <if test="clientCode != null">
                client_code = #{clientCode},
            </if>
            <if test="tableName != null">
                table_name = #{tableName},
            </if>
            <if test="filterKey != null">
                filter_key = #{filterKey},
            </if>
            <if test="filterValue != null">
                filter_value = #{filterValue},
            </if>
            <if test="filterNode != null">
                filter_node = #{filterNode},
            </if>
            <if test="filterRule != null">
                filter_rule = #{filterRule},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="uBy != null">
                u_by = #{uBy,jdbcType=VARCHAR},
            </if>
            u_time = now()
        </set>
        <where>
            id = #{id,jdbcType=VARCHAR}
        </where>
    </update>

</mapper>
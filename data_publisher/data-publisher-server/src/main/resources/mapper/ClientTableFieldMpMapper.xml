<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jy.mapper.ClientTableFieldMpMapper">

    <select id="listClientTableFieldMp" resultType="com.jy.bean.po.ClientTableFieldMp">
        select *
        from
        client_table_field_mp p
        <where>
            1=1
            <if test="_parameter.containsKey('id') and id != null and id != ''">
                and p.id = #{id}
            </if>
            <if test="_parameter.containsKey('clientCode') and clientCode != null and clientCode != ''">
                and p.client_code = #{clientCode}
            </if>
            <if test="_parameter.containsKey('tableName') and tableName != null and tableName != ''">
                and p.table_name = #{tableName}
            </if>
            <if test="_parameter.containsKey('baseTableField') and baseTableField != null and baseTableField != ''">
                and p.base_table_field = #{baseTableField}
            </if>
        </where>
        order by p.client_code asc, p.table_name asc, p.u_time desc
    </select>

    <sql id="Base_Column_List" >
        id,client_code,table_name,table_field,base_table_field,c_time,u_time
    </sql>

    <insert id="save" parameterType="com.jy.bean.po.ClientTableFieldMp" >
        INSERT  INTO client_table_field_mp (<include refid="Base_Column_List" />)
        values(
        #{id,jdbcType=VARCHAR},#{clientCode,jdbcType=VARCHAR},#{tableName,jdbcType=VARCHAR},
        #{tableField,jdbcType=VARCHAR},#{baseTableField,jdbcType=VARCHAR},NOW(),NOW()
        )
    </insert>

    <insert id="saveBatch" parameterType="java.util.List">
        INSERT INTO client_table_field_mp (<include refid="Base_Column_List" />)
        VALUES
        <foreach collection ="list" item="clientTableFieldMp" separator =",">
            (
            #{clientTableFieldMp.id,jdbcType=VARCHAR},#{clientTableFieldMp.clientCode,jdbcType=VARCHAR},#{clientTableFieldMp.tableName,jdbcType=VARCHAR},
            #{clientTableFieldMp.tableField,jdbcType=VARCHAR},#{clientTableFieldMp.baseTableField,jdbcType=VARCHAR},NOW(),NOW()
            )
        </foreach >
    </insert>

    <delete id="delete" >
        delete from client_table_field_mp
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <update id="update" parameterType="com.jy.bean.po.ClientTableFieldMp" >
        update client_table_field_mp
        <set>
            <if test="clientCode != null">
                client_code = #{clientCode},
            </if>
            <if test="tableName != null">
                table_name = #{tableName},
            </if>
            <if test="tableField != null">
                table_field = #{tableField},
            </if>
            <if test="baseTableField != null">
                base_table_field = #{baseTableField},
            </if>
            u_time = now()
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="selectFieldCount" resultType="java.lang.Integer">
        select count(1) from client_table_field_mp p
        where p.client_code = #{clientCode}
        and p.table_name = #{tableName}
    </select>
</mapper>
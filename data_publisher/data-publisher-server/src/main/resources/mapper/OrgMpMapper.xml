<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jy.mapper.OrgMpMapper">

    <select id="listOrgMp" resultType="com.jy.bean.po.OrgMp">
        select *
        from
        org_mp p
        <where>
            1=1
            <if test="_parameter.containsKey('clientCode') and clientCode != null and clientCode != ''">
                and p.client_code = #{clientCode}
            </if>
            <if test="_parameter.containsKey('id') and id != null and id != ''">
                and p.id = #{id}
            </if>
            <if test="_parameter.containsKey('baseOrgCode') and baseOrgCode != null and baseOrgCode != ''">
                and p.base_org_code = #{baseOrgCode}
            </if>
            <if test="_parameter.contains<PERSON>ey('orgCode') and orgCode != null and orgCode != ''">
                and p.org_code = #{orgCode}
            </if>
        </where>
        order by p.client_code asc, p.base_org_code asc, p.u_time desc
    </select>

    <select id="listOrgAndCodeNameByClientCode" resultType="com.jy.bean.po.OrgMp">
        select p.*,d.name AS orgName
        from
        org_mp p LEFT JOIN dict d ON p.base_org_code = d.code
        where p.client_code = #{clientCode}
        order by p.client_code asc, p.base_org_code asc, p.u_time desc
    </select>


    <sql id="Base_Column_List" >
        id,base_org_code,org_code,client_code,c_time,u_time
    </sql>

    <insert id="save" parameterType="com.jy.bean.po.OrgMp" >
        INSERT  INTO org_mp (<include refid="Base_Column_List" />)
        values(
        #{id,jdbcType=VARCHAR},#{baseOrgCode,jdbcType=VARCHAR},#{orgCode,jdbcType=VARCHAR},
        #{clientCode,jdbcType=VARCHAR},NOW(),NOW()
        )
    </insert>

    <delete id="delete" >
        delete from org_mp
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <update id="update" parameterType="com.jy.bean.po.OrgMp" >
        update org_mp
        <set>
            <if test="baseOrgCode != null">
                base_org_code = #{baseOrgCode},
            </if>
            <if test="orgCode != null">
                org_code = #{orgCode},
            </if>
            <if test="clientCode != null">
                client_code = #{clientCode},
            </if>
            u_time = now()
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

</mapper>
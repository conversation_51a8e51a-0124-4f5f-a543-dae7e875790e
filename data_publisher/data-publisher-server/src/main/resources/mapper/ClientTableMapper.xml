<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jy.mapper.ClientTableMapper">

    <select id="listClientTable" resultType="com.jy.bean.po.ClientTable">
        select *
        from
        client_table p
        <where>
            <if test="_parameter.containsKey('id') and id != null and id != ''">
                and p.id = #{id}
            </if>
            <if test="_parameter.containsKey('tableName') and tableName != null and tableName != ''">
                and p.table_name = #{tableName}
            </if>
            <if test="_parameter.containsKey('baseTableName') and baseTableName != null and baseTableName != ''">
                and p.base_table_name = #{baseTableName}
            </if>
            <if test="_parameter.contains<PERSON><PERSON>('clientCode') and clientCode != null and clientCode != ''">
                and p.client_code = #{clientCode}
            </if>
        </where>
        order by p.client_code asc, p.u_time desc
    </select>

    <select id="listBybaseTableName" resultType="com.jy.bean.po.ClientTable">
        select base_table_name
        from
        client_table p
        group by p.base_table_name
    </select>

    <select id="getClientTable" resultType="com.jy.bean.po.ClientTable">
        select *
        from client_table p where p.client_code = #{clientCode} and p.table_name = #{tableName}
    </select>

    <sql id="Base_Column_List" >
        id,client_code,table_name,base_table_name,c_time,u_time
    </sql>

    <insert id="save" parameterType="com.jy.bean.po.ClientTable" >
        INSERT  INTO client_table (<include refid="Base_Column_List" />)
        values(
        #{id,jdbcType=VARCHAR},#{clientCode,jdbcType=VARCHAR},#{tableName,jdbcType=VARCHAR},
        #{baseTableName,jdbcType=VARCHAR},NOW(),NOW()
        )
    </insert>

    <insert id="saveBatch" parameterType="java.util.List">
        INSERT INTO client_table (<include refid="Base_Column_List" />)
        VALUES
        <foreach collection ="list" item="clientTable" separator =",">
            (
            #{clientTable.id,jdbcType=VARCHAR},#{clientTable.clientCode,jdbcType=VARCHAR},#{clientTable.tableName,jdbcType=VARCHAR},
            #{clientTable.baseTableName,jdbcType=VARCHAR},NOW(),NOW()
            )
        </foreach >
    </insert>

    <delete id="delete" >
        delete from client_table
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <update id="update" parameterType="com.jy.bean.po.ClientTable" >
        update client_table
        <set>
            <if test="clientCode != null">
                client_code = #{clientCode},
            </if>
            <if test="tableName != null">
                table_name = #{tableName},
            </if>
            <if test="baseTableName != null">
                base_table_name = #{baseTableName},
            </if>
            u_time = now()
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="getFacadePartTableField" resultType="map">
        select c.table_name tableName,c.base_table_name baseTableName,c1.table_field tableField,c1.base_table_field baseTableField from client_table c inner join client_table_field_mp c1 on c.table_name = c1.table_name
        where c.client_code = 'FACADE-PART'
    </select>
</mapper>
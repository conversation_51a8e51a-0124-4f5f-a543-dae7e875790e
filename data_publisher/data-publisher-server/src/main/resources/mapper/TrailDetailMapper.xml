<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jy.mapper.TrailDetailMapper">

    <select id="listTrailDetail" resultType="com.jy.bean.po.TrailDetail">
        select *
        from
        trail_detail p
        <where>
            1=1
            <if test="_parameter.containsKey('id') and id != null and id != ''">
                and p.id = #{id}
            </if>
            <if test="_parameter.containsKey('clientCode') and clientCode != null and clientCode != ''">
                and p.client_code = #{clientCode}
            </if>
            <if test="_parameter.containsKey('status') and status != null and status != ''">
                and p.status = #{status}
            </if>
        </where>
        order by c_time asc
    </select>

    <sql id="Base_Column_List">
        id,
        client_code,
        batch_no,
        status,
        c_by,
        c_time,
        u_by,
        u_time,
        del_flag,
        remark
    </sql>

    <delete id="delete" parameterType="java.lang.String">
        delete from trail_detail
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <insert id="save" parameterType="com.jy.bean.po.TrailDetail">
        insert into trail_detail (<include refid="Base_Column_List" />)
        values (
            #{id,jdbcType=VARCHAR},
            #{clientCode,jdbcType=VARCHAR},
            #{batchNo,jdbcType=VARCHAR},
            #{status,jdbcType=VARCHAR},
            #{cBy,jdbcType=VARCHAR},
            NOW(),
            #{uBy,jdbcType=VARCHAR},
            NOW(),
            #{delFlag,jdbcType=VARCHAR},
            #{remark,jdbcType=VARCHAR}
        )
    </insert>

    <update id="update" parameterType="com.jy.bean.po.TrailDetail">
        update trail_detail
        <set>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="uBy != null">
                u_by = #{uBy,jdbcType=VARCHAR},
            </if>
            u_time = now()
        </set>
        where batch_no = #{batchNo,jdbcType=VARCHAR} and client_code = #{clientCode,jdbcType=VARCHAR}
    </update>

</mapper>
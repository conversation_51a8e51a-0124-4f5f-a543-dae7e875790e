<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jy.mapper.FieldOrgMpMapper">

    <select id="listFieldOrgMp" resultType="com.jy.bean.po.FieldOrgMp">
        select *
        from
        field_org_mp p
        <where>
            1=1
            <if test="_parameter.containsKey('id') and id != null and id != ''">
                and p.id = #{id}
            </if>
            <if test="_parameter.containsKey('clientCode') and clientCode != null and clientCode != ''">
                and p.client_code = #{clientCode}
            </if>
            <if test="_parameter.containsKey('baseTableName') and baseTableName != null and baseTableName != ''">
                and p.base_table_name = #{baseTableName}
            </if>
            <if test="_parameter.containsKey('baseTableField') and baseTableField != null and baseTableField != ''">
                and p.base_table_field = #{baseTableField}
            </if>
            <if test="_parameter.containsKey('baseOrgCode') and baseOrgCode != null and baseOrgCode != ''">
                and p.base_org_code = #{baseOrgCode}
            </if>
            <if test="_parameter.containsKey('toBaseOrgCode') and toBaseOrgCode != null and toBaseOrgCode != ''">
                and p.to_base_org_code = #{toBaseOrgCode}
            </if>
        </where>
    </select>

    <select id="listOrgCode" resultType="java.lang.String">
        select distinct to_base_org_code
        from
        field_org_mp p
        <where>
            1=1
            <if test="_parameter.containsKey('id') and id != null and id != ''">
                and p.id = #{id}
            </if>
            <if test="_parameter.containsKey('clientCode') and clientCode != null and clientCode != ''">
                and p.client_code = #{clientCode}
            </if>
            <if test="_parameter.containsKey('baseTableName') and baseTableName != null and baseTableName != ''">
                and p.base_table_name = #{baseTableName}
            </if>
            <if test="_parameter.containsKey('baseOrgCode') and baseOrgCode != null and baseOrgCode != ''">
                and p.base_org_code = #{baseOrgCode}
            </if>
        </where>
    </select>


    <sql id="Base_Column_List" >
        id,client_code,base_table_name,base_table_field,base_org_code,to_base_org_code,c_time,u_time
    </sql>

    <insert id="save" parameterType="com.jy.bean.po.FieldOrgMp" >
        INSERT  INTO field_org_mp (<include refid="Base_Column_List" />)
        values(
        #{id,jdbcType=VARCHAR},#{clientCode,jdbcType=VARCHAR},#{baseTableName,jdbcType=VARCHAR},
        #{baseTableField,jdbcType=VARCHAR},#{baseOrgCode,jdbcType=VARCHAR},#{toBaseOrgCode,jdbcType=VARCHAR},NOW(),NOW()
        )
    </insert>

    <insert id="saveBatch" parameterType="java.util.List">
        INSERT INTO field_org_mp (<include refid="Base_Column_List" />)
        VALUES
        <foreach collection ="list" item="fieldOrgMp" separator =",">
            (
            #{fieldOrgMp.id,jdbcType=VARCHAR},#{fieldOrgMp.clientCode,jdbcType=VARCHAR},#{fieldOrgMp.baseTableName,jdbcType=VARCHAR},
            #{fieldOrgMp.baseTableField,jdbcType=VARCHAR},#{fieldOrgMp.baseOrgCode,jdbcType=VARCHAR},#{fieldOrgMp.toBaseOrgCode,jdbcType=VARCHAR},NOW(),NOW()
            )
        </foreach >
    </insert>

    <delete id="delete" >
        delete from field_org_mp
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <update id="update" parameterType="com.jy.bean.po.FieldOrgMp" >
        update field_org_mp
        <set>
            <if test="clientCode != null">
                client_code = #{clientCode},
            </if>
            <if test="baseTableName != null">
                base_table_name = #{baseTableName},
            </if>
            <if test="baseTableField != null">
                base_table_field = #{baseTableField},
            </if>
            <if test="baseOrgCode != null">
                base_org_code = #{baseOrgCode},
            </if>
            <if test="toBaseOrgCode != null">
                to_base_org_code = #{toBaseOrgCode},
            </if>
            u_time = now()
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
</mapper>
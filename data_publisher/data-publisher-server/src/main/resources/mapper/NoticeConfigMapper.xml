<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jy.mapper.NoticeConfigMapper">

    <select id="listNoticeConfig" resultType="com.jy.bean.po.NoticeConfig">
        select *
        from
        notice_config p
        <where>
            1=1
            <if test="_parameter.containsKey('clientCodes') and clientCodes != null and clientCodes != ''">
                and p.client_code in  ${clientCodes}
            </if>
        </where>
        order by client_code desc
    </select>

    <sql id="Base_Column_List" >
        id,
        client_code,
        notice_type,
        username,
        email,
        phone_number,
        batch_type
    </sql>
    <insert id="save" parameterType="com.jy.bean.po.NoticeConfig" >
        INSERT  INTO notice_config (<include refid="Base_Column_List" />)
        values(
        #{id,jdbcType=VARCHAR},
        #{clientCode,jdbcType=VARCHAR},
        #{noticeType,jdbcType=VARCHAR},
        #{username,jdbcType=VARCHAR},
        #{email,jdbcType=VARCHAR},
        #{phoneNumber,jdbcType=VARCHAR},
        #{batchType,jdbcType=INTEGER}
        )
    </insert>

    <delete id="delete" >
        delete from notice_config
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <update id="update" parameterType="com.jy.bean.po.NoticeConfig" >
        update notice_config
        <set>
            <if test="clientCode != null">
                client_code = #{clientCode},
            </if>
            <if test="noticeType != null">
                notice_type = #{noticeType},
            </if>
            <if test="username != null">
                username = #{username},
            </if>
            <if test="email != null">
                email = #{email},
            </if>
            <if test="phoneNumber != null">
                phone_number = #{phoneNumber},
            </if>
            <if test="batchType != null">
                batch_type = #{batchType},
            </if>
        </set>
        <where>
            id = #{id,jdbcType=VARCHAR}
        </where>
    </update>

</mapper>
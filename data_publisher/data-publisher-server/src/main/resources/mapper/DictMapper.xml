<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jy.mapper.DictMapper">

    <select id="listDict" resultType="com.jy.bean.po.Dict">
        select *
        from
        dict p
        <where>
            1=1
            <if test="_parameter.containsKey('id') and id != null and id != ''">
                and p.id = #{id}
            </if>
            <if test="_parameter.containsKey('name') and name != null and name != ''">
                and p.name like CONCAT('%','${name}','%')
            </if>
            <if test="_parameter.containsKey('code') and code != null and code != ''">
                and p.code = #{code}
            </if>
            <if test="_parameter.containsKey('type') and type != null and type != ''">
                and p.type = #{type}
            </if>
        </where>
        order by p.type asc,dict_order asc, p.code asc
    </select>

    <sql id="Base_Column_List" >
        id,
        CODE,
        name,
        type,
        type_name,
        dict_order
    </sql>

    <insert id="save" parameterType="com.jy.bean.po.Dict" >
        INSERT  INTO dict (<include refid="Base_Column_List" />)
        values(
            #{id,jdbcType=VARCHAR},
            #{code,jdbcType=VARCHAR},
            #{name,jdbcType=VARCHAR},
            #{type,jdbcType=VARCHAR},
            #{typeName,jdbcType=VARCHAR},
            #{dictOrder,jdbcType=INTEGER}
        )
    </insert>

    <delete id="delete" >
        delete from dict
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <update id="update" parameterType="com.jy.bean.po.Dict" >
        update dict
        <set>
            <if test="code != null">
                code = #{code},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="typeName != null">
                type_name = #{typeName},
            </if>
            <if test="dictOrder != null">
                dict_order = #{dictOrder},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>



    <select id="getMatchCode" resultType="java.lang.String" parameterType="java.lang.String">
        select t.egg_code from match_code t where t.company_code=#{companyCode} and del_flag = '0'
    </select>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jy.mapper.SysAuthorityMapper">
    <select id="listSysAuthorities" resultType="com.jy.bean.po.SysAuthority">
        select p.id, p.name, p.value, m.role_id
        from
        sys_authority p, sys_role_authorities m
        <where>
            m.authority_id = p.id
            <if test="_parameter.containsKey('roleIds') and roleIds != null">
                and m.role_id in ${roleIds}
            </if>
        </where>
    </select>

</mapper>
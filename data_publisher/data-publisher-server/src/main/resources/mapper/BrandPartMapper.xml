<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jy.mapper.BrandPartMapper">

    <sql id="Base_Column_List">
        id, batch_no, sup_table_id, sup_part_id, sup_part_code, sup_part_name,
        original_part_id, original_part_name, original_part_code, original_short_name, brand_id,
        brand_code, status, table_name, operate, create_time, update_time
    </sql>

    <sql id="Table_Name">
        d_brand_part_${brandCode}
    </sql>

    <select id="getBrandPartById" resultType="com.jy.bean.po.BrandPart">
        select
        <include refid="Base_Column_List"/>
        from <include refid="Table_Name"/>
        where id = #{id}
    </select>

    <select id="getBrandPartByOe" resultType="com.jy.bean.po.BrandPart">
        select
        <include refid="Base_Column_List"/>
        from <include refid="Table_Name"/>
        where original_part_code = #{oe}
    </select>

    <select id="listBrandPart" resultType="com.jy.bean.po.BrandPart">
        select
        <include refid="Base_Column_List"/>
        from <include refid="Table_Name"/>
        <where>
            1=1
            <if test="_parameter.containsKey('batchNo') and batchNo != null and batchNo != ''">
                and batch_no = #{batchNo}
            </if>
            <if test="_parameter.containsKey('originalPartCode') and originalPartCode != null and originalPartCode != ''">
                and original_part_code = #{originalPartCode}
            </if>
            <if test="_parameter.containsKey('supTableId') and supTableId != null and supTableId != ''">
                and sup_table_id = #{supTableId}
            </if>
            <if test="_parameter.containsKey('brandId') and brandId != null and brandId != ''">
                and brand_id = #{brandId}
            </if>
            <if test="_parameter.containsKey('status') and status != null and status != ''">
                and status = #{status}
            </if>
        </where>
    </select>

    <select id="listByBatchAndCodeAndTime" resultType="com.jy.bean.po.BrandPart">
        select
        <include refid="Base_Column_List"/>
        from <include refid="Table_Name"/>
        where batch_no = #{batchNo}
        and original_part_code = #{originalPartCode}
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
    </select>

    <insert id="saveBrandPart" parameterType="com.jy.bean.po.BrandPart">
        insert into <include refid="Table_Name"/> (
            id, batch_no, sup_table_id, sup_part_id, sup_part_code, sup_part_name,
            original_part_name, original_part_code, original_short_name, brand_id,
            brand_code, status, table_name, operate, original_part_id
        ) values (
            #{id}, #{batchNo}, #{supTableId}, #{supPartId}, #{supPartCode}, #{supPartName},
            #{originalPartName}, #{originalPartCode}, #{originalShortName}, #{brandId},
            #{brandCode}, #{status}, #{tableName}, #{operate}, #{originalPartId}
        )
    </insert>

    <update id="updateBrandPart" parameterType="com.jy.bean.po.BrandPart">
        update <include refid="Table_Name"/>
        <set>
            <if test="supTableId != null">sup_table_id = #{supTableId},</if>
            <if test="supPartId != null">sup_part_id = #{supPartId},</if>
            <if test="supPartCode != null">sup_part_code = #{supPartCode},</if>
            <if test="supPartName != null">sup_part_name = #{supPartName},</if>
            <if test="originalPartName != null">original_part_name = #{originalPartName},</if>
            <if test="originalShortName != null">original_short_name = #{originalShortName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="operate != null">operate = #{operate},</if>
            <if test="originalPartId != null">original_part_id = #{originalPartId},</if>
            <if test="batchNo != null">batch_no = #{batchNo},</if>
            <if test="originalPartCode != null">original_part_code = #{originalPartCode},</if>
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="brandCode != null">brand_code = #{brandCode},</if>
            <if test="tableName != null">table_name = #{tableName},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteBrandPart">
        delete from <include refid="Table_Name"/> where id = #{id}
    </delete>

    <select id="getBatchPartCount" resultType="java.lang.Integer">
        select count(1) from <include refid="Table_Name"/> where batch_no = #{batchNo}
    </select>

    <!-- Check if a table exists -->
    <select id="checkTableExists" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM information_schema.tables
        WHERE table_name = 'd_brand_part_${brandCode}'
    </select>
    <!-- 创建表结构 -->
    <update id="createBrandPartTable">
        CREATE TABLE <include refid="Table_Name"/>
        (
        id                  varchar(64)                         not null comment '原始数据ID' primary key,
        batch_no            varchar(128)                        not null comment '小批次号',
        sup_table_id        varchar(64)                         null comment '配件id，根据oe与品牌id生成',
        sup_part_id         varchar(64)                         null comment '标准配件ID',
        sup_part_code       varchar(64)                         null comment '标准配件编码',
        sup_part_name       varchar(200)                        null comment '标准配件名称',
        original_part_name  varchar(200)                        not null comment '原厂配件名称',
        original_part_code  varchar(64)                         not null comment '原厂配件编码 OE号',
        original_short_name varchar(64)                         not null comment '去除特殊符号OE',
        brand_id            varchar(64)                         not null comment '品牌ID',
        brand_code          varchar(64)                         not null comment '品牌编码',
        status              varchar(1)                          not null comment '是否有效 0:无效 1:有效',
        table_name          varchar(100)                        not null comment '表名',
        operate             varchar(20)                         not null comment '操作',
        original_part_id               varchar(64)                         null comment 'OE ID',
        create_time         timestamp default CURRENT_TIMESTAMP not null,
        update_time         timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
        )
    </update>

    <!-- 创建批次号和原厂编码联合索引 -->
    <update id="createBatchNoAndOriginalPartCodeIndex">
        create index idx_d_brand_part_${brandCode}_batch_original_code
        on <include refid="Table_Name"/> (batch_no, original_part_code)
    </update>

    <!-- 创建批次号、原厂编码和更新时间联合索引 -->
    <update id="createBatchNoOriginalPartCodeUtIndex">
        create index idx_d_brand_part_${brandCode}_batch_original_ut
        on <include refid="Table_Name"/> (batch_no, original_part_code, update_time)
    </update>

    <!-- 创建批次号和供应商表ID联合索引 -->
    <update id="createBatchNoAndSupTableIdIndex">
        create index idx_d_brand_part_${brandCode}_batch_sup_id
        on <include refid="Table_Name"/> (batch_no, sup_table_id)
    </update>

    <!-- 创建原厂编码索引 -->
    <update id="createOriginalPartCodeIndex">
        create index idx_d_brand_part_${brandCode}_original_code
        on <include refid="Table_Name"/> (original_part_code)
    </update>

    <!-- 创建批次号索引 -->
    <update id="createBatchNoIndex">
        create index idx_d_brand_part_${brandCode}_batch_no
        on <include refid="Table_Name"/> (batch_no)
    </update>

    <select id="getBrandPartBySupTableId" resultType="com.jy.bean.po.BrandPart">
        select
        <include refid="Base_Column_List"/>
        from <include refid="Table_Name"/>
        where sup_table_id = #{supTableId}
    </select>

    <select id="queryBrandPartWithPage" resultType="com.jy.bean.po.BrandPart">
        select
        <include refid="Base_Column_List"/>
        from <include refid="Table_Name"/>
        <where>
            1=1
            <if test="startTime != null and startTime != ''">
                and update_time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and update_time &lt;= #{endTime}
            </if>
            <if test="batchNo != null and batchNo != ''">
                and batch_no like CONCAT(#{batchNo}, '%')
            </if>
            <if test="originalPartCode != null and originalPartCode != ''">
                and original_part_code like CONCAT('%', #{originalPartCode}, '%')
            </if>
        </where>
        order by update_time desc
        <!--<if test="page != null and size != null">
            limit #{page}, #{size}
        </if>-->
    </select>

    <select id="queryMultipleBrandPartsWithPage" resultType="com.jy.bean.po.BrandPart">
        <foreach collection="brandCodes" item="brandCode" separator=" UNION ALL ">
            (select
            <include refid="Base_Column_List"/>
            from d_brand_part_${brandCode}
            <where>
                1=1
                <if test="startTime != null and startTime != ''">
                    and update_time &gt;= #{startTime}
                </if>
                <if test="endTime != null and endTime != ''">
                    and update_time &lt;= #{endTime}
                </if>
                <if test="batchNo != null and batchNo != ''">
                    and batch_no like CONCAT(#{batchNo}, '%')
                </if>
                <if test="originalPartCode != null and originalPartCode != ''">
                    and original_part_code like CONCAT('%', #{originalPartCode}, '%')
                </if>
            </where>)
        </foreach>
        order by update_time desc
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jy.mapper.BrandDictMapper">

    <sql id="Base_Column_List">
        id, brand_code, brand_name
    </sql>

    <!-- 查询品牌字典列表，支持分页和搜索 -->
    <select id="listBrandDict" resultType="com.jy.bean.po.BrandDict">
        select
        <include refid="Base_Column_List"/>
        from d_brand_dict
        <where>
            1=1
            <if test="_parameter.containsKey('keyword') and keyword != null and keyword != ''">
                and (brand_code like CONCAT('%', #{keyword}, '%') 
                     or brand_name like CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="_parameter.containsKey('brandCode') and brandCode != null and brandCode != ''">
                and brand_code = #{brandCode}
            </if>
            <if test="_parameter.containsKey('brandName') and brandName != null and brandName != ''">
                and brand_name like CONCAT('%', #{brandName}, '%')
            </if>
        </where>
        order by brand_code asc
        <if test="_parameter.containsKey('size') and size != null and size > 0">
            limit #{offset}, #{size}
        </if>
    </select>

    <!-- 根据品牌编码查询品牌字典 -->
    <select id="getBrandDictByCode" resultType="com.jy.bean.po.BrandDict">
        select
        <include refid="Base_Column_List"/>
        from d_brand_dict
        where brand_code = #{brandCode}
    </select>

    <!-- 根据品牌名称模糊查询品牌字典 -->
    <select id="searchBrandDictByName" resultType="com.jy.bean.po.BrandDict">
        select
        <include refid="Base_Column_List"/>
        from d_brand_dict
        where brand_name like CONCAT('%', #{brandName}, '%')
        order by brand_code asc
    </select>

</mapper>

package com.jy.transform;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: zy
 * @Date: Created in 2018/5/14
 */
@Component
public class TransformFactory {

    @Autowired
    private DataAbstractTransform clientDataTransform;
    @Autowired
    private DataAbstractTransform facadeDataTransform;

    public DataAbstractTransform create(String clientCode) {
        if(clientCode.contains("FACADE")){
            return facadeDataTransform;
        } else {
            return clientDataTransform;
        }
    }
}

package com.jy.transform;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jy.bean.dto.BaseDataDTO;
import com.jy.bean.po.Client;
import com.jy.bean.po.ClientTableFieldMp;
import com.jy.bean.po.ClientUrlMp;
import com.jy.service.ClientTableFieldMpService;
import com.jy.service.ClientUrlMpService;
import com.jy.util.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 *
 * @Author: zy
 * @Date: Created in 2018/3/29
 */
@Service
public class FacadeDataTransform extends DataAbstractTransform {

    private static final Logger logger = LogManager.getLogger(FacadeDataTransform.class);

    @Autowired
    private ClientUrlMpService clientUrlMpService;
    @Autowired
    private ClientTableFieldMpService clientTableFieldMpService;

    /**
     * 价格
     * @param baseDataDTO
     * @param client
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject transform(BaseDataDTO baseDataDTO, Client client) throws Exception {
        baseDataDTO.setId(StringUtils.getUUID());
        //组装facade路径地址
        logger.info("clientCode:{}, tableName:{}",client.getCode(), baseDataDTO.getTableName());
        ClientUrlMp clientUrlMp = clientUrlMpService.getByClientCodeAndTableName(client.getCode(), baseDataDTO.getTableName());
        logger.info("clientUrlMp:{}", clientUrlMp);
        baseDataDTO.setClientCode(client.getCode());
        baseDataDTO.setClientUrl(clientUrlMp.getUrl());
        baseDataDTO.setClientPath(clientUrlMp.getPath());
        baseDataDTO.setTableName(getTableName(baseDataDTO, client));
        //查询客户端-表中字段对应关系
        Map<String, List<ClientTableFieldMp>> fieldsMp = clientTableFieldMpService.mapByClientCodeAndTableName(client.getCode(), baseDataDTO.getTableName());
        baseDataDTO.setFields(getFields(baseDataDTO, fieldsMp));
        baseDataDTO.setKeys(getKeys(baseDataDTO, fieldsMp));
        return (JSONObject) JSON.toJSON(baseDataDTO);
    }

    /**
     * 整车
     * @param baseDataDTO
     * @param client
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject transformFullcar(BaseDataDTO baseDataDTO, Client client) throws Exception {
        return this.transform(baseDataDTO, client);
    }

    /**
     * 测试
     * @param baseDataDTO
     * @param client
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject transformTest(BaseDataDTO baseDataDTO, Client client) throws Exception {
        return this.transform(baseDataDTO, client);
    }
    /**
     * 测试
     * @param baseDataDTO
     * @param client
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject transformPart(BaseDataDTO baseDataDTO, Client client) throws Exception {
        return this.transform(baseDataDTO, client);
    }

}

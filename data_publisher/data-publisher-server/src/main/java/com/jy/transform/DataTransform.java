package com.jy.transform;

import com.alibaba.fastjson.JSONObject;
import com.jy.bean.dto.BaseDataDTO;
import com.jy.bean.po.Client;
import com.jy.bean.po.ClientTableFieldMp;

import java.util.List;
import java.util.Map;

/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2018/3/29
 */
public interface DataTransform {
    /**
     * 装配价格数据
     * @param baseDataDTO
     * @param client
     * @return
     * @throws Exception
     */
    JSONObject transform(BaseDataDTO baseDataDTO, Client client) throws Exception;
    /**
     * 装配整车数据
     * @param baseDataDTO
     * @param client
     * @return
     * @throws Exception
     */
    JSONObject transformFullcar(BaseDataDTO baseDataDTO, Client client) throws Exception;
    /**
     * 装配测试数据
     * @param baseDataDTO
     * @param client
     * @return
     * @throws Exception
     */
    JSONObject transformTest(BaseDataDTO baseDataDTO, Client client) throws Exception;
    /**
     * 装配配件数据
     * @param baseDataDTO
     * @param client
     * @return
     * @throws Exception
     */
    JSONObject transformPart(BaseDataDTO baseDataDTO, Client client) throws Exception;

    String getTableName(BaseDataDTO baseDataDTO, Client client) throws Exception;

    Map<String, String> getFields(BaseDataDTO baseDataDTO, Map<String,List<ClientTableFieldMp>> fieldsMap) throws Exception;

    Map<String, String> getKeys(BaseDataDTO baseDataDTO, Map<String,List<ClientTableFieldMp>> fieldsMap) throws Exception;

   // List<Map<String, Object>> getArrays(BaseDataDTO baseDataDTO, Map<String,List<ClientTableFieldMp>> fieldsMap) throws Exception;
}

package com.jy.transform;

import com.jy.bean.dto.BaseDataDTO;
import com.jy.bean.po.Client;
import com.jy.bean.po.ClientTable;
import com.jy.bean.po.ClientTableFieldMp;
import com.jy.service.ClientTableService;
import com.jy.util.EmptyUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/6/20
 */
public abstract class DataAbstractTransform implements DataTransform{

    @Autowired
    private ClientTableService clientTableService;

    @Override
    public String getTableName(BaseDataDTO baseDataDTO, Client client) throws Exception {
        Map<String, ClientTable> clientTableMap = clientTableService.mapByBaseTableName(baseDataDTO.getTableName());
        if(EmptyUtils.isNotEmpty(clientTableMap) && EmptyUtils.isNotEmpty(clientTableMap.get(client.getCode()))){
            ClientTable clientTable = clientTableMap.get(client.getCode());
            return clientTable.getTableName();
        }
        return baseDataDTO.getTableName();
    }

    @Override
    public Map<String, String> getFields(BaseDataDTO baseDataDTO, Map<String,List<ClientTableFieldMp>> fieldsMap) throws Exception {
        return changeFileldName(baseDataDTO.getFields(), fieldsMap);
    }

    @Override
    public Map<String, String> getKeys(BaseDataDTO baseDataDTO, Map<String,List<ClientTableFieldMp>> fieldsMap) throws Exception {
        return changeFileldName(baseDataDTO.getKeys(), fieldsMap);
    }


   /* @Override
    public List<Map<String, Object>> getArrays(BaseDataDTO baseDataDTO, Map<String, List<ClientTableFieldMp>> fieldsMap) throws Exception {
        return changeArrayFileldName(baseDataDTO.getArrays(), fieldsMap);
    }*/

    public Map<String, String> getModify(Map<String,List<ClientTableFieldMp>> fieldsMap) throws Exception {
        Map<String, String> temp = new HashMap<String, String>(1);
        String key = "xgrid";
        String value = "jlrid";
        if(fieldsMap.containsKey(key) && fieldsMap.containsKey(value)){
            temp.put(fieldsMap.get(key).get(0).getTableField(), fieldsMap.get(value).get(0).getTableField());
        }
        return temp;
    }
    public Map<String, String> changeFileldName(Map<String, String> fields, Map<String,List<ClientTableFieldMp>> fieldsMap) throws Exception {
        Map<String, String> temp = new HashMap<String, String>();
        if(EmptyUtils.isNotEmpty(fields)){
            // 转换字段名称
            fields.forEach((k,v)->{
                if(fieldsMap.containsKey(k)){
                    for (ClientTableFieldMp c : fieldsMap.get(k)){
                        temp.put(c.getTableField(), v);
                    }
                }
            });
        }
        return temp;
    }

/*
    public List<Map<String, Object>> changeArrayFileldName(List<Map<String, Object>> arrays, Map<String,List<ClientTableFieldMp>> fieldsMap) throws Exception {
        List<Map<String, Object>> result = new ArrayList<>(arrays.size());
        if(EmptyUtils.isNotEmpty(arrays)){
            // 转换字段名称
            arrays.forEach(key->{
                key.forEach((k,v)->{
                    if(fieldsMap.containsKey(k)){
                        for (ClientTableFieldMp c : fieldsMap.get(k)){
                            Map<String, Object> temp = new HashMap<>();
                            temp.put(c.getTableField(), v);
                            result.add(temp);
                        }
                    }
                });
            });

        }
        return result;
    }*/
}

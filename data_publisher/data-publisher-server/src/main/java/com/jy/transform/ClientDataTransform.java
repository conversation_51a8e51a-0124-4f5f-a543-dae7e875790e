package com.jy.transform;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jy.ann.MethodMonitor;
import com.jy.bean.common.ClientPriceTypeMenu;
import com.jy.bean.dto.BaseDataDTO;
import com.jy.bean.po.Client;
import com.jy.bean.po.ClientTableFieldMp;
import com.jy.bean.po.OrgMp;
import com.jy.service.ClientTableFieldMpService;
import com.jy.service.OrgMpService;
import com.jy.util.EmptyUtils;
import com.jy.util.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 *
 * @Author: zy
 * @Date: Created in 2018/3/29
 */
@Service
public class ClientDataTransform extends DataAbstractTransform {

    @Autowired
    private OrgMpService orgMpService;
    @Autowired
    private ClientTableFieldMpService clientTableFieldMpService;

    /**
     * 价格数据装配
     * @param baseDataDTO
     * @param client
     * @return
     * @throws Exception
     */
    @Override
    @MethodMonitor
    public JSONObject transform(@NotNull BaseDataDTO baseDataDTO, @NotNull Client client) throws Exception {

        String baseTableName = baseDataDTO.getTableName();
        String tableName = getTableName(baseDataDTO, client);
        //查询客户端-表中字段对应关系
        Map<String,List<ClientTableFieldMp>> fieldsMap = clientTableFieldMpService.mapByClientCodeAndTableName(client.getCode(), tableName);

        baseDataDTO.setId(StringUtils.getUUID());
        baseDataDTO.setTableName(tableName);
        baseDataDTO.setFields(getFieldsPriceType(baseDataDTO.getFields(), client.getCode(), baseTableName));
        baseDataDTO.setFields(getFields(baseDataDTO, fieldsMap));
        baseDataDTO.setMust(getMust(baseDataDTO));//, client, baseTableName
        baseDataDTO.setKeys(getKeys(baseDataDTO,fieldsMap));
        baseDataDTO.setModify(getModify(fieldsMap));
        baseDataDTO.setTableName(getOrgTableName(baseDataDTO, client, baseTableName));
        baseDataDTO.setOrgCode(getOrgCode(baseDataDTO, client));
        baseDataDTO.setSqlType(getSqlType(baseDataDTO,baseTableName));
        baseDataDTO.setClientCode(client.getCode());
        return (JSONObject) JSON.toJSON(baseDataDTO);
    }
    /**
     * 整车数据装配
     * @param baseDataDTO
     * @param client
     * @return
     * @throws Exception
     */
    @Override
    @MethodMonitor
    public JSONObject transformFullcar(@NotNull BaseDataDTO baseDataDTO, @NotNull Client client) throws Exception {

        String baseTableName = baseDataDTO.getTableName();
        String tableName = getTableName(baseDataDTO, client);
        Map<String,List<ClientTableFieldMp>> fieldsMp = clientTableFieldMpService.mapByClientCodeAndTableName(client.getCode(), tableName);//查询客户端-表中字段对应关系

        baseDataDTO.setTableName(tableName);
        baseDataDTO.setId(StringUtils.getUUID());
        baseDataDTO.setFields(getFieldsPriceType(baseDataDTO.getFields(), client.getCode(), baseTableName));
        baseDataDTO.setFields(getFields(baseDataDTO, fieldsMp));
        baseDataDTO.setMust(getMust(baseDataDTO));//, client, baseTableName
        baseDataDTO.setKeys(getKeys(baseDataDTO,fieldsMp));
        baseDataDTO.setModify(getModify(fieldsMp));
        baseDataDTO.setTableName(getOrgTableName(baseDataDTO, client, baseTableName));
        baseDataDTO.setOrgCode(getOrgCode(baseDataDTO, client));
        return JSONObject.parseObject(JSONObject.toJSONString(baseDataDTO));
    }
    private String getOrgCode(BaseDataDTO baseDataDTO, Client client) throws Exception {
        if(EmptyUtils.isEmpty(baseDataDTO.getOrgCode())){
            return null;
        }
        OrgMp orgMp = orgMpService.getByClientCodeAndBaseOrgCode(client.getCode(), baseDataDTO.getOrgCode());
        if(EmptyUtils.isEmpty(orgMp)){
            return baseDataDTO.getOrgCode();
        }
        return orgMp.getOrgCode();
    }

    /**
     * 测试数据装配
     * @param baseDataDTO
     * @param client
     * @return
     * @throws Exception
     */
    @Override
    @MethodMonitor
    public JSONObject transformTest(@NotNull BaseDataDTO baseDataDTO, @NotNull Client client) throws Exception {

        String tableName = getTableName(baseDataDTO, client);
        String baseTableName = baseDataDTO.getTableName();
        Map<String,List<ClientTableFieldMp>>fieldsMp = clientTableFieldMpService.mapByClientCodeAndTableName(client.getCode(), tableName);//查询客户端-表中字段对应关系

        baseDataDTO.setTableName(tableName);
        baseDataDTO.setFields(getFieldsPriceType(baseDataDTO.getFields(), client.getCode(), baseTableName));
        baseDataDTO.setId(StringUtils.getUUID());
        baseDataDTO.setFields(getFields(baseDataDTO, fieldsMp));
        baseDataDTO.setMust(getMust(baseDataDTO));//, client, baseTableName
        baseDataDTO.setKeys(getKeys(baseDataDTO,fieldsMp));
        baseDataDTO.setModify(getModify(fieldsMp));
        baseDataDTO.setTableName(getOrgTableName(baseDataDTO, client, baseTableName));
        baseDataDTO.setOrgCode(getOrgCode(baseDataDTO, client));
        baseDataDTO.setSqlType(getSqlType(baseDataDTO,baseTableName));
        return JSONObject.parseObject(JSONObject.toJSONString(baseDataDTO));
    }
    /**
     * 配件数据装配
     * @param baseDataDTO
     * @param client
     * @return
     * @throws Exception
     */
    @Override
    @MethodMonitor
    public JSONObject transformPart(@NotNull BaseDataDTO baseDataDTO, @NotNull Client client) throws Exception {

        String tableName = getTableName(baseDataDTO, client);
        String baseTableName = baseDataDTO.getTableName();
        Map<String,List<ClientTableFieldMp>> fieldsMp = clientTableFieldMpService.mapByClientCodeAndTableName(client.getCode(), tableName);//查询客户端-表中字段对应关系

        baseDataDTO.setId(StringUtils.getUUID());
        baseDataDTO.setTableName(tableName);
        baseDataDTO.setFields(getFieldsPriceType(baseDataDTO.getFields(), client.getCode(), baseTableName));
        baseDataDTO.setFields(getFields(baseDataDTO, fieldsMp));
        baseDataDTO.setMust(getMust(baseDataDTO));//, client, baseTableName
        baseDataDTO.setKeys(getKeys(baseDataDTO,fieldsMp));
        baseDataDTO.setModify(getModify(fieldsMp));
        baseDataDTO.setTableName(getOrgTableName(baseDataDTO, client, baseTableName));
        baseDataDTO.setOrgCode(getOrgCode(baseDataDTO, client));
        return JSONObject.parseObject(JSONObject.toJSONString(baseDataDTO));
    }

    private String getSqlType(BaseDataDTO baseDataDTO,String baseTableName) throws Exception {
        if("system_price".equals(baseTableName)){
            return "searchSql";
        } else {
            return baseDataDTO.getSqlType();
        }
    }
    private String getOrgTableName(BaseDataDTO baseDataDTO, Client client, String baseTableName) throws Exception {
        /** 如果是本地化价格表  需要拼接保险公司机构代码 */
        if("local_market_price".equals(baseTableName)){
            return baseDataDTO.getTableName() + getOrgCode(baseDataDTO, client);
        }
        if(EmptyUtils.isNotEmpty(baseDataDTO.getSuffix()) && EmptyUtils.isNotEmpty(baseDataDTO.getSuffix().get("tableSuffix"))){
            return baseDataDTO.getTableName() + "_" +  baseDataDTO.getSuffix().get("tableSuffix");
        }
        return baseDataDTO.getTableName();
    }

    private Map<String, String> getMust(BaseDataDTO baseDataDTO) throws Exception {
//        if("pj_cllbjdyb_".equals(baseDataDTO.getTableName())
//                || "pj_clljtxdyb_".equals(baseDataDTO.getTableName())) {
//            baseDataDTO.setTableName(baseDataDTO.getTableName()+baseDataDTO.getMust().get("ppbm"));
//            baseDataDTO.getMust().remove("ppbm");
//        }
        return baseDataDTO.getMust();
    }

    private Map<String, String> getFieldsPriceType(Map<String, String> fields, String clientCode, String baseTableName) throws Exception {
        if("local_market_price".equals(baseTableName)) {
            //更改分省价格对应类型的值
            Map<String, String> priceTypes = ClientPriceTypeMenu.valueof(clientCode);
            priceTypes.forEach((k, v)->{
                if(fields.containsKey(k)){
                    fields.put(k, v);
                }
            });
        }
        return fields;
    }
}

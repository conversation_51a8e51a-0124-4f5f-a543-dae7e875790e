package com.jy.util;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.Date;


/**

 * <AUTHOR>
 */
public class CertUtil {

    public static String Md5(String plainText )
    {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(plainText.getBytes());
            byte b[] = md.digest();
            return byte2hex(b);

        }
        catch (Exception e)
        {

        }
        return null;
    }

    public static String Md5(String plainText,String encoding )
    {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(plainText.getBytes(encoding));
            byte b[] = md.digest();
            return byte2hex(b);

        }
        catch (Exception e)
        {

        }
        return null;
    }

    public static byte[] encrypt(byte[] src, byte[] key,String name) {

        try
        {
            SecretKeySpec securekey = new SecretKeySpec(key,name);

            Cipher cipher = Cipher.getInstance(name);

            cipher.init(Cipher.ENCRYPT_MODE, securekey);

            return cipher.doFinal(src);
        }
        catch(Exception e)
        {

        }
        return null;
    }

    public static byte[] decrypt(byte[] src, byte[] key,String name){

        try
        {
            SecretKeySpec securekey = new SecretKeySpec(key,name);

            Cipher cipher = Cipher.getInstance(name);

            cipher.init(Cipher.DECRYPT_MODE, securekey);

            return cipher.doFinal(src);
        }
        catch(Exception e)
        {

        }
        return null;

    }

    public  static String decrypt(String data,String key,String name){
        byte b[] =  decrypt(hex2byte(data.getBytes()), key.getBytes(),name);
        if(b!=null)
        {
            return new String(b);
        }
        else
        {
            return null;
        }

    }

    public  static String encrypt(String data,String key,String name){

        return  byte2hex(encrypt(data.getBytes(),key.getBytes(),name));

    }

    public  static String encrypt(String data,byte [] b,String name){

        return  byte2hex(encrypt(data.getBytes(),b,name));

    }
    public static String byte2hex(byte[] b)
    {
        String hs="";
        String stmp="";
        for (int n=0;n<b.length;n++)
        {
            stmp=(java.lang.Integer.toHexString(b[n] & 0XFF));
            if (stmp.length()==1) hs=hs+"0"+stmp;
            else hs=hs+stmp;

        }
        return hs.toUpperCase();
    }

    public static byte[] hex2byte(byte[] b)
    {
        if((b.length%2)!=0)
        {
            return null;
        }
        byte[] b2 = new byte[b.length/2];
        for (int n = 0; n < b.length; n+=2)
        {
            String item = new String(b,n,2);
            b2[n/2] = (byte)Integer.parseInt(item,16);
        }
        return b2;
    }

    public static long getTime(String s1)
    {

        try{
            SimpleDateFormat formatter = new SimpleDateFormat ("yyyy/MM/dd HH:mm:ss");

            Date dt2=(Date) formatter .parse(s1);

            return dt2.getTime();
        }
        catch(Exception e)
        {
            e.printStackTrace();
        }

        return 0;
    }

}


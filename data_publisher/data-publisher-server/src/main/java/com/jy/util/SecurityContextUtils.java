package com.jy.util;

import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;

/**
 * @Author: zy
 * @Date: Created in 2018/4/26
 */
public class SecurityContextUtils {
    public static String getUserName() {
        Object principal =  SecurityContextHolder.getContext()
                .getAuthentication().getPrincipal();
        return ((UserDetails)principal).getUsername();
    }
}

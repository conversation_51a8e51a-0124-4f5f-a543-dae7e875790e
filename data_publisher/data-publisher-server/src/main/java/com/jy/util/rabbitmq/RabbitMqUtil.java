package com.jy.util.rabbitmq;

import com.jy.util.EmptyUtils;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

/**################### RabbitMq Sys系统路由配置范例 ###################
 * spring.rabbitmq.dataTraceInfo: dataTraceExchange,dataTraceRoutingKey,dataTraceQueue,direct
 * 注意中间2个逗号之间的是Rounting Key,fanout模式可以为空,
 */
@Component
public class RabbitMqUtil {
    public static MQAccessBuilder mqAccessBuilderSys;
    public static MessageSender messageSenderSys;
    public static ConnectionFactory devConnectionFactory;
    public static String sysInfo;
    @Resource(name = "DevMqConnectionFactory")
    private ConnectionFactory connectionFactory;

    @Value("${spring.rabbitmq.dataTraceInfo}")
    private void initRabbitMqElkSysInfo(String dataTraceInfo) {
        sysInfo = dataTraceInfo;
        devConnectionFactory = connectionFactory;
    }

    public synchronized static void initSys() throws IOException {
        if (EmptyUtils.isNotEmpty(messageSenderSys)) {
            return;
        }
        mqAccessBuilderSys = new MQAccessBuilder(devConnectionFactory);
        String[] rabbitMqConfig = sysInfo.split(",");
        try {
            messageSenderSys = mqAccessBuilderSys.buildMessageSender(rabbitMqConfig[0], rabbitMqConfig[1],
                    rabbitMqConfig[2], rabbitMqConfig[3]);
        }
        catch (Exception e) {
            messageSenderSys = null;
            throw e;
        }
    }
    public static boolean sendElk(Object dto) throws IOException {
        try{
            if (EmptyUtils.isEmpty(messageSenderSys)) {
                initSys();
            }
            if (EmptyUtils.isEmpty(dto)) {
                return false;
            }
            DetailRes detailRes = messageSenderSys.send(dto);
            if (detailRes.isSuccess()) {
                return true;
            } else {
                messageSenderSys = null;
                return false;
            }
        }
        catch (Exception e) {
            messageSenderSys = null;
            throw e;
        }
//        return false;
    }
}

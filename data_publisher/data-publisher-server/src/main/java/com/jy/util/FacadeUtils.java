package com.jy.util;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: zy
 * @Date: Created in 2018/4/26
 */
@Component
public class FacadeUtils extends BaseFacadeUtils {
    @Autowired
    private Environment env;

    @Override
    @PostConstruct
    /** 项目启动时，从配置文件、或者数据库获取 */
    public void initParam() {
        try {
            Auth auth = new Auth();
            auth.setUsername(env.getProperty("facade.env1.username"));
            auth.setPassword(env.getProperty("facade.env1.password"));
            auth.setAuthorization(env.getProperty("facade.authorization"));
            auth.setUrl(env.getProperty("facade.env1.url"));
            auth.setProxyHost(env.getProperty("facade.env1.proxyHost"));
            auth.setProxyPort(env.getProperty("facade.env1.proxyPort"));
            auth.setProxyUserName(env.getProperty("facade.env1.proxyUserName"));
            auth.setProxyPassword(env.getProperty("facade.env1.proxyPassword"));
            authMap.put(auth.getUrl(), auth);

            Auth auth1 = new Auth();
            auth1.setUsername(env.getProperty("facade.env2.username"));
            auth1.setPassword(env.getProperty("facade.env2.password"));
            auth1.setAuthorization(env.getProperty("facade.authorization"));
            auth1.setUrl(env.getProperty("facade.env2.url"));
            auth1.setProxyHost(env.getProperty("facade.env2.proxyHost"));
            auth1.setProxyPort(env.getProperty("facade.env2.proxyPort"));
            auth1.setProxyUserName(env.getProperty("facade.env2.proxyUserName"));
            auth1.setProxyPassword(env.getProperty("facade.env2.proxyPassword"));
            authMap.put(auth1.getUrl(), auth1);

            Auth auth2 = new Auth();
            auth2.setUsername(env.getProperty("facade.env3.username"));
            auth2.setPassword(env.getProperty("facade.env3.password"));
            auth2.setAuthorization(env.getProperty("facade.authorization"));
            auth2.setUrl(env.getProperty("facade.env3.url"));
            auth2.setProxyHost(env.getProperty("facade.env3.proxyHost"));
            auth2.setProxyPort(env.getProperty("facade.env3.proxyPort"));
            auth2.setProxyUserName(env.getProperty("facade.env3.proxyUserName"));
            auth2.setProxyPassword(env.getProperty("facade.env3.proxyPassword"));
            authMap.put(auth2.getUrl(), auth2);

            /** 海外加代理 */
            Auth auth3 = new Auth();
            auth3.setUsername(env.getProperty("facade.env4.username"));
            auth3.setPassword(env.getProperty("facade.env4.password"));
            auth3.setAuthorization(env.getProperty("facade.authorization"));
            auth3.setUrl(env.getProperty("facade.env4.url"));
            auth3.setProxyHost(env.getProperty("facade.env4.proxyHost"));
            auth3.setProxyPort(env.getProperty("facade.env4.proxyPort"));
            auth3.setProxyUserName(env.getProperty("facade.env4.proxyUserName"));
            auth3.setProxyPassword(env.getProperty("facade.env4.proxyPassword"));
            authMap.put(auth3.getUrl(), auth3);

            if(StringUtils.isNotBlank(env.getProperty("httpClient.maxConnectionNum")) ){
                HttpUtils.MAX_CONNECTION_NUM =  Integer.parseInt(env.getProperty("httpClient.maxConnectionNum"));
            }
            if(StringUtils.isNotBlank(env.getProperty("httpClient.maxPerRoute"))){
                HttpUtils.MAX_PER_ROUTE = Integer.parseInt(env.getProperty("httpClient.maxPerRoute"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    /**
     * 从缓存中获取token
     * @return
     */
    public String getTokenCache(String authKey) throws Exception {
        if(authMap.get(authKey) == null){
            throw new Exception("BaseFacadeUtils 中的 authKey: " + authKey + " 的用户为空");
        }
        return authMap.get(authKey).getToken();
    }

    @Override
    public Map<String, String> getProxyConfig(String authKey) throws Exception {
        if(authMap.get(authKey) == null){
            throw new Exception("BaseFacadeUtils 中的 authKey: " + authKey + " 的用户为空");
        }
        Map<String, String> proxyMap = new HashMap<>();
        proxyMap.put("proxyHost", authMap.get(authKey).getProxyHost());
        proxyMap.put("proxyPort", authMap.get(authKey).getProxyPort());
        proxyMap.put("proxyUserName", authMap.get(authKey).getProxyUserName());
        proxyMap.put("proxyPassword", authMap.get(authKey).getProxyPassword());
        return proxyMap;
    }

    @Override
    /**
     * 更新缓存中的token
     * @return
     */
    public void updateTokenCache(String authKey, String token) throws Exception {
        if(authMap.get(authKey) == null){
            throw new Exception("BaseFacadeUtils 中的 authKey: " + authKey + " 的用户为空");
        }
        authMap.get(authKey).setToken(token);
    }


    public static void main(String args[]) throws Exception {
        FacadeUtils facadeUtils = new FacadeUtils();
        Auth auth = facadeUtils.new Auth();
        auth.setUsername("test");
        auth.setPassword("test");
        auth.setAuthorization("YW5kcm9pZDphbmRyb2lk");
        auth.setUrl("http://*************:8765/");
        facadeUtils.authMap.put(auth.getUrl(), auth);

        Map<String, String> querys = new HashMap<String, String>();
        querys.put("oe", "6103121JA");
        try {
            // 正常调用
//            JSONObject jsonObject = facadeUtils.doPostFile("http://192.168.120.10:8765/","vehicle-service/imageOCRs", querys, byt, "1.jpg");
            // 压缩调用
         //   JSONObject jsonObject = facadeUtils.doCompressGet("part-service/partDetails", querys);
         //   System.out.println(jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}

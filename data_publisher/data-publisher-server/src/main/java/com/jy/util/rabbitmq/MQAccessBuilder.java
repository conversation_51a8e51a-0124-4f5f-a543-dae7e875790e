package com.jy.util.rabbitmq;

import com.jy.util.EmptyUtils;
import com.jy.util.FeishuSendMsgUtil;
import com.rabbitmq.client.Channel;
import org.springframework.amqp.rabbit.connection.Connection;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.support.CorrelationData;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.retry.backoff.ExponentialBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.concurrent.TimeoutException;

public class MQAccessBuilder {
    private ConnectionFactory connectionFactory;

    private volatile static RetryCache retryCache;

    public MQAccessBuilder(ConnectionFactory connectionFactory) {
        this.connectionFactory = connectionFactory;
    }

    private void buildQueue(String exchange, String routingKey, final String queue, Connection connection, String
            type) throws IOException {
        Channel channel = null;
        try {
            channel = connection.createChannel(false);
            switch (type) {
                //绑定队列1对1的通讯消息
                case "direct":
                    channel.exchangeDeclare(exchange, "direct", true, false, null);
                    break;
                //以rountingKey开头的队列分发该消息
                case "topic":
                    channel.exchangeDeclare(exchange, "topic", true, false, null);
                    break;
                //分发路由消息
                case "fanout":
                    channel.exchangeDeclare(exchange, "fanout", true);
                    break;
                default:
                    break;
            }
            channel.queueDeclare(queue, true, false, false, null);
            channel.queueBind(queue, exchange, routingKey);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (channel != null) {
                try {
                    channel.close();
                } catch (TimeoutException ex) {
                    System.out.println("close channel time out ");
                    ex.printStackTrace();
                }
            }
        }
    }

//    1 构造template, exchange, routingkey等
//    2 设置message序列化方法
//    3 设置发送确认
//    4 构造sender方法
    public MessageSender buildMessageSender(final String exchange, final String routingKey, final String queue, final
    String type) throws IOException {
        Connection connection = connectionFactory.createConnection();
        //1
        switch (type) {
            //绑定队列1对1的通讯消息
            case "direct":
                buildQueue(exchange, routingKey, queue, connection, "direct");
                break;
            //以rountingKey开头的队列分发该消息
            case "topic":
                buildQueue(exchange, routingKey + ".#", queue, connection, "topic");
                break;
            //分发路由消息
            case "fanout":
                buildQueue(exchange, "", queue, connection, "fanout");
                break;
            default:
                return null;
        }
        final RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);

        rabbitTemplate.setMandatory(true);
        rabbitTemplate.setExchange(exchange);
        rabbitTemplate.setRoutingKey(routingKey);

        //2
        rabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
        if (EmptyUtils.isEmpty(retryCache)) {
            synchronized (MQAccessBuilder.class) {
                if (EmptyUtils.isEmpty(retryCache)) {
                    retryCache = new RetryCache();
                }
            }
        }


        //3
        rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {
            if (!ack) {
                System.out.println("send message failed: " + cause + correlationData.toString());
            } else {
                retryCache.del(Long.valueOf(correlationData.getId()));
            }
        });

        rabbitTemplate.setReturnCallback((message, replyCode, replyText, tmpExchange, tmpRoutingKey) -> {
            try {
                Thread.sleep(Constants.ONE_SECOND);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            System.out.println("send message failed: " + replyCode + " " + replyText);
            rabbitTemplate.send(message);
        });

        //4
        return new MessageSender() {
            @Override
            public DetailRes send(Object message) {
                long id = retryCache.generateId();
                long time = System.currentTimeMillis();
                return send(new MessageWithTime(id, time, message, this));
            }

            @Override
            public DetailRes send(MessageWithTime messageWithTime) {
                try {
                    retryCache.add(messageWithTime);
                    rabbitTemplate.correlationConvertAndSend(messageWithTime.getMessage(), new CorrelationData(String
                            .valueOf(messageWithTime.getId())));
                } catch (Exception e) {
                    FeishuSendMsgUtil.sendQueueMsg("MQAccessBuilder.send", queue,"轨迹信息rabbitmq发送消息失败" + e.getMessage());
                    return new DetailRes(false, "");
                }
                return new DetailRes(true, "");
            }
        };
    }

}

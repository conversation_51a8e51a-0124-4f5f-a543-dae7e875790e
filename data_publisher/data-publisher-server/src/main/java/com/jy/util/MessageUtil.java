package com.jy.util;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class MessageUtil {

	public static final String DEFAULT_CODE = "utf-8";

	public static boolean verifySign(Map<String,Object> map, String secretKey) throws Exception
	{
		boolean result = false;
		String sign="";
		String originSign = (String) map.get("sign");
		map.remove("sign");
		sign = sign(map,secretKey);
		if(sign.equals(originSign))result = true;
		return result;
	}


	/**
	 * 签名
	 * @param map map
	 * @param secretKey 秘钥
	 * @return 签名
	 * @throws Exception
	 */
	public static String sign(Map<String,Object> map,String secretKey) throws Exception {
		StringBuilder contentBuffer = new StringBuilder();
		Object[] signParamArray = map.keySet().toArray();
		Arrays.sort(signParamArray);
		for (Object key : signParamArray) {
			Object value = map.get(key);
			if (!"digest".equals(key) && value!=null&& !value.equals("")) {
				contentBuffer.append(key + "=" + value);
			}
		}
		String sign=contentBuffer+secretKey;
		sign =CertUtil.Md5(sign,DEFAULT_CODE);
		return sign;
	}


	public static void main(String[] args) throws Exception {
		Map<String,Object> map = new HashMap<String,Object>();
		String secretKey = "";
		map.put("appId", "123");
		map.put("timestamp", "1541178568000");
		map.put("sign", "ABBAECB11ABA59E06FDF02D1EB324684");

		String str =sign(map,secretKey);
		System.out.println(str);
	}




}

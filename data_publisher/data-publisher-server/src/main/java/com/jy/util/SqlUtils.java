package com.jy.util;

import com.jy.ann.MethodMonitor;
import org.springframework.data.domain.Sort;

import java.lang.reflect.Field;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2017/12/26
 */
public class SqlUtils {

    @MethodMonitor
    public static String Sort2SqlString(Sort sort){
        if(sort != null){
            String reStr = sort.toString().replace(": ASC", " asc")
                    .replace(": DESC", " desc");
            return StringUtils.humpToLine(reStr);
        } else {
            return null;
        }
    }

    @MethodMonitor
    public static String Set2SqlInString(Set<?> set, String filedName) throws NoSuchFieldException, IllegalAccessException {
        String sqlIn = "";
        for(Object o : set){
            Field field = o.getClass().getDeclaredField(filedName);
            field.setAccessible(true);
            sqlIn = sqlIn + "'" + field.get(o) + "',";
        }
        if(sqlIn.length() > 0){
            sqlIn = "(" + sqlIn.substring(0, sqlIn.length() -1) + ")";
        }
        return sqlIn;
    }

    @MethodMonitor
    public static String List2SqlInString(List<?> list, String filedName) throws NoSuchFieldException, IllegalAccessException {
        String sqlIn = "";
        list = ListUtils.removeDuplicate(list);
        for(Object o : list){
            Field field = o.getClass().getDeclaredField(filedName);
            field.setAccessible(true);
            sqlIn = sqlIn + "'" + field.get(o) + "',";
        }
        if(sqlIn.length() > 0){
            sqlIn = "(" + sqlIn.substring(0, sqlIn.length() -1) + ")";
        }
        return sqlIn;
    }
    @MethodMonitor
    public static String List2SqlInString(List<String> list) throws NoSuchFieldException, IllegalAccessException {
        String sqlIn = "";
        list = ListUtils.removeDuplicate(list);
        for(String s : list){
            sqlIn = sqlIn + "'" + s + "',";
        }
        if(sqlIn.length() > 0){
            sqlIn = "(" + sqlIn.substring(0, sqlIn.length() -1) + ")";
        }
        return sqlIn;
    }

    @MethodMonitor
    public static Set<String> List2SqlInSet(List<?> list, String filedName) throws NoSuchFieldException, IllegalAccessException {
        Set<String> set = new HashSet<String>();
        for(Object o : list){
            Field field = o.getClass().getDeclaredField(filedName);
            field.setAccessible(true);
            set.add((String)field.get(o));
        }
        return set;
    }

    @MethodMonitor
    public static Set<String> List2SqlInSet(List<String> list) throws NoSuchFieldException, IllegalAccessException {
        Set<String> set = new HashSet<String>();
        for(String s : list){
            set.add(s);
        }
        return set;
    }
}

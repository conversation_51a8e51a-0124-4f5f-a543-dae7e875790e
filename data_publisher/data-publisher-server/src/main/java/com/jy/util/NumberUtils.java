package com.jy.util;

/**
 * @Author: zy
 * @Date: Created in 2018/3/30
 */
public class NumberUtils {

    public static int String2Int(String str, int defaultValue){
        if (str == null) return defaultValue;
        try {
            return Integer.parseInt(str);
        } catch (NumberFormatException x) {
            return defaultValue;
        }
    }

    public static double String2Double(String str, double defaultValue){
        if (str == null) return defaultValue;
        try {
            return Double.parseDouble(str);
        } catch (NumberFormatException x) {
            return defaultValue;
        }
    }
}

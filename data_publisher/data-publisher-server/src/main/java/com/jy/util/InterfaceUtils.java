package com.jy.util;

/**
 * @Auther: miao
 * @Date: 2018/7/17 13:57
 * @Description:
 */
public class InterfaceUtils {

    /**平台编码start**/
    //一代加工平台
    public static final String PLATFORM_CODE_ONE_PROCESS = "001";
    //二代加工平台
    public static final String PLATFORM_CODE_TWO_PROCESS = "002";
    //三代加工平台
    public static final String PLATFORM_CODE_THREE_PROCESS= "003";
    //一代发布平台
    public static final String PLATFORM_CODE_ONE_PUBLISH = "004";
    //整车
    public static final String PLATFORM_CODE_WHOLE_VEHICLE = "005";
    //分省
    public static final String PLATFORM_CODE_PROVINCIAL_DIVISION = "006";
    //医疗
    public static final String PLATFORM_CODE_MEDICAL = "007";
    //一代加工平台
    public static final String PLATFORM_CODE_DATA_PUBLISHER = "008";
    //一代加工平台
    public static final String PLATFORM_CODE_PRODUCT_PUBLISHER = "009";
    //一代加工平台
    public static final String PLATFORM_CODE_CLIENT = "010";
    /**平台编码end**/

    /**接口编码start**/
    public static final String INTERFACE_CODE_LOG_001101 = "001101";
    public static final String INTERFACE_CODE_LOG_008101 = "008101";
    public static final String INTERFACE_CODE_LOG_008201 = "008201";
    public static final String INTERFACE_CODE_LOG_008301 = "008301";
    public static final String INTERFACE_CODE_LOG_009101 = "009101";
    public static final String INTERFACE_CODE_LOG_009201 = "009201";
    public static final String INTERFACE_CODE_LOG_009301 = "009301";
    public static final String INTERFACE_CODE_LOG_009401 = "009401";
    public static final String INTERFACE_CODE_LOG_010101 = "010101";
    public static final String INTERFACE_CODE_LOG_010201 = "010201";

}

package com.jy.util;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;

/**
 * Created by anxing on 2018/7/17.
 */
public class ReceiveDataUtils {

    /**
     * 解析报文
     * @param request
     * @return
     */
    public static String getSendString(HttpServletRequest request) {
        String sendString = "";
        try {
            InputStream in = request.getInputStream();
            InputStreamReader insreader = new InputStreamReader(in, "UTF-8");
            BufferedReader bin = new BufferedReader(insreader);
            StringBuffer bs = new StringBuffer();
            String line;
            while ((line = bin.readLine()) != null) {
                bs.append(line);
            }
            bin.close();
            insreader.close();
            //处理接收到的xml内容
            sendString = bs.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sendString;
    }
}

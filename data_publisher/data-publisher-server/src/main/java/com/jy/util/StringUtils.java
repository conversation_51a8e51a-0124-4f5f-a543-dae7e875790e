package com.jy.util;

import com.jy.ann.MethodMonitor;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;

import javax.servlet.http.HttpServletRequest;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by zy on 2017/11/13.
 */
public class StringUtils {
    public static boolean isContainChinese(String str) {

        Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
        Matcher m = p.matcher(str);
        return m.find();
    }
    private static Pattern humpPattern = Pattern.compile("[A-Z]");

    /**驼峰转下划线*/
    @MethodMonitor
    public static String humpToLine(String str){
        if(str != null){
            Matcher matcher = humpPattern.matcher(str);
            StringBuffer sb = new StringBuffer();
            while(matcher.find()){
                matcher.appendReplacement(sb, "_"+matcher.group(0).toLowerCase());
            }
            matcher.appendTail(sb);
            return sb.toString();
        } else {
            return null;
        }
    }
    @MethodMonitor
    public static String filterNumAndLetter(String str){
        if(str != null){
            return  str.replaceAll("[^(A-Za-z0-9.%_)]", "");
        } else {
            return null;
        }
    }
    public static String getUUID() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }


    /**
     * 字符串的MD5值加密 --返回16位
     * @param sourceStr
     * @return
     */
    public static String MD5(String sourceStr) {
        String result = "";
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(sourceStr.getBytes());
            byte b[] = md.digest();
            int i;
            StringBuffer buf = new StringBuffer("");
            for (int offset = 0; offset < b.length; offset++) {
                i = b[offset];
                if (i < 0)
                    i += 256;
                if (i < 16)
                    buf.append("0");
                buf.append(Integer.toHexString(i));
            }
            result = buf.toString().substring(8, 24);
        } catch (NoSuchAlgorithmException e) {
            System.out.println(e);
        }
        return result;
    }

    public static void main(String [] args){
/*String str = "Replacer_10_20200713_17260100_1_2_1";
        System.out.println(str.substring(str.lastIndexOf("_") + 1));
        String [] list = str.split("_");
        System.out.println(list[list.length -1 ]);*/
        System.out.println(MD5("BPIC"));
/*
String batchNo = "AmVeh_203_20200518_163648_0_36";
        String[] batch = batchNo.split("_");
        String year = DateUtil.convertDateToString("yyyy", new Date());
        System.out.println(year);
        String date = batch.length > 3 ? batch[2] : batchNo.substring(batchNo.indexOf("2020"), batchNo.indexOf("2020") + 4);
        System.out.println(date);
*/
    }
}

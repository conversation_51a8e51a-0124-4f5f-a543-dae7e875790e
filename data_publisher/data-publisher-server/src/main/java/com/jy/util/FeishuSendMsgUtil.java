package com.jy.util;

import com.jy.mq.RabbitCommon;
import com.jy.service.SendExpMsgService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * @program: data-publisher
 * @description:
 * @author: Ykuee
 * @create: 2024-12-31 15:38
 **/

@Component
public class FeishuSendMsgUtil {

    private static final Logger logger = LogManager.getLogger(RabbitCommon.class);

    private static SendExpMsgService sendExpMsgService;

    @Autowired
    public FeishuSendMsgUtil(SendExpMsgService sendExpMsgService) {
        this.sendExpMsgService = sendExpMsgService;
    }

    //发送飞书消息
    public static void sendQueueMsg(String methodName, String queueName,String msg) {
        try {
            LocalDateTime startTime = LocalDateTime.now();
            sendExpMsgService.sendMsg(methodName, startTime, "异常队列:" + queueName + "\n 异常信息:" + msg + "\n");
        } catch (Exception msgE) {
            logger.error("及时更新发送RabbitMQ连接警告时发生异常", msgE);
        }
    }

    public static void sendExpMsg(String methodName, String msg) {
        try {
            LocalDateTime startTime = LocalDateTime.now();
            sendExpMsgService.sendMsg(methodName, startTime, "\n 异常信息:" + msg + "\n");
        } catch (Exception msgE) {
            logger.error("及时更新发送RabbitMQ连接警告时发生异常", msgE);
        }
    }
}

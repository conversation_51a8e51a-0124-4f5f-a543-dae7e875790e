package com.jy.util;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.entity.ContentType;
import org.apache.http.nio.entity.NStringEntity;
import org.apache.http.util.EntityUtils;
import org.elasticsearch.client.Request;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.Collections;
import java.util.Map;

/**
 * @Author: zy
 * @Date: Created in 2019/11/22
 */

@Component
public class ElasticsearchUtil {

    @Resource(name="restHighLevelClient")
    private RestHighLevelClient restHighLevelClient;

    private static RestHighLevelClient client;

    private static ObjectMapper mapper = new ObjectMapper();

    @PostConstruct
    public void init() {
        client = this.restHighLevelClient;
    }

    /**
     * 获取低水平客户端
     * @return
     */
    public static RestClient getLowLevelClient() {
        return client.getLowLevelClient();
    }

    public static JSONObject search(String endPoint , HttpEntity entity) throws Exception{
        Request request = new Request("POST", endPoint);
        request.setEntity(entity);
        Response response = ElasticsearchUtil.getLowLevelClient().performRequest(request);
        String retSrc = EntityUtils.toString(response.getEntity());
        return JSONObject.parseObject(retSrc);
    }

    public static JSONObject aggs(String endPoint , HttpEntity entity) throws Exception{
        JSONObject jsonObject = ElasticsearchUtil.search(endPoint, entity);
        return jsonObject.getJSONObject("aggregations");
    }



    public static void main(String[] args){
        RestClient restClient = RestClient.builder(
                new HttpHost("**************", 9400, "http")).build();

        Map<String, String> params = Collections.emptyMap();
        String jsonString = "{\n" +
                "   \n" +
                "   \"query\": {\n" +
                "    \"bool\": {\n" +
                "      \"must_not\": {\n" +
                "        \"match\":{\n" +
                "                    \"num\":1\n" +
                "          }\n" +
                "      }\n" +
                "    }\n" +
                "  }\n" +
                "}";
        HttpEntity entity = new NStringEntity(jsonString, ContentType.APPLICATION_JSON);
        try {
            Request request = new Request("POST", "/data-trace-alias/_search");
            request.setEntity(entity);
            Response response = ElasticsearchUtil.getLowLevelClient().performRequest(request);
           // Response response = restClient.performRequest("POST", "/data-trace/_search", params, entity);
            HttpEntity httpEntity = response.getEntity();
            if (httpEntity != null) {
                String retSrc = EntityUtils.toString(httpEntity);
                // parsing JSON
                JSONObject result = JSONObject.parseObject(retSrc); //Convert String to JSON Object
                System.out.println(result);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }


        try {
            restClient.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}

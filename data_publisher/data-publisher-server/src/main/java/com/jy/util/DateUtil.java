package com.jy.util;

import com.jy.ann.MethodMonitor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018/4/26
 */
public class DateUtil {
    public static Logger logger = LogManager.getLogger(DateUtil.class);
    public static String datePattern = "yyyy-MM-dd";
    public static String timePattern = "yyyy-MM-dd HH:mm:ss";
    public static String milliPattern = "yyyy-MM-dd HH:mm:ss SSS";
    // 定义日期时间格式
    public static DateTimeFormatter formatter = DateTimeFormatter.ofPattern(timePattern);

    @MethodMonitor
    public static final Date convertStringToDate(String aMask, String strDate) {
        SimpleDateFormat df =  new SimpleDateFormat(aMask);
        Date date = null;
        try {
            date = df.parse(strDate);
        } catch (ParseException pe) {
            logger.info(pe.getMessage());
        }
        return (date);
    }

    @MethodMonitor
    public static final String convertDateToString(String aMask, Date date) {
        SimpleDateFormat df =  new SimpleDateFormat(aMask);
        String dateStr = null;
        try {
            dateStr = df.format(date);
        } catch (Exception pe) {
            logger.info(pe.getMessage());
        }
        return (dateStr);
    }

    @MethodMonitor
    public static final String convertDateToString(Date aDate) {
        return convertDateToString(datePattern, aDate);
    }

    @MethodMonitor
    public static final String convertDateToString(LocalDateTime aDate) {
        return aDate.format(formatter);
    }

    @MethodMonitor
    public static Date convertStringToDate(String strDate) {
        return convertStringToDate(datePattern, strDate);
    }

    // 当前时间
    @MethodMonitor
    public static Timestamp crunttime() {
        return new Timestamp(System.currentTimeMillis());
    }

    public static String getTime(int num){
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, num);
        Date date = cal.getTime();
        SimpleDateFormat df = new SimpleDateFormat(datePattern);
        return df.format(date);
    }

    public static long getBetweenDay(String date) {
        LocalDate dateOfFeb = LocalDate.parse(date);
        LocalDate today = LocalDate.now();
        return today.toEpochDay() - dateOfFeb.toEpochDay();
    }

    public static String getDateByBatchNo(String batchNo){
        String year = DateUtil.convertDateToString("yyyy", new Date());
        String[] batch = batchNo.split("_");
        return batch.length > 3 ? batch[2] : batchNo.substring(batchNo.indexOf(year), batchNo.indexOf(year) + 8);
    }

}

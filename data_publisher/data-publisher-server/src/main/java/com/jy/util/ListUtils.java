package com.jy.util;

import java.lang.reflect.Method;
import java.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.Predicate;

/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2017/12/20
 */
public class ListUtils<T> {

    public static List removeDuplicate(List list) {
        List reList = new ArrayList<>();
        Set set = new HashSet();
        for(Object object : list){
            if(set.add(object))
                reList.add(object);
        }
        return reList;
    }

    /**
     * 从列表中找出包括Value值的对象列表
     * @param list
     * @param filedName
     * @param value
     * @return
     */
    public static <T,V> List<T> findObjFromList(List<T> list, String filedName, V value) {

        Predicate<T> predicate = new Predicate<T>() {

            @Override
            public boolean evaluate(T obj) {
                // TODO Auto-generated method stub
                try {
                    String methodName = "get" + filedName.substring(0, 1).toUpperCase() + filedName.substring(1);
                    Method method = obj.getClass().getMethod(methodName);
                    @SuppressWarnings("unchecked")
                    V keyNameValue = (V) method.invoke(obj);
                    return keyNameValue.equals(value);
                }
                catch (Exception e) {
                    return false;
                }
            }
        };

        List<T> result = (List<T>) CollectionUtils.select(list, predicate);
        return result;

    }

    /**
     * 从列表中找出包括Value值的对象列表
     * @param list
     * @param keyName2ValueMap
     * @return
     */
    public static <T,V> List<T> findObjFromList(List<T> list, Map<String, V> keyName2ValueMap) {

        Predicate<T> predicate = new Predicate<T>() {

            @Override
            public boolean evaluate(T obj) {
                // TODO Auto-generated method stub
                try {
                    for(Map.Entry<String, V> entry : keyName2ValueMap.entrySet()){
                        String keyName  = entry.getKey();
                        V value = entry.getValue();
                        String methodName = "get" + keyName.substring(0, 1).toUpperCase() + keyName.substring(1);
                        Method method = obj.getClass().getMethod(methodName);
                        V keyNameValue = (V) method.invoke(obj);
                        if(!keyNameValue.equals(value)){
                            return false;
                        }
                    }
                    return true;
                }
                catch (Exception e) {
                    return false;
                }
            }
        };

        List<T> result = (List<T>) CollectionUtils.select(list, predicate);
        return result;

    }
    public static void main(String args[]){
    }
}

package com.jy.util;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;

/**
/**
 * @Author: zy
 * @Date: Created in 2019/11/5
 */
public class ToolUtils {

    /**
     * 获取异常的具体信息
     */
    public static String getExceptionMsg(Exception e) {
        StringWriter sw = new StringWriter();
        try {
            e.printStackTrace(new PrintWriter(sw));
        } finally {
            try {
                sw.close();
            } catch (IOException e1) {
                e1.printStackTrace();
            }
        }
        return sw.getBuffer().toString().replaceAll("\\$", "T");
    }

    public static void main(String [] args) {
        String [] s = new String[] {};
        try {
            System.out.println(s[3]);
        }catch (Exception r){
            System.out.println(ToolUtils.getExceptionMsg(r));
        }

    }




}

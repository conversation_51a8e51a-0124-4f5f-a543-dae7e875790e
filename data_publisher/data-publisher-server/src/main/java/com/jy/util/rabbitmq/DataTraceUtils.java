package com.jy.util.rabbitmq;

import com.alibaba.fastjson.JSONObject;
import com.jy.bean.common.DataTraceMenu;
import com.jy.service.DataTraceService;
import com.jy.util.DateUtil;
import com.jy.util.EmptyUtils;
import com.jy.util.StringUtils;
import com.jy.util.ToolUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.Map;

/**
 * @Author: zy
 * @Date: Created in 2019/11/1
 */
@Component
public class DataTraceUtils {
    private static final Logger logger = LogManager.getLogger(DataTraceUtils.class);

    @Autowired
    private DataTraceService dataTraceService;

    public static boolean sendTrace(JSONObject jsonObject, String nodeCode, String clientCode, String status, String message){
        return sendTrace(null, jsonObject, nodeCode, clientCode, status, message, DateUtil.crunttime(), jsonObject.getInteger("sendTimes"));
    }

    public static boolean sendTrace(String sId, JSONObject jsonObject, String nodeCode, String clientCode, String status, String message){
        return sendTrace(sId, jsonObject, nodeCode, clientCode, status, message, DateUtil.crunttime(), jsonObject.getInteger("sendTimes"));
    }

    public static boolean sendTrace(JSONObject jsonObject, String nodeCode, String clientCode, String status, String message, Timestamp time){
        return sendTrace(null, jsonObject, nodeCode, clientCode, status, message, time, jsonObject.getInteger("sendTimes"));
    }

    public static boolean sendTrace(JSONObject jsonObject, String nodeCode, String clientCode, String status, String message, Integer sendTimes){
        return sendTrace(null, jsonObject, nodeCode, clientCode, status, message, DateUtil.crunttime(), sendTimes);
    }

    public static boolean sendTrace(String sId, JSONObject jsonObject, String nodeCode, String clientCode, String status, String message, Timestamp time, Integer sendTimes){
        if(EmptyUtils.isNotEmpty(jsonObject.get("batchNoStatus"))){
            return true;
        }
        boolean f = false;
        JSONObject trace = new JSONObject();
        Map<String, String> nodeInfo = DataTraceMenu.valueof(nodeCode);
        trace.put("id", jsonObject.get("id"));
        //s_id作为es文档中的主键

        if (EmptyUtils.isEmpty(sId)) {
            if (EmptyUtils.isEmpty(jsonObject.get("id"))) {
                sId = StringUtils.getUUID();
            }else{
                sId = jsonObject.get("id") + "";
            }
        }
        trace.put("s_id", "publisher_"+sId);
        trace.put("mainBatchNo", jsonObject.get("mainBatchNo"));
        trace.put("batchNo", jsonObject.get("batchNo"));
        trace.put("num", jsonObject.containsKey("num") ? jsonObject.get("num") : 1);
        trace.put("clientCode", clientCode);
        trace.put("status", status);
        trace.put("sendTimes", sendTimes);
        trace.put("message", message);
        trace.put("serviceName", nodeInfo.get("serviceName"));
        trace.put("nodeName", nodeInfo.get("nodeName"));
        trace.put("orgCode", EmptyUtils.isNotEmpty(jsonObject.get("orgCode")) ? jsonObject.get("orgCode").toString() : "");
        trace.put("currentTime", time);
        trace.put("dataSource", "publisher");
        //trace.put("dataVersionId", dataVersionId);


       // System.out.println("记录轨迹=------------------" + trace.toJSONString());

        try {
            f = RabbitMqUtil.sendElk(trace);
        } catch (IOException e) {
            logger.error(ToolUtils.getExceptionMsg(e));
        }
        return f;
    }
}

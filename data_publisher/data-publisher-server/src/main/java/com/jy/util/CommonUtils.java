package com.jy.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class CommonUtils {

    @Value("${facade.env1.url}")
    private  String url;
    @Value("${facade.smsPath}")
    private  String smsPath;
    @Value("${facade.emailPath}")
    private  String emailPath;
    @Value("${facade.defaultEmailTo}")
    private  String defaultEmailTo;
    @Value("${facade.workWechatPath}")
    private  String workWechatPath;

    @Autowired
    private FacadeUtils facadeUtils;

    public void sendEmail(String subject, String msg){
        sendEmail(subject, msg, defaultEmailTo);
    }

    public void sendEmail(String subject, String msg, String emailTo){
        Map<String, String> querys = new HashMap<>();
        querys.put("simple", "simple");
        querys.put("to", emailTo);
        querys.put("subject", subject);
        querys.put("msg", msg);
        try {
            facadeUtils.doPost(url, emailPath, querys, "");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void sendSms(String phoneNumber, String msg){
        Map<String, String> querys = new HashMap<>();
        querys.put("templateId", "929369");
        querys.put("phoneNumber", phoneNumber);
        querys.put("msg", msg);
        try {
            facadeUtils.doPost(url, smsPath, querys, "");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void sendWorkWechatPath(String username, String msg){
        Map<String, String> querys = new HashMap<>();
        querys.put("text", "text");
        querys.put("names", username);
        querys.put("msg", msg);
        try {
            facadeUtils.doGet(url, workWechatPath, querys);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

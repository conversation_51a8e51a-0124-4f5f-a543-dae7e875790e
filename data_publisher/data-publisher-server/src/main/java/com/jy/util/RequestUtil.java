package com.jy.util;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpRequestRetryHandler;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> SHUAI
 *
 */
public class RequestUtil {

	@Value("${thePath.appId}")
	private  String appId;
	@Value("${thePath.authorization}")
	private  String authorization;

	private final static String CONTENT_TYPE_FORM = "x-www-form-urlencoded";

	// 采用绕过验证的方式处理https请求
	public static String postRequest(String urlStr, String requestText, String charSet) throws Exception {
		String responseBody = null;
		URL url = new URL(urlStr);
		if ("https".equalsIgnoreCase(url.getProtocol())) {
			SslUtils.ignoreSsl();
		}
        HttpURLConnection u = (HttpURLConnection) url.openConnection();
        u.setRequestMethod("POST");
        u.setDoOutput(true);
        u.setRequestProperty("Content-Type",CONTENT_TYPE_FORM);
        u.connect();
		//设置请求和传输超时时间
		u.setConnectTimeout(100000);
		u.setReadTimeout(100000);
		OutputStreamWriter osw = new OutputStreamWriter(u.getOutputStream(), charSet);
		osw.write(requestText);
		osw.flush();
		osw.close();
		u.getOutputStream();
        InputStream is;
		if (u.getResponseCode() == 200) {
            is = u.getInputStream();
        } else {
            is = u.getErrorStream();
        }
		responseBody = IOUtils.toString(is);
		return responseBody;
	}
	/**
	 * get请求
	 * @param urlStr
	 * @param requestText
	 * @return
	 * @throws Exception
	 */
	public static String readContentFromGet(String urlStr, String path,Map<String, String> querys) throws Exception {
		// 拼凑get请求的URL字串，使用URLEncoder.encode对特殊和不可见字符进行编码
		String getURL = HttpUtils.buildUrl(urlStr,path,querys);
		URL getUrl = new URL(getURL);
		if ("https".equalsIgnoreCase(getUrl.getProtocol())) {
			SslUtils.ignoreSsl();
		}
		// 根据拼凑的URL，打开连接，URL.openConnection函数会根据URL的类型，
		// 返回不同的URLConnection子类的对象，这里URL是一个http，因此实际返回的是HttpURLConnection
		HttpURLConnection connection = (HttpURLConnection) getUrl
				.openConnection();
		// 进行连接，但是实际上get request要在下一句的connection.getInputStream()函数中才会真正发到
		//设置请求和传输超时时间
		connection.setConnectTimeout(100000);
		connection.setReadTimeout(100000);
		// 服务器
		connection.connect();
		// 取得输入流，并使用Reader读取
		BufferedReader reader = new BufferedReader(new InputStreamReader(
				connection.getInputStream()));
		String lines;
		while ((lines = reader.readLine()) != null){
			System.out.println(lines);
		}
		//关闭读取流
		reader.close();
		return lines;
	}
	/**
	 * Post String
	 * @return
	 * @throws Exception
	 */
	public static HttpResponse doPost(String host, String path,Map<String, String> headers,Map<String, String> querys,String body)
			throws Exception {
		System.out.println(HttpUtils.buildUrl(host, path, querys));
		HttpClient httpClient = buildHttpClient();
		if ("https".equalsIgnoreCase(host)) {
			SslUtils.ignoreSsl();
		}
		HttpHost proxy = new HttpHost("*********", 80);//大族机房代理设置
		RequestConfig config = RequestConfig.custom().setProxy(proxy)
				.setProxy(proxy)
				.setConnectTimeout(30000)
				.setSocketTimeout(30000)
				.setConnectionRequestTimeout(3000)
				.build();
		HttpPost request = new HttpPost(HttpUtils.buildUrl(host, path, querys));
		request.setConfig(config);
		if(headers != null) {
			for (Map.Entry<String, String> e : headers.entrySet()) {
				request.addHeader(e.getKey(), e.getValue());
			}
		}
		if (StringUtils.isNotBlank(body)) {
			StringEntity stringEntity = new StringEntity(body, ContentType.APPLICATION_JSON);
			stringEntity.setContentEncoding("utf-8");
			request.setEntity(stringEntity);
			//request.setEntity(new StringEntity(body, "utf-8"));
		}
		return httpClient.execute(request);
	}
	/**
	 * 请求连接超时设置
	 * DefaultHttpRequestRetryHandler 请求连接次数
	 * setConnectTimeout  连接超时设置
	 * setSocketTimeout   传输超时设置
	 * @return
	 */
	private static HttpClient buildHttpClient() {
		HttpClientBuilder cb = HttpClientBuilder
				.create()
				.disableAutomaticRetries()
				.setRetryHandler(new DefaultHttpRequestRetryHandler(10, true))
				.setDefaultRequestConfig(
						RequestConfig.custom().setConnectTimeout(60 * 1000)
								.setSocketTimeout(60 * 1000).build());
		return cb.build();
	}
	public static HttpResponse doGet(String host, String path,Map<String, String> headers,Map<String, String> querys,String body)
			throws Exception {
		HttpClient httpClient = buildHttpClient();
		if ("https".equalsIgnoreCase(host)) {
			SslUtils.ignoreSsl();
		}
		HttpGet request = new HttpGet(HttpUtils.buildUrl(host, path, querys));
		if(headers != null) {
			for (Map.Entry<String, String> e : headers.entrySet()) {
				request.addHeader(e.getKey(), e.getValue());
			}
		}
		return httpClient.execute(request);
	}


	public static void main(String[] args){
		Map<String, String> querys = new HashMap<String, String>();
		Map<String, Object> obj = new HashMap<String, Object>();
		querys.put("appId", "10011001");
		querys.put("timestamp", "111");
		querys.put("sign", "111");
		try {
			String secret ="secret=fd640177f87c9924cf064ea579181267";//密钥
			String time = String.valueOf(System.currentTimeMillis()).toString();//Unix 时间戳，检验请求时效性, 毫秒级
			obj.put("appId", "10011001");//appId
			obj.put("", "");//请求参数
			obj.put("timestamp", time);//时间戳
			String sign = MessageUtil.sign(obj,secret);//生成签名字符串（32位大写）
			querys.put("", "");
			querys.put("appId", "10011001");
			querys.put("timestamp",time);
			querys.put("sign", sign);
			String url = "https://do-scgw-sit.nio.com";
			String path = "/api/v2/acsData/acsPartInfo";
			HttpResponse response = doPost(url,path,null,querys,null);
			JSONObject jsonObject = JSONObject.parseObject(EntityUtils.toString(response.getEntity()));
			System.out.println(jsonObject);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}

package com.jy.controller;

import com.jy.ann.MethodMonitor;
import com.jy.ann.PageResult;
import com.jy.bean.common.Constant;
import com.jy.bean.po.FlBatchInfo;
import com.jy.bean.result.JsonResult;
import com.jy.service.FlBatchInfoService;
import com.jy.util.EmptyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.RolesAllowed;
import java.util.List;
import java.util.Map;

/**
 * 数据轨迹
 */
@RestController
@RolesAllowed(Constant.ROLE_QUERY)
@RequestMapping("/flBatchInfo")
public class FlBatchInfoController {

    @Autowired
    private FlBatchInfoService flBatchInfoService;

    @PageResult
    @MethodMonitor
    @RequestMapping(value="query", method = RequestMethod.GET)
    public JsonResult<List<FlBatchInfo>> listClient(@PageableDefault(size = 100, page = 0) Pageable page,
                                                    @RequestParam Map<String, Object> paramMap) throws Exception{
        List<FlBatchInfo> flBatchInfoList = flBatchInfoService.listFlBatchInfo(paramMap);
        JsonResult<List<FlBatchInfo>> jsonResult = new JsonResult<List<FlBatchInfo>>();
        jsonResult.setResult(flBatchInfoList);
        return jsonResult;
    }

    @MethodMonitor
    @RequestMapping(value="/detailBatch", method = RequestMethod.GET)
    public JsonResult<List<FlBatchInfo>> detailBatch(@RequestParam Map<String, Object> paramMap) throws Exception{
        List<FlBatchInfo> flBatchInfoList = flBatchInfoService.listFlBatchInfo(paramMap);
        JsonResult<List<FlBatchInfo>> jsonResult = new JsonResult<List<FlBatchInfo>>();
        jsonResult.setResult(flBatchInfoList);
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(method = RequestMethod.PUT)
    public JsonResult<FlBatchInfo> update(@RequestBody FlBatchInfo flBatchInfo) throws Exception {
        JsonResult<FlBatchInfo> jsonResult = new JsonResult<>();
        if(EmptyUtils.isEmpty(flBatchInfo.getStatus()) || EmptyUtils.isEmpty(flBatchInfo.getUnsuccessNum()) || EmptyUtils.isEmpty(flBatchInfo.getSuccessNum())
        || EmptyUtils.isEmpty(flBatchInfo.getMainBatchNo())){
            throw new Exception("参数错误，缺失参数");
        }
        flBatchInfoService.updateTraceStatus(flBatchInfo);
        jsonResult.setResult(flBatchInfo);
        return jsonResult;
    }

    @MethodMonitor
    @ResponseBody
    @RequestMapping("/refreshData")
    public JsonResult<FlBatchInfo> refreshData(@RequestBody FlBatchInfo flBatchInfo) throws Exception {
        JsonResult<FlBatchInfo> jsonResult = new JsonResult<>();
        if(EmptyUtils.isEmpty(flBatchInfo.getId())
                || EmptyUtils.isEmpty(flBatchInfo.getMainBatchNo())
                || EmptyUtils.isEmpty(flBatchInfo.getBatchNo())
                || EmptyUtils.isEmpty(flBatchInfo.getNodeName())
                || EmptyUtils.isEmpty(flBatchInfo.getClientCode())){
            throw new Exception("参数错误，缺失参数");
        }
        FlBatchInfo flBatchInfoEs = flBatchInfoService.getFlBatchInfo(flBatchInfo);
        flBatchInfoService.updateSelective(flBatchInfoEs);
        jsonResult.setResult(flBatchInfoService.getById(flBatchInfo.getId()));
        return jsonResult;
    }

}

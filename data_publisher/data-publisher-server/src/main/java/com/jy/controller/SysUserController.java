package com.jy.controller;

import com.jy.ann.MethodMonitor;
import com.jy.bean.common.Constant;
import com.jy.bean.po.SysUser;
import com.jy.bean.result.JsonResult;
import com.jy.service.SysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.RolesAllowed;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/5/14
 */
@RestController
@RolesAllowed(Constant.ROLE_QUERY)
@RequestMapping("/user")
public class SysUserController {

    @Autowired
    private SysUserService sysUserService;


    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping( value="query", method = RequestMethod.GET)
    public JsonResult<List<SysUser>> listUser(@RequestParam Map<String, Object> map) throws Exception{
        List<SysUser> sysUsers = sysUserService.listSortFieldSysUser(map);
        JsonResult<List<SysUser>> jsonResult = new JsonResult<List<SysUser>>();
        jsonResult.setResult(sysUsers);
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(method = RequestMethod.POST)
    public JsonResult<SysUser> save(@RequestBody SysUser sysUser) throws Exception {
        JsonResult<SysUser> jsonResult = new JsonResult<SysUser>();
        jsonResult.setResult(sysUserService.save(sysUser));
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(params="id", method = RequestMethod.DELETE)
    public JsonResult<SysUser> delete(String id) throws Exception {
        sysUserService.delete(id);
        JsonResult<SysUser> jsonResult = new JsonResult<SysUser>();
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(method = RequestMethod.PUT)
    public JsonResult<SysUser> update(@RequestBody SysUser sysUser) throws Exception {
        JsonResult<SysUser> jsonResult = new JsonResult<SysUser>();
        jsonResult.setResult(sysUserService.update(sysUser));
        return jsonResult;
    }
}

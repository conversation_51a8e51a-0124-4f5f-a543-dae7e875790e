package com.jy.controller;

import com.jy.ann.MethodMonitor;
import com.jy.bean.po.ClientTable;
import com.jy.bean.po.FieldOrgMp;
import com.jy.bean.result.JsonResult;
import com.jy.service.SysUserService;
import com.jy.util.SecurityContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;

import java.security.Principal;
/**
 * <AUTHOR>
 * @date 2018/4/23
 */
@Controller
@RequestMapping(value = "/page")
public class LoginController {

    @Autowired
    private SysUserService sysUserService;
    @RequestMapping(value = "/index")
    public String index(){
        return "index";
    }
    @RequestMapping(value = "/user")
    public String clientUser(){
        return "html/client/user";
    }
    @RequestMapping(value = "/client")
    public String client(){
        return "html/client/client";
    }
    @RequestMapping(value = "/clientTable")
    public String clientTable(){
        return "html/client/clientTable";
    }
    @RequestMapping(value = "/clientFilter")
    public String clientFilter(){
        return "html/client/clientFilter";
    }
    @RequestMapping(value = "/clientTableField")
    public String clientTableField(Model model, ClientTable clientTable){
        model.addAttribute("clientCode", clientTable.getClientCode());
        model.addAttribute("tableName", clientTable.getTableName());
        return "html/client/clientTableField";
    }
    @RequestMapping(value = "/clientUrlMp")
    public String clientUrlMp(){
        return "html/client/clientUrlMp";
    }
    @RequestMapping(value = "/sendData")
    public String sendData(Model model, ClientTable clientTable){
        model.addAttribute("clientCode", clientTable.getClientCode());
        return "html/client/sendData";
    }

    @RequestMapping(value = "/dataTrace", params={"batchNo","clientCode","status"})
    public String dataTrace(Model model, @ModelAttribute String batchNo, @ModelAttribute String clientCode, @ModelAttribute String status){
        model.addAttribute("batchNo", batchNo);
        model.addAttribute("clientCode",clientCode);
        model.addAttribute("status", status);
        return "html/client/dataTrace";
    }

    @RequestMapping(value = "/dashBoardDetail", params={"mainBatchNo"})
    public String dataTrace(Model model, @ModelAttribute String mainBatchNo){
        model.addAttribute("mainBatchNo", mainBatchNo);
        return "html/client/dashBoardDetail";
    }
    @RequestMapping(value = "/batchInfo")
    public String batchInfo(Model model){
        return "html/client/batchInfo";
    }

    @RequestMapping(value = "/mainBatchInfo")
    public String mainbatchInfo(Model model){
        return "html/client/mainbatchInfo";
    }

    @RequestMapping(value = "/dataDetail", params={"batchNo","clientCode", "status"})
    public String dataDetail(@ModelAttribute String batchNo, @ModelAttribute String clientCode, @ModelAttribute String status, Model model){
        model.addAttribute("batchNo", batchNo);
        model.addAttribute("clientCode",clientCode);
        model.addAttribute("status",status);
        return "html/client/dataDetail";
    }
    @RequestMapping(value = "/org")
    public String org(){
        return "html/org/org";
    }
    @RequestMapping(value = "/clientOrg")
    public String clientOrg(){
        return "html/org/clientOrg";
    }
    @RequestMapping(value = "/clientTableFieldOrg")
    public String clientTableFieldOrg(Model model, FieldOrgMp fieldOrgMp){
        model.addAttribute("clientCode", fieldOrgMp.getClientCode());
        model.addAttribute("baseTableName", fieldOrgMp.getBaseTableName());
        model.addAttribute("baseTableField", fieldOrgMp.getBaseTableField());
        return "html/org/clientTableFieldOrg";
    }

    @RequestMapping(value = "/dashBoard")
    public String dashBoard(Model model){
        return "html/client/dashBoard";
    }

    @RequestMapping(value = "/brandPart")
    public String brandPart(){
        return "html/client/brandPart";
    }

    @RequestMapping(value = "/brandPartHis", params={"brandCode", "supTableId"})
    public String brandPartHis(@ModelAttribute String brandCode, @ModelAttribute String supTableId, Model model){
        model.addAttribute("brandCode", brandCode);
        model.addAttribute("supTableId", supTableId);
        return "html/client/brandPartHis";
    }

    @RequestMapping(value = "/userLogin")
    public String getAuthenticationToken(Principal user) throws AuthenticationException {
        OAuth2Authentication authentication = (OAuth2Authentication)user;

        // Return the token
        JsonResult<OAuth2Authentication> jsonResult = new JsonResult<OAuth2Authentication>();
        jsonResult.setResult(authentication);
        return "html/index";
    }

    @MethodMonitor
    @RequestMapping("loginOut")
    public String loginOut(){

        String clientCode = SecurityContextUtils.getUserName().toUpperCase();
        try {
            sysUserService.removeToken(clientCode);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "login";
    }
}

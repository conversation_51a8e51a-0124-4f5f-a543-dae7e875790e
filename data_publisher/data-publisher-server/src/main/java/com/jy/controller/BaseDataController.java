package com.jy.controller;
import com.google.common.collect.Lists;
import java.util.Date;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jy.ann.MethodMonitor;
import com.jy.bean.common.ClientStatus;
import com.jy.bean.common.Constant;
import com.jy.bean.common.DataTraceMenu;
import com.jy.bean.dto.BaseDataDTOs;
import com.jy.bean.po.*;
import com.jy.bean.result.JsonResult;
import com.jy.bean.result.ResultStatus;
import com.jy.mq.RabbitCommon;
import com.jy.service.*;
import com.jy.util.*;
import com.jy.util.rabbitmq.DataTraceUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.security.RolesAllowed;
import java.io.File;
import java.util.*;

@RestController
@RolesAllowed(Constant.ROLE_QUERY)
@RequestMapping("/baseDatas")
public class BaseDataController {
    private static final Logger logger = LogManager.getLogger(BaseDataController.class);

    @Value("${srcData.filePath}")
    private String filePath;
    @Autowired
    RabbitTemplate rabbitTemplate;
    @Autowired
    private RabbitCommon rabbitCommon;
    @Autowired
    private DictService dictService;
    @Autowired
    private ClientService clientService;
    @Autowired
    private BatchDetailService batchDetailService;
    @Autowired
    private FlBatchInfoService flBatchInfoService;
    @Autowired
    private FlMainBatchInfoService flMainBatchInfoService;

    @RequestMapping(params={"receive"}, method = RequestMethod.POST)
    @MethodMonitor
    public JsonResult<String> receiveData(@RequestBody BaseDataDTOs baseDataDTOs) {
        logger.info("产品发布平台接收batchNo为:{}", baseDataDTOs.getBatchNo());
        JsonResult<String> jsonResult = new JsonResult<String>();
        String clientCode = SecurityContextUtils.getUserName().toUpperCase();
        String localFilePath = DateUtil.convertDateToString(new Date()) + File.separator + clientCode + File.separator + baseDataDTOs.getBatchNo() + ".json";
        //如果上游指定了顺序则以上游顺序为准
        Integer batchOrder = baseDataDTOs.getBatchOrder();
        //如果上游没有指定，则按照数据类型区分处理顺序
        if(EmptyUtils.isEmpty(baseDataDTOs.getBatchOrder())){
            Dict dict = dictService.getByTypeAndCode(clientCode, baseDataDTOs.getTableName());
            batchOrder = EmptyUtils.isEmpty(dict) ? 9999 : dict.getDictOrder();
        }

        int receiveNum = batchDetailService.getCountByClientCodeAndStatus(clientCode, ResultStatus.UNSENT.getStatus());
        Client srcClient = clientService.getOneByCode(clientCode);
        int count = srcClient.getReceiveLimit() > receiveNum ? srcClient.getReceiveLimit() - receiveNum : 0;
        if(count > 0){
            BatchDetail batchDetail = new BatchDetail(baseDataDTOs, clientCode, localFilePath, batchOrder);
            try {
                BatchDetail localBatchDetail = batchDetailService.getByBatchNo(baseDataDTOs.getBatchNo());
                if(EmptyUtils.isEmpty(localBatchDetail)){
                    this.trailMain(baseDataDTOs, clientCode, "publisher");
                    this.trail(baseDataDTOs, clientCode, "publisher");
                    FileUtils.writeFile(JSONObject.toJSONString(baseDataDTOs), filePath + localFilePath);
                    batchDetailService.save(batchDetail);
                    DataTraceUtils.sendTrace((JSONObject) JSON.toJSON(batchDetail), DataTraceMenu.PUBLISH_RECEIVE_DESC.getCode(), clientCode, ResultStatus.SUCCESS.getStatus(), ResultStatus.SUCCESS.getMessage(), 1);
                }
            } catch (Exception e) {
                String message = ToolUtils.getExceptionMsg(e);
                jsonResult.setStatus(ResultStatus.INTERNAL_SERVER_ERROR.getStatus());
                jsonResult.setMessage(message);
                DataTraceUtils.sendTrace((JSONObject) JSON.toJSON(batchDetail), DataTraceMenu.PUBLISH_RECEIVE_DESC.getCode(), clientCode, ResultStatus.INTERNAL_SERVER_ERROR.getStatus(), message, 1);
                logger.error("产品发布平台接收数据文件失败：文件名为:" + filePath + "{}, message: {}", batchDetail, message);
            }
        }else{
            jsonResult.setStatus(ResultStatus.INTERNAL_SERVER_ERROR.getStatus());
            jsonResult.setMessage("接收数据已达上限, 当前已接收"+receiveNum+"条数据，"+"上限为"+srcClient.getReceiveLimit()+"条数据");
        }


        return jsonResult;
    }

    /**
     * 为了解决MQ数据丢失，重新将数据放入MQ中推送
     * 适用于已有srcJson文件，在不修改批次号的情况下，重置状态与时间，达到重新推送的目的。
     * */
    @RequestMapping(params={"rePushMQ"}, method = RequestMethod.POST)
    @MethodMonitor
    public JsonResult<String> rePushMQ(@RequestBody List<BatchDetail> rePushList) throws Exception {
        //传入参数为mainBatchNo与batchNo 必传其一
        logger.info("重新放入MQ中推送 batchDetailList:{}", rePushList);
        JsonResult<String> jsonResult = new JsonResult<String>();
        Date startTime = new Date();
        //将rePushList拆为100个一组
        List<List<BatchDetail>> lists = Lists.partition(rePushList, 100);
        for (List<BatchDetail> list : lists) {
            List<BatchDetail> batchDetailList = batchDetailService.batchListByMainAndBatchNo(list);
            //查询MainBatchNo
            if (CollectionUtils.isNotEmpty(batchDetailList)) {
                for (BatchDetail batchDetail : batchDetailList) {
                    batchDetail.setStatus(ResultStatus.UNSENT.getStatus());
                    batchDetail.setUTime(new Date());
                }
                batchDetailService.updateBatch(batchDetailList);
                flMainBatchInfoService.resetBatch(batchDetailList, startTime);
                flBatchInfoService.resetBatch(batchDetailList, startTime);
            }
        }
        return jsonResult;
    }

    /**
     * 为了解决MQ数据丢失，重新将数据放入MQ中推送
     * 适用于已有srcJson文件，在不修改批次号的情况下，重置状态与时间，达到重新推送的目的。
     * */
    @RequestMapping(params={"reCreateMQConnection"}, method = RequestMethod.GET)
    @MethodMonitor
    public JsonResult<String> reCreateMQConnection() throws Exception {
        JsonResult<String> jsonResult = new JsonResult<String>();
        rabbitCommon.resetConnection();
        return jsonResult;
    }

    @RequestMapping(params={"receiveTransData"}, method = RequestMethod.POST)
    @MethodMonitor
    public JsonResult<String> receiveTransData(@RequestBody SendDetail sendDetail) {
        logger.info("产品发布平台(仅记录轨迹) 接收batchNo为:{}", sendDetail.getBatchNo());
        String clientCode = SecurityContextUtils.getUserName().toUpperCase();
        JsonResult<String> jsonResult = new JsonResult<String>();
        try {
                Date now = new Date();
                //仅记录轨迹不会插入batch_detail表，所以一直会进入trail方法
                FlBatchInfo flBatchInfo = new FlBatchInfo();
                flBatchInfo.setDataSource("transfer");
                flBatchInfo.setBatchNo(EmptyUtils.isNotEmpty(sendDetail.getBatchNo())?sendDetail.getBatchNo():sendDetail.getMainBatchNo()+"-*");
                flBatchInfo.setMainBatchNo(sendDetail.getMainBatchNo());
                flBatchInfo.setStatus(sendDetail.getStatus());
                flBatchInfo.setMessage(StringUtils.substring(sendDetail.getMessage(),0,200));
                flBatchInfo.setServiceName(sendDetail.getServiceName());
                flBatchInfo.setNodeName(sendDetail.getNodeName());
                flBatchInfo.setClientCode(clientCode);
                flBatchInfo.setStartTime(now);
                flBatchInfo.setEndTime(now);
                flBatchInfo.setSuccessNum(0);
                flBatchInfo.setFailNum(0);
                flBatchInfo.setProcessingNum(0);
                flBatchInfo.setUnsuccessNum(0);
                flBatchInfo.setDelFlag("0");
                flBatchInfo.setCBy("");
                flBatchInfo.setCTime(now);
                flBatchInfo.setUBy("");
                flBatchInfo.setUTime(now);
                flBatchInfoService.save(flBatchInfo);
        } catch (Exception e) {
            String message = ToolUtils.getExceptionMsg(e);
            jsonResult.setStatus(ResultStatus.INTERNAL_SERVER_ERROR.getStatus());
            jsonResult.setMessage(message);
            logger.error("产品发布平台(仅记录轨迹)接收数据失败：接收batchNo为:{}, message: {}", sendDetail.getBatchNo(), message);
        }
        return jsonResult;
    }

    @RequestMapping(params={"receivePriceNum"}, method = RequestMethod.GET)
    @MethodMonitor
    public JsonResult<Integer> getReceivePriceNum() throws Exception {
        JsonResult<Integer> jsonResult = new JsonResult<>();
        int receiveNum = batchDetailService.getCountByClientCodeAndStatus("SRC_LOCAL_PRICE", ResultStatus.UNSENT.getStatus());
        int count = Constant.SRC_LOCAL_PRICE_RECEIVE_LIMIT > receiveNum ? Constant.SRC_LOCAL_PRICE_RECEIVE_LIMIT - receiveNum : 0;
        jsonResult.setResult(count);
        return jsonResult;
    }

    @RequestMapping(params={"LocalPrice", "receiveTopLimit"}, method = RequestMethod.GET)
    @MethodMonitor
    public JsonResult<Map<String, Object>> getReceiveLocalPriceLimit() throws Exception {
        JsonResult<Map<String, Object>> jsonResult = new JsonResult<>();
        Map<String, Object> result = new HashMap<>();
        String srcClientName = "SRC_LOCAL_PRICE";
        int receiveNum = batchDetailService.getCountByClientCodeAndStatus(srcClientName, ResultStatus.UNSENT.getStatus());
        Client srcClient = clientService.getOneByCode(srcClientName);
        int count = srcClient.getReceiveLimit() > receiveNum ? srcClient.getReceiveLimit() - receiveNum : 0;
        result.put("topLimit", count);

        if(count > 0){
            List<Client> list = clientService.listClientByBaseTable("local_market_price");
            List<String> clients = new ArrayList<>();
            for(Client client : list){
                int num = rabbitCommon.getCount(client.getCode());
                if(num < client.getReceiveLimit() && !ClientStatus.CLIENT_OFFLINE.equals(client.getStatus())){
                    clients.add(client.getCode());
                }
            }
            result.put("clients", clients);
        }
        jsonResult.setResult(result);
        return jsonResult;
    }

    private void trail(BaseDataDTOs baseDataDTOs, String clientCode, String dataSource){
        JSONObject obj = new JSONObject();
        obj.put("mainBatchNo", baseDataDTOs.getMainBatchNo());
        obj.put("batchNo", baseDataDTOs.getBatchNo());
        obj.put("clientCode", clientCode);
        obj.put("tableName", baseDataDTOs.getTableName());
        obj.put("num", baseDataDTOs.getTotal());
        obj.put("dataSource", dataSource);
     //   System.out.println("放入mq数据--------------tmp-batch-queue-----------"  + obj.toJSONString());
        rabbitTemplate.convertAndSend("tmp-batch-queue", obj.toJSONString());
    }

    private void trailMain(BaseDataDTOs baseDataDTOs,String clientCode, String dataSource) throws Exception {
        Map<String, Object> queryMap = new HashMap();
        queryMap.put("mainBatchNo", baseDataDTOs.getMainBatchNo());
        List<FlMainBatchInfo> flMainBatchInfoList = flMainBatchInfoService.listFlMainBatchInfo(queryMap);
        if(CollectionUtils.isEmpty(flMainBatchInfoList)){
            //如果不存在，放入MQ中。
            JSONObject obj = new JSONObject();
            obj.put("mainBatchNo", baseDataDTOs.getMainBatchNo());
            obj.put("dataRemark", clientCode);
            obj.put("clientCode", clientCode);
            obj.put("dataSource", dataSource);
            rabbitTemplate.convertAndSend("tmp-mainBatch-queue", obj.toJSONString());
        }
    }
}

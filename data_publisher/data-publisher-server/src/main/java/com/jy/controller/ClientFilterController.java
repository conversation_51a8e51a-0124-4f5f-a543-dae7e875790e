package com.jy.controller;

import com.jy.ann.MethodMonitor;
import com.jy.bean.common.Constant;
import com.jy.bean.po.ClientFilter;
import com.jy.bean.result.JsonResult;
import com.jy.service.ClientFilterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.RolesAllowed;
import java.util.List;
import java.util.Map;

/**
 * 客户端表管理
 */
@RestController
@RolesAllowed(Constant.ROLE_QUERY)
@RequestMapping("/clientFilter")
public class ClientFilterController {

    @Autowired
    private ClientFilterService clientFilterService;

    //@RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(value="query", method = RequestMethod.GET)
    public JsonResult<List<ClientFilter>> listClientFilter(@RequestParam Map<String, Object> map) throws Exception{
        List<ClientFilter> clientFilters = clientFilterService.listClientFilter(map);
        JsonResult<List<ClientFilter>> jsonResult = new JsonResult<List<ClientFilter>>();
        jsonResult.setResult(clientFilters);
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(method = RequestMethod.POST)
    public JsonResult<ClientFilter> save(@RequestBody ClientFilter clientFilter) throws Exception {
        JsonResult<ClientFilter> jsonResult = new JsonResult<>();
        jsonResult.setResult(clientFilterService.save(clientFilter));
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(params="id", method = RequestMethod.DELETE)
    public JsonResult<ClientFilter> delete(String id) throws Exception {
        clientFilterService.delete(id);
        JsonResult<ClientFilter> jsonResult = new JsonResult<>();
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(method = RequestMethod.PUT)
    public JsonResult<ClientFilter> update(@RequestBody ClientFilter clientFilter) throws Exception {
        JsonResult<ClientFilter> jsonResult = new JsonResult<>();
        jsonResult.setResult(clientFilterService.update(clientFilter));
        return jsonResult;
    }
}

package com.jy.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jy.ann.MethodMonitor;
import com.jy.bean.common.ClientStatus;
import com.jy.bean.common.Constant;
import com.jy.bean.common.DataTraceMenu;
import com.jy.bean.dto.BaseDataDTO;
import com.jy.bean.po.Client;
import com.jy.bean.po.DataTrace;
import com.jy.bean.result.JsonResult;
import com.jy.compress.CompressFactory;
import com.jy.config.RabbitMQConstants;
import com.jy.mq.BaseDataSender;
import com.jy.mq.RabbitCommon;
import com.jy.service.ClientService;
import com.jy.service.DataTraceService;
import com.jy.service.RedisService;
import com.jy.util.EmptyUtils;
import com.jy.util.HttpUtils;
import com.jy.util.SecurityContextUtils;
import com.jy.util.StringUtils;
import com.jy.util.rabbitmq.DataTraceUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.RolesAllowed;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据
 * @Author: zy
 * @Date: Created in 2018/3/28
 */
@RestController
@RolesAllowed(Constant.ROLE_QUERY)
@RequestMapping("/data")
public class DataController {
    private static final Logger logger = LogManager.getLogger(DataController.class);

    private int batchToplimit = 100;

    @Autowired
    private CompressFactory compressFactory;
    @Autowired
    private RabbitCommon rabbitCommon;
    @Autowired
    private BaseDataSender baseDataSender;
    @Autowired
    private RedisService redisService;
    @Autowired
    private ClientService clientService;
    @Autowired
    private DataTraceService dataTraceService;

    @PostMapping("/push")
    public void push() {
     //   baseDataSender.sendUpdateTestData();
           baseDataSender.sendInsertTestData();
        //baseDataSender.sendDeleteTestData();
    }

    @PostMapping("/pushOne")
    public JsonResult<String> pushOne(@RequestBody BaseDataDTO baseDataDTO, String queueName) {
        rabbitCommon.sendQueue(queueName, JSONObject.toJSONString(baseDataDTO));
        JsonResult<String> jsonResult = new JsonResult<String>();
        return jsonResult;
    }

    @RequestMapping(params={"count"}, method = RequestMethod.GET)
    public JsonResult<String> count() throws Exception{
        String clientCode = SecurityContextUtils.getUserName().toUpperCase();
        int count = rabbitCommon.getCount(clientCode);
        JsonResult<String> jsonResult = new JsonResult<String>();
        jsonResult.setResult(count + "");
        return jsonResult;
    }

    @MethodMonitor
    @RequestMapping(params={"register"}, method = RequestMethod.GET)
    public JsonResult<String> register(HttpServletRequest request) throws Exception {
        //动态注册 客户端间隔一段时间注册 更新缓存信息
        String clientCode = SecurityContextUtils.getUserName().toUpperCase();
        Client client = new Client();
        client.setIp(HttpUtils.getIpAddress(request));
        client.setClient(clientCode, ClientStatus.CLIENT_UP);
        clientService.update(client);

        redisService.saveClient(clientCode);
        JsonResult<String> jsonResult = new JsonResult<String>();
        return jsonResult;
    }

    @RequestMapping(params={"process"}, method = RequestMethod.GET)
    @MethodMonitor
    public JsonResult<List<JSONObject>> process() throws Exception{
        String clientCode = SecurityContextUtils.getUserName().toUpperCase();
        logger.info(clientCode + "：客户端请求获取数据！");
        List<JSONObject> list = new ArrayList<>(batchToplimit);
        for(int i=0; i<batchToplimit; i++){
            String message = rabbitCommon.processQueue(clientCode);
            if(EmptyUtils.isNotEmpty(message)){
                JSONObject jsonObject = JSONObject.parseObject(message);
                //更新mongodb中状态
                list.add(jsonObject);
            } else {
                break;
            }
        }
      //  logger.info(clientCode + "：客户端请求获取数据！" + list);
        JsonResult<List<JSONObject>> jsonResult = new JsonResult<List<JSONObject>>();
        jsonResult.setResult(list);
        return jsonResult;
    }

    /**
     * @Description Process message and extract fields to send to HIS_PART_UPDATE queue
     * @return JsonResult with status of the operation
     * <AUTHOR>
     */
    @RequestMapping(params={"dataprocess"}, method = RequestMethod.GET)
    @MethodMonitor
    public JsonResult<Object> dataprocess() throws Exception {
        String clientCode = SecurityContextUtils.getUserName().toUpperCase();
        logger.info(clientCode + "：客户端请求处理数据！");

        List<JSONObject> updateList = new ArrayList<>();

        // Get message from queue
        String message = rabbitCommon.processQueue(clientCode);
        if (EmptyUtils.isNotEmpty(message)) {
            JSONObject jsonObject = JSONObject.parseObject(message);

        }

        JsonResult<Object> jsonResult = new JsonResult<>();
        jsonResult.setResult(updateList);
        return jsonResult;
    }

    /**
    * @Description  client客户端 通过此接口获取数据
    * @Parames batchLimit单个批次处理的数量 固定100
    * @return 需要更新的json数据
    * <AUTHOR>
    * @Date 2025/3/24
    */
    @RequestMapping(params={"dataProcess"}, method = RequestMethod.GET)
    @MethodMonitor
    public JsonResult<Object> dataProcess(String batchLimit) throws Exception{
        int limit = batchToplimit;// EmptyUtils.isEmpty(batchLimit) ? batchToplimit : Integer.parseInt(batchLimit);
        String clientCode = SecurityContextUtils.getUserName().toUpperCase();
        logger.info(clientCode + "：客户端请求获取数据！");
        List<JSONObject> list = new ArrayList<>(limit);
        List<JSONObject> updateList = new ArrayList<>();
        for(int i=0; i<limit; i++){
            String message = rabbitCommon.processQueue(clientCode);
            logger.debug("客户端请求获取数据：{}", message);
            if(EmptyUtils.isNotEmpty(message)){
                JSONObject jsonObject = JSONObject.parseObject(message);
                logger.debug("客户端请求获取数据 source：{}", jsonObject.getJSONObject("fields").getString("source"));
                if(("warehouse".equals(jsonObject.getJSONObject("fields").getString("source")))){
                    String batchNo = jsonObject.getString("batchNo");
                    JSONObject updateObj = new JSONObject();
                    updateObj.put("batchNo", batchNo);
                    updateObj.put("clientCode", clientCode);
                    updateObj.put("updateId", jsonObject.getJSONObject("fields").getString("update_id"));
                    updateObj.put("brandCode", jsonObject.getJSONObject("fields").getString("brand_code"));
                    updateObj.put("supTableId", jsonObject.getJSONObject("keys").getString("sup_table_id"));
                    updateList.add(updateObj);
                }
                list.add(jsonObject);
            } else {
                break;
            }
        }
      //  logger.info(clientCode + "：客户端请求获取数据！" + list);
        Object json = null;
        if(EmptyUtils.isNotEmpty(list)){
            String data = JSONObject.toJSONString(list);
            json = compressFactory.create(data).compress(data);
        }
        if(EmptyUtils.isNotEmpty(updateList)){
            rabbitCommon.sendQueueNoTrace(RabbitMQConstants.QUEUE_WAREHOUSE_HIS_PART_UPDATE, JSONObject.toJSONString(updateList));
        }

        JsonResult<Object> jsonResult = new JsonResult<>();
        jsonResult.setResult(json);
        return jsonResult;
    }

    @RequestMapping(params={"callback"}, method = RequestMethod.POST)
    @MethodMonitor
    public JsonResult<String> callback(@RequestBody JSONObject result) throws Exception {
        JsonResult<String> jsonResult = new JsonResult<String>();

        JSONArray array = result.getJSONArray("publishTrails");
        String clientCode = SecurityContextUtils.getUserName().toUpperCase();

        for(int i=0; i<array.size(); i++){
            JSONObject json = array.getJSONObject(i);
            if(EmptyUtils.isNotEmpty(json.get("batchNo"))){
               /* if(json.containsKey("isJudge")){
                    //暂时注释掉，解决es高并发查询下cpu告警问题
                    try{
                        dataTraceService.deleteDataTrace(json.getString("batchNo"), json.getString("id"), json.getInteger("sendTimes"), DataTraceMenu.CLIENT_DEAL_DESC.getName());
                    }catch (Exception e){
                        e.printStackTrace();
                    }
                }*/
                DataTraceUtils.sendTrace(StringUtils.getUUID(), json, DataTraceMenu.CLIENT_DEAL_DESC.getCode(), clientCode, json.getString("status"), json.getString("message"));

            }
        }
        return jsonResult;
    }
}

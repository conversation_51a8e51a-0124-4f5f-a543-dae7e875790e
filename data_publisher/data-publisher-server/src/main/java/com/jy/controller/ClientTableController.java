package com.jy.controller;

import com.jy.ann.MethodMonitor;
import com.jy.bean.common.Constant;
import com.jy.bean.po.ClientTable;
import com.jy.bean.po.ClientTableFieldMp;
import com.jy.bean.po.ClientTableList;
import com.jy.bean.result.JsonResult;
import com.jy.service.ClientTableFieldMpService;
import com.jy.service.ClientTableService;
import com.jy.util.EmptyUtils;
import com.jy.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.RolesAllowed;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 客户端表管理
 */
@RestController
@RolesAllowed(Constant.ROLE_QUERY)
@RequestMapping("/clientTable")
public class ClientTableController {

    @Autowired
    private ClientTableService clientTableService;
    @Autowired
    private ClientTableFieldMpService clientTableFieldMpService;

    //@RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(value="query", method = RequestMethod.GET)
    public JsonResult<List<ClientTable>> listClientTable(@RequestParam Map<String, Object> map) throws Exception{
        List<ClientTable> clientTables = clientTableService.listClientTable(map);
        JsonResult<List<ClientTable>> jsonResult = new JsonResult<List<ClientTable>>();
        jsonResult.setResult(clientTables);
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(value="baseTableName", method = RequestMethod.GET)
    public JsonResult<List<ClientTable>> listBybaseTableName() throws Exception{
        List<ClientTable> clientTables = clientTableService.listBybaseTableName();
        JsonResult<List<ClientTable>> jsonResult = new JsonResult<List<ClientTable>>();
        jsonResult.setResult(clientTables);
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(value="/batch", method = RequestMethod.POST)
    public JsonResult<List<ClientTable>> saveList(@RequestBody ClientTableList clientTableList) throws Exception{
        List<ClientTable> clientTables = clientTableList.getClientTables();
        for(ClientTable clientTable : clientTables){
            clientTable.setId(StringUtils.getUUID());
            clientTable.setClientCode(clientTableList.getClientCode());
        }
        JsonResult<List<ClientTable>> jsonResult = new JsonResult<List<ClientTable>>();
        jsonResult.setResult(clientTableService.saveBatch(clientTables));
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(method = RequestMethod.POST)
    public JsonResult<ClientTable> save(@RequestBody ClientTable clientTable) throws Exception {
        JsonResult<ClientTable> jsonResult = new JsonResult<ClientTable>();
        jsonResult.setResult(clientTableService.save(clientTable));
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(params="id", method = RequestMethod.DELETE)
    public JsonResult<ClientTable> delete(String id) throws Exception {
        clientTableService.delete(id);
        JsonResult<ClientTable> jsonResult = new JsonResult<ClientTable>();
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(method = RequestMethod.PUT)
    public JsonResult<ClientTable> update(@RequestBody ClientTable clientTable) throws Exception {
        JsonResult<ClientTable> jsonResult = new JsonResult<ClientTable>();
        jsonResult.setResult(clientTableService.update(clientTable));
        return jsonResult;
    }


    @RolesAllowed(Constant.ROLE_QUERY)
    @MethodMonitor
    @RequestMapping(params={"clientCode"}, method = RequestMethod.GET)
    public JsonResult<List<ClientTable>> listClientTable(String clientCode, String detail) throws Exception{
        List<ClientTable> clientTables = clientTableService.listByClientCode(clientCode);
        if(EmptyUtils.isNotEmpty(detail)){
            List<ClientTableFieldMp> clientTableFieldMps = clientTableFieldMpService.listByClientCode(clientCode);
            clientTables = this.filterTableFieldMp(clientTables, clientTableFieldMps);
        }

        JsonResult<List<ClientTable>> jsonResult = new JsonResult<List<ClientTable>>();
        jsonResult.setResult(clientTables);
        return jsonResult;
    }

    private List<ClientTable> filterTableFieldMp(List<ClientTable> clientTables, List<ClientTableFieldMp> clientTableFieldMps){
        List<ClientTable> result = new ArrayList<>();
        Map<String, List<ClientTableFieldMp>> map = clientTableFieldMps.stream().collect(Collectors.groupingBy(ClientTableFieldMp::getTableName));
        for(ClientTable clientTable : clientTables){
            if(map.containsKey(clientTable.getTableName())){
                clientTable.setClientTableFieldMps(map.get(clientTable.getTableName()));
                result.add(clientTable);
            }
        }
        return result;
    }
}

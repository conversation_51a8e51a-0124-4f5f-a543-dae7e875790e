package com.jy.controller;

import com.jy.ann.MethodMonitor;
import com.jy.bean.common.ClientStatus;
import com.jy.bean.common.Constant;
import com.jy.bean.po.Client;
import com.jy.bean.po.ClientUrlMp;
import com.jy.bean.result.JsonResult;
import com.jy.service.ClientService;
import com.jy.service.ClientUrlMpService;
import com.jy.util.EmptyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.RolesAllowed;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/5/23
 */
@RestController
@RolesAllowed(Constant.ROLE_QUERY)
@RequestMapping("/clientUrlMp")
public class ClientUrlMpController {

    @Autowired
    private ClientService clientService;
    @Autowired
    private ClientUrlMpService clientUrlMpService;

    @MethodMonitor
    @RequestMapping(params={"baseClientCode", "clientCode"}, method = RequestMethod.GET)
    public JsonResult<List<ClientUrlMp>> listCompareClientByBaseClientCode(String baseClientCode, String clientCode) throws Exception{
        List<ClientUrlMp> clientUrlMps = clientUrlMpService.listByBaseClientCode(baseClientCode);
        clientUrlMps = this.filterClientUrl(clientUrlMps);
        if(EmptyUtils.isNotEmpty(clientCode)){
            clientUrlMps = clientUrlMps.stream().filter(clientUrlMp->clientCode.equals(clientUrlMp.getClientCode())).collect(Collectors.toList());
        }
        JsonResult<List<ClientUrlMp>> jsonResult = new JsonResult<>();
        jsonResult.setResult(clientUrlMps);
        return jsonResult;
    }

    @MethodMonitor
    @RequestMapping(params={"clientCode"}, method = RequestMethod.GET)
    public JsonResult<List<ClientUrlMp>> listByBaseClientCode(String clientCode) throws Exception{
        List<ClientUrlMp> clientUrlMps = clientUrlMpService.listByClientCode(clientCode);
        JsonResult<List<ClientUrlMp>> jsonResult = new JsonResult<>();
        jsonResult.setResult(clientUrlMps);
        return jsonResult;
    }


    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(value="query", method = RequestMethod.GET)
    public JsonResult<List<ClientUrlMp>> listClientUrlMp(@RequestParam Map<String, Object> map) throws Exception{
        List<ClientUrlMp> clientUrlMps = clientUrlMpService.listClientUrlMp(map);
        JsonResult<List<ClientUrlMp>> jsonResult = new JsonResult<>();
        jsonResult.setResult(clientUrlMps);
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(method = RequestMethod.POST)
    public JsonResult<ClientUrlMp> save(@RequestBody ClientUrlMp clientUrlMp) throws Exception {
        JsonResult<ClientUrlMp> jsonResult = new JsonResult<>();
        jsonResult.setResult(clientUrlMpService.save(clientUrlMp));
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(params="id", method = RequestMethod.DELETE)
    public JsonResult<ClientUrlMp> delete(String id) throws Exception {
        clientUrlMpService.delete(id);
        JsonResult<ClientUrlMp> jsonResult = new JsonResult<>();
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(method = RequestMethod.PUT)
    public JsonResult<ClientUrlMp> update(@RequestBody ClientUrlMp clientUrlMp) throws Exception {
        JsonResult<ClientUrlMp> jsonResult = new JsonResult<>();
        jsonResult.setResult(clientUrlMpService.update(clientUrlMp));
        return jsonResult;
    }


    private List<ClientUrlMp> filterClientUrl(List<ClientUrlMp> clientUrlMps){
        List<ClientUrlMp> result = new ArrayList<>();
        for(ClientUrlMp clientUrlMp : clientUrlMps){
            Client client = clientService.getOneByCode(clientUrlMp.getClientCode());
            if (ClientStatus.CLIENT_UP.equals(client.getStatus())) {
                result.add(clientUrlMp);
            }
        }
        return result;
    }
}

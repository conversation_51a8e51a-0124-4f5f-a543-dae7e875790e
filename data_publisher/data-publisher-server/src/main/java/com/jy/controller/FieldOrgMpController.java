package com.jy.controller;

import com.jy.ann.MethodMonitor;
import com.jy.bean.common.Constant;
import com.jy.bean.po.FieldOrgMp;
import com.jy.bean.po.FieldOrgMpList;
import com.jy.bean.result.JsonResult;
import com.jy.service.FieldOrgMpService;
import com.jy.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.RolesAllowed;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/5/7
 */
@RestController
@RolesAllowed(Constant.ROLE_QUERY)
@RequestMapping("/fieldOrgMp")
public class FieldOrgMpController {

    @Autowired
    private FieldOrgMpService fieldOrgMpService;

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(value="query", method = RequestMethod.GET)
    public JsonResult<List<FieldOrgMp>> listClientTable(@RequestParam Map<String, Object> map) throws Exception{
        List<FieldOrgMp> fieldOrgMps = fieldOrgMpService.listClientFieldOrgMp(map);
        JsonResult<List<FieldOrgMp>> jsonResult = new JsonResult<>();
        jsonResult.setResult(fieldOrgMps);
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(value="/batch", method = RequestMethod.POST)
    public JsonResult<List<FieldOrgMp>> saveList(@RequestBody FieldOrgMpList fieldOrgMpList) throws Exception{
        List<FieldOrgMp> fieldOrgMps = fieldOrgMpList.getFieldOrgMps();
        for(FieldOrgMp clientTableFieldMp : fieldOrgMps){
            clientTableFieldMp.setId(StringUtils.getUUID());
            clientTableFieldMp.setClientCode(fieldOrgMpList.getClientCode());
            clientTableFieldMp.setBaseTableName(fieldOrgMpList.getTableName());
        }
        JsonResult<List<FieldOrgMp>> jsonResult = new JsonResult<List<FieldOrgMp>>();
        jsonResult.setResult(fieldOrgMpService.saveBatch(fieldOrgMps));
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(method = RequestMethod.POST)
    public JsonResult<FieldOrgMp> save(@RequestBody FieldOrgMp fieldOrgMp) throws Exception {
        JsonResult<FieldOrgMp> jsonResult = new JsonResult<FieldOrgMp>();
        jsonResult.setResult(fieldOrgMpService.save(fieldOrgMp));
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(params="id", method = RequestMethod.DELETE)
    public JsonResult<FieldOrgMp> delete(String id) throws Exception {
        fieldOrgMpService.delete(id);
        JsonResult<FieldOrgMp> jsonResult = new JsonResult<FieldOrgMp>();
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(method = RequestMethod.PUT)
    public JsonResult<FieldOrgMp> update(@RequestBody FieldOrgMp fieldOrgMp) throws Exception {
        JsonResult<FieldOrgMp> jsonResult = new JsonResult<FieldOrgMp>();
        jsonResult.setResult(fieldOrgMpService.update(fieldOrgMp));
        return jsonResult;
    }
}

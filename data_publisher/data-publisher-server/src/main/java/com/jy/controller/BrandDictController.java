package com.jy.controller;

import com.jy.ann.MethodMonitor;
import com.jy.ann.PageResult;
import com.jy.bean.common.Constant;
import com.jy.bean.po.BrandDict;
import com.jy.bean.result.JsonResult;
import com.jy.service.BrandDictService;
import com.jy.util.EmptyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.RolesAllowed;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 品牌字典Controller
 */
@RestController
@RolesAllowed(Constant.ROLE_QUERY)
@RequestMapping("/brandDict")
public class BrandDictController {

    @Autowired
    private BrandDictService brandDictService;

    /**
     * 分页查询品牌字典列表，支持搜索
     * @param page 分页参数
     * @param paramMap 查询参数
     * @return 品牌字典列表
     */
    @PageResult
    @MethodMonitor
    @RequestMapping(value = "list", method = RequestMethod.GET)
    public JsonResult<List<BrandDict>> listBrandDict(@PageableDefault(size = 20, page = 0) Pageable page,
                                                     @RequestParam Map<String, Object> paramMap) {
        JsonResult<List<BrandDict>> jsonResult = new JsonResult<>();
        
        try {
            // 设置分页参数
            paramMap.put("page", page.getPageNumber());
            paramMap.put("size", page.getPageSize());
            paramMap.put("offset", page.getPageNumber() * page.getPageSize());
            
            List<BrandDict> brandDictList = brandDictService.listBrandDictWithPage(paramMap);
            jsonResult.setResult(brandDictList);
            jsonResult.setStatus("200");
            jsonResult.setMessage("查询成功");
        } catch (Exception e) {
            jsonResult.setStatus("500");
            jsonResult.setMessage("查询失败：" + e.getMessage());
        }
        
        return jsonResult;
    }

    /**
     * 根据关键词搜索品牌字典
     * @param keyword 搜索关键词（可选）
     * @param page 页码（默认0）
     * @param size 每页数量（默认20）
     * @return 品牌字典列表
     */
    @MethodMonitor
    @RequestMapping(value = "search", method = RequestMethod.GET)
    public JsonResult<List<BrandDict>> searchBrandDict(@RequestParam(required = false) String keyword,
                                                       @RequestParam(defaultValue = "0") int page,
                                                       @RequestParam(defaultValue = "20") int size) {
        JsonResult<List<BrandDict>> jsonResult = new JsonResult<>();
        
        try {
            List<BrandDict> brandDictList = brandDictService.searchBrandDict(keyword, page, size);
            jsonResult.setResult(brandDictList);
            jsonResult.setStatus("200");
            jsonResult.setMessage("搜索成功");
        } catch (Exception e) {
            jsonResult.setStatus("500");
            jsonResult.setMessage("搜索失败：" + e.getMessage());
        }
        
        return jsonResult;
    }

    /**
     * 根据品牌编码查询品牌字典
     * @param brandCode 品牌编码
     * @return 品牌字典
     */
    @MethodMonitor
    @RequestMapping(value = "getByCode", method = RequestMethod.GET)
    public JsonResult<BrandDict> getBrandDictByCode(@RequestParam String brandCode) {
        JsonResult<BrandDict> jsonResult = new JsonResult<>();
        
        if (EmptyUtils.isEmpty(brandCode)) {
            jsonResult.setStatus("400");
            jsonResult.setMessage("品牌编码不能为空");
            return jsonResult;
        }
        
        try {
            BrandDict brandDict = brandDictService.getBrandDictByCode(brandCode);
            jsonResult.setResult(brandDict);
            jsonResult.setStatus("200");
            jsonResult.setMessage("查询成功");
        } catch (Exception e) {
            jsonResult.setStatus("500");
            jsonResult.setMessage("查询失败：" + e.getMessage());
        }
        
        return jsonResult;
    }
}

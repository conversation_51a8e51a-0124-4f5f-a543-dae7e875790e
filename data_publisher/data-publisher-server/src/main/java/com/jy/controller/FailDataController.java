package com.jy.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jy.ann.MethodMonitor;
import com.jy.bean.common.Constant;
import com.jy.bean.dto.BaseDataDTO;
import com.jy.bean.dto.BaseDataDTOs;
import com.jy.bean.po.DataTrace;
import com.jy.bean.result.JsonResult;
import com.jy.bean.result.ResultStatus;
import com.jy.service.DataFitService;
import com.jy.service.DataTraceService;
import com.jy.task.ErrorDataRemindTask;
import com.jy.task.service.ErrorDataRemindTaskService;
import com.jy.util.DateUtil;
import com.jy.util.EmptyUtils;
import com.jy.util.ListUtils;
import com.jy.util.ToolUtils;
import org.apache.commons.io.LineIterator;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.security.RolesAllowed;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 * @Author: zy
 * @Date: Created in 2018/3/28
 */
@RestController
@RolesAllowed(Constant.ROLE_QUERY)
@RequestMapping("/failData")
public class FailDataController {
    private static final Logger logger = LogManager.getLogger(FailDataController.class);

    @Value("${clientData.filePath}")
    private String filePath;
    @Autowired
    private DataFitService dataFitService;
    @Autowired
    private ErrorDataRemindTaskService errorDataRemindTask;
    @Autowired
    private DataTraceService dataTraceService;

    @RequestMapping(params={"batchNo","clientCode", "status"}, value = "/rePush", method = RequestMethod.POST)
    public JsonResult<String> rePush(String batchNo, String clientCode, String status) throws Exception {
        String clientFilePath = filePath + DateUtil.getDateByBatchNo(batchNo) + File.separator + clientCode + File.separator + batchNo + ".json";
        List<DataTrace> dataTraces = null;
        if(ResultStatus.SUCCESS.getStatus().equals(status)){
            dataTraces = dataTraceService.listDataTracesByClientCodeAndBatchNoAndStatus(clientCode, batchNo, status);
        } else {
            dataTraces = dataTraceService.listDataTracesByClientCodeAndBatchNoAndStatus(clientCode, batchNo, ResultStatus.PROCESSING.getStatus());
            List<DataTrace> rightList = dataTraceService.listDataTracesByClientCodeAndBatchNoAndStatus(clientCode, batchNo, ResultStatus.SUCCESS.getStatus());
            dataTraces.removeAll(rightList);
        }
        List<BaseDataDTO>  baseDataDTOs = this.filterFile(dataTraces, clientFilePath);
        if(EmptyUtils.isNotEmpty(baseDataDTOs)){
            dataFitService.resend(baseDataDTOs, clientCode);
        }
        JsonResult<String> jsonResult = new JsonResult<String>();
        return jsonResult;
    }
    @RequestMapping(params={"batchNo","clientCode", "status"}, method = RequestMethod.GET)
    public JsonResult<List<BaseDataDTO>> listByBatchNoAndQueueNameAndStatus(@PageableDefault(size = 20) Pageable pageable, String batchNo, String clientCode, String status) throws Exception {
        String clientFilePath = filePath + DateUtil.getDateByBatchNo(batchNo) + File.separator + clientCode + File.separator + batchNo + ".json";
        List<DataTrace> dataTraces = null;
        if(ResultStatus.SUCCESS.getStatus().equals(status)){
            dataTraces = dataTraceService.listDataTracesByClientCodeAndBatchNoAndStatus(clientCode, batchNo, status);
        } else {
            List<DataTrace> unsuccessList = dataTraceService.listDataTracesByClientCodeAndBatchNoAndStatus(clientCode, batchNo, ResultStatus.PROCESSING.getStatus());
            unsuccessList = ListUtils.removeDuplicate(unsuccessList);
            List<DataTrace> rightList = dataTraceService.listDataTracesByClientCodeAndBatchNoAndStatus(clientCode, batchNo, ResultStatus.SUCCESS.getStatus());
            unsuccessList.removeAll(rightList);
            dataTraces = dataTraceService.listDataTracesByClientCodeAndBatchNoAndStatus(clientCode, batchNo, ResultStatus.INTERNAL_SERVER_ERROR.getStatus());
            if(dataTraces.size() > 5){
                dataTraces = dataTraces.subList(0, 5);
            }
            dataTraces.addAll(unsuccessList);
        }

        if(dataTraces.size() > pageable.getPageSize()){
            dataTraces = dataTraces.subList(0, pageable.getPageSize());
        }
        List<BaseDataDTO>  baseDataDTOs = this.filterFile(dataTraces, clientFilePath);
        JsonResult<List<BaseDataDTO>> jsonResult = new JsonResult<>();
        jsonResult.setResult(baseDataDTOs);
        return jsonResult;
    }

    @MethodMonitor
    @RequestMapping(params="rePushBatch",method = RequestMethod.POST)
    public JsonResult againSend(@RequestBody BaseDataDTOs baseDataDTOs){
        JsonResult jsonResult = new JsonResult();
        for (BaseDataDTO baseDataDTO : baseDataDTOs.getData()){
            try{
                this.rePush(baseDataDTO.getBatchNo(), baseDataDTO.getClientCode(), baseDataDTO.getStatus());
            }catch (Exception e){
                logger.error("产品发布平台重新推送批次失败，批次号： {}, message: {}", baseDataDTO, ToolUtils.getExceptionMsg(e));
            }
        }
        return jsonResult;
    }

    @RequestMapping(params={"errorDataRemind"}, method = RequestMethod.GET)
    public JsonResult<String> errorDataRemind() throws Exception {
        long endTime = System.currentTimeMillis();
        long startTime = endTime - 60 * 60 * 1000 * 24;
        JsonResult<String> jsonResult = new JsonResult<>();
        errorDataRemindTask.errorDataRemind(new Date(startTime), new Date(endTime));

        jsonResult.setResult("成功");
        return jsonResult;
    }

    public List<BaseDataDTO> filterFile(List<DataTrace> dataTraces, String filePath) throws Exception {
        List<BaseDataDTO> result = new ArrayList<>();
        Map<String, DataTrace> map = dataTraces.stream().collect(Collectors.toMap(DataTrace::getId, dataTrace->dataTrace,(k1,k2)->k1));
        File file = new File(filePath);
        BufferedReader br = new BufferedReader(new FileReader(file));
        LineIterator iterator = new LineIterator(br);
        while(iterator.hasNext()) {
            String line = iterator.nextLine();
            BaseDataDTO baseDataDTO = JSON.parseObject(line, BaseDataDTO.class);
            if(map.containsKey(baseDataDTO.getId())){
                DataTrace dataTrace = map.get(baseDataDTO.getId());
                baseDataDTO.setSendTimes(dataTrace.getSendTimes());
                baseDataDTO.setStatus(dataTrace.getStatus());
                baseDataDTO.setMessage(dataTrace.getMessage());
                result.add(baseDataDTO);
            }
            if(dataTraces.size() == result.size()){
                break;
            }
        }
        iterator.close();
        br.close();
        return  result;
    }
}

package com.jy.controller;

import com.jy.ann.MethodMonitor;
import com.jy.ann.PageResult;
import com.jy.bean.common.Constant;
import com.jy.bean.dto.DashBordDailyDTO;
import com.jy.bean.po.FlMainBatchInfo;
import com.jy.bean.result.JsonResult;
import com.jy.service.FlMainBatchInfoService;
import com.jy.util.EmptyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.RolesAllowed;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 数据轨迹
 */
@RestController
@RolesAllowed(Constant.ROLE_QUERY)
@RequestMapping("/flMainBatchInfo")
public class FlMainBatchInfoController {

    @Autowired
    private FlMainBatchInfoService flMainBatchInfoService;

    @PageResult
    @MethodMonitor
    @RequestMapping(value="query", method = RequestMethod.GET)
    public JsonResult<List<FlMainBatchInfo>> listClient(@PageableDefault(size = 100, page = 0) Pageable page,
                                                        @RequestParam Map<String, Object> paramMap) throws Exception{
        List<FlMainBatchInfo> flMainBatchInfoList = flMainBatchInfoService.listFlMainBatchInfo(paramMap);
        JsonResult<List<FlMainBatchInfo>> jsonResult = new JsonResult<>();
        jsonResult.setResult(flMainBatchInfoList);
        return jsonResult;
    }

    @MethodMonitor
    @RequestMapping("/dailyStats")
    public JsonResult<DashBordDailyDTO> dailyStats() throws Exception {
        JsonResult<DashBordDailyDTO> jsonResult = new JsonResult<>();
        DashBordDailyDTO dto = flMainBatchInfoService.getDailyStats();
        jsonResult.setResult(dto);
        return jsonResult;
    }

    @PageResult
    @MethodMonitor
    @RequestMapping("/daily")
    public JsonResult<List<FlMainBatchInfo>> daily(@PageableDefault(size = 20, page = 0) Pageable page,
                                                   @RequestParam(required = false)String mainBatchNo,
                                                   @RequestParam(required = false)String status,
                                                   @RequestParam(required = false)String clientCode) throws Exception {
        JsonResult<List<FlMainBatchInfo>> jsonResult = new JsonResult<>();
        List<FlMainBatchInfo> mainBatchList = flMainBatchInfoService.getDailyData(page, mainBatchNo, status, clientCode);
        jsonResult.setResult(mainBatchList);
        return jsonResult;
    }

    @PageResult
    @MethodMonitor
    @RequestMapping("/dailyOptimized")
    public JsonResult<List<FlMainBatchInfo>> dailyOptimized(@PageableDefault(size = 20, page = 0) Pageable page,
                                                            @RequestParam(required = false)String mainBatchNo,
                                                            @RequestParam(required = false)String status,
                                                            @RequestParam(required = false)String clientCode) throws Exception {
        JsonResult<List<FlMainBatchInfo>> jsonResult = new JsonResult<>();
        List<FlMainBatchInfo> mainBatchList = ((FlMainBatchInfoServiceImpl) flMainBatchInfoService).getDailyDataOptimized(page, mainBatchNo, status, clientCode);
        jsonResult.setResult(mainBatchList);
        return jsonResult;
    }

    @MethodMonitor
    @RequestMapping("/transferOrder")
    public JsonResult<Integer> transferOrder(@RequestParam String mainBatchNo) throws Exception {
        JsonResult<Integer> jsonResult = new JsonResult<>();
        Integer transferOrder = flMainBatchInfoService.transferOrder(mainBatchNo);
        jsonResult.setResult(transferOrder);
        return jsonResult;
    }

    @MethodMonitor
    @RequestMapping("/publishOrder")
    public JsonResult<Integer> publishOrder(@RequestParam String mainBatchNo) throws Exception {
        JsonResult<Integer> jsonResult = new JsonResult<>();
        Integer transferOrder = flMainBatchInfoService.publishOrder(mainBatchNo);
        jsonResult.setResult(transferOrder);
        return jsonResult;
    }

    @RequestMapping(value = "/updateTransferOrder", method = RequestMethod.POST)
    @MethodMonitor
    public JsonResult<String> updateTransferOrder(@RequestBody DashBordDailyDTO dto) throws Exception {
        if(EmptyUtils.isEmpty(dto.getMainBatchNo())){
            throw new Exception("主批次号不能为空");
        }
        JsonResult<String> jsonResult = new JsonResult<String>();
        flMainBatchInfoService.updateTransferOrder(dto.getMainBatchNo(), dto.getTransferOrder());
        return jsonResult;
    }

    @RequestMapping(value = "/updatePublishOrder", method = RequestMethod.POST)
    @MethodMonitor
    public JsonResult<String> updatePublishOrder(@RequestBody DashBordDailyDTO dto) throws Exception {
        if(EmptyUtils.isEmpty(dto.getMainBatchNo())){
            throw new Exception("主批次号不能为空");
        }
        JsonResult<String> jsonResult = new JsonResult<String>();
        flMainBatchInfoService.updatePublishOrder(dto.getMainBatchNo(), dto.getPublishOrder());
        return jsonResult;
    }




    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(method = RequestMethod.PUT)
    public JsonResult<FlMainBatchInfo> update(@RequestBody FlMainBatchInfo flMainBatchInfo) throws Exception {
        JsonResult<FlMainBatchInfo> jsonResult = new JsonResult<>();
        flMainBatchInfo.setEndTime(new Date());
        flMainBatchInfoService.update(flMainBatchInfo);
        jsonResult.setResult(flMainBatchInfo);
        return jsonResult;
    }
}

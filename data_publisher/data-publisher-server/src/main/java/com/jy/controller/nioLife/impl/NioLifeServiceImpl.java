package com.jy.controller.nioLife.impl;

import com.alibaba.fastjson.JSONObject;
import com.jy.controller.nioLife.NioLifeService;
import com.jy.util.MessageUtil;
import com.jy.util.RequestUtil;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: ay
 * @Date: Created in 2018/4/16
 */
@Service
@CacheConfig(cacheManager = "ehCacheCacheManager", cacheNames = "nioLife")
public class NioLifeServiceImpl implements NioLifeService {

    @Value("${thePath.nioLifeAppId}")
    private String nioLifeAppId;
    @Value("${thePath.nioLifeSecret}")
    private String nioLifeSecret;

    @Override
    public String sendJsonNioLife(String url,String key,String value) {
        Map<String, String> querys = new HashMap<String, String>();
        Map<String, Object> obj = new HashMap<String, Object>();
        String content = "";
        try {
                String time = String.valueOf(System.currentTimeMillis()).toString();
                //Unix时间戳，检验请求时效性, 毫秒级
                obj.put("appId", nioLifeAppId);//appId
                obj.put(key, value);//请求参数
                obj.put("timestamp", time);//时间戳
                String sign = MessageUtil.sign(obj,nioLifeSecret);//生成签名字符串（32位大写）
                querys.put(key, value);
                querys.put("appId", nioLifeAppId);
                querys.put("timestamp",time);
                querys.put("sign", sign);
                System.out.println("请求地址："+url);
                System.out.println("请求地址："+querys.toString());
                HttpResponse response = RequestUtil.doPost(url,null,null,querys,"");
                JSONObject jsonObject = JSONObject.parseObject(EntityUtils.toString(response.getEntity()));
//                System.out.println("post請求返回:"+jsonObject);
                //处理接收到的xml内容
                content = jsonObject.toString();
        } catch (Exception e) {
            e.printStackTrace();
            content = e.getMessage();
        }
        return content;
    }




}

package com.jy.controller;

import com.jy.ann.MethodMonitor;
import com.jy.bean.common.Constant;
import com.jy.bean.po.BatchDetail;
import com.jy.bean.result.JsonResult;
import com.jy.service.BatchDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.security.RolesAllowed;

/**
 * @Author: caolt
 * @Description:
 * @Version:
 * @Date: Created in  2020/07/01
 */
@RestController
@RolesAllowed(Constant.ROLE_QUERY)
@RequestMapping("/batchDetails")
public class BatchDetailController {

    @Autowired
    private BatchDetailService batchDetailService;

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(method = RequestMethod.POST)
    public JsonResult<BatchDetail> save(@RequestBody BatchDetail batchDetail) throws Exception {
        JsonResult<BatchDetail> jsonResult = new JsonResult<>();
        jsonResult.setResult(batchDetailService.save(batchDetail));
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(params="id", method = RequestMethod.DELETE)
    public JsonResult<BatchDetail> delete(String id) throws Exception {
        batchDetailService.delete(id);
        JsonResult<BatchDetail> jsonResult = new JsonResult<>();
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(method = RequestMethod.PUT)
    public JsonResult<BatchDetail> update(@RequestBody BatchDetail batchDetail) throws Exception {
        JsonResult<BatchDetail> jsonResult = new JsonResult<>();
        jsonResult.setResult(batchDetailService.update(batchDetail));
        return jsonResult;
    }
}

package com.jy.controller;

import com.jy.ann.MethodMonitor;
import com.jy.bean.result.JsonResult;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 *  日志-（彩蛋）
 * Created by anxing on 2018/7/5.
 */
@RestController
@RequestMapping("/matchCode")
public class DataLogController {

    private static final Logger logger = LogManager.getLogger(DataLogController.class);


    @MethodMonitor
    @RequestMapping(params={"getMatchCode"}, method = RequestMethod.POST)
    public JsonResult getMatchCode(@RequestBody String egg){
        JsonResult<String> jsonResult = new JsonResult<String>();
     /*   JSONObject json = new JSONObject();
        String code = "";
        try {
            if(egg.getCompanyCode()!=null){
                code = dictService.getMatchCode(egg.getCompanyCode());
                jsonResult.setResult(code);
            }
        } catch (Exception e) {
            jsonResult.setStatus(ResultStatus.INTERNAL_SERVER_ERROR.getStatus());
            jsonResult.setMessage(e.getMessage());
            logger.error("产品端查询失败:" + e.getMessage() + ":数据为:" + JSONObject.toJSONString(egg.getCompanyCode()));
        }*/
        return jsonResult;
    }

    /**
     * 转发客户端日志-基础数据平台
     * @param request
     * @return
     */
    @MethodMonitor
    @RequestMapping(params={"getMatchCodeLog"}, method = RequestMethod.POST)
    public synchronized JsonResult getMatchCodeLog(HttpServletRequest request){
        JsonResult<String> jsonResult = new JsonResult<String>();
       /* String code = "";
        JSONObject json = null;
        try {
                String sendJson = ReceiveDataUtils.getSendString(request);
                json = JSONObject.parseObject(sendJson);
                String resultType = json.getString("RESULT_TYPE");
                if("1".equals(resultType)) {//加工-基础
                    String interfaceCode = json.getString("INTERFACE_CODE");
                    if(InterfaceUtils.INTERFACE_CODE_LOG_001101.equals(interfaceCode)){
                        json.remove("INTERFACE_CODE");
                        json.put("INTERFACE_CODE",InterfaceUtils.INTERFACE_CODE_LOG_001101);
                    }
                }else if("2".equals(resultType)) {//基础-产品端
                    String interfaceCode = json.getString("INTERFACE_CODE");
                    if(InterfaceUtils.INTERFACE_CODE_LOG_010101.equals(interfaceCode)){
                        json.remove("INTERFACE_CODE");
                        json.put("INTERFACE_CODE",InterfaceUtils.INTERFACE_CODE_LOG_009101);
                    }
                }else if("3".equals(resultType)) {//产品-客户端
                    String interfaceCode = json.getString("INTERFACE_CODE");
                    if(InterfaceUtils.INTERFACE_CODE_LOG_010201.equals(interfaceCode)){
                        json.remove("INTERFACE_CODE");
                        json.put("INTERFACE_CODE",InterfaceUtils.INTERFACE_CODE_LOG_008301);
                    }else if(InterfaceUtils.INTERFACE_CODE_LOG_010101.equals(interfaceCode)){
                        json.remove("INTERFACE_CODE");
                        json.getJSONObject("DATA").remove("STATE");
                        json.put("INTERFACE_CODE",InterfaceUtils.INTERFACE_CODE_LOG_009101);
                        String companyCode = json.getJSONObject("DATA").getString("COMPANY_CODE");
                        String versionCode = json.getJSONObject("DATA").getString("VERSION_CODE");

                        long num = publishTrailService.listByBatchNoStatus(versionCode, companyCode);
                        String status = num > 0 ? ResultStatus.INTERNAL_SERVER_ERROR.getStatus() : ResultStatus.SUCCESS.getStatus();
                        trailDetailService.updateBatchNoState(versionCode, companyCode, status);
                        // List<PublishCollection> publishTrails = publishTrailService.listByBatchNoAndQueueNameAndStatus(json.getString("batchNo"), map.get("queueName").toString(), "500", true);


                        *//*PublishTrailVO publishTrailVO = new PublishTrailVO();
                        publishTrailVO.setBatchNo(versionCode);
                        publishTrailVO.setQueueName(companyCode);
                        publishTrailVO.setStatus(ResultStatus.INTERNAL_SERVER_ERROR.getStatus());
                        List<PublishTrailVO> result = publishTrailService.listGroupByBatchNoAndQueueNameAndStatus(publishTrailVO);*//*
                        if(num > 0){
                            if("TEST".equals(companyCode)){
                                json.getJSONObject("DATA").put("STATE","908");
                            }else{
                                json.getJSONObject("DATA").put("STATE","909");
                            }
                        }else{
                            if("TEST".equals(companyCode)){
                                json.getJSONObject("DATA").put("STATE","016");
                            }else{
                                json.getJSONObject("DATA").put("STATE","022");
                            }
                        }
                    }
                }
                //请求产品发布平台获取最新的版本号
                String sendLogObj = HttpUtils.doPost(indexUrl, methodPath, null, null, json.toJSONString());
                logger.info("基础数据平台日志返回报文："+sendLogObj);
                JSONObject objJson = JSONObject.parseObject(sendLogObj);
                String message = objJson.getString("message");//获取编码
                logger.info("基础数据平台日志返回："+message);
                jsonResult.setResult("基础数据平台日志返回："+message);
        } catch (Exception e) {
            jsonResult.setStatus(ResultStatus.INTERNAL_SERVER_ERROR.getStatus());
            jsonResult.setMessage(e.getMessage());
            logger.error("基础数据发布平台请求失败:" + e.getMessage() + ":数据为:" + JSONObject.toJSONString(json));
            jsonResult.setResult("基础数据发布平台请求失败:" + e.getMessage() + ":数据为:" + JSONObject.toJSONString(json));
        }*/
        return jsonResult;
    }

}

package com.jy.controller;

import com.jy.ann.MethodMonitor;
import com.jy.bean.common.Constant;
import com.jy.bean.po.DataTrace;
import com.jy.bean.result.JsonResult;
import com.jy.service.DataTraceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.security.RolesAllowed;
import java.util.List;
import java.util.Map;

/**
 * 数据轨迹
 */
@RestController
@RolesAllowed(Constant.ROLE_QUERY)
@RequestMapping("/dataTraces")
public class DataTraceController {

    @Autowired
    private DataTraceService dataTraceService;


    @MethodMonitor
    @RequestMapping(value="query", method = RequestMethod.GET)
    public JsonResult<List<DataTrace>> listDataTrace(@RequestParam Map<String, Object> paramMap) throws Exception{
        List<DataTrace> dataTraceList = dataTraceService.listDataTraces(paramMap);
        JsonResult<List<DataTrace>> jsonResult = new JsonResult<List<DataTrace>>();
        jsonResult.setResult(dataTraceList);
        return jsonResult;
    }
}

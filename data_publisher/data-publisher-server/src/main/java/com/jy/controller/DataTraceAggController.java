package com.jy.controller;

import com.jy.ann.MethodMonitor;
import com.jy.bean.common.ClientDescMenu;
import com.jy.bean.common.Constant;
import com.jy.bean.po.DataTraceAgg;
import com.jy.bean.result.JsonResult;
import com.jy.service.DataTraceService;
import com.jy.util.EmptyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.security.RolesAllowed;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据轨迹
 */
@RestController
@RolesAllowed(Constant.ROLE_QUERY)
@RequestMapping("/dataTraceAggs")
public class DataTraceAggController {

    @Autowired
    private DataTraceService dataTraceService;

    @MethodMonitor
    @RequestMapping(params={"batchNo","clientCode", "dataSource"}, method = RequestMethod.GET)
    public JsonResult<List<DataTraceAgg>> listDataTraceAgg(String batchNo, String clientCode, String dataSource,
                                                           @RequestParam(name = "mainBatchNo", required = false)String mainBatchNo,
                                                           @RequestParam(name = "nodeName", required = false)String nodeName) throws Exception{
        List<DataTraceAgg> returnList = new ArrayList<>();
        List<DataTraceAgg> dataTraceAggList = null;
        if("transfer".equals(dataSource)){
            dataTraceAggList = dataTraceService.listTransferDataTraceAggs(mainBatchNo,batchNo,nodeName, dataSource);
        }else{
            dataTraceAggList = dataTraceService.listDataTraceAggs(batchNo, dataSource);
        }
        if(EmptyUtils.isNotEmpty(clientCode) && EmptyUtils.isNotEmpty(dataTraceAggList)){
            for(DataTraceAgg dataTraceAgg : dataTraceAggList){
                if(EmptyUtils.isEmpty(dataTraceAgg.getClientCode())
                        || clientCode.equals(dataTraceAgg.getClientCode())
                        || dataTraceAgg.getClientCode().startsWith(ClientDescMenu.SRC_DESC.getCode())){
                    returnList.add(dataTraceAgg);
                }
            }
        }
        JsonResult<List<DataTraceAgg>> jsonResult = new JsonResult<List<DataTraceAgg>>();
        jsonResult.setResult(returnList);
        return jsonResult;
    }
}

package com.jy.controller;

import com.jy.ann.MethodMonitor;
import com.jy.bean.common.Constant;
import com.jy.bean.common.DictTypeMenu;
import com.jy.bean.po.Dict;
import com.jy.bean.result.JsonResult;
import com.jy.service.DictService;
import com.jy.util.EmptyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.RolesAllowed;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/5/11
 */
@RestController
@RolesAllowed(Constant.ROLE_QUERY)
@RequestMapping("/dict")
public class DictController {

    @Autowired
    private DictService dictService;

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(params={"query"}, method = RequestMethod.GET)
    public JsonResult<List<Dict>> listByOrgCodeType(@RequestParam Map<String, Object> map) throws Exception {
        map.put("type", DictTypeMenu.ORGCODE.getTypeCode());
        List<Dict> dicts = dictService.listDict(map);
        JsonResult<List<Dict>> jsonResult = new JsonResult<>();
        jsonResult.setResult(dicts);
        return jsonResult;
    }

    @MethodMonitor
    @RequestMapping(params={"type"}, method = RequestMethod.GET)
    public JsonResult<List<Dict>> listByType(String type) throws Exception {
        List<Dict> dicts = dictService.listByType(type);
        JsonResult<List<Dict>> jsonResult = new JsonResult<>();
        jsonResult.setResult(dicts);
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(method = RequestMethod.POST)
    public JsonResult<Dict> save(@RequestBody Dict dict) throws Exception {
        JsonResult<Dict> jsonResult = new JsonResult<>();
        String typeName = DictTypeMenu.getNameByCode(dict.getType());
        if(EmptyUtils.isNotEmpty(typeName)){
            dict.setTypeName(typeName);
        }

        jsonResult.setResult(dictService.save(dict));
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(params="id", method = RequestMethod.DELETE)
    public JsonResult<Dict> delete(String id) throws Exception {
        dictService.delete(id);
        JsonResult<Dict> jsonResult = new JsonResult<Dict>();
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(method = RequestMethod.PUT)
    public JsonResult<Dict> update(@RequestBody Dict dict) throws Exception {
        JsonResult<Dict> jsonResult = new JsonResult<Dict>();
        jsonResult.setResult(dictService.update(dict));
        return jsonResult;
    }
}

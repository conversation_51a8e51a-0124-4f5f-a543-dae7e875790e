package com.jy.controller;

import com.jy.ann.MethodMonitor;
import com.jy.bean.common.Constant;
import com.jy.bean.po.Client;
import com.jy.bean.po.OrgMp;
import com.jy.service.OrgMpService;
import com.jy.bean.result.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.RolesAllowed;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/4/26
 */
@RestController
@RolesAllowed(Constant.ROLE_QUERY)
@RequestMapping("/orgMp")
public class OrgMpController {

    @Autowired
    private OrgMpService orgMpService;

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(value="query", method = RequestMethod.GET)
    public JsonResult<List<OrgMp>> listClient(@RequestParam Map<String, Object> map) throws Exception{
        List<OrgMp> clients = orgMpService.listOrgMp(map);
        JsonResult<List<OrgMp>> jsonResult = new JsonResult<List<OrgMp>>();
        jsonResult.setResult(clients);
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(params={"clientCode"}, method = RequestMethod.GET)
    public JsonResult<List<OrgMp>> listByClientCode(String clientCode) throws Exception{
        List<OrgMp> clients = orgMpService.listByClientCode(clientCode);
        JsonResult<List<OrgMp>> jsonResult = new JsonResult<List<OrgMp>>();
        jsonResult.setResult(clients);
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(method = RequestMethod.POST)
    public JsonResult<OrgMp> save(@RequestBody OrgMp orgMp) throws Exception {
        JsonResult<OrgMp> jsonResult = new JsonResult<OrgMp>();
        jsonResult.setResult(orgMpService.save(orgMp));
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(params="id", method = RequestMethod.DELETE)
    public JsonResult<OrgMp> delete(String id) throws Exception {
        orgMpService.delete(id);
        JsonResult<OrgMp> jsonResult = new JsonResult<OrgMp>();
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(method = RequestMethod.PUT)
    public JsonResult<OrgMp> update(@RequestBody OrgMp orgMp) throws Exception {
        JsonResult<OrgMp> jsonResult = new JsonResult<OrgMp>();
        jsonResult.setResult(orgMpService.update(orgMp));
        return jsonResult;
    }
}

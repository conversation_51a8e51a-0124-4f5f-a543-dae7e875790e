package com.jy.controller.nioLife;

import com.alibaba.fastjson.JSONObject;
import com.jy.util.DateUtil;
import com.jy.util.ReceiveDataUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Iterator;

/**
 * Created by anxing on 2019/2/13.
 */
@RestController
@RequestMapping("/nioLifePost")
public class NioLifeForwardPostController {

    private static final Logger logger = LogManager.getLogger(NioLifeForwardPostController.class);

    private static final String CONTENT_TYPE = "text/html; charset=UTF-8";
    @Autowired
    private NioLifeService nioLifeService;

    @Value("${thePath.nioLifeUrl}")
    private String nioLifeUrl;
    @Value("${thePath.nioLifeDataPath}")
    private String nioLifeDataPath;
    @Value("${thePath.nioLifePicPath}")
    private String nioLifePicPath;

    @RequestMapping(value = "/nioLifePostPartDataUrl")
    public void nioLifePostPartDataUrl(HttpServletRequest request, HttpServletResponse response) {
        response.setContentType(CONTENT_TYPE);
        String error = "";
        try {
            //收到发来的信息
            String json = ReceiveDataUtils.getSendString(request);
            String value = "";
            String key = "";
            if(json!=null){
                JSONObject jsonObject = JSONObject.parseObject(json.toString());
                Iterator<String> it = jsonObject.keySet().iterator();
                while(it.hasNext()){
                    // 获得key
                    key = it.next();
                    value = jsonObject.getString(key);
                    break;
                }
                //转发请求信息
                String content = nioLifeService.sendJsonNioLife(nioLifeUrl+nioLifeDataPath,key,value);
                logger.info("时间：" + DateUtil.crunttime());
                logger.info("接收蔚来配件数据报文："+content);
                //收到返回数据返回数据
                response.getWriter().write(content);
            }else {
                error = "{'code':'error','message':'报文获取失败！'}";
                response.getWriter().write(error);
            }
        }catch (Exception e){
            error = "{'code':'error','message':'"+e.getMessage()+"'}";
            e.printStackTrace();
        }
    }
    @RequestMapping(value = "/nioLifePostPicUrl")
    public void nioLifePostPicUrl(HttpServletRequest request, HttpServletResponse response) {
        response.setContentType(CONTENT_TYPE);
        String error = "";
        try {
            //收到发来的信息
            String json = ReceiveDataUtils.getSendString(request);
            String key = "";
            String value = "";
            if(json!=null){
                JSONObject jsonObject = JSONObject.parseObject(json.toString());
                Iterator<String> it = jsonObject.keySet().iterator();
                while(it.hasNext()){
                    // 获得key
                    key = it.next();
                    value = jsonObject.getString(key);
                    break;
                }
                //转发请求信息
                String content = nioLifeService.sendJsonNioLife(nioLifeUrl+nioLifePicPath,key,value);
                logger.info("时间：" + DateUtil.crunttime());
                logger.info("接收蔚来图片数据报文："+content);
                //收到返回数据返回数据
                response.getWriter().write(content);
            }else {
                error = "{'code':'error','message':'报文获取失败！'}";
                response.getWriter().write(error);
            }
        }catch (Exception e){
            e.printStackTrace();
            error = "{'code':'error','message':'"+e.getMessage()+"'}";
        }
    }


}

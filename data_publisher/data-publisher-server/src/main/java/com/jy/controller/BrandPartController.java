package com.jy.controller;

import com.jy.ann.MethodMonitor;
import com.jy.ann.PageResult;
import com.jy.bean.common.Constant;
import com.jy.bean.po.BrandPart;
import com.jy.bean.po.BrandPartHis;
import com.jy.bean.result.JsonResult;
import com.jy.service.BrandPartHisService;
import com.jy.service.BrandPartService;
import com.jy.util.EmptyUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.RolesAllowed;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * BrandPart查询控制器
 */
@RestController
@RolesAllowed(Constant.ROLE_QUERY)
@RequestMapping("/brandPart")
public class BrandPartController {

    private static final Logger logger = LogManager.getLogger(BrandPartController.class);

    @Autowired
    private BrandPartService brandPartService;

    @Autowired
    private BrandPartHisService brandPartHisService;

    /**
     * 分页查询BrandPart列表
     * @param page 分页参数
     * @param paramMap 查询参数
     * @return BrandPart列表
     * @throws Exception
     */
    @PageResult
    @MethodMonitor
    @RequestMapping(value = "query", method = RequestMethod.GET)
    public JsonResult<List<BrandPart>> queryBrandPart(@PageableDefault(size = 20, page = 0) Pageable page,
                                                      @RequestParam Map<String, Object> paramMap) throws Exception {
        // 验证必选参数
        Object brandCodeParam = paramMap.get("brandCode");
        if (EmptyUtils.isEmpty(brandCodeParam)) {
            JsonResult<List<BrandPart>> jsonResult = new JsonResult<>();
            jsonResult.setStatus("400");
            jsonResult.setMessage("品牌编码不能为空");
            return jsonResult;
        }

        String startTime = (String) paramMap.get("startTime");
        String endTime = (String) paramMap.get("endTime");
        if (EmptyUtils.isEmpty(startTime) || EmptyUtils.isEmpty(endTime)) {
            JsonResult<List<BrandPart>> jsonResult = new JsonResult<>();
            jsonResult.setStatus("400");
            jsonResult.setMessage("查询时间范围不能为空");
            return jsonResult;
        }

        // 处理品牌编码参数，支持单个或多个品牌编码
        List<String> brandCodeList = new ArrayList<>();

        if (brandCodeParam instanceof String) {
            String brandCodeStr = (String) brandCodeParam;
            // 支持逗号分隔的多个品牌编码
            String[] brandCodes = brandCodeStr.split(",");
            for (String brandCode : brandCodes) {
                if (EmptyUtils.isNotEmpty(brandCode.trim())) {
                    brandCodeList.add(brandCode.trim());
                }
            }
        } else if (brandCodeParam instanceof String[]) {
            String[] brandCodes = (String[]) brandCodeParam;
            for (String brandCode : brandCodes) {
                if (EmptyUtils.isNotEmpty(brandCode.trim())) {
                    brandCodeList.add(brandCode.trim());
                }
            }
        } else {
            String brandCode = brandCodeParam.toString().trim();
            if (EmptyUtils.isNotEmpty(brandCode)) {
                brandCodeList.add(brandCode);
            }
        }

        if (brandCodeList.isEmpty()) {
            JsonResult<List<BrandPart>> jsonResult = new JsonResult<>();
            jsonResult.setStatus("400");
            jsonResult.setMessage("品牌编码不能为空");
            return jsonResult;
        }

        try {
            List<BrandPart> brandPartList;

            if (brandCodeList.size() == 1) {
                // 单个品牌查询，使用原有方法
                paramMap.put("brandCode", brandCodeList.get(0));
                brandPartList = brandPartService.queryBrandPartWithPage(paramMap);
            } else {
                // 多个品牌查询，使用新的UNION ALL方法
                paramMap.put("brandCodes", brandCodeList);
                brandPartList = brandPartService.queryMultipleBrandPartsWithPage(paramMap);
            }

            JsonResult<List<BrandPart>> jsonResult = new JsonResult<>();
            jsonResult.setResult(brandPartList);
            return jsonResult;
        } catch (Exception e) {
            logger.error("查询品牌部件数据时出错: {}", e.getMessage(), e);
            JsonResult<List<BrandPart>> jsonResult = new JsonResult<>();
            jsonResult.setStatus("500");
            jsonResult.setMessage("查询失败：" + e.getMessage());
            return jsonResult;
        }
    }

    /**
     * 查询BrandPartHis轨迹列表
     * @param page 分页参数
     * @param brandCode 品牌编码
     * @param supTableId 供应商表ID
     * @return BrandPartHis轨迹列表
     * @throws Exception
     */
    @PageResult
    @MethodMonitor
    @RequestMapping(value = "queryHis", method = RequestMethod.GET)
    public JsonResult<List<BrandPartHis>> queryBrandPartHis(@PageableDefault(size = 20, page = 0) Pageable page,
                                                            @RequestParam String brandCode,
                                                            @RequestParam String supTableId) throws Exception {
        // 验证必选参数
        if (EmptyUtils.isEmpty(brandCode)) {
            JsonResult<List<BrandPartHis>> jsonResult = new JsonResult<>();
            jsonResult.setStatus("400");
            jsonResult.setMessage("品牌编码不能为空");
            return jsonResult;
        }

        if (EmptyUtils.isEmpty(supTableId)) {
            JsonResult<List<BrandPartHis>> jsonResult = new JsonResult<>();
            jsonResult.setStatus("400");
            jsonResult.setMessage("供应商表ID不能为空");
            return jsonResult;
        }

        // 构建查询参数
        Map<String, Object> params = new HashMap<>();
        params.put("brandCode", brandCode);
        params.put("supTableId", supTableId);
        /*params.put("page", page.getPageNumber() * page.getPageSize());
        params.put("size", page.getPageSize());*/

        List<BrandPartHis> brandPartHisList = brandPartHisService.queryBrandPartHisWithPage(params);
        JsonResult<List<BrandPartHis>> jsonResult = new JsonResult<>();
        jsonResult.setResult(brandPartHisList);
        return jsonResult;
    }
}

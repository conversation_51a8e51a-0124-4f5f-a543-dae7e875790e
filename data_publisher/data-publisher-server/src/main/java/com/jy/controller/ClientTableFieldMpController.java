package com.jy.controller;

import com.jy.ann.MethodMonitor;
import com.jy.bean.common.Constant;
import com.jy.bean.po.ClientTable;
import com.jy.bean.po.ClientTableFieldMp;
import com.jy.bean.po.ClientTableFieldMpList;
import com.jy.bean.result.JsonResult;
import com.jy.service.ClientTableFieldMpService;
import com.jy.service.ClientTableService;
import com.jy.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.RolesAllowed;
import java.util.List;
import java.util.Map;

/**
 * 客户端表字段管理
 * <AUTHOR>
 * @date 2018/5/7
 */
@RestController
@RolesAllowed(Constant.ROLE_QUERY)
@RequestMapping("/clientTableFieldMp")
public class ClientTableFieldMpController {

    @Autowired
    private ClientTableFieldMpService clientTableFieldMpService;
    @Autowired
    private ClientTableService clientTableService;

    @MethodMonitor
    @RequestMapping(params={"clientCode","tableName"}, method = RequestMethod.GET)
    public JsonResult<List<ClientTableFieldMp>> listClientTable(String clientCode, String tableName) throws Exception{
        List<ClientTableFieldMp> clientTableFieldMps = clientTableFieldMpService.listByClientCodeAndTableName(clientCode, tableName);

        JsonResult<List<ClientTableFieldMp>> jsonResult = new JsonResult<>();
        jsonResult.setResult(clientTableFieldMps);
        return jsonResult;
    }


    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(value="query", method = RequestMethod.GET)
    public JsonResult<List<ClientTableFieldMp>> listClientTable(@RequestParam Map<String, Object> map) throws Exception{
        List<ClientTableFieldMp> clientTables = clientTableFieldMpService.listClientTableFieldMp(map);
        JsonResult<List<ClientTableFieldMp>> jsonResult = new JsonResult<>();
        jsonResult.setResult(clientTables);
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(value="/batch", method = RequestMethod.POST)
    public JsonResult<List<ClientTableFieldMp>> saveList(@RequestBody ClientTableFieldMpList clientTableFieldMpList) throws Exception{
        List<ClientTableFieldMp> clientTableFieldMps = clientTableFieldMpList.getClientTableFieldMps();
        for(ClientTableFieldMp clientTableFieldMp : clientTableFieldMps){
            clientTableFieldMp.setId(StringUtils.getUUID());
            clientTableFieldMp.setClientCode(clientTableFieldMpList.getClientCode());
            clientTableFieldMp.setTableName(clientTableFieldMpList.getTableName());
        }
        JsonResult<List<ClientTableFieldMp>> jsonResult = new JsonResult<List<ClientTableFieldMp>>();
        jsonResult.setResult(clientTableFieldMpService.saveBatch(clientTableFieldMps));
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(method = RequestMethod.POST)
    public JsonResult<ClientTableFieldMp> save(@RequestBody ClientTableFieldMp clientTableFieldMp) throws Exception {
        JsonResult<ClientTableFieldMp> jsonResult = new JsonResult<ClientTableFieldMp>();
        jsonResult.setResult(clientTableFieldMpService.save(clientTableFieldMp));
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(params="id", method = RequestMethod.DELETE)
    public JsonResult<ClientTableFieldMp> delete(String id) throws Exception {
        clientTableFieldMpService.delete(id);
        JsonResult<ClientTableFieldMp> jsonResult = new JsonResult<ClientTableFieldMp>();
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(method = RequestMethod.PUT)
    public JsonResult<ClientTableFieldMp> update(@RequestBody ClientTableFieldMp clientTableFieldMp) throws Exception {
        JsonResult<ClientTableFieldMp> jsonResult = new JsonResult<ClientTableFieldMp>();
        jsonResult.setResult(clientTableFieldMpService.update(clientTableFieldMp));
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(value="/selectField", method = RequestMethod.POST)
    public JsonResult selectField(@RequestBody ClientTableFieldMp clientTableFieldMp) throws Exception {
        JsonResult jsonResult = new JsonResult();
        int count = clientTableFieldMpService.selectFieldCount(clientTableFieldMp);
        if(count<=1){//最后一个字段直接删除整张表
            ClientTable clientTable = new ClientTable();
            clientTable.setTableName(clientTableFieldMp.getTableName());
            clientTable.setClientCode(clientTableFieldMp.getClientCode());
            clientTable = clientTableService.getClientTable(clientTable);
            clientTableFieldMpService.delete(clientTableFieldMp.getId());
            clientTableService.delete(clientTable.getId());

            jsonResult.setResult("删除全表");
        }else{
            jsonResult.setResult("无");
        }
        return jsonResult;
    }
}

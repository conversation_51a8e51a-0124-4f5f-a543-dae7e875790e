package com.jy.controller;

import com.alibaba.fastjson.JSONObject;
import com.jy.bean.result.JsonResult;
import com.jy.bean.result.ResultStatus;
import com.jy.service.MonitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 运维监控接口
 * @Author: zy
 * @Date: Created in 2018/5/18
 */
@RestController
@RequestMapping("/monitor")
public class MonitorController {

    @Autowired
    private MonitorService monitorService;

    @RequestMapping(params={"health"}, method = RequestMethod.GET)
    public JSONObject health() throws Exception{
        long start = System.currentTimeMillis();
        JsonResult<String> testMysqlJsonResult =  monitorService.testMysql();
        JsonResult<String> testMqJsonResult =  monitorService.testMq();
       // JsonResult<String> testMongoDBJsonResult =  monitorService.testMongoDB();
        JsonResult<String> testRedisJsonResult =  monitorService.testRedis();
        JSONObject reJsonResult = mergeResult(testMysqlJsonResult, testMqJsonResult, testRedisJsonResult);
        long end = System.currentTimeMillis();
        reJsonResult.put("timeDuration", (end - start));
        return reJsonResult;
    }

    private JSONObject mergeResult(JsonResult<String> ...jsonResults){
        JSONObject mergedJsonResult = new JSONObject();
        List<String> messages = new ArrayList<>();
        for(JsonResult<String> jsonResult : jsonResults){
            if(ResultStatus.SUCCESS.getStatus().equals(jsonResult.getStatus())){
                mergedJsonResult.put("status","Success");
            } else {
                mergedJsonResult.put("status","Error");
            }
            messages.add(jsonResult.getResult());
        }
        mergedJsonResult.put("message", messages.toString());
        return mergedJsonResult;
    }

}

package com.jy.controller;

import com.jy.ann.MethodMonitor;
import com.jy.bean.common.ClientStatus;
import com.jy.bean.common.Constant;
import com.jy.bean.po.Client;
import com.jy.bean.po.ClientTable;
import com.jy.bean.po.OrgMp;
import com.jy.bean.result.JsonResult;
import com.jy.mq.FacadeDataReceiver;
import com.jy.service.ClientService;
import com.jy.service.ClientTableService;
import com.jy.service.OrgMpService;
import com.jy.util.EmptyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.RolesAllowed;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 客户端管理
 */
@RestController
@RolesAllowed(Constant.ROLE_QUERY)
@RequestMapping("/client")
public class ClientController {

    @Autowired
    private ClientService clientService;
    @Autowired
    private ClientTableService clientTableService;
    @Autowired
    private OrgMpService orgMpService;
    @Autowired
    private FacadeDataReceiver facadeDataReceiver;

    @MethodMonitor
    @RequestMapping(value="query", method = RequestMethod.GET)
    public JsonResult<List<Client>> listClient(@RequestParam Map<String, Object> map) throws Exception{
        List<Client> clients = clientService.listClient(map);
        JsonResult<List<Client>> jsonResult = new JsonResult<List<Client>>();
        jsonResult.setResult(clients);
        return jsonResult;
    }

    @MethodMonitor
    @RequestMapping(params={"path","status"}, method = RequestMethod.PUT)
    public JsonResult<String> updateByPath(String path, String status) throws Exception{
        List<Client> clients = clientService.listClientByPath(path);
        if(EmptyUtils.isEmpty(clients)){
            throw new Exception("无对应数据！");
        }
        status = status.toUpperCase();
        if(!ClientStatus.CLIENT_DOWN.equals(status) && !ClientStatus.CLIENT_UP.equals(status)){
            throw new Exception("无此状态！客户端状态值范围：UP，DOWN");
        }
        for(Client client : clients){
            client.setStatus(status);
            clientService.update(client);
        }
        JsonResult<String> jsonResult = new JsonResult<>();
        return jsonResult;
    }

    @MethodMonitor
    @RequestMapping(params="code", method = RequestMethod.GET)
    public JsonResult<Client> getByClientCode(String code) throws Exception{
        Client client = clientService.getOneByCode(code);
        JsonResult<Client> jsonResult = new JsonResult<>();
        jsonResult.setResult(client);
        return jsonResult;
    }

    @MethodMonitor
    @RequestMapping(params="baseTableName", method = RequestMethod.GET)
    public JsonResult<List<Client>> listByBaseTableName(String baseTableName) throws Exception{
        Map<String, ClientTable> map = clientTableService.mapByBaseTableName(baseTableName);
        List<Client> clients = clientService.listClient(new HashMap<>());
        clients = clients.stream().filter(str->map.containsKey(str.getCode()) && !ClientStatus.CLIENT_OFFLINE.equals(str.getStatus()))
                .collect(Collectors.toList());
        for (Client client : clients){
            List<OrgMp> orgMps = orgMpService.listByClientCode(client.getCode());
            client.setOrgMps(orgMps);
        }
        JsonResult<List<Client>> jsonResult = new JsonResult<List<Client>>();
        jsonResult.setResult(clients);
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(method = RequestMethod.POST)
    public JsonResult<Client> save(@RequestBody Client client) throws Exception {
        JsonResult<Client> jsonResult = new JsonResult<Client>();
        jsonResult.setResult(clientService.save(client));
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(params="id", method = RequestMethod.DELETE)
    public JsonResult<Client> delete(String id) throws Exception {
        clientService.delete(id);
        JsonResult<Client> jsonResult = new JsonResult<Client>();
        return jsonResult;
    }

    @RolesAllowed(Constant.ROLE_UPDATE)
    @MethodMonitor
    @RequestMapping(method = RequestMethod.PUT)
    public JsonResult<Client> update(@RequestBody Client client) throws Exception {
        JsonResult<Client> jsonResult = new JsonResult<Client>();
        jsonResult.setResult(clientService.update(client));
        return jsonResult;
    }

    /**
    * @Description 对facadeDataReceiver中的ignoreClientSet增删改查
    * @Parames
    * @return null
    * <AUTHOR>
    * @Date 2025/4/2
    */
    @MethodMonitor
    @RequestMapping(value = "/getIgnoreReceiverClient", method = RequestMethod.GET)
    public JsonResult<Set> getIgnoreReceiverClient() throws Exception{
        Set<String> set = facadeDataReceiver.getIgnoreClientSet();
        JsonResult<Set> jsonResult = new JsonResult<>();
        jsonResult.setResult(set);
        return jsonResult;
    }

    @MethodMonitor
    @RequestMapping(value = "/setIgnoreReceiverClient", method = RequestMethod.POST)
    public JsonResult<Set> setIgnoreReceiverClient(@RequestBody Client client) throws Exception {
        facadeDataReceiver.setIgnoreClientSet(client.getCode());
        Set<String> set = facadeDataReceiver.getIgnoreClientSet();
        JsonResult<Set> jsonResult = new JsonResult<Set>();
        jsonResult.setResult(set);
        return jsonResult;
    }

    @MethodMonitor
    @RequestMapping(value = "/removeIgnoreReceiverClient", method = RequestMethod.POST)
    public JsonResult<Set> removeIgnoreReceiverClient(@RequestBody Client client) throws Exception {
        facadeDataReceiver.removeIgnoreClientSet(client.getCode());
        Set<String> set = facadeDataReceiver.getIgnoreClientSet();
        JsonResult<Set> jsonResult = new JsonResult<Set>();
        jsonResult.setResult(set);
        return jsonResult;
    }

}

package com.jy.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jy.bean.po.MenuJson;
import com.jy.bean.result.JsonResult;
import com.jy.service.ClientTableService;
import com.jy.service.MenuService;
import com.jy.util.SecurityContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/4/26
 */
@RestController
@RequestMapping("/menu")
public class MenuController {

    @Autowired
    private MenuService menuService;

    @Autowired
    private ClientTableService clientTableService;

    @RequestMapping(params={"menu"}, method = RequestMethod.GET)
    public JsonResult<MenuJson> getMenu() throws Exception{
        JsonResult<MenuJson> jsonResult = new JsonResult<MenuJson>();
        MenuJson s = menuService.getOne();
        JSONArray menuList = null;
        String clientCode = SecurityContextUtils.getUserName().toUpperCase();
        if(!"ADMIN".equals(clientCode)){
            menuList = new JSONArray();
          //  JSONArray parentSide = new JSONArray();
            if(s.getMenu()!=null && s.getMenu().size()>0){
              //  JSONObject menuJson = new JSONObject();
              //  menuJson.put("parentName","轨迹管理");
                for (int j = 0; j <s.getMenu().size() ; j++) {
                    JSONObject jsonObject = s.getMenu().getJSONObject(j);
                    String parentName = jsonObject.getString("parentName");
                    if("轨迹管理".equals(parentName) || "看板".equals(parentName)){
                        menuList.add(jsonObject);
                    }
                 /*   for (int f = 0; f <menuArray1.size() ; f++) {
                        JSONObject json = menuArray1.getJSONObject(f);
                        if(!"发布轨迹".equals(json.getString("childName"))){
                            json.remove(f);
                        }else{
                            parentSide.add(json);
                        }
                    }*/
                }
              //  menuJson.put("parentSide",parentSide);

            }
            s.setMenu(menuList);
        }
        jsonResult.setResult(s);
        return jsonResult;
    }

    @ResponseBody
    @RequestMapping("getFacadePartTableField")
    public List<Map<String,Object>> getFacadePartTableField(){
        return clientTableService.getFacadePartTableField();
    }
}

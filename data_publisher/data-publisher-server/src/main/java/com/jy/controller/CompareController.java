package com.jy.controller;

import com.jy.ann.MethodMonitor;
import com.jy.bean.common.Constant;
import com.jy.bean.result.JsonResult;
import com.jy.service.AsyncService;
import com.jy.util.HttpUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.security.RolesAllowed;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/8/30
 */
@RestController
@RolesAllowed(Constant.ROLE_QUERY)
@RequestMapping("/compares")
public class CompareController {

    @Value("${compare.clientComparePath}")
    private String clientComparePath;
    @Value("${compare.clientTablePath}")
    private String clientTablePath;
    @Value("${compare.serverUrl}")
    private String serverUrl;
    @Value("${compare.serverPath}")
    private String serverPath;
    @Autowired
    private AsyncService asyncService;

    @MethodMonitor
    @RequestMapping(params={"compare","clientCode","url", "compareBatchNo"}, method = RequestMethod.POST)
    public JsonResult<String> compare(@RequestBody String bodys, String clientCode,  String url, String compareBatchNo) throws Exception{
        Map<String, String> querys = new HashMap<>();
        querys.put("compare", "compare");
        querys.put("clientCode", clientCode);
        querys.put("url", url);
        querys.put("compareBatchNo", compareBatchNo);
        JsonResult jsonResult = new JsonResult();
        String path = clientCode.contains("FACADE") ? ("datacheck-service/" + clientComparePath): clientComparePath;
        jsonResult.setResult(asyncService.doPost(url, path, new HashMap<String, String>(), querys, bodys));
        return jsonResult;
    }

    @RequestMapping(params={"compare","baseTableName"}, method = RequestMethod.GET)
    public String listBranchCountByTableName(String baseTableName, String layerKey, String layerValue) throws Exception {
        Map<String, String> querys = new HashMap<>();
        querys.put("compare", "compare");
        querys.put("baseTableName", baseTableName);
        querys.put("layerKey", layerKey);
        querys.put("layerValue", layerValue);
        String response = HttpUtils.doGet(serverUrl, serverPath, new HashMap<String, String>(), querys);
        return response;
    }

    @RequestMapping(params={"resend"}, method = RequestMethod.POST)
    public String resend(@RequestBody String bodys) throws Exception {
        Map<String, String> querys = new HashMap<>();
        querys.put("resend", "resend");
        String response = HttpUtils.doPost(serverUrl, serverPath, new HashMap<String, String>(), querys, bodys);
        return response;
    }

    @RequestMapping(params={"unlock","clientCode", "clientUrl"}, method = RequestMethod.POST)
    public String unlock(@RequestBody String bodys, String clientCode, String clientUrl) throws Exception {

        //根据一个客户端返回结果直接开锁（暂时只有人保，后续修改）
        Map<String, String> querys = new HashMap<>();
        querys.put("unlock", "unlock");
        querys.put("clientCode", clientCode);
        querys.put("clientUrl", clientUrl);
        String response = HttpUtils.doPost(serverUrl, serverPath, new HashMap<String, String>(), querys, bodys);
        return response;
    }

    @MethodMonitor
    @RequestMapping(params="create" , method = RequestMethod.POST)
    public String createTable(String url, String tableNames, String tableSuffix) throws Exception{
        Map<String, String> querys = new HashMap<>();
        querys.put("create", "create");
        querys.put("tableNames", tableNames);
        querys.put("tableSuffix", tableSuffix);
        querys.put("baseTableSuffix", "ADA0");
        String response = HttpUtils.doPost(url, clientTablePath, new HashMap<String, String>(), querys, "");
        return response;
    }

}

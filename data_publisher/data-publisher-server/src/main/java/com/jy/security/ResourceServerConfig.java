package com.jy.security;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;

import javax.servlet.http.HttpServletResponse;

/**
 * 
 * <AUTHOR>
 * @date 2018/1/15
 **/

@Configuration
@EnableResourceServer
public class ResourceServerConfig extends ResourceServerConfigurerAdapter {

    @Autowired
    private RedisTokenStore redisTokenStore;


    @Override
    public void configure(HttpSecurity http) throws Exception {
        http.headers().frameOptions().disable();
        http.csrf().disable()
                .exceptionHandling()
                .authenticationEntryPoint((request, response, authException) -> response.sendError(HttpServletResponse.SC_UNAUTHORIZED))
                .and()
                .authorizeRequests()
                .antMatchers("/druid/**").permitAll()
                .antMatchers("/js/**").permitAll()
                .antMatchers("/img/**").permitAll()
                .antMatchers("/css/**").permitAll()
                .antMatchers("/login.html").permitAll()
                .antMatchers("/login").permitAll()
                .antMatchers("/page/**").permitAll()
                .antMatchers("/html/**").permitAll()
                .antMatchers("/plugin/**").permitAll()
                .antMatchers("/health/**").permitAll()
                .antMatchers("/metrics/**").permitAll()
                .antMatchers("/info/**").permitAll()
                .antMatchers("/oauth/**").permitAll()
                .antMatchers("/monitor/**").permitAll()
                .antMatchers("/nioLifePost/**").permitAll()
              //  .antMatchers("/menu/getList").permitAll()
                .antMatchers("/loginOut").permitAll()
                .anyRequest().authenticated()
               // .and().formLogin().loginPage("/login")
              /*  .and().formLogin().loginPage("/login").successHandler(new AuthenticationSuccessHandler() {
            @Override
            public void onAuthenticationSuccess(HttpServletRequest arg0, HttpServletResponse arg1, Authentication arg2)
                    throws IOException, ServletException {
                Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
                System.out.println("Authorities:"+ SecurityContextHolder.getContext().getAuthentication().getAuthorities());
                if (principal != null && principal instanceof UserDetails) {
                    UserDetails user = (UserDetails) principal;
                    System.out.println("loginUser:"+ arg2);
                    System.out.println("loginUser----:"+user.getAuthorities());
                    //维护token
                   // arg0.getSession().setAttribute("userDetail", user);
                    try {
                        facadeUtils.getToken("http://localhost:8080/");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    arg1.sendRedirect("/index");
                }
            }
        })*/ .and()
                .httpBasic();
    }
//    @Override
//    public void configure(HttpSecurity http) throws Exception {
//        http.requestMatcher(new OAuthRequestedMatcher())
//                .authorizeRequests()
//                .antMatchers(HttpMethod.OPTIONS).permitAll()
//                .anyRequest().authenticated();
//    }
//
//    private static class OAuthRequestedMatcher implements RequestMatcher {
//        public boolean matches(HttpServletRequest request) {
//            String auth = request.getHeader("Authorization");
//            // Determine if the client request contained an OAuth Authorization
//            boolean haveOauth2Token = (auth != null) && auth.startsWith("Bearer");
//            boolean haveAccessToken = request.getParameter("access_token")!=null;
//            return haveOauth2Token || haveAccessToken;
//        }
//    }
}

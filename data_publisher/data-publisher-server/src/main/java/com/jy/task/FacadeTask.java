package com.jy.task;

import com.jy.bean.common.ClientDescMenu;
import com.jy.bean.po.Client;
import com.jy.mq.FacadeDataReceiver;
import com.jy.service.ClientService;
import com.jy.util.ToolUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/5/17
 */
@Component
@EnableScheduling
public class FacadeTask {
    private static final Logger logger = LogManager.getLogger(FacadeTask.class);

    @Autowired
    private ClientService clientService;
    @Autowired
    private FacadeDataReceiver facadeDataReceiver;

    public static Map<String, Boolean> clientStatusMap = new HashMap<>();

    //该方法会在上一次任务执行完成 1 秒 后再执行一次。
    @Scheduled(fixedDelay=1000)
    public void timerPull() {
        try{
            List<Client> facadeClients = clientService.listClientByPath(ClientDescMenu.FACADE_DESC.getCode());
            for(Client client : facadeClients){
                if(!clientStatusMap.containsKey(client.getCode())){
                    clientStatusMap.put(client.getCode(), true);
                }
                if(clientStatusMap.get(client.getCode())){
                    //System.out.println("mq调用了---" + client.getCode());
                    facadeDataReceiver.clientPull(client);
                }
            }
        } catch (Exception e) {
            logger.error("timerPull定时执行失败:" + ToolUtils.getExceptionMsg(e));
        }
    }
}

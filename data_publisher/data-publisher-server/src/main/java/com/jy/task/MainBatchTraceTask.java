package com.jy.task;

import com.jy.ann.ExceptionMonitor;
import com.jy.bean.common.DataTraceMenu;
import com.jy.bean.po.DataTraceAgg;
import com.jy.bean.po.FlMainBatchInfo;
import com.jy.bean.result.ResultStatus;
import com.jy.service.DataTraceService;
import com.jy.service.FlMainBatchInfoService;
import com.jy.task.service.MainBatchTraceTaskService;
import com.jy.util.EmptyUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 定时聚合批次信息：
 * 1、 uTime 0-2h 的批次 每分钟聚合
 * 2、 utime 2h-24h 的批次 每10分聚合
 * 3、 utime 24h-30d 的批次 每小时聚合
 * 4、 utime 30d 以上的批次 每天聚合
 * @Author: zy
 * @Date: Created in 2019/11/22
 */
@Component
@EnableScheduling
public class MainBatchTraceTask {

    private static final Logger logger = LogManager.getLogger(MainBatchTraceTask.class);

    @Autowired
    private MainBatchTraceTaskService mainBatchTraceTask;

    private static long ONE_HOUR = 60 * 60 * 1000;
    private static long ONE_DAY = ONE_HOUR * 24;
    private static long ONE_MONTH = ONE_DAY * 30;

    @Scheduled(cron="* 0/1 * * * ?")
    public void minuteBatchAggs() {
        try {

            Map map = new HashMap();
            long now = System.currentTimeMillis();
            long startTime = now - ONE_HOUR * 3;
            long endTime = now;
            map.put("noStatus", ResultStatus.SUCCESS.getStatus());
            map.put("startTime", new Date(startTime));
            map.put("endTime", new Date(endTime));

            mainBatchTraceTask.doAggs(map);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Scheduled(cron="0 0/10 * * * ?")
    public void tenMinuteBatchAggs() {
        try {
            Map map = new HashMap();
            long now = System.currentTimeMillis();
            long startTime = now - ONE_DAY;
            long endTime = now - ONE_HOUR * 3;
            map.put("noStatus", ResultStatus.SUCCESS.getStatus());
            map.put("startTime", new Date(startTime));
            map.put("endTime", new Date(endTime));

            mainBatchTraceTask.doAggs(map);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Scheduled(cron="0 0 0/1 * * ?")
    public void hourBatchAggs() {
        try {

            Map map = new HashMap();
            long now = System.currentTimeMillis();
            long startTime = now - ONE_MONTH;
            long endTime = now - ONE_DAY;
            map.put("noStatus", ResultStatus.SUCCESS.getStatus());
            map.put("startTime", new Date(startTime));
            map.put("endTime", new Date(endTime));

            mainBatchTraceTask.doAggs(map);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    /**
     * 每天凌晨
     */
    @Scheduled(cron="0 0 0 * * ?")
    public void dayBatchAggs() {
        try {

            Map map = new HashMap();
            long now = System.currentTimeMillis();
            long startTime = 0l;
            long endTime = now - ONE_MONTH;
            map.put("noStatus", ResultStatus.SUCCESS.getStatus());
            map.put("startTime", new Date(startTime));
            map.put("endTime", new Date(endTime));

            mainBatchTraceTask.doAggs(map);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}

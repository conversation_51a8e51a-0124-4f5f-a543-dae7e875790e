package com.jy.task;

import com.jy.bean.common.ClientStatus;
import com.jy.bean.po.Client;
import com.jy.service.ClientService;
import com.jy.task.service.SrcWarehouseTaskService;
import com.jy.util.ToolUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @Author: caolt
 * @Description:
 * @Version:
 * @Date: Created in  2021/10/13
 */
@Component
@EnableScheduling
public class SrcWarehouseTask {

    private static final Logger logger = LogManager.getLogger(SrcWarehouseTask.class);
    @Autowired
    private ClientService clientService;
    @Autowired
    RabbitTemplate rabbitTemplate;
    @Autowired
    private SrcWarehouseTaskService srcWarehouseTask;


    /**
     * 此定时为单独处理es中配件数据使用
     */
    @Scheduled(cron = "0 0/1 * * * ?")
    public void srcEsPartPush() {
        try {
            String srcClientCode = "SRC_WAREHOUSE";
            //判定开关是否打开，关闭则不推送数据
            Client client = clientService.getOneByCode(srcClientCode);
            if (ClientStatus.CLIENT_UP.equals(client.getStatus())) {
                //es中的数据即时更新单独处理，每次处理定量数据
                int doneNum = 1;
                while (doneNum < client.getTopLimit()) {
                    //取数据放入srcMQ中，直到大于上限数据
                    srcWarehouseTask.sendData(srcClientCode);
                    doneNum++;
                }
            }
        } catch (Exception e) {
            logger.error("srcPartPush定时执行失败:" + ToolUtils.getExceptionMsg(e));
        }
    }

}

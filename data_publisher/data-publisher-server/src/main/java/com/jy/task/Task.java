package com.jy.task;

import com.jy.bean.common.ClientStatus;
import com.jy.bean.po.Client;
import com.jy.service.ClientService;
import com.jy.service.RedisService;
import com.jy.util.DateUtil;
import com.jy.util.CommonUtils;
import com.jy.util.ToolUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Set;


/**
 * <AUTHOR>
 * @date 2018/4/16
 */
@Component
@EnableScheduling
public class Task {
    private static final Logger logger = LogManager.getLogger(Task.class);

    @Autowired
    private ClientService clientService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private CommonUtils commonUtils;

    @Scheduled(cron="0 0/12 * * * ?")
    public void timerInit() {
        try {
            //取出失效的数据  更新数据库数据  把失效的数据置于down
            Client client = new Client();
            Object temp = redisService.getClient();
            if (temp != null) {
                Set<Object> clientSet = (Set<Object>) temp;
                for (Object str : clientSet) {
                    if (!redisService.hasKey(str.toString())) {
                        //失效数据置于down
                        client.setClient(str.toString(), ClientStatus.CLIENT_DOWN);
                        clientService.update(client);
                        //删除set中元素
                        redisService.removeClient(str.toString());
                        if(ClientStatus.CLIENT_DOWN.equals(client.getStatus())){
                            String msg = "即时更新：" + client.getCode() + "客户端在" + DateUtil.convertDateToString(DateUtil.milliPattern, new Date()) + "宕机。";
                            String subject = "即时更新" + client.getCode() + "宕机提醒";
                            //昨日有失败数据，邮件提醒
                            commonUtils.sendEmail(subject, msg);
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("timerInit定时执行失败:" + ToolUtils.getExceptionMsg(e));
        }
    }

}

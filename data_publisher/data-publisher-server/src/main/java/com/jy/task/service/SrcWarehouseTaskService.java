package com.jy.task.service;

import com.alibaba.fastjson.JSONObject;
import com.jy.ann.ExceptionMonitor;
import com.jy.bean.dto.BaseDataDTO;
import com.jy.config.RabbitMQConstants;
import com.jy.mq.RabbitCommon;
import com.jy.util.DateUtil;
import com.jy.util.EmptyUtils;
import com.jy.util.StringUtils;
import com.jy.util.ToolUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @program: data-publisher-server
 * @description:
 * @author: Ykuee
 * @create: 2024-04-30 17:02
 **/
@Service
public class SrcWarehouseTaskService {

    private static final Logger logger = LogManager.getLogger(SrcWarehouseTaskService.class);
    @Autowired
    private RabbitCommon rabbitCommon;
    @Autowired
    RabbitTemplate rabbitTemplate;

    private static String batchNo = "";

    @ExceptionMonitor(methodName = "SrcWarehouseTask.sendData", remark = "定时任务")
    public void sendData(String clientCode) {
        int sendTimes = 1;
        String targetExchangeCode = RabbitMQConstants.EXCHANGE_WAREHOUSE_PART;
        try {
            String message = rabbitCommon.processQueue(clientCode);
            BaseDataDTO baseDataDTO = JSONObject.parseObject(message, BaseDataDTO.class);
            if (EmptyUtils.isNotEmpty(baseDataDTO)) {
                String time = DateUtil.convertDateToString(DateUtil.timePattern, new Date());
                if ("insert".equals(baseDataDTO.getOperate())) {
                    baseDataDTO.getFields().put("jlrq", time);
                    baseDataDTO.getFields().put("scbz", "0");
                }
                baseDataDTO.getFields().put("xgrq", time);
                baseDataDTO.getFields().put("source", "warehouse");
                baseDataDTO.getFields().put("updateId", StringUtils.getUUID());
                baseDataDTO.setSendTimes(sendTimes);

                //判断批次号是否一致，不一致则添加至fl_batch_info中
                if (!batchNo.equals(baseDataDTO.getBatchNo())) {
                    this.trail(baseDataDTO, targetExchangeCode);
                    batchNo = baseDataDTO.getBatchNo();
                }
                //推送至交换机 SRC_PART与HIS_PART
                rabbitCommon.sendExchange(targetExchangeCode, JSONObject.toJSONString(baseDataDTO));
            }

        } catch (Exception e) {
            String message = ToolUtils.getExceptionMsg(e);
            logger.error("推送数据至exchange"+targetExchangeCode+"失败:" + message);
            throw e;
        }
    }


    private void trail(BaseDataDTO baseDataDTO, String clientCode) {
        JSONObject obj = new JSONObject();
        obj.put("mainBatchNo", baseDataDTO.getMainBatchNo());
        obj.put("batchNo", baseDataDTO.getBatchNo());
        obj.put("clientCode", clientCode);
        obj.put("tableName", baseDataDTO.getTableName());
        obj.put("num", 5000);
        //   System.out.println("放入mq数据--------------tmp-batch-queue-----------"  + obj.toJSONString());
        rabbitTemplate.convertAndSend("tmp-batch-queue", obj.toJSONString());
    }

}

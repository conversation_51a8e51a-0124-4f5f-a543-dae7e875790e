package com.jy.task.service;

import com.jy.ann.ExceptionMonitor;
import com.jy.bean.common.DataTraceMenu;
import com.jy.bean.po.DataTraceAgg;
import com.jy.bean.po.FlMainBatchInfo;
import com.jy.bean.result.ResultStatus;
import com.jy.exception.BusinessException;
import com.jy.service.DataTraceService;
import com.jy.service.FlMainBatchInfoService;
import com.jy.util.EmptyUtils;
import com.jy.util.ToolUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: data-publisher-server
 * @description:
 * @author: Ykuee
 * @create: 2024-04-30 16:53
 **/
@Service
public class MainBatchTraceTaskService {

    private static final Logger logger = LogManager.getLogger(MainBatchTraceTaskService.class);
    @Autowired
    private DataTraceService dataTraceService;
    @Autowired
    private FlMainBatchInfoService flMainBatchInfoService;

    @ExceptionMonitor(methodName = "MainBatchTraceTask.doAggs", remark = "定时任务")
    public void doAggs(Map map) throws Exception {
        List<FlMainBatchInfo> mainBatchInfoList = flMainBatchInfoService.listFlMainBatchInfo(map);
        boolean hasError = false;
        StringBuffer errorMsgSB = new StringBuffer();
        //更新五种时间  总时间  等待时间  处理时间 开始时间  结束时间
        for (FlMainBatchInfo flMainBatchInfo : mainBatchInfoList) {
            try {
                FlMainBatchInfo newFlMainBatchInfo = new FlMainBatchInfo(flMainBatchInfo.getMainBatchNo());
                List<DataTraceAgg> dataTraceAggList = dataTraceService.listDataTraceAggsByMainBatch(flMainBatchInfo.getMainBatchNo());
                if(CollectionUtils.isEmpty(dataTraceAggList)){
                    logger.info("es查询为空 mainBatchNo:{}", flMainBatchInfo.getMainBatchNo());
                    return;
                }
                Map<String, DataTraceAgg> dataTraceAggMap = dataTraceAggList.stream().collect(Collectors.toMap(dataTraceAgg-> dataTraceAgg.getNodeName(), DataTraceAgg->DataTraceAgg));
                String waitKey = DataTraceMenu.SRC_START_DESC.getName();
                long startTime = dataTraceAggList.get(0).getStartTime();
                long waitTime  = dataTraceAggMap.containsKey(waitKey) ? dataTraceAggMap.get(waitKey).getStartTime() : 0;
                long processTime = 0;
                long totalTime = 0;

                //等待时间更新
                if(EmptyUtils.isEmpty(flMainBatchInfo.getWaitTime()) && dataTraceAggMap.containsKey(waitKey)){
                    waitTime = dataTraceAggMap.get(waitKey).getStartTime();
                    newFlMainBatchInfo.setWaitTime((waitTime - startTime) / 1000);
                    newFlMainBatchInfo.setEndTime(new Date(dataTraceAggList.get(dataTraceAggList.size() - 1).getEndTime()));
                }
                //处理时间，总时间更新
                String pushKey = DataTraceMenu.CLIENT_PUSH_DESC.getName();
                String dealKey = DataTraceMenu.CLIENT_DEAL_DESC.getName();
                if(dataTraceAggMap.containsKey(dealKey) && dataTraceAggMap.containsKey(pushKey)){
                    Integer pushNum = dataTraceAggMap.get(pushKey).getDataNum();
                    Integer dealNum = dataTraceAggMap.get(dealKey).getDataNum();
                    if(pushNum.equals(dealNum)){
                        processTime = dataTraceAggMap.get(dealKey).getEndTime();
                        newFlMainBatchInfo.setProcessTime((processTime - waitTime) / 1000);
                        newFlMainBatchInfo.setTotalTime((processTime - startTime) / 1000);
                        newFlMainBatchInfo.setStatus(ResultStatus.SUCCESS.getStatus());
                        newFlMainBatchInfo.setEndTime(new Date(dataTraceAggList.get(dataTraceAggList.size() - 1).getEndTime()));
                    }
                }
                if(dataTraceAggMap.containsKey(DataTraceMenu.SRC_NO_PUSH_DESC.getServiceName())
                        && !dataTraceAggMap.containsKey(pushKey)
                        && !dataTraceAggMap.containsKey(dealKey)){
                    processTime = dataTraceAggMap.get(DataTraceMenu.SRC_NO_PUSH_DESC.getServiceName()).getEndTime();
                    // 将时间戳转换为Instant对象
                    Instant givenTime = Instant.ofEpochMilli(processTime);
                    // 获取当前时间的Instant对象
                    Instant now = Instant.now();
                    // 计算两个时间之间的持续时间
                    Duration duration = Duration.between(givenTime, now);
                    // 判断是否已经超过五分钟 (5分钟 = 5 * 60秒 = 300秒)
                    if (duration.getSeconds() > 300) {
                        newFlMainBatchInfo.setProcessTime((processTime - waitTime) / 1000);
                        newFlMainBatchInfo.setTotalTime((processTime - startTime) / 1000);
                        newFlMainBatchInfo.setStatus(ResultStatus.SUCCESS.getStatus());
                        newFlMainBatchInfo.setEndTime(new Date(dataTraceAggList.get(dataTraceAggList.size() - 1).getEndTime()));
                    }
                }
                if(EmptyUtils.isNotEmpty(newFlMainBatchInfo.getWaitTime()) || EmptyUtils.isNotEmpty(newFlMainBatchInfo.getProcessTime()) ||
                        EmptyUtils.isNotEmpty(newFlMainBatchInfo.getTotalTime())){
                    flMainBatchInfoService.update(newFlMainBatchInfo);
                }
            } catch (Exception e) {
                hasError = true;
                errorMsgSB.append("主批次号:").append(flMainBatchInfo.getMainBatchNo())
                        .append("\n")
                        .append(" 客户端:").append(flMainBatchInfo.getClientCode())
                        .append("\n")
                        .append("原始异常:")
                        .append("\n")
                        .append(ToolUtils.getExceptionMsg(e))
                        .append("\n");
            }
        }
        if(hasError){
            throw new BusinessException("500", "更新主批次状态时发生异常 \n" + errorMsgSB);
        }
    }

}

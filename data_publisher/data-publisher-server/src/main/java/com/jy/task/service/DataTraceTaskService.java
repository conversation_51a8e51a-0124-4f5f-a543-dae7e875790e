package com.jy.task.service;

import com.jy.ann.ExceptionMonitor;
import com.jy.bean.common.ClientDescMenu;
import com.jy.bean.common.DataTraceMenu;
import com.jy.bean.po.DataTraceAgg;
import com.jy.bean.po.FlBatchInfo;
import com.jy.bean.result.ResultStatus;
import com.jy.exception.BusinessException;
import com.jy.service.DataTraceService;
import com.jy.service.FlBatchInfoService;
import com.jy.task.DataTraceTask;
import com.jy.util.EmptyUtils;
import com.jy.util.ListUtils;
import com.jy.util.ToolUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: data-publisher-server
 * @description:
 * @author: Ykuee
 * @create: 2024-04-30 16:51
 **/

@Service
public class DataTraceTaskService {

    private static final Logger logger = LogManager.getLogger(DataTraceTaskService.class);
    @Autowired
    private DataTraceService dataTraceService;
    @Autowired
    private FlBatchInfoService flBatchInfoService;

    @ExceptionMonitor(methodName = "dataTraceTask.doAggs", remark = "定时任务")
    public void doAggs(Map map) throws Exception {
        List<FlBatchInfo> batchInfoList = flBatchInfoService.listFlBatchInfo(map);
        Map<String, List<FlBatchInfo>> batchInfoMap = batchInfoList.stream().collect(Collectors.groupingBy(FlBatchInfo::getBatchNo));
        boolean hasError = false;
        StringBuffer errorMsgSB = new StringBuffer();
        for (Map.Entry<String, List<FlBatchInfo>> entry : batchInfoMap.entrySet()) {
            try {

                List<DataTraceAgg> dataTraceAggList = dataTraceService.listDataTraceAggs(entry.getKey(), "publisher");
                List<FlBatchInfo> toSaveList = listBacthInfo(dataTraceAggList, entry.getValue().get(0));
                if(EmptyUtils.isNotEmpty(toSaveList)){
                    // 暂时未加事务，后续调整
                    // 刪除原 batchNo数据
                    flBatchInfoService.deleteByBacthNo(entry.getKey(), "publisher");
                    // 插入新数据
                    flBatchInfoService.saveBacth(toSaveList);
                }
            } catch (Exception e) {
                logger.error("batchError-----------------" + entry.getKey());
                hasError = true;
                errorMsgSB.append("小批次号：").append(entry.getKey());


                if(entry.getValue()!=null && entry.getValue().get(0)!=null){
                    FlBatchInfo flBatchInfo = entry.getValue().get(0);
                    errorMsgSB.append("客户端：").append(flBatchInfo.getClientCode());
                    errorMsgSB.append("节点：").append(flBatchInfo.getNodeName());
                }
                errorMsgSB.append("\n")
                        .append("原始异常:")
                        .append("\n")
                        .append(ToolUtils.getExceptionMsg(e))
                        .append("\n");
            }
        }
        if (hasError) {
            throw new BusinessException("500","更新小批次状态时发生异常 \n" + errorMsgSB);
        }
    }

    private List<FlBatchInfo> listBacthInfo(List<DataTraceAgg> dataTraceAggList, FlBatchInfo srcBatchInfo){
        List<FlBatchInfo> returnList = new ArrayList<>();
        if(EmptyUtils.isEmpty(dataTraceAggList)){
            return returnList;
        }
        Date startTime = new Date(dataTraceAggList.get(0).getStartTime());
        Map<String,Integer> totalList = dataTraceAggList.stream()
                .filter(dataTraceAgg-> DataTraceMenu.CLIENT_PUSH_DESC.getName().equals(dataTraceAgg.getNodeName())
                        && dataTraceAgg.getSendTimes() == 1)
                .collect(Collectors.toMap(DataTraceAgg::getClientCode,DataTraceAgg::getDataNum,(k1,k2)->k1));
        Set<String> clientNameList = dataTraceAggList.stream()
                .filter(dataTraceAgg->"publisher".equals(dataTraceAgg.getDataSource())||StringUtils.isEmpty(dataTraceAgg.getDataSource()))
                .map(DataTraceAgg::getClientCode).collect(Collectors.toSet());
        for(String clientCode : clientNameList){
            //基础数据mq SRC开头，其他源平台clientName为空
            if(EmptyUtils.isEmpty(clientCode) || clientCode.startsWith(ClientDescMenu.SRC_DESC.getCode())) {
                continue;
            }
            DataTraceAgg lastOne = getLastDataTraceAgg(dataTraceAggList, clientCode);
            List<DataTraceAgg> lastList = listLastDataTraceAgg(dataTraceAggList, lastOne);

            FlBatchInfo batchInfo = new FlBatchInfo();
            batchInfo.setMainBatchNo(srcBatchInfo.getMainBatchNo());
            batchInfo.setBatchNo(srcBatchInfo.getBatchNo());
            batchInfo.setDataType(srcBatchInfo.getDataType());
            batchInfo.setServiceName(lastOne.getServiceName());
            batchInfo.setNodeName(lastOne.getNodeName());
            batchInfo.setClientCode(lastOne.getClientCode());
            batchInfo.setUTime(new Date());
            batchInfo.setCTime(srcBatchInfo.getCTime());
            batchInfo.setStartTime(startTime);
            batchInfo.setEndTime(new Date(lastOne.getEndTime()));
            batchInfo.setDataSource("publisher");
            List<DataTraceAgg> clientLastList = ListUtils.findObjFromList(lastList, "clientCode", clientCode);
            //  logger.warn("---------1------clientCode:-" + clientCode + "----batchNo:"  + srcBatchInfo.getBatchNo() + "---total:" + totalList.containsKey(clientCode));
            int totalNum = totalList.get(clientCode)==null?0:totalList.get(clientCode);
            batchInfo = assembleNumAndStatus(totalNum, batchInfo, clientLastList);
            returnList.add(batchInfo);
        }

        /*
        List<DataTraceAgg> transList = dataTraceAggList.stream()
                .filter(dataTraceAgg -> "transfer".equals(dataTraceAgg.getDataSource()))
                .collect(Collectors.toList());

        if(!CollectionUtils.isEmpty(transList)){
            for(DataTraceAgg dataTraceAgg : transList){
                FlBatchInfo batchInfo = new FlBatchInfo();
                batchInfo.setMainBatchNo(srcBatchInfo.getMainBatchNo());
                batchInfo.setBatchNo(srcBatchInfo.getBatchNo());
                batchInfo.setDataType(srcBatchInfo.getDataType());
                batchInfo.setServiceName(dataTraceAgg.getServiceName());
                batchInfo.setNodeName(dataTraceAgg.getNodeName());
                batchInfo.setClientCode(dataTraceAgg.getClientCode());
                batchInfo.setUTime(new Date());
                batchInfo.setCTime(srcBatchInfo.getCTime());
                batchInfo.setStartTime(startTime);
                batchInfo.setEndTime(new Date(dataTraceAgg.getEndTime()));
                batchInfo.setStatus(dataTraceAgg.getStatus());
                batchInfo.setMessage(dataTraceAgg.getMessage());
                batchInfo.setDataSource("transfer");
                returnList.add(batchInfo);
            }
        }
        */
        return returnList;
    }

    /**
     * 获取最后一步的所有节点
     * @param dataTraceAggList
     * @param lastOne
     * @return
     */
    private List<DataTraceAgg> listLastDataTraceAgg(List<DataTraceAgg> dataTraceAggList, DataTraceAgg lastOne) {
        Map<String, Object> map = new HashMap<>();
        map.put("serviceName", lastOne.getServiceName());
        map.put("nodeName", lastOne.getNodeName());
        return ListUtils.findObjFromList(dataTraceAggList, map);
    }

    /**
     * 获取最后一步的节点
     * @param dataTraceAggList
     * @return
     */
    private DataTraceAgg getLastDataTraceAgg(List<DataTraceAgg> dataTraceAggList, String clientCode) {
        DataTraceAgg lastOne = null;

        //依赖list是时间顺序
//        dataTraceAggList.sort((a, b) -> (int) (a.getStartTime() - b.getStartTime()));
        for(DataTraceAgg dataTraceAgg : dataTraceAggList){
            if(EmptyUtils.isEmpty(dataTraceAgg.getClientCode()) || clientCode.equals(dataTraceAgg.getClientCode())){
                lastOne = dataTraceAgg;
            }
        }
//        Map<String, String> map = new HashMap<>();
//        map.put("serviceName", "客户端");
//        map.put("nodeName", "数据更新");
//        map.put("clientName", clientName);
//        List<DataTraceAgg> lastList = ListUtils.findObjFromList(dataTraceAggList, map);
//        if(EmptyUtils.isNotEmpty(lastList)){
//            lastOne = lastList.get(0);
//        } else {
//            map = new HashMap<>();
//            map.put("serviceName", "客户端");
//            map.put("nodeName", "数据更新");
//            map.put("clientName", "clientName");
//            List<DataTraceAgg> lastList = ListUtils.findObjFromList(dataTraceAggList, map);
//            lastOne = dataTraceAggList.get(dataTraceAggList.size() - 1);
//        }
        return lastOne;
    }

    /**
     * 预设批次总数。
     * 计算成功数： 最后一个节点的所有发送成功数量
     * 计算失败数量： 最后一次重发的失败数量
     */
    private FlBatchInfo assembleNumAndStatus(int totalNum, FlBatchInfo batchInfo, List<DataTraceAgg> clientList) {
        //  int multiple = fieldOrgMpService.getBatchNum(batchInfo.getBatchNo(), batchInfo.getClientCode());
        //    totalNum = multiple * totalNum;
        //  int maxSendTimes = 0;
        Map<Integer, Integer> successMap = new HashMap<>();
        //  Map<Integer, Integer> unsuccessMap = new HashMap<>();
        for(DataTraceAgg dataTraceAgg : clientList){
            if(ResultStatus.SUCCESS.getStatus().equals(dataTraceAgg.getStatus()) && DataTraceMenu.CLIENT_DEAL_DESC.getName().equals(dataTraceAgg.getNodeName())){
                successMap.put(dataTraceAgg.getSendTimes(), dataTraceAgg.getDataNum());
                //  unsuccessMap.put(dataTraceAgg.getSendTimes(), 0);
            }
          /*  if(maxSendTimes < dataTraceAgg.getSendTimes()){
                maxSendTimes = dataTraceAgg.getSendTimes();
            }*/
        }

     /*   for(DataTraceAgg dataTraceAgg : clientList){
            if (ResultStatus.INTERNAL_SERVER_ERROR.getStatus().equals(dataTraceAgg.getStatus())){
                failMap.put(dataTraceAgg.getSendTimes(), dataTraceAgg.getDataNum());
            }
        }*/
        int successNum = 0;
     /*   int failNum = 0;
        if(EmptyUtils.isNotEmpty(failMap)){
            failNum = failMap.get(maxSendTimes);
        }*/
        for(Map.Entry<Integer, Integer> entry : successMap.entrySet()){
            successNum = successNum + entry.getValue();
        }

        batchInfo.setSuccessNum(successNum);
        int unsuccessNum = 0;
        if(totalNum > 0){
            unsuccessNum = totalNum - successNum;
        }
        batchInfo.setUnsuccessNum(unsuccessNum);
        /*batchInfo.setFailNum(failNum);
        int processingNum = totalNum - successNum - failNum;
        batchInfo.setProcessingNum(processingNum > 0 ? processingNum : 0);*/
        batchInfo.setStatus(ResultStatus.SUCCESS.getStatus());
        batchInfo.setMessage(ResultStatus.SUCCESS.getMessage());
        if(batchInfo.getUnsuccessNum() > 0){
            batchInfo.setStatus(ResultStatus.INTERNAL_SERVER_ERROR.getStatus());
            batchInfo.setMessage(ResultStatus.INTERNAL_SERVER_ERROR.getMessage());

        }
     /*   if(batchInfo.getProcessingNum() > 0){
            batchInfo.setStatus(ResultStatus.PROCESSING.getStatus());
            batchInfo.setMessage(ResultStatus.PROCESSING.getMessage());
        }
        if(batchInfo.getFailNum() > 0){
            batchInfo.setStatus(ResultStatus.INTERNAL_SERVER_ERROR.getStatus());
            batchInfo.setMessage(ResultStatus.INTERNAL_SERVER_ERROR.getMessage());

        }*/
        return batchInfo;
    }
}

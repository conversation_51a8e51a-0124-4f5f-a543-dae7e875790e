package com.jy.task.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jy.ann.ExceptionMonitor;
import com.jy.bean.common.DataTraceMenu;
import com.jy.bean.dto.BaseDataDTO;
import com.jy.bean.dto.BaseDataDTOs;
import com.jy.bean.po.BatchDetail;
import com.jy.bean.po.Client;
import com.jy.bean.result.ResultStatus;
import com.jy.mq.RabbitCommon;
import com.jy.service.BatchDetailService;
import com.jy.service.ClientService;
import com.jy.task.BaseDataTask;
import com.jy.task.QueueLimitTask;
import com.jy.util.DateUtil;
import com.jy.util.EmptyUtils;
import com.jy.util.FileUtils;
import com.jy.util.ToolUtils;
import com.jy.util.rabbitmq.DataTraceUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * @program: data-publisher-server
 * @description:
 * @author: Ykuee
 * @create: 2024-04-30 16:55
 **/
@Service
public class BaseDataTaskService {

    private static final Logger logger = LogManager.getLogger(BaseDataTaskService.class);

    @Autowired
    private RabbitCommon rabbitCommon;
    @Autowired
    private BatchDetailService batchDetailService;

    @Value("${srcData.filePath}")
    private  String filePath;


    @ExceptionMonitor(methodName = "BaseDataTask.sendData", remark = "定时任务")
    public void sendData(String clientCode, BatchDetail batchDetail) throws Exception {
        Timestamp time = DateUtil.crunttime();
        int sendTimes = 1;
        try {
            String json = FileUtils.readToString(filePath + batchDetail.getFilePath());
            List<BaseDataDTO> baseDataDTOList = this.fitData(json, clientCode);
            if(EmptyUtils.isNotEmpty(baseDataDTOList)){
                sendTimes = baseDataDTOList.get(0).getSendTimes();
                for(BaseDataDTO baseDataDTO : baseDataDTOList){
                    rabbitCommon.sendQueue(clientCode, JSONObject.toJSONString(baseDataDTO));
                }
                //this.sendData(client.getCode(), baseDataDTOList);
                batchDetail.setStatus(ResultStatus.SUCCESS_ACCEPTED.getStatus());
                batchDetailService.update(batchDetail);
                DataTraceUtils.sendTrace(null, (JSONObject) JSON.toJSON(batchDetail), DataTraceMenu.PUBLISH_PUSH_DESC.getCode(), clientCode, ResultStatus.SUCCESS.getStatus(), ResultStatus.SUCCESS.getMessage(), time, sendTimes);
            }

        } catch (Exception e) {
            String message = ToolUtils.getExceptionMsg(e);
            DataTraceUtils.sendTrace(null, (JSONObject) JSON.toJSON(batchDetail), DataTraceMenu.PUBLISH_PUSH_DESC.getCode(), clientCode, ResultStatus.INTERNAL_SERVER_ERROR.getStatus(), message, time, sendTimes);
            logger.error("推送数据至" + clientCode + "失败:" + message);
            throw e;
        }
    }

    public List<BaseDataDTO> fitData(String json,String clientCode) throws Exception {
        if(StringUtils.isBlank(json)){
            return null;
        }
        BaseDataDTOs baseDataDTOs = JSON.parseObject(json, BaseDataDTOs.class);
        if(baseDataDTOs == null || EmptyUtils.isEmpty(baseDataDTOs.getData())){
            return null;
        }
        List<BaseDataDTO> list = baseDataDTOs.getData();

        int sendTimes = 1;//dataTraceService.getSendTimesByNodeNameAndBatchNo(DataTraceMenu.PUBLISH_PUSH_DESC.getName(), baseDataDTOs.getBatchNo(), clientCode, DataTraceMenu.PUBLISH_PUSH_DESC.getServiceName(), ResultStatus.SUCCESS.getStatus());
        BaseDataDTO end = new BaseDataDTO(baseDataDTOs.getMainBatchNo(), baseDataDTOs.getBatchNo(), sendTimes);
        list.add(end);
        for(BaseDataDTO baseDataDTO : list){
            baseDataDTO.setMainBatchNo(baseDataDTOs.getMainBatchNo());
            baseDataDTO.setBatchNo(baseDataDTOs.getBatchNo());
            baseDataDTO.setTableName(baseDataDTOs.getTableName());
            baseDataDTO.setSqlType(baseDataDTOs.getSqlType());
            baseDataDTO.setOrgCode(baseDataDTOs.getOrgCode());
            baseDataDTO.setClientCode(baseDataDTOs.getClientCode());
            baseDataDTO.setClientUrl(baseDataDTOs.getClientUrl());
            baseDataDTO.setSendTimes(sendTimes);
            if("SRC_VEH".equals(clientCode)  && "facade_fc_company,facade_fc_group,facade_fc_family,facade_fc_brand".contains(baseDataDTO.getTableName())){
                String time = DateUtil.convertDateToString(DateUtil.timePattern, new Date());
                if("insert".equals(baseDataDTO.getOperate())){
                    baseDataDTO.getFields().put("jlrq", time);
                    baseDataDTO.getFields().put("scbz", "0");
                    if("facade_fc_brand".contains(baseDataDTO.getTableName())){
                        baseDataDTO.getFields().put("existsPartFlag", "0");
                    }
                }
                if(EmptyUtils.isEmpty(baseDataDTO.getBatchNoStatus())){
                    baseDataDTO.getFields().put("xgrq", time);
                }
            }
        }
        return list;
    }

}

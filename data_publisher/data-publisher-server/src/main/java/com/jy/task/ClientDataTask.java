package com.jy.task;

import com.alibaba.fastjson.JSONObject;
import com.jy.util.DateUtil;
import com.jy.util.FileUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * @Author: caolt
 * @Description:
 * @Version:
 * @Date: Created in  2020/07/10
 */
@Component
@EnableScheduling
public class ClientDataTask {
    private static final Logger logger = LogManager.getLogger(ClientDataTask.class);
    private LinkedBlockingQueue<JSONObject> clientDataQueue = new LinkedBlockingQueue(10000000);


    @Value("${clientData.filePath}")
    private String filePath;

    @Scheduled(cron="0 0/1 * * * ?")
    public void srcClientPush() {
        try {
            Map<String, List<String>> map = new HashMap<>(100000);
            while(clientDataQueue.size() > 0){
                JSONObject obj = clientDataQueue.poll();
                String clientFilePath = filePath + DateUtil.getDateByBatchNo(obj.getString("batchNo")) + File.separator + obj.getString("clientCode") + File.separator + obj.getString("batchNo") + ".json";
                if(!map.containsKey(clientFilePath)){
                    map.put(clientFilePath, new ArrayList<>(1000000));
                }
                map.get(clientFilePath).add(obj.toJSONString());
            }
            for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                FileUtils.appendFile(entry.getKey(), entry.getValue());
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    public boolean addDataQueue(JSONObject json){
        return clientDataQueue.add(json);
    }
}

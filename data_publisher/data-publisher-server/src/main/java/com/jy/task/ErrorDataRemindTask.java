package com.jy.task;

import com.jy.ann.ExceptionMonitor;
import com.jy.bean.common.ClientDescMenu;
import com.jy.bean.common.PublishTypeEnum;
import com.jy.bean.po.Client;
import com.jy.bean.po.FlBatchInfo;
import com.jy.bean.po.NoticeConfig;
import com.jy.bean.result.ResultStatus;
import com.jy.mq.RabbitCommon;
import com.jy.service.ClientService;
import com.jy.service.FlBatchInfoService;
import com.jy.service.NoticeConfigService;
import com.jy.task.service.ErrorDataRemindTaskService;
import com.jy.util.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@EnableScheduling
public class ErrorDataRemindTask {
    private static final Logger logger = LogManager.getLogger(ErrorDataRemindTask.class);

    private static long ONE_HOUR = 60 * 60 * 1000;
    private static long ONE_DAY = ONE_HOUR * 24;

    @Autowired
    private ErrorDataRemindTaskService errorDataRemindTask;

    @Scheduled(cron="00 00 09 * * ?")
    public void errorDataRemindTaskOne() {
        try{
            long endTime = System.currentTimeMillis();
            long startTime = endTime - ONE_DAY;

            errorDataRemindTask.errorDataRemind(new Date(startTime), new Date(endTime));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("ErrorDataRemindTask定时执行失败:" + ToolUtils.getExceptionMsg(e));
        }
    }

    @Scheduled(cron="00 00 17 * * ?")
    public void errorDataRemindTaskTwo() {
        try{
            long endTime = System.currentTimeMillis() - ONE_HOUR * 1;
            long startTime = endTime - ONE_HOUR * 12;

            errorDataRemindTask.errorDataRemind(new Date(startTime), new Date(endTime));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("ErrorDataRemindTask定时执行失败:" + ToolUtils.getExceptionMsg(e));
        }
    }

}

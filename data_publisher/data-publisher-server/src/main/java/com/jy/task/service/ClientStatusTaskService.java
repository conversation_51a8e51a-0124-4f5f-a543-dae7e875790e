package com.jy.task.service;

import com.jy.ann.ExceptionMonitor;
import com.jy.bean.common.ClientStatus;
import com.jy.bean.po.Client;
import com.jy.service.ClientService;
import com.jy.service.RedisService;
import com.jy.service.SendExpMsgService;
import com.jy.task.ClientStatusTask;
import com.jy.util.CommonUtils;
import com.jy.util.DateUtil;
import com.jy.util.ToolUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Set;

/**
 * @program: data-publisher-server
 * @description:
 * @author: Ykuee
 * @create: 2024-04-30 17:15
 **/

@Service
public class ClientStatusTaskService {

    private static final Logger logger = LogManager.getLogger(ClientStatusTask.class);

    @Autowired
    private ClientService clientService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private CommonUtils commonUtils;
    @Autowired
    private SendExpMsgService sendExpMsgService;

    @ExceptionMonitor(methodName = "clientStatusTask.check", remark = "客户端心跳检测")
    public void check() throws Exception {
        try {
            //取出失效的数据  更新数据库数据  把失效的数据置于down
            Client client = new Client();
            Object temp = redisService.getClient();
            if (temp != null) {
                Set<Object> clientSet = (Set<Object>) temp;
                for (Object str : clientSet) {
                    if (!redisService.hasKey(str.toString())) {
                        //失效数据置于down
                        client.setClient(str.toString(), ClientStatus.CLIENT_DOWN);
                        clientService.update(client);
                        //删除set中元素
                        redisService.removeClient(str.toString());
                        if(ClientStatus.CLIENT_DOWN.equals(client.getStatus())){
                            // 开始时间
                            LocalDateTime startTime = LocalDateTime.now();
                            // 结束时间
                            String msg = "即时更新：" + client.getCode() + "客户端在" + DateUtil.convertDateToString(DateUtil.milliPattern, new Date()) + "宕机。";
                            String subject = "即时更新" + client.getCode() + "宕机提醒";
                            sendExpMsgService.sendMsg("clientStatusTask.check", startTime, msg);
                            //昨日有失败数据，邮件提醒
                            commonUtils.sendEmail(subject, msg);
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("检测客户端心跳发生异常:" + ToolUtils.getExceptionMsg(e));
            throw e;
        }
    }

}

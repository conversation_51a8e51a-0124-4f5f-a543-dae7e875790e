package com.jy.task;

import com.jy.util.DateUtil;
import com.jy.util.FileUtils;
import com.jy.util.ToolUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.File;

/**
 * <AUTHOR>
 * @date 2019/12/4
 */
@Component
@EnableScheduling
public class CleanFileTask {
    private static final Logger logger = LogManager.getLogger(CleanFileTask.class);

    @Value("${srcData.filePath}")
    private String filePath;

    /**
     * 定时清除60天前的发送数据
     * @throws Exception
     */
    @Scheduled(cron="0 55 23 * * ?")
    public void cleanFile() {
        try {
            File srcFile = new File(filePath);
            File[] childFiles = srcFile.listFiles();
            for(File file : childFiles){
                String name = file.getName();
                long num = DateUtil.getBetweenDay(name);
                if(num > 60){
                    FileUtils.delete(file);
                }
            }
        } catch (Exception e) {
            logger.error("cleanFile定时执行失败:" + ToolUtils.getExceptionMsg(e));
        }
    }
}

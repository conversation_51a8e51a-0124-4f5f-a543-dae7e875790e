package com.jy.task.service;

import com.jy.ann.ExceptionMonitor;
import com.jy.bean.common.ClientDescMenu;
import com.jy.bean.common.PublishTypeEnum;
import com.jy.bean.po.Client;
import com.jy.bean.po.FlBatchInfo;
import com.jy.bean.po.NoticeConfig;
import com.jy.bean.result.ResultStatus;
import com.jy.mq.RabbitCommon;
import com.jy.service.ClientService;
import com.jy.service.FlBatchInfoService;
import com.jy.service.NoticeConfigService;
import com.jy.util.CommonUtils;
import com.jy.util.DateUtil;
import com.jy.util.EmptyUtils;
import com.jy.util.SqlUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: data-publisher-server
 * @description:
 * @author: Y<PERSON>ee
 * @create: 2024-04-30 17:00
 **/

@Service
public class ErrorDataRemindTaskService {

    @Autowired
    private CommonUtils commonUtils;
    @Autowired
    private FlBatchInfoService flBatchInfoService;
    @Autowired
    private NoticeConfigService noticeConfigService;
    @Autowired
    private RabbitCommon rabbitCommon;
    @Autowired
    private ClientService clientService;

    @ExceptionMonitor(methodName = "errorDataRemindTask.errorDataRemindTaskOne", remark = "定时任务")
    public void errorDataRemind(Date startTime, Date endTime) throws Exception {
        List<Client> clients = clientService.listClient(new HashMap<>());
        Map<String, String> clientMap = clients.stream().collect(Collectors.toMap(Client::getCode, Client::getName));
        Map<String, Object> map = new HashMap<>();
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        map.put("dataSource", "publisher");
        List<FlBatchInfo> batchInfoList = flBatchInfoService.listFlBatchInfo(map);
        Map<String, List<FlBatchInfo>> flBatchInfoMap = batchInfoList.stream().filter(flBatchInfo-> !ResultStatus.SUCCESS.getStatus().equals(flBatchInfo.getStatus()) && !flBatchInfo.getClientCode().contains(ClientDescMenu.SRC_DESC.getCode()))
                .collect(Collectors.groupingBy(FlBatchInfo::getClientCode));
        Map<String, String> flBatchInfos = new HashMap<>();

        //客户端编码维度
        flBatchInfoMap.forEach((k, v)->{
            int mqCount = rabbitCommon.getCount(k);
            //数据类型维度
            Map<String, List<FlBatchInfo>> batchMap = v.stream().collect(Collectors.groupingBy(flBatchInfo->flBatchInfo.getBatchNo().split("_")[0]));
            batchMap.forEach((k1,v1)->{
                int failNum = v1.stream().collect(Collectors.summingInt(FlBatchInfo::getUnsuccessNum));
                String message = "数据类型：" + PublishTypeEnum.nameOf(k1) + "。 处理失败" + failNum + "条。当前mq剩余数量" + mqCount + "条。" ;// "客户端名称：" + v.get(0).getClientName() +
                flBatchInfos.put(k + k1, message);
            });
        });
        Set<String> clientCodes = flBatchInfoMap.keySet();
        List<NoticeConfig> noticeConfigs = noticeConfigService.listByClientCodes(SqlUtils.List2SqlInString(new ArrayList<>(clientCodes)));
        Map<String, List<NoticeConfig>>  noticeMaps = noticeConfigs.stream().collect(Collectors.groupingBy(NoticeConfig::getUsername));
        noticeMaps.forEach((k,v)->{
            String message = "";
            for(NoticeConfig noticeConfig : v){
                String key = noticeConfig.getClientCode();
                String title = "客户端名称：" + clientMap.get(key) + "。";
                String data = "";
                String[] batchTypes = noticeConfig.getBatchType().split(",");

                for(String str : batchTypes){
                    if(flBatchInfos.containsKey(key + str)){
                        data += flBatchInfos.get(key + str)  + "\b\b\b\b";
                    }
                }
                if(EmptyUtils.isNotEmpty(data)){
                    message += title + data + "\n";
                }
            };
            System.out.println(message);
            //根据推送方式处理;
            if(EmptyUtils.isNotEmpty(message)){
                String[] noticeTypes = v.get(0).getNoticeType().split(",");
                String emailTo = v.get(0).getEmail();
                String phoneNumber = v.get(0).getPhoneNumber();
                String username = v.get(0).getUsername();
                for(String str : noticeTypes){
                    switch (str) {
                        case "sms":
                            String data = DateUtil.convertDateToString("yyyy-MM-dd HH:mm:ss", startTime) + "," + DateUtil.convertDateToString("yyyy-MM-dd HH:mm:ss", endTime) + "," + message;
                            commonUtils.sendSms(phoneNumber, data);
                            continue;
                        case "email":
                            String mes = "在" + DateUtil.convertDateToString("yyyy-MM-dd HH:mm:ss", startTime) + "至" + DateUtil.convertDateToString("yyyy-MM-dd HH:mm:ss", endTime) + "时间段内，失败数据情况：\n";
                            mes += "----------------------------------------------------\n";
                            mes += message;
                            String subject = "即时更新数据处理失败提醒";
                            //昨日有失败数据，邮件提醒
                            commonUtils.sendEmail(subject, mes, emailTo);
                            continue;
                        case "workWechat":
                            String workWechatMessage = "在" + DateUtil.convertDateToString("yyyy-MM-dd HH:mm:ss", startTime) + "至" + DateUtil.convertDateToString("yyyy-MM-dd HH:mm:ss", endTime) + "时间段内，失败数据情况：\n";
                            workWechatMessage += "-----------------------------------------------\n";
                            workWechatMessage += message;
                            commonUtils.sendWorkWechatPath(username, workWechatMessage);
                            continue;
                        default:
                            continue;
                    }
                }

            }
        });

    }
}

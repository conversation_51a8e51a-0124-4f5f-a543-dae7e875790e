package com.jy.task;

import com.alibaba.fastjson.JSONObject;
import com.jy.bean.common.ClientSlowCache;
import com.jy.bean.common.DataTraceMenu;
import com.jy.bean.po.Client;
import com.jy.bean.result.ResultStatus;
import com.jy.mq.RabbitCommon;
import com.jy.service.ClientService;
import com.jy.util.EmptyUtils;
import com.jy.util.StringUtils;
import com.jy.util.ToolUtils;
import com.jy.util.rabbitmq.DataTraceUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@EnableScheduling
public class QueueLimitTask {
    private static final Logger logger = LogManager.getLogger(QueueLimitTask.class);

    private  int clienttoplimit = 1000000 ;

    @Autowired
    private RabbitCommon rabbitCommon;
    @Autowired
    private ClientService clientService;

    @Scheduled(cron="0 0/1 * * * ?")
    public void queueLimit() {
        try {
            List<Client> clients = clientService.listByNoOfflineStatus();
            for(Client client : clients){
                //监听mq客户端数量，达到临界值放入mongodb中   等待手动重新发送
                if(EmptyUtils.isEmpty(ClientSlowCache.clientCache.get(client.getCode()))){
                    ClientSlowCache.clientCache.put(client.getCode(), false);
                }
                int count = rabbitCommon.getCount(client.getCode());
                int topLimit = (EmptyUtils.isEmpty(client.getTopLimit()) || client.getTopLimit() == 0 )? clienttoplimit : client.getTopLimit();
                if(count >= topLimit && !ClientSlowCache.clientCache.get(client.getCode())){
                    ClientSlowCache.clientCache.put(client.getCode(),true);
                    this.exportQueue(client.getCode(), topLimit);
                }
            }
        } catch (Exception e) {
            logger.error("queueLimit定时执行失败:" + ToolUtils.getExceptionMsg(e));
        }
    }

    public void exportQueue(String clientCode, int topLimit) throws Exception {
        for(int i=0; i<topLimit; i++){
            String message = rabbitCommon.processQueue(clientCode);
            JSONObject json = JSONObject.parseObject(message);
            DataTraceUtils.sendTrace(StringUtils.getUUID(), json, DataTraceMenu.CLIENT_DEAL_DESC.getCode(), clientCode, ResultStatus.INTERNAL_SERVER_ERROR.getStatus(), "触发MQ数量上限");
        }
        ClientSlowCache.clientCache.put(clientCode, false);
    }

}

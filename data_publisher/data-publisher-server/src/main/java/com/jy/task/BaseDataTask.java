package com.jy.task;

import com.jy.bean.common.ClientDescMenu;
import com.jy.bean.common.ClientStatus;
import com.jy.bean.po.BatchDetail;
import com.jy.bean.po.Client;
import com.jy.bean.result.ResultStatus;
import com.jy.mq.RabbitCommon;
import com.jy.service.BatchDetailService;
import com.jy.service.ClientService;
import com.jy.task.service.BaseDataTaskService;
import com.jy.util.ToolUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

@Component
@EnableScheduling
public class BaseDataTask {
    private static final Logger logger = LogManager.getLogger(BaseDataTask.class);

    @Autowired
    private ClientService clientService;
    @Autowired
    private RabbitCommon rabbitCommon;
    @Autowired
    private BatchDetailService batchDetailService;
    @Autowired
    private BaseDataTaskService baseDataTask;

    private static Map<String, Boolean> srcClientStatusMap = new HashMap<>();


    @Scheduled(cron="0 0/3 * * * ?")
    public void srcClientPush() {
        try {
            List<Client> srcClients = clientService.listClientByPath(ClientDescMenu.SRC_DESC.getCode());
            //配件即时更新单独处理，不在3分定时中
            for(Client client : srcClients){
                if(!"SRC_PART".equals(client.getCode()) && !"SRC_VIN_PART".equals(client.getCode()) && !"SRC_WAREHOUSE".equals(client.getCode())){
                    Client temp = clientService.getOneByCode(client.getCode());
                    if(ClientStatus.CLIENT_UP.equals(temp.getStatus())) {
                        if (!srcClientStatusMap.containsKey(client.getCode())) {
                            srcClientStatusMap.put(client.getCode(), true);
                        }
                        if (srcClientStatusMap.get(client.getCode())) {
                            dataPush(client);
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("srcClientPush定时执行失败:" + ToolUtils.getExceptionMsg(e));
        }
    }

    /**
     * 此定时为单独处理配件数据使用，因为配件紧急推送实时性要求过高
     */
    @Scheduled(cron="0/1 * * * * ?")
    public void srcPartPush() {
        try {
            String srcClientCode = "SRC_PART";
            //判定开关是否打开，关闭则不推送数据
            Client temp = clientService.getOneByCode(srcClientCode);
            if(ClientStatus.CLIENT_UP.equals(temp.getStatus())){
                //配件即时更新单独处理，不在3分定时中,实时判断Facade-Part和src的mq中数量
                int doneNum = rabbitCommon.getCount("FACADE-PART") + rabbitCommon.getCount(srcClientCode);
                List<BatchDetail> batchDetails = batchDetailService.listByClientCodeAndStatus(srcClientCode, ResultStatus.UNSENT.getStatus());
                Iterator<BatchDetail> it = batchDetails.iterator();
                while (doneNum < temp.getTopLimit() && it.hasNext()) {
                    //取数据放入srcMQ中，直到大于上限数据
                    BatchDetail batchDetail = it.next();
                    baseDataTask.sendData(srcClientCode, batchDetail);
                    doneNum = rabbitCommon.getCount(srcClientCode);
                }
            }
        } catch (Exception e) {
            logger.error("srcPartPush定时执行失败:" + ToolUtils.getExceptionMsg(e));
        }
    }

    /**
     * 此定时为单独处理精准定件数据使用，因为精准定件紧急推送实时性要求过高
     */
    @Scheduled(cron="0/1 * * * * ?")
    public void srcVinPartPush() {
        try {
            String srcClientCode = "SRC_VIN_PART";
            Client temp = clientService.getOneByCode(srcClientCode);
            if(ClientStatus.CLIENT_UP.equals(temp.getStatus())){
                //精准定件即时更新单独处理，不在3分定时中,实时判断Facade-vin-Part和src的mq中数量
                int doneNum = rabbitCommon.getCount("FACADE-VIN-PART") + rabbitCommon.getCount(srcClientCode);
                List<BatchDetail> batchDetails = batchDetailService.listByClientCodeAndStatus(srcClientCode, ResultStatus.UNSENT.getStatus());
                Iterator<BatchDetail> it = batchDetails.iterator();
                while (doneNum < temp.getTopLimit() && it.hasNext()) {
                    //取数据放入srcMQ中，直到大于上限数据
                    BatchDetail batchDetail = it.next();
                    baseDataTask.sendData(srcClientCode, batchDetail);
                    doneNum = rabbitCommon.getCount(srcClientCode);
                }
            }
        } catch (Exception e) {
            logger.error("srcVinPartPush定时执行失败:" + ToolUtils.getExceptionMsg(e));
        }
    }

    public void dataPush(Client client){
        BaseDataTask.srcClientStatusMap.put(client.getCode(), false);
        try {
            List<BatchDetail> batchDetails = batchDetailService.listByClientCodeAndStatus(client.getCode(), ResultStatus.UNSENT.getStatus());
            int doneNum = rabbitCommon.getCount(client.getCode());
            Iterator<BatchDetail> it = batchDetails.iterator();
            while (doneNum < client.getTopLimit() && it.hasNext()) {
                //取数据放入srcMQ中，直到大于上限数据
                BatchDetail batchDetail = it.next();
                baseDataTask.sendData(client.getCode(), batchDetail);
                doneNum = rabbitCommon.getCount(client.getCode());
            }
            BaseDataTask.srcClientStatusMap.put(client.getCode(), true);
        } catch (Exception e) {
            logger.error("推送数据至客户端失败:" + ToolUtils.getExceptionMsg(e));
            BaseDataTask.srcClientStatusMap.put(client.getCode(), true);
        }
    }

}

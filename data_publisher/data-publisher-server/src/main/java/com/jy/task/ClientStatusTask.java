package com.jy.task;

import com.jy.task.service.ClientStatusTaskService;
import com.jy.util.ToolUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @date 2018/4/16
 */
@Component
@EnableScheduling
public class ClientStatusTask {
    private static final Logger logger = LogManager.getLogger(ClientStatusTask.class);

    @Autowired
    private ClientStatusTaskService clientStatusService;

    @Scheduled(cron="0 0/12 * * * ?")
    public void timerInit() {
        try {
            clientStatusService.check();
        } catch (Exception e) {
            logger.error("timerInit定时执行失败:" + ToolUtils.getExceptionMsg(e));
        }
    }

}

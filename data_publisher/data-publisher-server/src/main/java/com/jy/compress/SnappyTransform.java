package com.jy.compress;

import com.alibaba.fastjson.JSON;
import com.jy.bean.po.Zip;
import org.springframework.stereotype.Service;
import org.xerial.snappy.Snappy;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2018/6/14
 */
@Service
public class SnappyTransform implements CompressionTool {

    @Override
    public Object compress(Object obj){
        byte[] srcBytes = null;
        try {
            srcBytes = JSON.toJSONString(obj).getBytes("UTF-8");
            Zip zip = new Zip();
            zip.setCompressType("Snappy");
            zip.setResult(Snappy.compress(srcBytes));
            return zip;
        } catch (IOException e) {
            return obj;
        }
    }

    @Override
    public byte[] uncompress(byte[] obj){
        try {
            return Snappy.uncompress(obj);
        } catch (IOException e) {
            return obj;
        }
    }
}

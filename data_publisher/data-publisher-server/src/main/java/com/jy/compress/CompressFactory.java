package com.jy.compress;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2018/6/14
 */
@Component
public class CompressFactory {
    @Autowired
    private GZipTransform gZipTransform;
    @Autowired
    private SnappyTransform snappyTransform;
    @Autowired
    private DefaultTransform defaultTransform;

    public CompressionTool create(String data) {
        int len = data.length()/1024;
        if(len < 300){
            return gZipTransform;
        } else if(len >= 300){
            return snappyTransform;
        } else {
            return defaultTransform;
        }
    }

}

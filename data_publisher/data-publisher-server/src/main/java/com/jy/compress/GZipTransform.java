package com.jy.compress;

import com.alibaba.fastjson.JSON;
import com.jy.bean.po.Zip;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * <AUTHOR>
 * @date 2018/6/14
 */
@Service
public class GZipTransform implements CompressionTool {

    @Override
    public Object compress(Object obj) {
        byte[] srcBytes = null;
        try {
            srcBytes = JSON.toJSONString(obj).getBytes("UTF-8");
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            GZIPOutputStream gzip = new GZIPOutputStream(out);
            gzip.write(srcBytes);
            gzip.close();
            Zip zip = new Zip();
            zip.setCompressType("GZip");
            zip.setResult(out.toByteArray());
            return zip;
        } catch (IOException e) {
            return obj;
        }

    }

    @Override
    public byte[] uncompress(byte[] obj) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream in = new ByteArrayInputStream(obj);
        try {
            GZIPInputStream ungzip = new GZIPInputStream(in);
            byte[] buffer = new byte[2048];
            int n;
            while ((n = ungzip.read(buffer)) >= 0) {
                out.write(buffer, 0, n);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return out.toByteArray();
    }
}

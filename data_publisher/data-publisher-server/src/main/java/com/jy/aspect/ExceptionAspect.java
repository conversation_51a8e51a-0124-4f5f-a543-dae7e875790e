package com.jy.aspect;

import com.jy.ann.ExceptionMonitor;
import com.jy.exception.BusinessException;
import com.jy.service.SendExpMsgService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;

@Aspect
@Component
public class ExceptionAspect {

    private static final Logger log = LogManager.getLogger(ExceptionAspect.class);

    @Autowired
    private SendExpMsgService sendExpMsgService;

    // 先创建一个方法，方法名随意，但是需要制定@annotation为刚刚自定义的注解
    @Pointcut("@annotation(com.jy.ann.ExceptionMonitor)")
    public void annPoint() {
    }

    @Around("annPoint() && @annotation(exceptionMonitor)")
    public Object doAround(ProceedingJoinPoint point, ExceptionMonitor exceptionMonitor) throws Throwable {
        Object result = null;
        // 开始时间
        LocalDateTime startTime = LocalDateTime.now();
        try {
            // 执行完方法的返回值：调用proceed()方法，就会触发切入点方法执行
            // result的值就是被拦截方法的返回值
            result = point.proceed();

            //endTime = new Date();
            //this.writeLog(point, exceptionMonitor, startTime, endTime);
        } catch (BusinessException e) {
            log.error("环绕通知打印异常 BusinessException: ", e);
            log.error("BusinessExceptionMsg: {}", e.getMsg());
            // 结束时间
            LocalDateTime endTime = LocalDateTime.now();
            this.sendMsg(point, exceptionMonitor, startTime, endTime, e);
            throw e;
        } catch (Exception e) {
            log.error("环绕通知打印异常 Exception: ", e);
            // 结束时间
            LocalDateTime endTime = LocalDateTime.now();
            this.sendMsg(point, exceptionMonitor, startTime, endTime, e);
            throw e;
        }
        return result;
    }

    /***记录日志*/
    private void writeLog(JoinPoint point, ExceptionMonitor myTask, Date startTime, Date endTime) {
        log.info("记录日志 方法名:{}, startTime:{}, endTime:{}", myTask.methodName(), startTime, endTime);
    }

    /***记录日志*/
    private void sendMsg(JoinPoint point, ExceptionMonitor monitor, LocalDateTime startTime, LocalDateTime endTime, BusinessException e) {
        log.info("记录日志 方法名:{}, startTime:{}, endTime:{}", monitor.methodName(), startTime, endTime);
        try {
            sendExpMsgService.sendMsg(monitor, startTime, endTime, point.getArgs(), e.getMsg());
        }catch (Exception exception){
            log.error("及时更新发送报错信息消息时发生异常", exception);
        }
    }
    /***记录日志*/
    private void sendMsg(JoinPoint point, ExceptionMonitor monitor, LocalDateTime startTime, LocalDateTime endTime, Exception e) {
        log.info("记录日志 方法名:{}, startTime:{}, endTime:{}", monitor.methodName(), startTime, endTime);
        try {
            sendExpMsgService.sendMsg(monitor, startTime, endTime, point.getArgs(), e);
        }catch (Exception exception){
            log.error("及时更新发送报错信息消息时发生异常", exception);
        }
    }
}

package com.jy.aspect;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jy.bean.result.JsonResult;
import com.jy.bean.result.Page;
import com.jy.util.EmptyUtils;
import javassist.*;
import javassist.bytecode.CodeAttribute;
import javassist.bytecode.LocalVariableAttribute;
import javassist.bytecode.MethodInfo;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2018/6/25
 */
@Component
@Aspect
public class PageResultAspect {

    @Around("@annotation(com.jy.ann.PageResult)")
    public <T> JsonResult<List<T>> pageMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        Object[] args = joinPoint.getArgs();     //方法的参数
        Pageable pageable = (Pageable) args[0];//(Pageable) getValueByFieldName("pageable", joinPoint);
        //  String isPage = (String) getValueByFieldName("isPage", joinPoint);
        String isPage = request.getHeader("isPage");
        boolean startPage = EmptyUtils.isNotEmpty(pageable) && !"0".equals(isPage);//默认分页，为1分页  为0不分页
        if(startPage){
            PageHelper.startPage(pageable.getPageNumber() + 1, pageable.getPageSize());
        }
        JsonResult<List<T>> result = null;//返回结果
        Object jsonResult = joinPoint.proceed();//方法的结果
        if(jsonResult != null){
            result = (JsonResult<List<T>>) jsonResult;
        }
        boolean isBack = EmptyUtils.isNotEmpty(pageable) && "1".equals(isPage);//默认分页不返回页码信息，为1分页返回页码信息，为0不分页
        if(isBack && result != null){
            List<T> list = result.getResult();
            if(EmptyUtils.isNotEmpty(list)){
                PageInfo<T> pageInfo = new PageInfo<T>(list);
                result.setPageInfo(new Page(pageInfo.getTotal(), pageInfo.getPageSize(), pageInfo.getPageNum() - 1));
            }
        }
        if(startPage){
            PageHelper.clearPage();//避免返回结果为空， pageHelper没有被消费，手动清除分页信息
        }
        return result;
    }

    private static String[] getFieldsName(Class cls, String clazzName, String methodName) throws NotFoundException {
        ClassPool pool = ClassPool.getDefault();
        ClassClassPath classPath = new ClassClassPath(cls);
        pool.insertClassPath(classPath);

        CtClass cc = pool.get(clazzName);
        CtMethod cm = cc.getDeclaredMethod(methodName);
        MethodInfo methodInfo = cm.getMethodInfo();
        CodeAttribute codeAttribute = methodInfo.getCodeAttribute();
        LocalVariableAttribute attr = (LocalVariableAttribute) codeAttribute.getAttribute(LocalVariableAttribute.tag);
        if (attr == null) {
            // exception
            return null;
        }
        String[] paramNames = new String[cm.getParameterTypes().length];
        int pos = Modifier.isStatic(cm.getModifiers()) ? 0 : 1;
        for (int i = 0; i < paramNames.length; i++){
            paramNames[i] = attr.variableName(i + pos); //paramNames即参数名
        }
        return paramNames;
    }
    private Object getValueByFieldName(String paramName, JoinPoint joinPoint) throws Exception {
        String classType = joinPoint.getTarget().getClass().getName();
        Class<?> clazz = Class.forName(classType);
        String clazzName = clazz.getName();
        String methodName = joinPoint.getSignature().getName();

        String[] paramNames = getFieldsName(this.getClass(), clazzName, methodName);
        if(EmptyUtils.isEmpty(paramName) || EmptyUtils.isEmpty(paramNames) ){
            return null;
        }
        Object[] args = joinPoint.getArgs();     //方法的参数
        List<String> list = Arrays.asList(paramNames);
        if(!list.contains(paramName)){
            return null;
        }
        for(int i=0; i<list.size(); i++){
            if(list.get(i).equals(paramName)){
                return args[i];
            }
        }
        return null;
    }


}

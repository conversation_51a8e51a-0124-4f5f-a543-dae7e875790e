package com.jy.aspect;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;

/**
 * Created by zy on 2017/11/20.
 * Service 默认pageable 与 默认sort 设定切面
 * 废弃不用
 */
//@Component
//@Aspect
@Deprecated
public class DefalutParamAspect {

    /**
     * 使用AOP对使用了SortDefault 的方法进行代理，设置默认参数
     * @throws Throwable
     */
    @Around("@within(org.springframework.stereotype.Service)  && args(org.springframework.data.domain.Sort, ..)")
    public Object sortDefaultAround(ProceedingJoinPoint joinPoint) throws Throwable  {
        SortDefault an = null;
        Object[] args =  null ;
        Method method = null;
        Object target = null ;
        String methodName = null;

        methodName = joinPoint.getSignature().getName();
        target = joinPoint.getTarget();
        method = getMethodByClassAndName(target.getClass(), methodName);    //得到拦截的方法
        args = joinPoint.getArgs();     //方法的参数
        an = (SortDefault)getAnnotationByMethod(method, Sort.class, SortDefault.class );
        if(an != null){
            Sort defaultSort = new Sort(an.direction(), an.sort());
            args[0] = defaultSort;  //切面拦截第一个参数为Sort的
        }
        return joinPoint.proceed(args);
    }

    /**
     * 使用AOP对使用了 PageableDefault 的方法进行代理，设置默认参数
     * @throws Throwable
     */
    @Around("@within(org.springframework.stereotype.Service)  && args(org.springframework.data.domain.Pageable, ..)")
    public Object pageableDefaultAround(ProceedingJoinPoint joinPoint) throws Throwable  {
        PageableDefault an = null;
        Object[] args =  null ;
        Method method = null;
        Object target = null ;
        String methodName = null;

        methodName = joinPoint.getSignature().getName();
        target = joinPoint.getTarget();
        method = getMethodByClassAndName(target.getClass(), methodName);    //得到拦截的方法
        args = joinPoint.getArgs();     //方法的参数
        an = (PageableDefault)getAnnotationByMethod(method, Pageable.class, PageableDefault.class );
        if(an != null){
            PageRequest pageable = (PageRequest)args[0];
            int anSize = pageable.getPageSize();
            int anPage = pageable.getPageNumber();
            Sort anSort = pageable.getSort();

            if(pageable.getPageSize() == 20){
                anSize = an.size()==10 ? 20 : an.size();
            }
            if(pageable.getPageNumber() == 0){
                anPage = an.page();
            }
            if(pageable.getSort() == null){
                anSort = new Sort(an.direction(), an.sort());
            }

            Pageable defaultPageable = new PageRequest(anPage, anSize, anSort);
            args[0] = defaultPageable;  //切面拦截第一个参数为Pageable的
        }
        return joinPoint.proceed(args);
    }

    /**
     * 根据目标方法和注解类型  得到该目标方法的指定注解
     */
    public Annotation getAnnotationByMethod(Method method, Class argClass, Class annoClass){
        Parameter parameters[] = method.getParameters();
        for(Parameter parameter : parameters){
            if(parameter.getType() == argClass){
                Annotation all[] = parameter.getAnnotations();
                for (Annotation annotation : all) {
                    if (annotation.annotationType() == annoClass) {
                        return annotation;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 根据类和方法名得到方法
     */
    public Method getMethodByClassAndName(Class c , String methodName){
        Method[] methods = c.getDeclaredMethods();
        for (Method method : methods) {
            if(method.getName().equals(methodName)){
                return method ;
            }
        }
        return null;
    }
}

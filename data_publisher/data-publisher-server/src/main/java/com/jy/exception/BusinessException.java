package com.jy.exception;

import org.apache.commons.lang.builder.ToStringBuilder;

public class BusinessException extends Exception  {
	private static final long serialVersionUID = 1L;

	private String code;
	private String msg;

	public BusinessException(String code, String msg) {
		super(code);
		this.msg = msg;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	@Override
	public String toString() {
		return new ToStringBuilder(this)
				.append("code", code)
				.append("msg", msg)
				.toString();
	}
}

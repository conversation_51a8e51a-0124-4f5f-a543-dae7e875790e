/*
package com.jy.repository;

import com.jy.bean.po.PublishTrail;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.Date;
import java.util.Set;

*/
/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2018/1/18
 *//*

public interface PublishTrailRepository extends MongoRepository<PublishTrail, String>{
    Set<PublishTrail> findByCTimeBetweenAndStatus(Date startDate, Date endDate, String status);

    Set<PublishTrail> findByStatusAndBatchNoAndQueueName(String status, String batchNo, String queueName);

    Set<PublishTrail> findByStatus(String status);
}
*/

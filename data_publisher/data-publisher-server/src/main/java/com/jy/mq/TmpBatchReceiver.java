package com.jy.mq;

import com.alibaba.fastjson.JSONObject;
import com.jy.ann.MethodMonitor;
import com.jy.bean.po.FlBatchInfo;
import com.jy.bean.result.ResultStatus;
import com.jy.service.FlBatchInfoService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 *
 */
@Component
@RabbitListener(queues = "tmp-batch-queue")
public class TmpBatchReceiver {
    private static final Logger logger = LogManager.getLogger(TmpBatchReceiver.class);

    @Autowired
    private FlBatchInfoService batchInfoService;

    @RabbitHandler
    @MethodMonitor
    public void process(String baseData) {
        FlBatchInfo batchInfo = null;
        try {
            batchInfo = JSONObject.parseObject(baseData, FlBatchInfo.class);
            batchInfo.setStatus(ResultStatus.PROCESSING.getStatus());
            Date now = new Date();
            batchInfo.setCTime(now);
            batchInfo.setStartTime(now);
            batchInfo.setEndTime(now);
            batchInfoService.save(batchInfo);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("tmp-batch-queue：{}, message: {}", batchInfo, e.getMessage());
        }
    }

}

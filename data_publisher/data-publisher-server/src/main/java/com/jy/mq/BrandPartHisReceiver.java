package com.jy.mq;

import com.alibaba.fastjson.JSONObject;
import com.jy.ann.MethodMonitor;
import com.jy.bean.common.DataTraceMenu;
import com.jy.bean.po.BrandPart;
import com.jy.bean.po.BrandPartHis;
import com.jy.bean.result.ResultStatus;
import com.jy.config.RabbitMQConstants;
import com.jy.service.BrandPartHisService;
import com.jy.service.BrandPartService;
import com.jy.util.DateUtil;
import com.jy.util.StringUtils;
import com.jy.util.ToolUtils;
import com.jy.util.rabbitmq.DataTraceUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.*;

/**
 * @Description 品牌配件历史数据轨迹保存(mq)--HIS_PART
 * <AUTHOR>
 * @Date 2025/6/27
 */
@Component
@RabbitListener(queues = RabbitMQConstants.QUEUE_WAREHOUSE_HIS_PART)
public class BrandPartHisReceiver {
    private static final Logger logger = LogManager.getLogger(BrandPartHisReceiver.class);

    @Autowired
    private BrandPartService brandPartService;

    @Autowired
    private BrandPartHisService brandPartHisService;

    @Autowired
    private RestHighLevelClient esClient;

    private static final String ES_INDEX_PREFIX = "brand_part_his_";

    @RabbitHandler
    @MethodMonitor
    public void process(String baseData) {
        Timestamp time = DateUtil.crunttime();
        try {
            // 解析JSON数据
            JSONObject jsonObject = JSONObject.parseObject(baseData);
            String batchNo = jsonObject.getString("batchNo");
            String tableName = jsonObject.getString("tableName");

            // 使用转换方法创建BrandPart对象，传入整个data节点
            BrandPart brandPart = BrandPart.convertFromJson(jsonObject, batchNo, tableName);
            brandPart.setId(StringUtils.getUUID());
            String brandCode = brandPart.getBrandCode();
            // 检查BrandPart表是否存在，如果不存在则创建
            if (!brandPartService.checkTableExists(brandCode)) {
                brandPartService.createBrandPartTable(brandCode);
                brandPartService.createIndexes(brandCode);
            }
            // 检查BrandPartHis表是否存在，如果不存在则创建
            if (!brandPartHisService.checkTableExists(brandCode)) {
                brandPartHisService.createBrandPartHisTable(brandCode);
                brandPartHisService.createIndexes(brandCode);
            }

            // 处理BrandPart对象
            if (brandPart.getBrandCode() != null && !brandPart.getBrandCode().isEmpty()) {
                try {
                    // 保存或更新实体
                    brandPartService.saveOrUpdate(brandPart);

                    // 再保存一份BrandPartHis用作更新轨迹保存
                    BrandPartHis brandPartHis = new BrandPartHis(brandPart);
                    brandPartHisService.save(brandPartHis);

                    // 将BrandPartHis数据存入ES
                    //saveToElasticsearch(brandPartHis);
                } catch (Exception e) {
                    logger.error("保存或更新BrandPartHis失败: {}", ToolUtils.getExceptionMsg(e));
                }
            }

            DataTraceUtils.sendTrace(JSONObject.parseObject(baseData), DataTraceMenu.PUBLISH_DEAL_DESC.getCode(),
                    "HIS_PART", ResultStatus.SUCCESS.getStatus(), ResultStatus.SUCCESS.getMessage(), time);
        } catch (Exception e) {
            String message = ToolUtils.getExceptionMsg(e);
            DataTraceUtils.sendTrace(JSONObject.parseObject(baseData), DataTraceMenu.PUBLISH_DEAL_DESC.getCode(),
                    "HIS_PART", ResultStatus.INTERNAL_SERVER_ERROR.getStatus(), message, time);
            logger.error(RabbitMQConstants.QUEUE_WAREHOUSE_HIS_PART + "装配出错：{}, message: {}", baseData, message);
        }
    }

    /**
     * 将BrandPartHis数据保存到Elasticsearch
     *
     * @param brandPartHis 要保存的BrandPartHis对象
     */
    private void saveToElasticsearch(BrandPartHis brandPartHis) {
        try {
            String indexName = ES_INDEX_PREFIX + brandPartHis.getBrandCode().toLowerCase();
            String documentId = brandPartHis.getBrandCode() + brandPartHis.getSupTableId();

            // 检查是否已存在相同条件的记录
            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("brandCode", brandPartHis.getBrandCode()))
                    .must(QueryBuilders.termQuery("batchNo", brandPartHis.getBatchNo()));

            // 根据original_part_code或sup_table_id查询
            if (brandPartHis.getOriginalPartCode() != null && !brandPartHis.getOriginalPartCode().isEmpty()) {
                queryBuilder.must(QueryBuilders.termQuery("originalPartCode", brandPartHis.getOriginalPartCode()));
            } else if (brandPartHis.getSupTableId() != null && !brandPartHis.getSupTableId().isEmpty()) {
                queryBuilder.must(QueryBuilders.termQuery("supTableId", brandPartHis.getSupTableId()));
            }

            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder().query(queryBuilder);
            SearchRequest searchRequest = new SearchRequest(indexName).source(sourceBuilder);

            try {
                // 执行搜索
                SearchResponse searchResponse = esClient.search(searchRequest, RequestOptions.DEFAULT);

                if (searchResponse.getHits().getTotalHits().value > 0) {
                    // 找到匹配记录，更新clientCodes
                    SearchHit hit = searchResponse.getHits().getHits()[0];
                    String existingId = hit.getId();
                    String existingClientCodes = "";

                    if (hit.getSourceAsMap().containsKey("clientCodes")) {
                        existingClientCodes = (String) hit.getSourceAsMap().get("clientCodes");
                    }

                    // 合并clientCodes，去重
                    String newClientCodes = mergeClientCodes(existingClientCodes, brandPartHis.getClientCodes());
                    brandPartHis.setClientCodes(newClientCodes);

                    // 更新文档
                    UpdateRequest updateRequest = new UpdateRequest(indexName, existingId)
                            .doc(JSONObject.toJSONString(brandPartHis), XContentType.JSON);
                    esClient.update(updateRequest, RequestOptions.DEFAULT);

                    logger.info("更新ES中的BrandPartHis记录: {}", existingId);
                } else {
                    // 没有找到匹配记录，创建新记录
                    IndexRequest indexRequest = new IndexRequest(indexName)
                            .id(documentId)
                            .source(JSONObject.toJSONString(brandPartHis), XContentType.JSON);
                    esClient.index(indexRequest, RequestOptions.DEFAULT);

                    logger.info("创建ES中的BrandPartHis记录: {}", documentId);
                }
            } catch (Exception e) {
                logger.error("ES查询或更新失败: {}", ToolUtils.getExceptionMsg(e));
            }
        } catch (Exception e) {
            logger.error("保存BrandPartHis到ES失败: {}", ToolUtils.getExceptionMsg(e));
        }
    }

    /**
     * 合并clientCodes字符串，以逗号分隔，并去重
     *
     * @param existing 现有的clientCodes
     * @param newCodes 新的clientCodes
     * @return 合并后的clientCodes
     */
    private String mergeClientCodes(String existing, String newCodes) {
        Set<String> codeSet = new HashSet<>();

        // 添加现有codes
        if (existing != null && !existing.isEmpty()) {
            codeSet.addAll(Arrays.asList(existing.split(",")));
        }

        // 添加新codes
        if (newCodes != null && !newCodes.isEmpty()) {
            codeSet.addAll(Arrays.asList(newCodes.split(",")));
        }

        // 转换回逗号分隔的字符串
        return String.join(",", codeSet);
    }
}

package com.jy.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jy.ann.MethodMonitor;
import com.jy.bean.dto.BaseDataDTO;
import com.jy.bean.dto.BaseDataDTOs;
import com.jy.service.TestDataService;
import com.jy.util.EmptyUtils;
import com.jy.util.StringUtils;
import org.apache.commons.io.LineIterator;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2018/3/28
 */
@Component
public class BaseDataSender {

    @Autowired
    private AmqpTemplate rabbitTemplate;

    @MethodMonitor
    public void sendUpdateTestData() {

        //List<Price> list = testDataService.listAll();
        Random random = new Random(10000);
        for(int i=0; i<100; i++){
         //  for(Price price : list){
            BaseDataDTO baseDataDTO = new BaseDataDTO();
            baseDataDTO.setTableName("facade_am_cl_mp");
            baseDataDTO.setOperate("insert");
            baseDataDTO.setBatchNo("2019111800000000003222");

            Map<String, String> keys = new HashMap<>();
            Map<String, String> fields = new HashMap<>();
            Map<String, String> must = new HashMap<>();
            keys.put("id", StringUtils.getUUID());
            fields.put("amVehicleId", "45E1A59E015FFA01E050A8C077507226");
            fields.put("clVehicleId", "0A4D6C8472A0B9F6E050A8C01A3262BE");
            baseDataDTO.setFields(fields);
            baseDataDTO.setKeys(keys);
            baseDataDTO.setMust(must);
            String data = JSONObject.toJSONString(baseDataDTO);
            this.rabbitTemplate.convertAndSend("SRC_VEH", data);
        }

    }

    @Autowired
    private TestDataService testDataService;


    @MethodMonitor
    public void sendInsertTestData() {
        String obj = this.fileAnalyze("C:\\Users\\<USER>\\Desktop\\GroupData2020011517503750379291302200a109a.json");
        BaseDataDTOs baseDataDTOs = JSON.parseObject(obj, BaseDataDTOs.class);
        List<BaseDataDTO> list = baseDataDTOs.getData();
        if(EmptyUtils.isEmpty(list)){
            return ;
        }
        BaseDataDTO end = new BaseDataDTO(baseDataDTOs.getMainBatchNo(), baseDataDTOs.getBatchNo(),0);
        list.add(end);
        for(BaseDataDTO baseDataDTO : list){
            baseDataDTO.setMainBatchNo(baseDataDTOs.getMainBatchNo());
            baseDataDTO.setBatchNo(baseDataDTOs.getBatchNo());
            baseDataDTO.setTableName(baseDataDTOs.getTableName());
            baseDataDTO.setSqlType(baseDataDTOs.getSqlType());
            baseDataDTO.setOrgCode(baseDataDTOs.getOrgCode());
            baseDataDTO.setClientCode(baseDataDTOs.getClientCode());
            baseDataDTO.setClientUrl(baseDataDTOs.getClientUrl());
            baseDataDTO.setSendTimes(0);
            this.rabbitTemplate.convertAndSend("SRC_PART", JSONObject.toJSONString(baseDataDTO));
        }
    }


    @MethodMonitor
    public void sendDeleteTestData() {

        BaseDataDTO baseDataDTO = new BaseDataDTO();
        baseDataDTO.setTableName("jy_pj_qylbjjgxxb_bd");
        baseDataDTO.setOperate("delete");
        baseDataDTO.setOrgCode("130000");

        Map<String, String> keys = new HashMap<>();
        keys.put("ppid", "********************************");
        keys.put("ycljid", "********************************");
        baseDataDTO.setKeys(keys);
        this.rabbitTemplate.convertAndSend("baseData", JSONObject.toJSONString(baseDataDTO));
        // }

    }

    public String fileAnalyze(String fileName){
        File file = new File(fileName);
        String json  = "";
        try{
            BufferedReader br = new BufferedReader(new FileReader(file));
            LineIterator iterator = new LineIterator(br);
            while(iterator.hasNext()) {
                String line = iterator.nextLine();
                json = line;
            }
            iterator.close();
            br.close();
        }catch(Exception e){
            e.printStackTrace();
        }
        return json;
    }
}

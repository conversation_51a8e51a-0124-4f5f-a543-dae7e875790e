package com.jy.mq;

import com.alibaba.fastjson.JSONObject;
import com.jy.ann.MethodMonitor;
import com.jy.bean.common.DataTraceMenu;
import com.jy.bean.dto.BaseDataDTO;
import com.jy.bean.result.ResultStatus;
import com.jy.service.DataFitService;
import com.jy.util.DateUtil;
import com.jy.util.ToolUtils;
import com.jy.util.rabbitmq.DataTraceUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

/**
 * 整车数据处理(mq)--SRC_VEH
 * @Author: zy
 * @Description:
 * @Date: Created in 2018/3/28
 */
@Component
@RabbitListener(queues = "SRC_VEH")
public class SrcVehReceiver {
    private static final Logger logger = LogManager.getLogger(SrcVehReceiver.class);

    @Autowired
    private DataFitService dataFitService;

    @RabbitHandler
    @MethodMonitor
    public void process(String baseData) {
        BaseDataDTO baseDataDTO = null;
        Timestamp time = DateUtil.crunttime();
        try {
            baseDataDTO = JSONObject.parseObject(baseData, BaseDataDTO.class);
            dataFitService.fit(baseDataDTO);
            DataTraceUtils.sendTrace(JSONObject.parseObject(baseData), DataTraceMenu.PUBLISH_DEAL_DESC.getCode(), "SRC_VEH", ResultStatus.SUCCESS.getStatus(), ResultStatus.SUCCESS.getMessage(), time);
        } catch (Exception e) {
            String message = ToolUtils.getExceptionMsg(e);
            DataTraceUtils.sendTrace(JSONObject.parseObject(baseData), DataTraceMenu.PUBLISH_DEAL_DESC.getCode(), "SRC_VEH", ResultStatus.INTERNAL_SERVER_ERROR.getStatus(), message, time);
            logger.error("SRC_VEH装配出错：{}, message: {}", baseDataDTO, message);
        }

    }

}

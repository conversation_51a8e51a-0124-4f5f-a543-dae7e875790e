package com.jy.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jy.ann.ExceptionMonitor;
import com.jy.bean.common.ClientDescMenu;
import com.jy.bean.common.DataTraceMenu;
import com.jy.bean.result.ResultStatus;
import com.jy.service.SendExpMsgService;
import com.jy.util.FeishuSendMsgUtil;
import com.jy.util.rabbitmq.DataTraceUtils;
import com.jy.util.StringUtils;
import com.jy.util.ToolUtils;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.GetResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.core.ChannelCallback;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Properties;

/**
 * Mq实现方法调用
 * @Author: zy
 * @Date: Created in 2018/4/11
 */
@Component
public class RabbitCommon {
    private static final Logger logger = LogManager.getLogger(RabbitCommon.class);

    @Autowired
    private AmqpAdmin rabbitAdmin;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private CachingConnectionFactory connectionFactory;


    /** 创建队列 */
    public void createQueue(String queueName) {
        Queue queue = new Queue(queueName);
        try {
            rabbitAdmin.declareQueue(queue);
            logger.info("创建队列: {}", queueName);
        } catch (AmqpException e) {
            logger.error("创建队列发生异常: {}", queueName, e);
        }
    }

    //重新创建连接
    public void reCreateConnection(){
        connectionFactory.destroy();
        connectionFactory.createConnection();
    }

    //resetConnection
    public void resetConnection(){
        connectionFactory.resetConnection();
    }

    /** 获取指定队列中的消息数量 */
    public int getCount(String queueName) {
        try {
            Properties properties = rabbitAdmin.getQueueProperties(queueName);
            Integer messageCount = (Integer) properties.get(RabbitAdmin.QUEUE_MESSAGE_COUNT);
            return messageCount != null ? messageCount : 0;
        } catch (Exception e) {
            logger.error("获取队列中的消息数量发生异常: {}", queueName, e);
            FeishuSendMsgUtil.sendQueueMsg("RabbitCommon.getCount", queueName, "获取队列中的消息数量发生异常" + ToolUtils.getExceptionMsg(e));
            return 0;
        }
    }

    /** 获取指定队列中的一条消息 */
    public String processQueue(String queueName) {
        return rabbitTemplate.execute(new ChannelCallbackImpl(queueName));
    }

    /** 推送一条消息到指定队列中 */
    public void sendQueue(String queueName, String data) {
        String status = ResultStatus.SUCCESS.getStatus();
        String message = String.format("数据第[n]次发布，放入 %s MQ 成功", queueName);
        try {
            rabbitTemplate.convertAndSend(queueName, data);
        } catch (AmqpException e) {
            status = ResultStatus.INTERNAL_SERVER_ERROR.getStatus();
            message = String.format("数据第[n]次发布，放入 %s MQ 失败", queueName);
            logger.error("数据第[n]次发布，放入队列失败: {}", queueName, e);
            logger.error(ToolUtils.getExceptionMsg(e));
            FeishuSendMsgUtil.sendQueueMsg("RabbitCommon.sendQueue", queueName, "数据第[n]次发布，放入队列失败" + ToolUtils.getExceptionMsg(e));
        }

        // 如果消息不是推送到客户端描述队列，进行数据追踪
        if (!queueName.contains(ClientDescMenu.SRC_DESC.getCode())) {
            try {
                JSONObject obj = JSON.parseObject(data);
                DataTraceUtils.sendTrace(StringUtils.getUUID(), obj, DataTraceMenu.CLIENT_PUSH_DESC.getCode(), queueName, status, message);
            } catch (Exception e) {
                logger.error("Error sending trace for message: {}", data, e);
            }
        }
    }

    public void sendQueueNoTrace(String queueName, String data) {
        try {
            rabbitTemplate.convertAndSend(queueName, data);
        } catch (AmqpException e) {
            logger.error("数据第[n]次发布，放入队列失败: {}", queueName, e);
            logger.error(ToolUtils.getExceptionMsg(e));
            FeishuSendMsgUtil.sendQueueMsg("RabbitCommon.sendQueue", queueName, "数据第[n]次发布，放入队列失败" + ToolUtils.getExceptionMsg(e));
        }
    }

    /** 推送一条消息到指定交换机中 */
    public void sendExchange(String exchangeName, String data) {
        try {
            rabbitTemplate.convertAndSend(exchangeName, "", data);
        } catch (AmqpException e) {
            logger.error("数据第[n]次发布，放入队列失败: {}", exchangeName, e);
            logger.error(ToolUtils.getExceptionMsg(e));
            FeishuSendMsgUtil.sendQueueMsg("RabbitCommon.sendQueue", exchangeName, "数据第[n]次发布，放入队列失败" + ToolUtils.getExceptionMsg(e));
        }
    }

    /** ChannelCallback 实现类，用于处理消息 */
    public static class ChannelCallbackImpl implements ChannelCallback<String> {

        private final String queueName;

        public ChannelCallbackImpl(String queueName) {
            this.queueName = queueName;
        }

        @Override
        public String doInRabbit(Channel channel) {
            GetResponse response = null;
            try {
                // 获取消息
                response = channel.basicGet(queueName, false);
                if (response != null) {
                    // 确认消息
                    channel.basicAck(response.getEnvelope().getDeliveryTag(), false);
                    return new String(response.getBody(), "UTF-8");
                }
            } catch (IOException e) {
                logger.error("从队列中接收消息发生异常: {}", queueName, e);
                FeishuSendMsgUtil.sendQueueMsg("RabbitCommon.ChannelCallbackImpl", queueName, "从队列中接收消息发生异常" + ToolUtils.getExceptionMsg(e));
                // 如果获取消息失败，进行 nack 操作，将消息重新入队
                if (response != null) {
                    try {
                        channel.basicNack(response.getEnvelope().getDeliveryTag(), false, true);
                    } catch (IOException ex) {
                        logger.error("Error re-acknowledging message for queue: {}", queueName, ex);
                    }
                }
            }
            return null;
        }
    }

}

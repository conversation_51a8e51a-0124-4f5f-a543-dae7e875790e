package com.jy.mq;

import com.alibaba.fastjson.JSONObject;
import com.jy.ann.MethodMonitor;
import com.jy.bean.po.FlBatchInfo;
import com.jy.bean.po.FlMainBatchInfo;
import com.jy.bean.result.ResultStatus;
import com.jy.service.FlBatchInfoService;
import com.jy.service.FlMainBatchInfoService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 */
@Component
@RabbitListener(queues = "tmp-mainBatch-queue")
public class TmpMainBatchReceiver {
    private static final Logger logger = LogManager.getLogger(TmpMainBatchReceiver.class);

    @Autowired
    private FlMainBatchInfoService mianBatchInfoService;

    @RabbitHandler
    @MethodMonitor
    public void process(String baseData) {
        FlMainBatchInfo mainBatchInfo = null;
        try {
            mainBatchInfo = JSONObject.parseObject(baseData, FlMainBatchInfo.class);
            Map<String, Object> queryMap = new HashMap();
            queryMap.put("mainBatchNo", mainBatchInfo.getMainBatchNo());
            List<FlMainBatchInfo> flMainBatchInfoList = mianBatchInfoService.listFlMainBatchInfo(queryMap);
            if(CollectionUtils.isEmpty(flMainBatchInfoList)){
                mainBatchInfo.setStatus(ResultStatus.PROCESSING.getStatus());
                Date now = new Date();
                mainBatchInfo.setCTime(now);
                mainBatchInfo.setStartTime(now);
                mainBatchInfo.setEndTime(now);
                mianBatchInfoService.save(mainBatchInfo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("tmp-mainBatch-queue：{}, message: {}", mainBatchInfo, e.getMessage());
        }
    }

}

package com.jy.mq;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jy.ann.MethodMonitor;
import com.jy.bean.po.BrandPartHis;
import com.jy.config.RabbitMQConstants;
import com.jy.service.BrandPartHisService;
import com.jy.util.ToolUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description 品牌配件历史数据客户端更新(mq)--HIS_PART_UPDATE
 * <AUTHOR>
 * @Date 2025/6/27
 */
@Component
@RabbitListener(queues = RabbitMQConstants.QUEUE_WAREHOUSE_HIS_PART_UPDATE)
public class BrandPartHisUpdateReceiver {
    private static final Logger logger = LogManager.getLogger(BrandPartHisUpdateReceiver.class);

    @Autowired
    private BrandPartHisService brandPartHisService;

    @RabbitHandler
    @MethodMonitor
    public void process(String message) {
        try {
            logger.info("接收到HIS_PART_UPDATE消息: {}", message);
            JSONArray jsonArray = JSONArray.parseArray(message);

            // 按品牌编码分组处理
            Map<String, List<JSONObject>> brandGroupedData = new HashMap<>();

            // 第一步：分组数据
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String batchNo = jsonObject.getString("batchNo");
                String clientCode = jsonObject.getString("clientCode");
                String brandCode = jsonObject.getString("brandCode");

                if (StringUtils.isEmpty(batchNo) || StringUtils.isEmpty(clientCode)) {
                    logger.warn("批次号或客户端编码为空，跳过处理");
                    continue;
                }

                // 从批次号中提取品牌编码
                if (StringUtils.isEmpty(brandCode)) {
                    logger.warn("无法从批次号中提取品牌编码: {}", batchNo);
                    continue;
                }

                // 将数据按品牌编码分组
                if (!brandGroupedData.containsKey(brandCode)) {
                    brandGroupedData.put(brandCode, new ArrayList<>());
                }
                brandGroupedData.get(brandCode).add(jsonObject);
            }

            // 第二步：按品牌批量处理
            for (Map.Entry<String, List<JSONObject>> entry : brandGroupedData.entrySet()) {
                String brandCode = entry.getKey();
                List<JSONObject> dataList = entry.getValue();

                try {
                    processBrandData(brandCode, dataList);
                } catch (Exception e) {
                    logger.error("处理品牌[{}]数据失败: {}", brandCode, ToolUtils.getExceptionMsg(e));
                }
            }
        } catch (Exception e) {
            logger.error("处理HIS_PART_UPDATE消息失败: {}", ToolUtils.getExceptionMsg(e));
        }
    }

    /**
     * 批量处理单个品牌的数据
     *
     * @param brandCode 品牌编码
     * @param dataList 数据列表
     */
    private void processBrandData(String brandCode, List<JSONObject> dataList) throws Exception {
        // 收集所有需要更新的数据
        List<Map<String, Object>> updateParamsList = new ArrayList<>();

        for (JSONObject data : dataList) {
            String batchNo = data.getString("batchNo");
            String clientCode = data.getString("clientCode");
            String updateId = data.getString("updateId");
            String supTableId = data.getString("supTableId");

            if (StringUtils.isEmpty(batchNo) || StringUtils.isEmpty(clientCode)) {
                logger.warn("批次号或客户端编码为空，跳过处理");
                continue;
            }

            if (StringUtils.isEmpty(updateId) && StringUtils.isEmpty(supTableId)) {
                logger.warn("updateId和supTableId都为空，无法更新记录");
                continue;
            }

            Map<String, Object> params = new HashMap<>();
            params.put("brandCode", brandCode);
            params.put("batchNo", batchNo);
            params.put("clientCode", clientCode);

            if (StringUtils.isNotEmpty(updateId)) {
                params.put("updateId", updateId);
            }
            if (StringUtils.isNotEmpty(supTableId)) {
                params.put("supTableId", supTableId);
            }

            updateParamsList.add(params);
        }

        if (updateParamsList.isEmpty()) {
            return;
        }

        // 直接批量更新记录
        int updatedCount = brandPartHisService.batchDirectUpdate(updateParamsList);
        logger.info("成功批量更新记录: brandCode={}, 更新数量={}", brandCode, updatedCount);
    }


}

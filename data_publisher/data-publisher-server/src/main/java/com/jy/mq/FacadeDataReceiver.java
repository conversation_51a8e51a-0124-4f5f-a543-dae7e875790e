package com.jy.mq;

import com.alibaba.fastjson.JSONObject;
import com.jy.ann.ExceptionMonitor;
import com.jy.bean.common.ClientStatus;
import com.jy.bean.po.Client;
import com.jy.service.ClientService;
import com.jy.service.FacadeDataSendService;
import com.jy.task.FacadeTask;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * FACADE数据装配
 * @Author: zy
 * @Description:
 * @Date: Created in 2018/3/28
 */
@Component
public class FacadeDataReceiver {
    private static final Logger logger = LogManager.getLogger(FacadeDataReceiver.class);

    @Autowired
    private FacadeDataSendService facadeDataSendService;
    @Autowired
    private RabbitCommon rabbitCommon;
    @Autowired
    private ClientService clientService;

    private static Map<String, String> clientBatchNoMap = new HashMap<>();

    //忽略batchNo不同的客户端
    private Set<String>  ignoreClientSet = new HashSet<>();

    @Async("asyncServiceExecutor")
   // @MethodMonitor
    public void clientPull(Client client){
        try {
            FacadeTask.clientStatusMap.put(client.getCode(), false);
            int doneNum = rabbitCommon.getCount(client.getCode());
            while(doneNum > 0){
                Client temp = clientService.getOneByCode(client.getCode());
                if(ClientStatus.CLIENT_UP.equals(temp.getStatus())){
                    this.process(client.getCode());
                    doneNum --;
                } else {
                    doneNum = 0;
                }
            }
            FacadeTask.clientStatusMap.put(client.getCode(), true);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("客户端取数据失败:" + e.getMessage());
            FacadeTask.clientStatusMap.put(client.getCode(), true);
        }
    }


 //   @MethodMonitor
    @ExceptionMonitor(methodName = "FacadeDataReceiver.process", remark = "facade从MQ中的clientCode通道获取数据")
    public void process(String clientCode) throws Exception {
            JSONObject obj = null;
            try {
                String message = rabbitCommon.processQueue(clientCode);
                obj = JSONObject.parseObject(message);
                if(!clientCode.contains("FACADE-VIN-PART")){
                    logger.info("FacadeDataReceiver.process clientCode:{}, batchNo:{}, mapBatchNo：{}",clientCode, obj.getString("batchNo"), clientBatchNoMap.get(clientCode));
                    if(!clientBatchNoMap.containsKey(clientCode) || !obj.getString("batchNo").equals(clientBatchNoMap.get(clientCode))){
                        try{
                            if (!clientBatchNoMap.containsKey(clientCode) || !ignoreClientSet.contains(clientCode)) {
                                Thread.currentThread().sleep(2000);
                                logger.info("休息2秒~~~~~~~~~~~~~~~~~~~~~~~~~~~~");
                            }
                        }catch(InterruptedException ie){
                            ie.printStackTrace();
                        }
                        clientBatchNoMap.put(clientCode, obj.getString("batchNo"));
                    }
                }
                facadeDataSendService.receiveSwitch(obj, clientCode);
            } catch (Exception e) {
                e.printStackTrace();
                logger.error("baseData装配出错：{}, message: {}", obj, e.getMessage());
                throw e;
            }

    }


    public Set<String> getIgnoreClientSet() {
        return ignoreClientSet;
    }

    public void setIgnoreClientSet(Set<String> ignoreClientSet) {
        this.ignoreClientSet = ignoreClientSet;
    }

    public void setIgnoreClientSet(String ignoreClient) {
        this.ignoreClientSet.add(ignoreClient);
    }

    public void removeIgnoreClientSet(String ignoreClient) {
        this.ignoreClientSet.remove(ignoreClient);
    }

    public void cleanIgnoreClientSet() {
        this.ignoreClientSet.clear();
    }


}

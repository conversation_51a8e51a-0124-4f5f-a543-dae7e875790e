package com.jy.config;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.FanoutExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.boot.autoconfigure.amqp.RabbitProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.backoff.ExponentialBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;

/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2018/3/28
 */
@Configuration
public class RabbitConfig {

//    @Bean
//    public Queue Queue() {
//        return new Queue("hello");
//    }

    @Bean
    public ConnectionFactory connectionFactory(RabbitProperties rabbitProperties){
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory(rabbitProperties.getHost(), rabbitProperties.getPort());
        connectionFactory.setUsername(rabbitProperties.getUsername());
        connectionFactory.setPassword(rabbitProperties.getPassword());
        connectionFactory.setConnectionTimeout(rabbitProperties.getConnectionTimeout());
        connectionFactory.setRequestedHeartBeat(rabbitProperties.getRequestedHeartbeat());
        connectionFactory.setPublisherConfirms(rabbitProperties.isPublisherConfirms());
        connectionFactory.setPublisherReturns(rabbitProperties.isPublisherReturns());
        connectionFactory.getRabbitConnectionFactory().setAutomaticRecoveryEnabled(true);

        //启用或禁用连接自动恢复，默认：false
        connectionFactory.getRabbitConnectionFactory().setAutomaticRecoveryEnabled(true);
        //设置连接恢复时间间隔，默认：5000ms
        connectionFactory.getRabbitConnectionFactory().setNetworkRecoveryInterval(5000);
        //启用或禁用拓扑恢复，默认：true【拓扑恢复功能可以帮助消费者重新声明之前定义的队列、交换机和绑定等拓扑结构】
        connectionFactory.getRabbitConnectionFactory().setTopologyRecoveryEnabled(true);
        return connectionFactory;
    }


    @Bean
    public RetryTemplate retryTemplate() {
        RetryTemplate retryTemplate = new RetryTemplate();

        // 设置重试策略
        SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy();
        // 最大重试次数
        retryPolicy.setMaxAttempts(5);
        retryTemplate.setRetryPolicy(retryPolicy);

        // 设置退避策略
        ExponentialBackOffPolicy backOffPolicy = new ExponentialBackOffPolicy();
        // 初始间隔时间
        backOffPolicy.setInitialInterval(1000);
        // 每次重试间隔翻倍
        backOffPolicy.setMultiplier(2);
        retryTemplate.setBackOffPolicy(backOffPolicy);

        return retryTemplate;
    }

    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        // 设置重试模板
        rabbitTemplate.setRetryTemplate(retryTemplate());
        return rabbitTemplate;
    }

    @Bean
    public FanoutExchange fanoutExchange() {
        return new FanoutExchange(RabbitMQConstants.EXCHANGE_WAREHOUSE_PART);
    }

    @Bean
    public Queue queueSrcPart() {
        return new Queue(RabbitMQConstants.QUEUE_WAREHOUSE_SRC_PART);
    }

    @Bean
    public Queue queueHisPart() {
        return new Queue(RabbitMQConstants.QUEUE_WAREHOUSE_HIS_PART);
    }

    @Bean
    public Binding bindingA(FanoutExchange fanoutExchange, Queue queueSrcPart) {
        return BindingBuilder.bind(queueSrcPart).to(fanoutExchange);
    }

    @Bean
    public Binding bindingB(FanoutExchange fanoutExchange, Queue queueHisPart) {
        return BindingBuilder.bind(queueHisPart).to(fanoutExchange);
    }

}

/*
package com.jy.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jy.ann.MethodMonitor;
import com.jy.bean.po.PublishCollection;
import com.jy.bean.result.ResultStatus;
import com.jy.util.EmptyUtils;
import com.jy.util.rabbitmq.DataTraceUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.util.*;

*/
/**
 * <AUTHOR>
 * @date 2019/7/26.
 *//*

@Component
public class MongoDbConfig {

    private static MongoTemplate mongoTemplate;

    private static Map<String,Object> clientMap = new HashMap<>(100);

    @Autowired
    public MongoDbConfig(MongoTemplate mongoTemplate){
        MongoDbConfig.mongoTemplate = mongoTemplate;
    }

    */
/**
     * 创建mongo集合
     * @param collectionName 集合名称
     *//*

    @MethodMonitor
    public static void createCollection(String collectionName) throws Exception{

        if(!clientMap.containsKey(collectionName)){
            if(!mongoTemplate.collectionExists(collectionName)){
                //新建集合
                DBCollection db =  mongoTemplate.createCollection(collectionName);
                //创建索引
            //    db.createIndex(new BasicDBObject("uTime",1));
                db.createIndex(new BasicDBObject("batchNo",1));
              //  db.createIndex(new BasicDBObject("status",1));
               // db.createIndex(new BasicDBObject("uTime",1).append("queueName",1));
                db.createIndex(new BasicDBObject("cTime",1).append("staus",1).append("queueName",1).append("uTime",1));
                clientMap.put(collectionName,"");
            }else{
                clientMap.put(collectionName,"");
            }
        }

    }

    */
/**
     * 批量新增mongoDB数据
     * @param list 新增数据
     * @param collectionName 集合名称
     * @throws Exception
     *//*

    @MethodMonitor
    public static void insertAll(List<PublishCollection> list, String collectionName) throws Exception{
        mongoTemplate.insert(list,collectionName);
    }

    */
/**
     * 新增mongoDB数据
     * @param publishCollection 新增数据
     * @param collectionName 集合名称
     * @throws Exception
     *//*

    @MethodMonitor
    public static void insert(PublishCollection publishCollection, String collectionName) throws Exception{
        mongoTemplate.insert(publishCollection,collectionName);
    }

    */
/**
     * 批量修改mongoDB数据
     * @param collectionName 集合名称
     * @param list 修改的数据
     *//*

    public static void updateAll(String collectionName,List<PublishCollection> list) throws Exception{
        DBObject command = new BasicDBObject();
        command.put("update", collectionName);
        List<BasicDBObject> updateList = new ArrayList<BasicDBObject>();
        for (PublishCollection option : list) {
            BasicDBObject update = new BasicDBObject();
            update.put("q", new Query(Criteria.where("_id").is(option.getId())).getQueryObject());
            update.put("u", fitUpdate(option).getUpdateObject());
            updateList.add(update);
        }
        command.put("updates", updateList);
        mongoTemplate.getCollection(collectionName).getDB().command(command);
    }

    public static void update(String collectionName,PublishCollection publishCollection) throws Exception{
        mongoTemplate.updateFirst(new Query(Criteria.where("_id").is(publishCollection.getId())),fitUpdate(publishCollection),collectionName);
    }

    public static Update fitUpdate(PublishCollection trail){
        Update update = new Update()
                .set("uTime", trail.getUTime());
        if(EmptyUtils.isNotEmpty(trail.getSendCount()) && trail.getSendCount() != 0){
            update.set("sendCount", trail.getSendCount());
        }
        if(EmptyUtils.isNotEmpty(trail.getStatus())){
            update.set("status", trail.getStatus());
        }
        if(EmptyUtils.isNotEmpty(trail.getMessage())){
            update.set("message", trail.getMessage());
        }
        if(EmptyUtils.isNotEmpty(trail.getSendTimes())){
            update.set("sendTimes", trail.getSendTimes());
        }
        if(EmptyUtils.isNotEmpty(trail.getData())){
            update.set("data", trail.getData());
        }
        return update;
    }

    */
/**
     * 获取mongo所有的集合名称
     * @return
     *//*

    public static Set<String> getCollectionNames(){
        return mongoTemplate.getCollectionNames();
    }
}*/

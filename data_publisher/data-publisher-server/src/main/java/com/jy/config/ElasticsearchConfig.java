package com.jy.config;


import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.apache.http.nio.entity.NStringEntity;
import org.apache.http.util.EntityUtils;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.client.*;
import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: zy
 * @Date: Created in 2019/11/22
 */

@Configuration
public class ElasticsearchConfig {
    @Value("${ElasticsearchConfig.host}")
    private String host;
    @Value("${ElasticsearchConfig.port}")
    private int port;

    private String schema = "http";
    private int connectTimeOut = 1000;
    private int socketTimeOut = 30000;
    private int connectionRequestTimeOut = 500;
    private int maxConnectNum = 100;
    private int maxConnectPerRoute = 100;
    private HttpHost httpHost;
    private RestClientBuilder builder;
    private RestHighLevelClient client;

    /**
     * Bean name default  函数名字
     *
     * @return
     */
    @Bean(autowire = Autowire.BY_NAME, name = "restHighLevelClient")
    public RestHighLevelClient client() {
        httpHost = new HttpHost(host, port, schema);
        builder = RestClient.builder(httpHost);
        setConnectTimeOutConfig();
        setMutiConnectConfig();
        client = new RestHighLevelClient(builder);
        return client;
    }

    /**
     * 异步httpclient的连接延时配置
     */
    public void setConnectTimeOutConfig() {
        builder.setRequestConfigCallback(new RestClientBuilder.RequestConfigCallback() {
            @Override
            public Builder customizeRequestConfig(Builder requestConfigBuilder) {
                requestConfigBuilder.setConnectTimeout(connectTimeOut);
                requestConfigBuilder.setSocketTimeout(socketTimeOut);
                requestConfigBuilder.setConnectionRequestTimeout(connectionRequestTimeOut);
                return requestConfigBuilder;
            }
        });
    }


    /**
     * 异步httpclient的连接数配置
     */
    public void setMutiConnectConfig() {
        builder.setHttpClientConfigCallback(new RestClientBuilder.HttpClientConfigCallback() {
            @Override
            public HttpAsyncClientBuilder customizeHttpClient(HttpAsyncClientBuilder httpClientBuilder) {
                httpClientBuilder.setMaxConnTotal(maxConnectNum);
                httpClientBuilder.setMaxConnPerRoute(maxConnectPerRoute);
                return httpClientBuilder;
            }
        });
    }

    /**
     * 关闭连接
     */
    public void close() {
        if (client != null) {
            try {
                client.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }

    public static void main(String[] args){
        ElasticsearchConfig c = new ElasticsearchConfig();
        RestHighLevelClient restClient = c.client();

        Map<String, String> params = Collections.emptyMap();
        String jsonString = "{\n" +
                "   \n" +
                "   \"query\": {\n" +
                "    \"bool\": {\n" +
                "      \"must_not\": {\n" +
                "        \"match\":{\n" +
                "                    \"num\":1\n" +
                "          }\n" +
                "      }\n" +
                "    }\n" +
                "  }\n" +
                "}";
        HttpEntity entity = new NStringEntity(jsonString, ContentType.APPLICATION_JSON);
        try {
            Request request = new Request("POST", "/data-trace-alias/_search");
            request.setEntity(entity);
            Response response = restClient.getLowLevelClient().performRequest(request);
            HttpEntity httpEntity = response.getEntity();
            if (httpEntity != null) {
                String retSrc = EntityUtils.toString(httpEntity);
                // parsing JSON
                JSONObject result = JSONObject.parseObject(retSrc); //Convert String to JSON Object
                System.out.println(result);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }


        try {
            restClient.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
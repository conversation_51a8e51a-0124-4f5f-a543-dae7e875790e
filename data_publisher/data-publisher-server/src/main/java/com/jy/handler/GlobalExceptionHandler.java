package com.jy.handler;

import com.jy.bean.result.JsonResult;
import com.jy.bean.result.ResultStatus;
import com.jy.util.ToolUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;

/**
 * Created by zy on 2017/11/14.
 * Controller 异常统一处理
 */
@ControllerAdvice
@ResponseBody
public class GlobalExceptionHandler {

    private static final Logger logger = LogManager.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler(Exception.class)
    public JsonResult<String> defaultErrorHandler(HttpServletRequest request, Exception e){
        JsonResult jsonResult = new JsonResult(ResultStatus.INTERNAL_SERVER_ERROR);
        jsonResult.setMessage(e.getMessage());
        jsonResult.setPath(getCompleteUrl(request));
        logger.error("Message: {}",  e.getMessage(), e);
        return jsonResult;
    }

    private static String getCompleteUrl(HttpServletRequest request){
        if(null == request){
            return "";
        }
        String param = getRequestParameter(request);
        if(param != null && param.length() > 0){
            return request.getRequestURI() + "?" + param;
        } else {
            return request.getRequestURI().toString();
        }
    }

    private static String getRequestParameter(HttpServletRequest request) {

        if (null == request) {
            return null;
        }

        String method = request.getMethod();
        String param = null;
        if (method.equalsIgnoreCase("GET")) {
            param = request.getQueryString();
        } else {
            param = getBodyData(request);
        }
        return param;
    }

    //获取请求体中的字符串(POST)
    private static String getBodyData(HttpServletRequest request) {
        StringBuffer data = new StringBuffer();
        String line = null;
        BufferedReader reader = null;
        try {
            reader = request.getReader();
            while (null != (line = reader.readLine())){
                data.append(line);
            }
        } catch (Exception e) {
        } finally {
        }
        return data.toString();
    }
}

package com.jy.mapper;

import com.jy.bean.po.ClientTable;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @Author: zy
 * @Date: Created in 2018/4/16
 */
@Mapper
public interface ClientTableMapper {
    List<ClientTable> listClientTable(Map<String, Object> map);

    List<ClientTable> listBybaseTableName();

    void save(ClientTable clientTable);

    void saveBatch(List<ClientTable> list);

    void delete(Map<String, Object> map);

    void update(ClientTable clientTable);

    ClientTable getClientTable(ClientTable clientTable);

    List<Map<String,Object>> getFacadePartTableField();
}

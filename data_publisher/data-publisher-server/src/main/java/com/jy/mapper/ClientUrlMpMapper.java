package com.jy.mapper;

import com.jy.bean.po.ClientUrlMp;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/5/23
 */
@Mapper
public interface ClientUrlMpMapper {
    List<ClientUrlMp> listClientUrlMp(Map<String, Object> map);

    List<ClientUrlMp> listCompareClient(Map<String, Object> map);

    void save(ClientUrlMp clientUrlMp);

    void delete(Map<String, Object> map);

    void update(ClientUrlMp clientUrlMp);
}

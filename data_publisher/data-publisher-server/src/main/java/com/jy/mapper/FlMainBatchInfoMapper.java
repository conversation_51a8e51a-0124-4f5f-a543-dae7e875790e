package com.jy.mapper;

import com.jy.bean.dto.DashBordDailyDTO;
import com.jy.bean.po.BatchDetail;
import com.jy.bean.po.FlMainBatchInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: zy
 * @Date: Created in 2019/11/22
 */
@Mapper
public interface FlMainBatchInfoMapper {

    List<FlMainBatchInfo> listFlMainBatchInfo(Map<String, Object> map);

    FlMainBatchInfo selectFlMainBatchInfo(String mainBatchNo);

    void insertBatch(List<FlMainBatchInfo> mainBatchInfoList);

    void insert(FlMainBatchInfo mainBatchInfo);

    void update(FlMainBatchInfo mainBatchInfo);

    DashBordDailyDTO selectDaily(@Param("startOfDay") String startOfDay, @Param("endOfDay") String endOfDay);

    List<FlMainBatchInfo> listDailyMainBatchInfo(Map<String, Object> paramMap);

    /**
     * 高性能版本：简化查询，减少JOIN操作
     */
    List<FlMainBatchInfo> listDailyMainBatchInfoOptimized(Map<String, Object> paramMap);

    /**
     * 批量获取批次状态信息
     */
    List<Map<String, Object>> getBatchStatusByMainBatchNos(@Param("mainBatchNos") List<String> mainBatchNos);

    void reset(@Param("mainBatchNo") String mainBatchNo, @Param("startTime") Date startTime, @Param("status") String status);

    void resetBatch(@Param("list") List<BatchDetail> batchDetailList, @Param("startTime") Date startTime, @Param("status") String status);
}

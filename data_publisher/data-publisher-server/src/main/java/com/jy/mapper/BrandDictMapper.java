package com.jy.mapper;

import com.jy.bean.po.BrandDict;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 品牌字典Mapper接口
 */
@Mapper
public interface BrandDictMapper {
    
    /**
     * 查询品牌字典列表
     * @param params 查询参数
     * @return 品牌字典列表
     */
    List<BrandDict> listBrandDict(Map<String, Object> params);
    
    /**
     * 根据品牌编码查询品牌字典
     * @param brandCode 品牌编码
     * @return 品牌字典
     */
    BrandDict getBrandDictByCode(@Param("brandCode") String brandCode);
    
    /**
     * 根据品牌名称模糊查询品牌字典
     * @param brandName 品牌名称
     * @return 品牌字典列表
     */
    List<BrandDict> searchBrandDictByName(@Param("brandName") String brandName);
}

package com.jy.mapper;

import com.jy.bean.po.ClientTableFieldMp;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/5/2
 */
@Mapper
public interface ClientTableFieldMpMapper {
    /**
     * 客户端表字段列表
     * @param map
     * @return
     */
    List<ClientTableFieldMp> listClientTableFieldMp(Map<String, Object> map);
    /**
     * 客户端表字段表字段新增
     * @param clientTableFieldMp
     */
    void save(ClientTableFieldMp clientTableFieldMp);

    void saveBatch(List<ClientTableFieldMp> list);
    /**
     * 客户端表字段删除
     * @param map
     */
    void delete(Map<String, Object> map);
    /**
     * 客户端表字段更新
     * @param clientTableFieldMp
     */
    void update(ClientTableFieldMp clientTableFieldMp);
    /**
     * 查询客户端表字段总数
     * @param clientTableFieldMp
     * @return
     */
    int selectFieldCount(ClientTableFieldMp clientTableFieldMp);
}

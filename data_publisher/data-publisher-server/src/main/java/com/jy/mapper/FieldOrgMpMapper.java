package com.jy.mapper;

import com.jy.bean.po.FieldOrgMp;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/5/2
 */
@Mapper
public interface FieldOrgMpMapper {
    List<FieldOrgMp> listFieldOrgMp(Map<String, Object> map);

    List<String> listOrgCode(Map<String, Object> map);

    void save(FieldOrgMp fieldOrgMp);

    void saveBatch(List<FieldOrgMp> list);

    void delete(Map<String, Object> map);

    void update(FieldOrgMp fieldOrgMp);
}

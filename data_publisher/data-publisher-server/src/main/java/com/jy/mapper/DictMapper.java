package com.jy.mapper;

import com.jy.bean.po.Dict;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/5/11
 */
@Mapper
public interface DictMapper {
    List<Dict> listDict(Map<String, Object> map);

    void save(Dict dict);

    void delete(Map<String, Object> map);

    void update(Dict dict);

    String getMatchCode(@Param("companyCode") String companyCode);
}

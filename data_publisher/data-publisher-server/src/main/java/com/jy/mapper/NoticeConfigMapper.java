package com.jy.mapper;

import com.jy.bean.po.NoticeConfig;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @Author: caolt
 * @Description:
 * @Version:
 * @Date: Created in  2020/07/23
 */
@Mapper
public interface NoticeConfigMapper {
    List<NoticeConfig> listNoticeConfig(Map<String, Object> map);

    void save(NoticeConfig noticeConfig);

    void delete(Map<String, Object> map);

    void update(NoticeConfig noticeConfig);
}

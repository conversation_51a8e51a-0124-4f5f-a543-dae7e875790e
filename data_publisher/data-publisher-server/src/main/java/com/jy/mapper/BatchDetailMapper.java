package com.jy.mapper;

import com.jy.bean.po.BatchDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface BatchDetailMapper {

    List<BatchDetail> listBatchDetail(Map<String, Object> map);

    int getCount(Map<String, Object> map);

    void save(BatchDetail batchDetail);

    void delete(Map<String, Object> map);

    void update(BatchDetail batchDetail);

    Integer selectMaxOrder(@Param("mainBatchNo") String mainBatchNo);

    List<Integer> updateByMainBatchNo(BatchDetail batchDetail);

    List<BatchDetail> batchListByMainAndBatchNo(@Param("list") List<BatchDetail> list);

    void updateBatch(@Param("list") List<BatchDetail> list);
}

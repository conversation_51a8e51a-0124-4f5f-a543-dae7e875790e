package com.jy.mapper;

import com.jy.bean.po.SysUser;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2018/1/15
 */
@Mapper
public interface SysUserMapper {
    List<SysUser> listSysUser(Map<String, Object> map);

    List<SysUser> listSortFieldSysUser(Map<String, Object> map);

    void save(SysUser sysUser);

    void delete(Map<String, Object> map);

    void update(SysUser sysUser);
}

package com.jy.mapper;

import com.jy.bean.po.OrgMp;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2018/1/16
 */
@Mapper
public interface OrgMpMapper {
    List<OrgMp> listOrgMp(Map<String, Object> map);

    List<OrgMp> listOrgAndCodeNameByClientCode(Map<String, Object> map);

    void save(OrgMp orgMp);

    void delete(Map<String, Object> map);

    void update(OrgMp orgMp);
}

package com.jy.mapper;

import com.jy.bean.po.Client;
import com.jy.bean.po.OrgMp;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2018/1/16
 */
@Mapper
public interface ClientMapper {
    List<Client> listClient(Map<String, Object> map);

    List<Client> listClientByBaseTable(Map<String, Object> map);

    void save(Client client);

    void delete(Map<String, Object> map);

    void update(Client client);
}

package com.jy.mapper;

import com.jy.bean.po.Price;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2018/1/16
 */
@Mapper
public interface TestDataMapper {
    List<Price> listPrice(Map<String, Object> map);

    List<Price> listVehicleId(Map<String, Object> map);

    List<Price> listVcBaseApVehicleId(Map<String, Object> map);

}

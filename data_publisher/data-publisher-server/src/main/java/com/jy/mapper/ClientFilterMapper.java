package com.jy.mapper;

import com.jy.bean.po.ClientFilter;
import com.jy.bean.po.ClientTable;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @Author: zy
 * @Date: Created in 2018/4/16
 */
@Mapper
public interface ClientFilterMapper {
    List<ClientFilter> listClientFilter(Map<String, Object> map);

    void save(ClientFilter clientFilter);

    void saveBatch(List<ClientFilter> list);

    void delete(Map<String, Object> map);

    void update(ClientFilter clientFilter);

}

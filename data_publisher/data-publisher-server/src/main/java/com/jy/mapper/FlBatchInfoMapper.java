package com.jy.mapper;

import com.jy.bean.po.BatchDetail;
import com.jy.bean.po.FlBatchInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: zy
 * @Date: Created in 2019/11/22
 */
@Mapper
public interface FlBatchInfoMapper {

    List<FlBatchInfo> listFlBatchInfo(Map<String, Object> map);

    void delete(Map<String, Object> map);

    void insertBatch(List<FlBatchInfo> batchInfoList);

    void insert(FlBatchInfo batchInfo);

    void update(FlBatchInfo batchInfo);

    void updateTraceStatus(FlBatchInfo batchInfo);

    void updateSelective(FlBatchInfo batchInfo);

    FlBatchInfo getById(Long id);

    Integer countByBatchNo(@Param("batchNo") String batchNo, @Param("dataSource") String dataSource, @Param("nodeName") String nodeName);

    void updateSelectiveByBatchNo(FlBatchInfo flBatchInfo);

    void reset(@Param("batchNo") String batchNo, @Param("startTime") Date startTime, @Param("status") String status);

    void resetBatch(@Param("list") List<BatchDetail> list, @Param("startTime") Date startTime, @Param("status") String status);
}

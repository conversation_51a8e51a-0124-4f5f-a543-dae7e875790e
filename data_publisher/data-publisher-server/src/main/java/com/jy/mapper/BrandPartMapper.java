package com.jy.mapper;

import com.jy.bean.po.BrandPart;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Mapper interface for BrandPart operations
 * Supports table sharding based on brand code
 */
@Mapper
public interface BrandPartMapper {

    /**
     * Get brand part by ID
     * @param id The ID of the brand part
     * @param brandCode The brand code for table sharding
     * @return The brand part entity
     */
    BrandPart getBrandPartById(@Param("id") Long id, @Param("brandCode") String brandCode);

    BrandPart getBrandPartByOe(@Param("oe") String oe, @Param("brandCode") String brandCode);

    /**
     * List brand parts by various criteria
     * @param params Map containing query parameters and brandCode for sharding
     * @return List of brand parts
     */
    List<BrandPart> listBrandPart(Map<String, Object> params);

    /**
     * List brand parts by batch number, original part code and update time
     * @param batchNo The batch number
     * @param originalPartCode The original part code
     * @param updateTime The update time
     * @param brandCode The brand code for table sharding
     * @return List of brand parts
     */
    List<BrandPart> listByBatchAndCodeAndTime(
        @Param("batchNo") String batchNo,
        @Param("originalPartCode") String originalPartCode,
        @Param("updateTime") Date updateTime,
        @Param("brandCode") String brandCode);

    /**
     * Save a new brand part
     * @param brandPart The brand part to save
     */
    void saveBrandPart(BrandPart brandPart);

    /**
     * Update an existing brand part
     * @param brandPart The brand part with updated values
     */
    void updateBrandPart(BrandPart brandPart);

    /**
     * Delete a brand part by ID
     * @param id The ID of the brand part to delete
     * @param brandCode The brand code for table sharding
     */
    void deleteBrandPart(@Param("id") Long id, @Param("brandCode") String brandCode);

    /**
     * Get count of parts for a batch
     * @param batchNo The batch number
     * @param brandCode The brand code for table sharding
     * @return Count of parts
     */
    Integer getBatchPartCount(@Param("batchNo") String batchNo, @Param("brandCode") String brandCode);

    /**
     * Check if a table exists
     * @param brandCode The name of the table to check
     * @return 1 if the table exists, 0 otherwise
     */
    Integer checkTableExists(@Param("brandCode") String brandCode);


    /**
     * 创建品牌部件历史表
     */
    void createBrandPartTable(@Param("brandCode") String brandCode);

    /**
     * 创建批次号和原厂编码联合索引
     */
    void createBatchNoAndOriginalPartCodeIndex(@Param("brandCode") String brandCode);

    /**
     * 创建批次号、原厂编码和更新时间联合索引
     */
    void createBatchNoOriginalPartCodeUtIndex(@Param("brandCode") String brandCode);

    /**
     * 创建批次号和供应商表ID联合索引
     */
    void createBatchNoAndSupTableIdIndex(@Param("brandCode") String brandCode);

    /**
     * 创建原厂编码索引
     */
    void createOriginalPartCodeIndex(@Param("brandCode") String brandCode);

    /**
     * 创建批次号索引
     */
    void createBatchNoIndex(@Param("brandCode") String brandCode);

    /**
     * 根据brandCode和supTableId查询BrandPart
     * @param supTableId 供应商表ID
     * @param brandCode 品牌编码
     * @return BrandPart对象，如果不存在则返回null
     */
    BrandPart getBrandPartBySupTableId(@Param("supTableId") String supTableId, @Param("brandCode") String brandCode);

    /**
     * 根据查询条件分页查询BrandPart列表
     * @param params 查询参数，包含brandCode、startTime、endTime等
     * @return BrandPart列表
     */
    List<BrandPart> queryBrandPartWithPage(Map<String, Object> params);

    /**
     * 根据多个品牌编码查询BrandPart列表，支持分页
     * @param params 查询参数，包含brandCodes列表、startTime、endTime等
     * @return BrandPart列表
     */
    List<BrandPart> queryMultipleBrandPartsWithPage(Map<String, Object> params);

}

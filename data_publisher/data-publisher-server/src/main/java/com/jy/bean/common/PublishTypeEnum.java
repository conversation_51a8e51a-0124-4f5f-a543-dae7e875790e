package com.jy.bean.common;

public enum PublishTypeEnum {
    AP_VEH("ApVeh", "承保数据", "车型数据发布平台"),
    AM_VEH("AmVeh", "后市场数据", "车型数据发布平台" ),
    VIN("Vin", "vin码", "车型数据发布平台"),
    VIN_QUESTION("VinQuestion","vin码问题", "车型数据发布平台"),
    GROUP_DATA("GroupData","车组数据", "配件数据发布平台"),
    DIC_DATA("DicData","字典数据", "配件数据发布平台"),
    REPLACE_DATA("ReplaceData","替换件数据", "配件数据发布平台"),
    AFT_DATA("AftData","品牌件数据", "配件数据发布平台"),
    SINGLE_DATA("SingleData","单表数据", "配件数据发布平台"),
    COMMERCIAL_VEH("CommercialVeh","精细化商用车数据", "配件数据发布平台"),
    FITTING("MaketPrice","分省/分析价格数据", "价格数据发布平台"),
    ERROR_FIT("RegionPrice","区域价格数据", "价格数据发布平台"),
    SUCCESS_FIT("SysPrice","4s店价格数据" ,"价格数据发布平台"),
    SENDING("AutoTest","自动化数据" ,"自动化测试");

    private String code;
    private String name;
    private String partentName;

    PublishTypeEnum(String code, String name, String partentName) {
        this.code = code;
        this.name = name;
        this.partentName = partentName;
    }

    public String getCode() {
        return code;
    }

    public static String nameOf(String code) {
        for (PublishTypeEnum a : PublishTypeEnum.values()) {
            if (a.code.equals(code)){
                return a.name;
            }
        }
        return "";
    }

    public static String parentNameOf(String code) {
        for (PublishTypeEnum a : PublishTypeEnum.values()) {
            if (a.code.equals(code)){
                return a.partentName;
            }
        }
        return "";
    }
}

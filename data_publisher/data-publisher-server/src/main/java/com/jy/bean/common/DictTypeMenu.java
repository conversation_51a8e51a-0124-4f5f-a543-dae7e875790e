package com.jy.bean.common;

/**
 * <AUTHOR>
 * @date 2018/5/11
 */
public enum DictTypeMenu {

    ORGCODE("orgCode", "机构编码" );

    private String typeName;
    private String typeCode;

    DictTypeMenu(String typeCode, String typeName) {
        this.typeCode = typeCode;
        this.typeName = typeName;
    }

    public static String getNameByCode(String typeCode) {
        String typeName = "";
        for (DictTypeMenu a : DictTypeMenu.values()) {
            if (a.typeCode.equals(typeCode)){
                typeName = a.getTypeName();
            }
        }
        return typeName;
    }
    public String getTypeName() {
        return typeName;
    }

    public String getTypeCode() {
        return typeCode;
    }
}

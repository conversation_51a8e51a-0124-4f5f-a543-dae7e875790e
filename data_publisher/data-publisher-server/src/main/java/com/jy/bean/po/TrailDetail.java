package com.jy.bean.po;

import com.jy.bean.dto.BaseDataDTOs;
import com.jy.bean.result.ResultStatus;
import com.jy.util.StringUtils;
import lombok.Data;

@Data
public class TrailDetail extends BasePo {
    private String id;
    private String clientCode;
    private String batchNo;
    private String status;

    public TrailDetail(){}

    public TrailDetail(String batchNo, String clientCode){
        this.id = StringUtils.getUUID();
        this.batchNo = batchNo;
        this.status = ResultStatus.SUCCESS_ACCEPTED.getStatus();
        this.clientCode = clientCode;
    }

    public TrailDetail(String batchNo, String clientCode, String staus){
        this.id = StringUtils.getUUID();
        this.batchNo = batchNo;
        this.status = staus;
        this.clientCode = clientCode;
    }
}
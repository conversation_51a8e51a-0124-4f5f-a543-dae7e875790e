package com.jy.bean.po;

import com.jy.bean.dto.BaseDataDTOs;
import com.jy.bean.result.ResultStatus;
import com.jy.util.StringUtils;
import lombok.Data;

@Data
public class BatchDetail extends BasePo {
    private String id;
    private String mainBatchNo;
    private String batchNo;
    private String status;
    private String clientCode;
    private Integer num;
    private String tableName;
    private String filePath;
    private Integer batchOrder;
    private String orgCode;
    private String brandCode;
    private String brandName;

    public BatchDetail(){}

    public BatchDetail(BaseDataDTOs baseDataDTOs, String clientCode, String filePath, Integer batchOrder){
        this.id = StringUtils.getUUID();
        this.mainBatchNo = baseDataDTOs.getMainBatchNo();
        this.batchNo = baseDataDTOs.getBatchNo();
        this.status = ResultStatus.UNSENT.getStatus();
        this.clientCode = clientCode;
        this.num = baseDataDTOs.getTotal();
        this.tableName = baseDataDTOs.getTableName();
        this.filePath = filePath;
        this.batchOrder = batchOrder;
        this.orgCode = baseDataDTOs.getOrgCode();
        this.brandCode = baseDataDTOs.getBrandCode();
        this.brandName = baseDataDTOs.getBrandName();
    }

    public BatchDetail(String batchNo, String status){
        this.batchNo = batchNo;
        this.status = status;
    }
}
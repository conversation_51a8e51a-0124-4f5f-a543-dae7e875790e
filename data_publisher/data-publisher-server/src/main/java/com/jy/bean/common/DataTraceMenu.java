package com.jy.bean.common;

import com.jy.bean.result.ResultStatus;

import java.util.HashMap;
import java.util.Map;

public enum DataTraceMenu {

    SRC_RECEIVE_DESC("src_receive", "加工数据接收", "产品数据转换平台"),
    SRC_START_DESC("src_start", "加工数据开始处理", "产品数据转换平台"),
    SRC_ACCEPT_DATA("src_accept_data", "加工数据入库", "产品数据转换平台"),
    SRC_CREATE_TABLE("src_create_table", "产品配件表创建", "产品数据转换平台"),
    SRC_TRANSFER_DESC("src_transfer", "加工数据转换", "产品数据转换平台"),
    SRC_COMPARE_DESC("src_compare", "加工数据对比", "产品数据转换平台"),
    SRC_UPDATE_DESC("src_update", "加工数据回写", "产品数据转换平台"),
    SRC_PUSH_DESC("src_push", "加工数据推送", "产品数据转换平台"),
    SRC_NO_PUSH_DESC("src_no_push", "加工数据接收","发布平台无需处理"),

    PUBLISH_RECEIVE_DESC("publish_src_receive", "产品基础数据接收", "产品数据发布平台"),
    PUBLISH_PUSH_DESC("publish_src_push", "产品基础数据推送", "产品数据发布平台"),
    PUBLISH_DEAL_DESC("publish_src_deal", "产品基础数据处理", "产品数据发布平台"),
    CLIENT_PUSH_DESC("client_push", "产品数据发布", "产品数据发布平台"),
    CLIENT_DEAL_DESC("client_deal", "客户端数据更新", "客户端");

    private String code;
    private String name;
    private String serviceName;

    DataTraceMenu(String code , String name,String serviceName){
        this.code = code;
        this.name = name;
        this.serviceName = serviceName;
    }

    public static Map<String, String> valueof(String code) {
        Map<String, String> map = new HashMap<>();
        for (DataTraceMenu a : DataTraceMenu.values()) {
            if (a.code.equals(code)){
                map.put("serviceName", a.getServiceName());
                map.put("nodeName", a.getName());
            }
        }
        return map;
    }


    public static Map<String, String> nameof(String status) {
        Map<String, String> map = new HashMap<>();
        if(ResultStatus.PROCESSING.getStatus().equals(status)){
            map.put("serviceName", DataTraceMenu.CLIENT_PUSH_DESC.getServiceName());
            map.put("nodeName", DataTraceMenu.CLIENT_PUSH_DESC.getName());
            map.put("status", ResultStatus.SUCCESS.getStatus());
        } else if(ResultStatus.SUCCESS.getStatus().equals(status) || ResultStatus.INTERNAL_SERVER_ERROR.getStatus().equals(status)){
            map.put("serviceName", DataTraceMenu.CLIENT_DEAL_DESC.getServiceName());
            map.put("nodeName", DataTraceMenu.CLIENT_DEAL_DESC.getName());
            map.put("status", status);
        }
        return map;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getServiceName() {
        return serviceName;
    }
}

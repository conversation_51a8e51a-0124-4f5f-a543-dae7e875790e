package com.jy.bean.po;

import lombok.Data;

/**
 * 品牌字典实体类
 * 对应d_brand_dict表
 */
@Data
public class BrandDict extends BasePo {
    /**
     * 品牌id
     */
    private String id;

    /**
     * 品牌编码
     */
    private String brandCode;

    /**
     * 品牌名称
     */
    private String brandName;

    public BrandDict() {
    }

    public BrandDict(String id, String brandCode, String brandName) {
        this.id = id;
        this.brandCode = brandCode;
        this.brandName = brandName;
    }
}

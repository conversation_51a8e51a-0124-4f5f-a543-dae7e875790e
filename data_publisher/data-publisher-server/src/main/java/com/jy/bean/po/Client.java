package com.jy.bean.po;

import lombok.Data;

import java.util.List;

/**
 *
 * @Author: zy
 * @Date: Created in 2018/3/29
 */
@Data
public class Client extends BasePo{
    private String id;
    private String code;
    private String name;
    private String ip;
    private String path;
    private String status;
    private Integer topLimit; //src客户端：定时处理的数据阀值   client客户端：对应mq的阀值，超过此数量，数据会自动从mq取出，状态失败
    private List<OrgMp> orgMps;

    private Integer receiveLimit; //设定的客户端接收数据阀值,目前用于src接收数据上限控制以及本地化价格客户端接收上限

    public void setClient(String code, String status){
       this.code = code;
        this.status = status;
    }
}

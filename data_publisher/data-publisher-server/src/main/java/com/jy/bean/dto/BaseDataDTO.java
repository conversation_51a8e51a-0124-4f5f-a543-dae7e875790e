package com.jy.bean.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2018/1/31
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BaseDataDTO implements Cloneable{

    private String id;
    private String batchNoStatus;//状态
    /**  insert, update, delete */
    private String operate;

    private String tableName;

    private String orgCode;
    private String sqlType;
    /**  批次号 */
    private String batchNo;
    private String mainBatchNo;
    /**  主键 */
    private Map<String, String> keys;
    /**  主键 */
    private Map<String, String> modify;
    /**  属性信息 */
    private Map<String, String> fields;
    /**  必传属性信息 */
    private Map<String, String> must;

    /**list节点数据**/
    private Map<String, List<Map<String,Object>>> arrays;

    /**表后缀**/
    private Map<String,Object> suffix;

    private String clientCode;
    private String clientUrl;
    private String clientPath;
    private int sendTimes;

    private String status;
    private String message;

    public BaseDataDTO(){}

    public BaseDataDTO(String mainBatchNo, String batchNo,int sendTimes){
        this.mainBatchNo = mainBatchNo;
        this.batchNo = batchNo;
        this.batchNoStatus = "end";
        this.sendTimes = sendTimes;

    }

    @Override
    public BaseDataDTO clone() {
        BaseDataDTO baseDataDTO = null;
        try {
            baseDataDTO = (BaseDataDTO)super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        return baseDataDTO;
    }

    @Override
    public String toString() {
        return "BaseDataDTO{" +
                "id='" + id + '\'' +
                ", operate='" + operate + '\'' +
                ", tableName='" + tableName + '\'' +
                ", orgCode='" + orgCode + '\'' +
                ", sqlType='" + sqlType + '\'' +
                ", keys=" + keys +
                ", modify=" + modify +
                ", fields=" + fields +
                ", must=" + must +
                ", clientUrl=" + clientUrl +
                ", clientPath=" + clientPath +
                '}';
    }
}

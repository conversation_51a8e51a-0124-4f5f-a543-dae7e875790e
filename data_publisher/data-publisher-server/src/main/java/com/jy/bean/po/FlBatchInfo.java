package com.jy.bean.po;

import com.jy.util.DateUtil;
import com.jy.util.StringUtils;
import lombok.Data;

import java.util.Date;

/**
 * @Author: zy
 * @Date: Created in 2019/11/22
 */
@Data
public class FlBatchInfo extends BasePo {

    private Long id;
    private String batchNo;
    private String mainBatchNo;
    private String dataType;
    private String status;
    private String message;
    private String serviceName;
    private String nodeName;
    private String clientCode;
    private Date startTime;
    private Date endTime;
    private Integer successNum = 0;
    private Integer failNum = 0;
    private Integer processingNum = 0;
    private Integer unsuccessNum = 0;
    private String brandCode;
    private String brandName;
    private String clientName;
    private String dataSource;

    public FlBatchInfo() {
    }
    public  FlBatchInfo(String clientCode, String batchNo, String status){
        this.clientCode = clientCode;
        this.batchNo = batchNo;
        this.status = status;
        this.setUTime(new Date());
        this.endTime = new Date();
    }
    public Long getId() {
        return id;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public String getMainBatchNo() {
        return mainBatchNo;
    }

    public String getDataType() {
        return dataType;
    }

    public String getStatus() {
        return status;
    }

    public String getMessage() {
        return message;
    }

    public String getServiceName() {
        return serviceName;
    }

    public String getNodeName() {
        return nodeName;
    }

    public String getClientName() {
        return clientName;
    }

    public Integer getSuccessNum() {
        return successNum;
    }

    public Integer getFailNum() {
        return failNum;
    }

    public Integer getProcessingNum() {
        return processingNum;
    }
    public String getStartTimeStr() {
        return DateUtil.convertDateToString("yyyy-MM-dd HH:mm:ss", startTime);
    }
    public String getEndTimeStr() {
        return DateUtil.convertDateToString("yyyy-MM-dd HH:mm:ss", endTime);
    }

    public Integer getUnsuccessNum() {
        return unsuccessNum;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }
}

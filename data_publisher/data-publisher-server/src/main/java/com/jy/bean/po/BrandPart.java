package com.jy.bean.po;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import java.util.Date;

/**
 * Brand part entity class
 * Represents data from d_brand_part_* tables
 */
@Data
public class BrandPart extends BasePo {
    private String id;
    private String updateId;
    private String batchNo;
    private String supTableId;
    private String supPartId;
    private String supPartCode;
    private String supPartName;
    private String originalPartId;
    private String originalPartName;
    private String originalPartCode;
    private String originalShortName;
    private String brandId;
    private String brandCode;
    private String status;
    private String tableName;
    private String operate;
    private Date createTime;
    private Date updateTime;

    public BrandPart() {
    }

    /**
     * 从JSON数据节点转换为BrandPart实例
     * @param dataNode 完整的data节点JSON对象
     * @param batchNo 批次号
     * @param tableName 表名
     * @return 转换后的BrandPart对象
     */
    public static BrandPart convertFromJson(JSONObject dataNode, String batchNo, String tableName) {
        BrandPart brandPart = new BrandPart();

        // 获取fields、must和keys节点
        JSONObject fields = dataNode.getJSONObject("fields");
        JSONObject keys = dataNode.getJSONObject("keys");
        String operate = dataNode.getString("operate");

        // 设置ID (从keys中获取)
        if (keys != null && keys.containsKey("tableId")) {
            brandPart.setSupTableId(keys.getString("tableId"));
        }

        // 从fields节点获取基本字段
        if (fields != null) {
            brandPart.updateId = fields.getString("updateId");
            brandPart.supPartId = fields.getString("stdPartId");
            brandPart.supPartCode = fields.getString("stdPartCode");
            brandPart.supPartName = fields.getString("stdPartName");
            brandPart.originalPartName = fields.getString("oeName");
            brandPart.originalPartId = fields.getString("oeId");
            brandPart.originalPartCode = fields.getString("oe");
            brandPart.originalShortName = fields.getString("searchOe");

            // 处理brandId，可能来自"brandId"或"ppid"
            String brandId = fields.getString("brandId");
            if (brandId == null || brandId.isEmpty()) {
                brandId = fields.getString("ppid");
            }
            brandPart.brandId = brandId;

            // 从JSON获取brandCode
            brandPart.brandCode = fields.getString("brandCode");
            brandPart.status = fields.getString("scbz");

            // 处理日期字段
            Object createTimeObj = fields.get("jlrq");
            if (createTimeObj != null) {
                if (createTimeObj instanceof Date) {
                    brandPart.setCreateTime((Date) createTimeObj);
                } else {
                    brandPart.setCreateTime(fields.getDate("jlrq"));
                }
            }

            Object updateTimeObj = fields.get("xgrq");
            if (updateTimeObj != null) {
                if (updateTimeObj instanceof Date) {
                    brandPart.setUpdateTime((Date) updateTimeObj);
                } else {
                    brandPart.setUpdateTime(fields.getDate("xgrq"));
                }
            }
        }

        // 设置批次号、表名和操作类型
        brandPart.batchNo = batchNo;
        brandPart.tableName = tableName;
        brandPart.operate = operate;

        return brandPart;
    }
}

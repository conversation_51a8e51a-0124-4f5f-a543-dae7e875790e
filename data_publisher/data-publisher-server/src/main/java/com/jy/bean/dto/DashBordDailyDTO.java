package com.jy.bean.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jy.bean.po.FlMainBatchInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: zy
 * @Date: Created in 2019/11/22
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DashBordDailyDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String mainBatchNo;
    private String status;
    private String clientCode;
    private Integer totalCount = 0;
    private Integer totalProcessing = 0;
    private Integer publisherProcessing = 0;
    private Integer transferProcessing = 0;
    private Integer transferReceive = 0;

    private Integer transferOrder = 0;
    private Integer publishOrder = 0;

    private List<FlMainBatchInfo> mainBatchList;
}

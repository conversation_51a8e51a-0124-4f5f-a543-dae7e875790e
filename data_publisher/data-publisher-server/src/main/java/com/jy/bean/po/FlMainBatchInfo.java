package com.jy.bean.po;

import com.jy.util.DateUtil;
import lombok.Data;

import java.util.Date;

/**
 * @Author: zy
 * @Date: Created in 2019/11/22
 */
@Data
public class FlMainBatchInfo extends BasePo {

    private Long id;
    private String mainBatchNo;
    private String dataRemark;
    private String status;
    private String message;
    private String serviceName;
    private String nodeName;
    private String clientCode;
    private Date startTime;
    private Date endTime;
    private String clientName;
    private Long totalTime;
    private Long waitTime;
    private Long processTime;
    private String dataSource;

    public FlMainBatchInfo() {
    }

    public FlMainBatchInfo(String mainBatchNo){
        this.mainBatchNo = mainBatchNo;
    }

    public FlMainBatchInfo(String clientCode, String dataRemark, String mainBatchNo, String status){
        this.clientCode = clientCode;
        this.dataRemark = dataRemark;
        this.mainBatchNo = mainBatchNo;
        this.status = status;
        this.setUTime(new Date());
        this.endTime = new Date();
    }
    public Long getId() {
        return id;
    }

    public String getMainBatchNo() {
        return mainBatchNo;
    }

    public String getDataRemark() {
        return dataRemark;
    }

    public String getStatus() {
        return status;
    }

    public String getMessage() {
        return message;
    }

    public String getServiceName() {
        return serviceName;
    }

    public String getNodeName() {
        return nodeName;
    }

    public String getClientName() {
        return clientName;
    }

    public String getStartTimeStr() {
        return DateUtil.convertDateToString("yyyy-MM-dd HH:mm:ss", startTime);
    }
    public String getEndTimeStr() {
        return DateUtil.convertDateToString("yyyy-MM-dd HH:mm:ss", endTime);
    }


    public String getClientCode() {
        return clientCode;
    }

    public Long getTotalTime() {
        return totalTime;
    }

    public Long getWaitTime() {
        return waitTime;
    }

    public Long getProcessTime() {
        return processTime;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }
}

package com.jy.bean.common;

import com.jy.util.EmptyUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/5/15
 */
public enum ClientDescMenu {
    FACADE_DESC("FACADE", "facade类型客户端"),
    SRC_DESC("SRC", "src数据源类型客户端");

    private String code;
    private String name;

    ClientDescMenu(String code , String name){
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }}

package com.jy.bean.dto;

import lombok.Data;

import java.util.List;

@Data
public class BaseDataDTOs {

    private String mainBatchNo;
    private String batchNo;
    private String orgCode;
    private String sqlType;
    private String tableName;
    private String clientCode;
    private String clientUrl;
    private String brandCode;
    private String brandName;
    private Integer batchOrder;
    private String status;
    private String nodeName;
    private String serviceName;
    private String message;

    private int total;
    private List<BaseDataDTO> data;

}

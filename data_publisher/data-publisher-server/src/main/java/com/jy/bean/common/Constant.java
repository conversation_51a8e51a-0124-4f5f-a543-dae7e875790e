package com.jy.bean.common;

/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2017/12/18
 */
public class Constant {

    public static final String ROLE_QUERY = "ROLE_QUERY";

    public static final String ROLE_UPDATE = "ROLE_UPDATE";

    public static final String YES_STRING = "1";

    public static final String NO_STRING = "0";

    //Facade客户端配件服务控制推送的开关数量
    //public static Integer SRC_PART_UPDATE_LIMIT = 10000;
    //本地化价格服务控制接收的开关数量
    public static Integer SRC_LOCAL_PRICE_RECEIVE_LIMIT = 200000;

    //本地化价格服客户端务客户端mq承载的上限数量
    //public static Integer CLIENT_PRICE_RECEIVE_LIMIT = 2000000;

    //Facade客户端精准定件服务控制推送的开关数量
   // public static Integer SRC_VIN_PART_UPDATE_LIMIT = 30000;
}

package com.jy.bean.po;

import com.alibaba.fastjson.JSONObject;
import com.jy.util.NumberUtils;
import lombok.Data;

/**
 * @Author: zy
 * @Date: Created in 2019/11/22
 */
@Data
public class DataTraceAgg {
    private String serviceName;
    private String batchNo;
    private String nodeName;
    private String status;
    private String clientCode;
    private String dataSource;
    private Integer sendTimes;
    private Integer dataNum;
    private Long startTime;
    private Long endTime;

    private String mainBatchNo;
    private String message;

    public DataTraceAgg() {
    }

    public DataTraceAgg(JSONObject jsonObject){
        String key = jsonObject.getString("key");
        key = key.replace("[", "").replace("]","");
        String strArray[] = key.split("##");

        this.serviceName = strArray[0];
        this.nodeName = strArray[1];
        if(strArray.length > 2){
            this.status = strArray[2];
        }
        if(strArray.length > 3){
            this.sendTimes = NumberUtils.String2Int(strArray[3], 1);
        }
        if(strArray.length > 4){
            this.clientCode = strArray[4];
        }
        if(strArray.length > 5){
            this.dataSource = strArray[5];
        }

        this.dataNum = jsonObject.getJSONObject("dataCount").getInteger("value");
        this.startTime = jsonObject.getJSONObject("startTime").getLong("value");
        this.endTime = jsonObject.getJSONObject("endTime").getLong("value");
    }
    public DataTraceAgg(JSONObject jsonObject,String dataSource){
        String key = jsonObject.getString("key");
        key = key.replace("[", "").replace("]","");
        String strArray[] = key.split("##");

        this.serviceName = strArray[0];
        this.nodeName = strArray[1];
        if(strArray.length > 2){
            this.status = strArray[2];
        }
        if(strArray.length > 3){
            this.sendTimes = NumberUtils.String2Int(strArray[3], 1);
        }
        if(strArray.length > 4){
            this.clientCode = strArray[4];
        }
        this.dataSource = dataSource;
        this.dataNum = jsonObject.getJSONObject("dataCount").getInteger("value");
        this.startTime = jsonObject.getJSONObject("startTime").getLong("value");
        this.endTime = jsonObject.getJSONObject("endTime").getLong("value");
    }
}

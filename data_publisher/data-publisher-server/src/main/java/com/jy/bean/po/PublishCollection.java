//package com.jy.bean.po;
//
//import com.alibaba.fastjson.JSONObject;
//import com.jy.bean.result.ResultStatus;
//import lombok.Data;
//import org.springframework.data.annotation.Id;
//import org.springframework.data.annotation.Transient;
//
//import java.util.Date;
//
///**
// * @Author: jdd
// * @Date: Created in 2019/07/23
// */
//@Data
//public class PublishCollection extends BasePo {
//
//    @Id
//    private String id;
//    private String queueName;
//    private String status;
//    private String message;
//    private int sendCount;
//    private JSONObject data;
//    private String batchNo;
//
//    @Transient
//    private String createTime;
//    @Transient
//    private String updateTime;
//    @Transient
//    private String clientName;
//    @Transient
//    private String batchNoStatus;//状态
//    private int sendTimes;   //当条数据推送次数
//
//    private String mainBatchNo;
//
//
//    public PublishCollection() {
//        super();
//    }
//
//    public PublishCollection(JSONObject data, String queueName) {
//        this.data = data;
//        this.sendCount = 1;
//        this.setCTime(new Date());
//        this.setUTime(new Date());
//        this.queueName = queueName;
//        this.sendTimes = data.getInteger("sendTimes");
//        this.id = data.getString("id");
//        this.batchNo = data.getString("batchNo");
//        this.status = ResultStatus.UNSENT.getStatus();
//        this.message = ResultStatus.UNSENT.getMessage();
//    }
//
//    public void reSendPublishTrail(){
//        this.setUTime(new Date());
//        this.status = ResultStatus.UNSENT.getStatus();
//        this.message = ResultStatus.UNSENT.getMessage();
//        this.sendCount = sendCount + 1;
//    }
//
//    public void callbackPublishTrail(String status, String message){
//        //this.id = id;
//        this.status = status;
//        this.message = message;
//        this.setUTime(new Date());
//        //this.sendCount = sendCount + 1;
//    }
//}

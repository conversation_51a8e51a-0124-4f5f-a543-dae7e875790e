package com.jy.bean.po;

import lombok.Data;

import java.util.List;

/**
 *
 * @Author: zy
 * @Date: Created in 2018/3/29
 */
@Data
public class ClientFilter extends BasePo{
    private String id;
    private String clientCode;
    private String tableName;
    private String filterKey;
    private String filterValue;
    private String filterNode;

    private String filterRule;//intercept 拦截， approve 通过

}

/*
package com.jy.bean.dto;

import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

*/
/**
 * Created by anxing on 2018/7/5.
 *//*

@Data
@Document(collection = "egg")
public class LogDTO {

    private String versionCode;
    private String companyCode;
    private String state;
    private String resultType;
    private String interfaceCode;
    private String platformCode;
    private String dealTime;
    private String dataCode;
    private String dealName;
    private String batchNo;
    private String queueName;
    private String status;

    public LogDTO() {
        super();
    }

    public LogDTO(String versionCode, String companyCode, String state, String resultType, String interfaceCode,
                  String platformCode, String dealTime, String dataCode, String dealName,String batchNo,
                  String queueName,String status) {
        this.versionCode = versionCode;
        this.companyCode = companyCode;
        this.state = state;
        this.resultType = resultType;
        this.interfaceCode = interfaceCode;
        this.platformCode = platformCode;
        this.dealTime = dealTime;
        this.dataCode = dataCode;
        this.dealName = dealName;
        this.batchNo = batchNo;
        this.status = status;
        this.queueName = queueName;
    }

    @Override
    public String toString() {
        return "LogDTO{" +
                "versionCode='" + versionCode + '\'' +
                ", companyCode='" + companyCode + '\'' +
                ", state='" + state + '\'' +
                ", resultType='" + resultType + '\'' +
                ", interfaceCode='" + interfaceCode + '\'' +
                ", platformCode='" + platformCode + '\'' +
                ", dealTime='" + dealTime + '\'' +
                ", dataCode='" + dataCode + '\'' +
                ", dealName='" + dealName + '\'' +
                ", batchNo='" + batchNo + '\'' +
                ", status='" + status + '\'' +
                ", queueName='" + queueName + '\'' +
                '}';
    }
}
*/

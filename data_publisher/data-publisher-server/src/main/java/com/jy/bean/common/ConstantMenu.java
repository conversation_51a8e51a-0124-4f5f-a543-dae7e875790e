package com.jy.bean.common;

/**
 * <AUTHOR>
 * @date 2018/5/14
 */
public enum ConstantMenu {

    ADMIN("1", "管理员", "ROLE_UPDATE" ),
    COMMON("2", "普通用户", "ROLE_QUERY");

    private String id;
    private String name;
    private String value;

    ConstantMenu(String id, String name, String value){
        this.id = id;
        this.name = name;
        this.value = value;
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getValue() {
        return value;
    }
}

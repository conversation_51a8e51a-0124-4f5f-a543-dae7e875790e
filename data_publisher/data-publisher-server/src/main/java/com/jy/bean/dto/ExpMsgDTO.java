package com.jy.bean.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2018/1/31
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExpMsgDTO implements Cloneable{

    private String methodName;
    private String remark;
    private String startTime;
    private String endTime;
    private String hostName;
    private String msg;

    private String timestamp;
    private String sign;

}

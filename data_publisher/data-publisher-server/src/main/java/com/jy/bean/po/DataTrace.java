package com.jy.bean.po;

import lombok.Data;

import java.util.Objects;

/**
 * @Author: zy
 * @Date: Created in 2019/11/28
 */
@Data
public class DataTrace extends BasePo{
    private String nodeName;
    private String batchNo;
    private String clientName;
    private Integer num;
    private String message;
    private String serviceName;
    private String type;
    private String dataVersionId;
    private Long currentTime;
    private String mainBatchNo;
    private String status;

    private Integer sendTimes;
    private String clientCode;
    private String id;

    public DataTrace() {
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        DataTrace dataTrace = (DataTrace) o;
        return Objects.equals(batchNo, dataTrace.batchNo) &&
                Objects.equals(mainBatchNo, dataTrace.mainBatchNo) &&
                Objects.equals(clientCode, dataTrace.clientCode) &&
                Objects.equals(id, dataTrace.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), batchNo, mainBatchNo, clientCode, id);
    }
}

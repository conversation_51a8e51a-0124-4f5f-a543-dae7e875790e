package com.jy.bean.po;

import com.jy.util.StringUtils;
import lombok.Data;
import java.util.Date;

/**
 * Brand part history entity class
 * Represents data from d_brand_part_his_ada* tables
 */
@Data
public class BrandPartHis extends BasePo {
    private String id;
    // 及时更新id字段
    private String updateId;
    private String batchNo;
    private String supTableId;
    private String supPartId;
    private String supPartCode;
    private String supPartName;
    private String originalPartId;
    private String originalPartName;
    private String originalPartCode;
    private String originalShortName;
    private String brandId;
    private String brandCode;
    private String status;
    private String tableName;
    private String operate;
    private String clientCodes;
    private Date createTime;
    private Date updateTime;

    public BrandPartHis() {
    }

    public BrandPartHis(BrandPart brandPart) {
        this.id = StringUtils.getUUID();
        this.updateId = brandPart.getUpdateId();
        this.batchNo = brandPart.getBatchNo();
        this.supTableId = brandPart.getSupTableId();
        this.supPartId = brandPart.getSupPartId();
        this.supPartCode = brandPart.getSupPartCode();
        this.supPartName = brandPart.getSupPartName();
        this.originalPartName = brandPart.getOriginalPartName();
        this.originalPartCode = brandPart.getOriginalPartCode();
        this.originalShortName = brandPart.getOriginalShortName();
        this.brandId = brandPart.getBrandId();
        this.brandCode = brandPart.getBrandCode();
        this.status = brandPart.getStatus();
        this.tableName = brandPart.getTableName();
        this.operate = brandPart.getOperate();
        this.originalPartId = brandPart.getOriginalPartId();
        this.createTime = brandPart.getCreateTime();
        this.updateTime = brandPart.getUpdateTime();
    }
}

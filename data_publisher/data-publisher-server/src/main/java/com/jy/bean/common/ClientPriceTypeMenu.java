package com.jy.bean.common;

import com.jy.util.EmptyUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/5/15
 */
public enum ClientPriceTypeMenu {
    DEFAULT_MARKET_PRICE_TYPE("default", "market_price_type", "6"),
    DEFAULT_ANALYSIS_PRICE_TYPE("default", "analysis_price_type", "6"),
    DEFAULT_SUIT_PRICE_TYPE("default", "suit_price_type", "6");
/*    AHIC_MARKET_PRICE_TYPE("AHIC", "market_price_type", "1"),
    AHIC_ANALYSIS_PRICE_TYPE("AHIC", "analysis_price_type", "2");*/

    private String code;
    private String fieldName;
    private String fieldValue;

    ClientPriceTypeMenu(String code , String fieldName, String fieldValue){
        this.code = code;
        this.fieldName = fieldName;
        this.fieldValue = fieldValue;
    }

    public static Map<String, String> valueof(String code) {
        Map<String, String> map = new HashMap<>();
        for (ClientPriceTypeMenu a : ClientPriceTypeMenu.values()) {
            if (a.code.equals(code)){
                map.put(a.getFieldName(), a.getFieldValue());
            }
        }
        if(EmptyUtils.isEmpty(map)){
            map.put(DEFAULT_MARKET_PRICE_TYPE.getFieldName(), DEFAULT_MARKET_PRICE_TYPE.getFieldValue());
            map.put(DEFAULT_ANALYSIS_PRICE_TYPE.getFieldName(), DEFAULT_ANALYSIS_PRICE_TYPE.getFieldValue());
        }
        return map;
    }

    public String getCode() {
        return code;
    }

    public String getFieldName() {
        return fieldName;
    }

    public String getFieldValue() {
        return fieldValue;
    }
}

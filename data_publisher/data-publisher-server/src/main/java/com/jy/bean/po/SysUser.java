package com.jy.bean.po;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by zy on 2017/10/23.
 */
@Data
public class SysUser extends BasePo {
    private String id;
    private String username;
    private String password;
    private String email;
    private String imageUrl;
    private Date lastPasswordResetDate;
    private Set<SysRole> roles = new HashSet<>();

    @JsonIgnore
    public Set<GrantedAuthority> getAuthorities() {
        Set<GrantedAuthority> userAuthotities = new HashSet<>();
        for(SysRole role : this.roles){
            userAuthotities.add(new SimpleGrantedAuthority(role.getValue()));

            for(SysAuthority authority : role.getAuthorities()){
                userAuthotities.add(new SimpleGrantedAuthority(authority.getValue()));
            }
        }

        return userAuthotities;
    }
}

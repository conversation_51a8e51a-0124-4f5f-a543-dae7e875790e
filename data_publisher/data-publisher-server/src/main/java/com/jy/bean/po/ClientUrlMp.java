package com.jy.bean.po;

import lombok.Data;

import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 2018/5/23
 */
@Data
public class ClientUrlMp extends BasePo{
    private String id;
    private String baseClientCode;
    private String clientCode;
    private String tableName;
    private String url;
    private String path;

    @Override
    public String toString() {
        return new StringJoiner(", ", ClientUrlMp.class.getSimpleName() + "[", "]")
                .add("id='" + id + "'")
                .add("baseClientCode='" + baseClientCode + "'")
                .add("clientCode='" + clientCode + "'")
                .add("tableName='" + tableName + "'")
                .add("url='" + url + "'")
                .add("path='" + path + "'")
                .toString();
    }
}

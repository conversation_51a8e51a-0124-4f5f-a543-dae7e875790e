package com.jy.service.impl;

import com.jy.ann.MethodMonitor;
import com.jy.bean.po.NoticeConfig;
import com.jy.mapper.NoticeConfigMapper;
import com.jy.service.NoticeConfigService;
import com.jy.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: caolt
 * @Description:
 * @Version:
 * @Date: Created in  2020/07/23
 */
@Service
public class NoticeConfigServiceImpl implements NoticeConfigService {

    @Autowired
    private NoticeConfigMapper noticeConfigMapper;

    @Override
    @MethodMonitor
    public List<NoticeConfig> listByClientCodes(String clientCodes) {
        Map<String, Object> map = new HashMap<>();
        map.put("clientCodes", clientCodes);
        return noticeConfigMapper.listNoticeConfig(map);
    }

    @Override
    @MethodMonitor
    public NoticeConfig save(NoticeConfig noticeConfig) throws Exception {
        noticeConfig.setId(StringUtils.getUUID());
        noticeConfigMapper.save(noticeConfig);
        return noticeConfig;
    }

    @Override
    @MethodMonitor
    public void delete(String id) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("id", id);
        noticeConfigMapper.delete(map);
    }

    @Override
    @MethodMonitor
    public NoticeConfig update(NoticeConfig noticeConfig) throws Exception {
        noticeConfigMapper.update(noticeConfig);
        return noticeConfig;
    }
}

package com.jy.service.impl;

import com.jy.ann.MethodMonitor;
import com.jy.bean.po.OrgMp;
import com.jy.mapper.OrgMpMapper;
import com.jy.service.OrgMpService;
import com.jy.util.EmptyUtils;
import com.jy.util.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: zy
 * @Date: Created in 2018/4/16
 */
@Service
@CacheConfig(cacheManager = "ehCacheCacheManager", cacheNames = "orgMp")
public class OrgMpServiceImpl implements OrgMpService{

    @Autowired
    private OrgMpMapper orgMpMapper;
    @Autowired
    private OrgMpService orgMpService;

    @Override
    @Cacheable(key = "#root.targetClass + ':' + #root.methodName + ':' + #baseOrgCode")
    @MethodMonitor
    public Map<String ,OrgMp> mapByBaseOrgCode(String baseOrgCode) {
        Map<String, Object> map = new HashMap<>();
        map.put("baseOrgCode", baseOrgCode);
        List<OrgMp> orgMps = orgMpMapper.listOrgMp(map);
        return orgMps.stream().collect(
                Collectors.toMap(OrgMp::getClientCode, orgMp -> orgMp));
    }

    @Override
    @Cacheable(key = "#root.targetClass + ':' + #root.methodName + ':' + #clientCode +  ':' + #baseOrgCode")
    @MethodMonitor
    public OrgMp getByClientCodeAndBaseOrgCode(@NotNull String clientCode, @NotNull String baseOrgCode) {
        Map<String, Object> map = new HashMap<>();
        map.put("clientCode", clientCode);
        map.put("baseOrgCode", baseOrgCode);
        List<OrgMp> list = orgMpMapper.listOrgMp(map);
        if(EmptyUtils.isNotEmpty(list)){
            return list.get(0);
        }
        return null;
    }

    @Override
    @MethodMonitor
    public List<OrgMp> listOrgMp(Map<String, Object> map) {
        return orgMpMapper.listOrgMp(map);
    }

    @Override
    @MethodMonitor
    public List<OrgMp> listByClientCode(String clientCode) {
        Map<String, Object> map = new HashMap<>();
        map.put("clientCode", clientCode);
        return orgMpMapper.listOrgAndCodeNameByClientCode(map);
    }

    @Override
    @Caching(evict = {
            @CacheEvict(key = "#root.targetClass + ':mapByBaseOrgCode:' + #result.baseOrgCode"),
            @CacheEvict(key = "#root.targetClass + ':getByClientCodeAndBaseOrgCode:' + #result.clientCode +  ':' + #result.baseOrgCode")})
    @MethodMonitor
    public OrgMp save(OrgMp orgMp) throws Exception {
        orgMp.setId(StringUtils.getUUID());
        orgMpMapper.save(orgMp);
        return orgMp;
    }

    @Override
    @MethodMonitor
    public void delete(String id) throws Exception {
        orgMpService.clearCache(id);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("id", id);
        orgMpMapper.delete(map);
    }

    @Override
    @Caching(evict = {
            @CacheEvict(key = "#root.targetClass + ':mapByBaseOrgCode:' + #result.baseOrgCode"),
            @CacheEvict(key = "#root.targetClass + ':getByClientCodeAndBaseOrgCode:' + #result.clientCode +  ':' + #result.baseOrgCode")})
    @MethodMonitor
    public OrgMp update(OrgMp orgMp) throws Exception {
        orgMpService.clearCache(orgMp.getId());
        orgMpMapper.update(orgMp);
        return orgMp;
    }

    @Caching(evict = {
            @CacheEvict(key = "#root.targetClass + ':mapByBaseOrgCode:' + #result.baseOrgCode"),
            @CacheEvict(key = "#root.targetClass + ':getByClientCodeAndBaseOrgCode:' + #result.clientCode +  ':' + #result.baseOrgCode")})
    @Override
    public OrgMp clearCache(String id) throws Exception {
        OrgMp temp = null;
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("id", id);
        List<OrgMp> orgMps = orgMpMapper.listOrgMp(map);
        if(EmptyUtils.isNotEmpty(orgMps)){
            temp = orgMps.get(0);
        }
        return temp;
    }
}

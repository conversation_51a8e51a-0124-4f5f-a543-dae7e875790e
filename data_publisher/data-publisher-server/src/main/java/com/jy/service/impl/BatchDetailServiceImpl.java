package com.jy.service.impl;

import com.jy.ann.MethodMonitor;
import com.jy.bean.po.BatchDetail;
import com.jy.mapper.BatchDetailMapper;
import com.jy.service.BatchDetailService;
import com.jy.util.EmptyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class BatchDetailServiceImpl implements BatchDetailService {

    @Autowired
    private BatchDetailMapper batchDetailMapper;

    @Override
    @MethodMonitor
    public List<BatchDetail> listBatchDetail(Map<String, Object> map) {

        return null;
    }

    @Override
    //@MethodMonitor
    public List<BatchDetail> listByClientCodeAndStatus(String clientCode, String status) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("clientCode", clientCode);
        map.put("status", status);
        return batchDetailMapper.listBatchDetail(map);
    }

    @Override
    @MethodMonitor
    public int getCountByClientCodeAndStatus(String clientCode, String status) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("clientCode", clientCode);
        map.put("status", status);
        return batchDetailMapper.getCount(map);
    }

    @Override
    public List<BatchDetail> listByMainBatchNo(String mainBatchNo, String batchNo) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("mainBatchNo", mainBatchNo);
        map.put("batchNo", batchNo);
        return batchDetailMapper.listBatchDetail(map);
    }

    @Override
    @MethodMonitor
    public BatchDetail getByBatchNo(String batchNo) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("batchNo", batchNo);
        List<BatchDetail> batchDetails = batchDetailMapper.listBatchDetail(map);
        if(EmptyUtils.isNotEmpty(batchDetails)){
            return batchDetails.get(0);
        }
        return null;
    }

    @Override
    @MethodMonitor
    public BatchDetail save(BatchDetail batchDetail) throws Exception {
        batchDetailMapper.save(batchDetail);
        return batchDetail;
    }

    @Override
    @MethodMonitor
    public void delete(String id) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("id", id);
        batchDetailMapper.delete(map);
    }

    @Override
    @MethodMonitor
    public BatchDetail update(BatchDetail batchDetail) throws Exception {
        batchDetailMapper.update(batchDetail);
        return batchDetail;
    }

    @Override
    public List<BatchDetail> batchListByMainAndBatchNo(List<BatchDetail> list) {
        return batchDetailMapper.batchListByMainAndBatchNo(list);
    }

    @Override
    public void updateBatch(List<BatchDetail> batchDetailList) {
        batchDetailMapper.updateBatch(batchDetailList);
    }
}

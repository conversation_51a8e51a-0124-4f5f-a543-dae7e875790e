package com.jy.service.impl;

import com.jy.ann.MethodMonitor;
import com.jy.bean.po.ClientTable;
import com.jy.bean.po.ClientTableFieldMp;
import com.jy.mapper.ClientTableFieldMpMapper;
import com.jy.service.ClientTableFieldMpService;
import com.jy.service.ClientTableService;
import com.jy.util.EmptyUtils;
import com.jy.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/5/2
 */
@Service
@CacheConfig(cacheManager = "ehCacheCacheManager", cacheNames = "clientTableFieldMp")
public class ClientTableFieldMpServiceImpl implements ClientTableFieldMpService {

    @Autowired
    private ClientTableFieldMpMapper clientTableFieldMpMapper;
    @Autowired
    private ClientTableFieldMpService clientTableFieldMpService;
    @Autowired
    private ClientTableService clientTableService;

    @Override
    @MethodMonitor
    public List<ClientTableFieldMp> listClientTableFieldMp(Map<String, Object> map) {
        if(EmptyUtils.isNotEmpty(map.get("baseTableName"))){
            List<ClientTable> clientTables = clientTableService.listClientTable(map);
            if(EmptyUtils.isNotEmpty(clientTables)){
                map.put("tableName", clientTables.get(0).getTableName());
            }
        }
        return clientTableFieldMpMapper.listClientTableFieldMp(map);
    }

    @Override
    @Cacheable(key = "#root.targetClass + ':' + #root.methodName + ':' + #clientCode")
    @MethodMonitor
    public List<ClientTableFieldMp> listByClientCode(String clientCode) {
        Map<String, Object> map = new HashMap<>();
        map.put("clientCode", clientCode);
        return clientTableFieldMpMapper.listClientTableFieldMp(map);
    }

    @Override
    @Cacheable(key = "#root.targetClass + ':' + #root.methodName + ':' + #clientCode + ':' + #tableName")
    @MethodMonitor
    public List<ClientTableFieldMp> listByClientCodeAndTableName(String clientCode, String tableName) {
        Map<String, Object> map = new HashMap<>();
        map.put("clientCode", clientCode);
        map.put("tableName", tableName);
        return clientTableFieldMpMapper.listClientTableFieldMp(map);
    }

    @Override
    @Cacheable(key = "#root.targetClass + ':' + #root.methodName + ':' + #clientCode + ':' + #tableName")
    @MethodMonitor
    public Map<String,List<ClientTableFieldMp>> mapByClientCodeAndTableName(String clientCode, String tableName) {
        Map<String, Object> map = new HashMap<>();
        map.put("clientCode", clientCode);
        map.put("tableName", tableName);
        List<ClientTableFieldMp> list = clientTableFieldMpMapper.listClientTableFieldMp(map);
        return list.stream().collect(Collectors.groupingBy(ClientTableFieldMp::getBaseTableField));
    }

    @Override
    @Caching(evict = {
            @CacheEvict(key = "#root.targetClass + ':listByClientCodeAndTableName:' + #result.clientCode + ':' + #result.tableName"),
            @CacheEvict(key = "#root.targetClass + ':mapByClientCodeAndTableName:' + #result.clientCode + ':' + #result.tableName"),
            @CacheEvict(key = "#root.targetClass + ':listByClientCode:' + #result.clientCode")})
    @MethodMonitor
    public ClientTableFieldMp save(ClientTableFieldMp clientTableFieldMp) throws Exception {
        clientTableFieldMp.setId(StringUtils.getUUID());
        clientTableFieldMpMapper.save(clientTableFieldMp);
        return clientTableFieldMp;
    }

    @Override
    @CacheEvict(value="clientTableFieldMp",allEntries=true)
    @Transactional
    @MethodMonitor
    public List<ClientTableFieldMp> saveBatch(List<ClientTableFieldMp> clientTableFieldMps) throws Exception {
        clientTableFieldMpMapper.saveBatch(clientTableFieldMps);
        return  clientTableFieldMps;
    }

    @Override
    @MethodMonitor
    public void delete(String id) throws Exception {
        clientTableFieldMpService.clearCache(id);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("id", id);
        clientTableFieldMpMapper.delete(map);
    }

    @Override
    @Caching(evict = {
            @CacheEvict(key = "#root.targetClass + ':listByClientCodeAndTableName:' + #result.clientCode + ':' + #result.tableName"),
            @CacheEvict(key = "#root.targetClass + ':mapByClientCodeAndTableName:' + #result.clientCode + ':' + #result.tableName"),
            @CacheEvict(key = "#root.targetClass + ':listByClientCode:' + #result.clientCode")})
    @MethodMonitor
    public ClientTableFieldMp update(ClientTableFieldMp clientTableFieldMp) throws Exception {
        clientTableFieldMpService.clearCache(clientTableFieldMp.getId());
        clientTableFieldMpMapper.update(clientTableFieldMp);
        return clientTableFieldMp;
    }

    @Override
    @Caching(evict = {
            @CacheEvict(key = "#root.targetClass + ':listByClientCodeAndTableName:' + #result.clientCode + ':' + #result.tableName"),
            @CacheEvict(key = "#root.targetClass + ':mapByClientCodeAndTableName:' + #result.clientCode + ':' + #result.tableName"),
            @CacheEvict(key = "#root.targetClass + ':listByClientCode:' + #result.clientCode")})
    @MethodMonitor
    public ClientTableFieldMp clearCache(String id) throws Exception {
        ClientTableFieldMp temp = null;
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("id", id);
        List<ClientTableFieldMp> clientTableFieldMps = clientTableFieldMpMapper.listClientTableFieldMp(map);
        if(EmptyUtils.isNotEmpty(clientTableFieldMps)){
            temp = clientTableFieldMps.get(0);
        }
        return temp;
    }

    @Override
    public int selectFieldCount(ClientTableFieldMp clientTableFieldMp) {
        return clientTableFieldMpMapper.selectFieldCount(clientTableFieldMp);
    }
}

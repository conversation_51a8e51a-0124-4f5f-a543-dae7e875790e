/*
package com.jy.service;

import com.jy.bean.po.PublishCollection;
import com.jy.bean.po.PublishTrail;
import com.jy.bean.po.PublishTrailVO;

import java.util.List;

*/
/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2018/1/18
 *//*

public interface PublishTrailService {

    void save(PublishTrail trail) throws Exception;

    void save(List<PublishTrail> trails) throws Exception;

    void update(PublishTrail trail) throws Exception;

    void update(List<PublishTrail> trails);

    PublishTrail getOne(String id);

    List<PublishCollection> listByBatchNoAndQueueNameAndStatus(String batchNo, String queueName, String status, boolean type);

    long listByBatchNoStatus(String batchNo, String queueName);

    long listByQueueNameAndTimeNoStatus(String queueName, String createTime, String updateTime);

    List<PublishTrailVO> listGroupByBatchNoAndQueueNameAndStatus(PublishTrailVO publishTrailVO);
}
*/

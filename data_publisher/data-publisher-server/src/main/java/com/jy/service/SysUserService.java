package com.jy.service;

import com.jy.bean.po.SysUser;

import java.util.List;
import java.util.Map;

/**
 * Created by zy on 2017/10/24.
 */
public interface SysUserService {

    SysUser listByUserName(String username) throws Exception;

    List<SysUser> listSortFieldSysUser(Map<String, Object> map) throws Exception;

    SysUser save(SysUser sysUser) throws Exception;

    void delete(String id) throws Exception;

    SysUser update(SysUser sysUser) throws Exception;

    SysUser clearCache(String id) throws Exception;

    void removeToken(String userName) throws Exception;
}

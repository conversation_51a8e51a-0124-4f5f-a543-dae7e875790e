package com.jy.service;

import com.jy.ann.MethodMonitor;
import com.jy.bean.po.BatchDetail;
import com.jy.bean.po.FlBatchInfo;
import org.jetbrains.annotations.NotNull;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: zy
 * @Date: Created in 2019/11/22
 */
public interface FlBatchInfoService {
    void saveBacth(@NotNull List<FlBatchInfo> batchInfoList) throws Exception;
    void save(@NotNull FlBatchInfo batchInfo) throws Exception;
    void update(@NotNull FlBatchInfo batchInfo) throws Exception;

    @MethodMonitor
    void updateSelective(@NotNull FlBatchInfo batchInfo) throws Exception;

    void deleteByBacthNo(@NotNull String bacthNo, String dataSource) throws Exception;
    List<FlBatchInfo> listFlBatchInfo(Map<String,Object> map) throws Exception;

    void updateTraceStatus(FlBatchInfo batchInfo) throws Exception;

    FlBatchInfo getById(Long id);

    FlBatchInfo getFlBatchInfo(FlBatchInfo flBatchInfo) throws Exception;

    void saveOrUpdate(FlBatchInfo baseDataDTOs);

    void reset(String batchNo, Date startTime);

    void resetBatch(List<BatchDetail> batchDetailList, Date startTime);
}

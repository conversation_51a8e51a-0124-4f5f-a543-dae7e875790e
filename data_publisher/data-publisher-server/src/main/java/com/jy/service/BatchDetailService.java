package com.jy.service;

import com.jy.bean.po.BatchDetail;

import java.util.List;
import java.util.Map;

public interface BatchDetailService {

    List<BatchDetail> listBatchDetail(Map<String, Object> map);

    List<BatchDetail> listByClientCodeAndStatus(String clientCode, String status);

    int getCountByClientCodeAndStatus(String clientCode, String status);

    //@MethodMonitor
    List<BatchDetail> listByMainBatchNo(String mainBatchNo, String batchNo);

    BatchDetail getByBatchNo(String batchNo);

    BatchDetail save(BatchDetail batchDetail) throws Exception;

    void delete(String id) throws Exception;

    BatchDetail update(BatchDetail batchDetail) throws Exception;

    List<BatchDetail> batchListByMainAndBatchNo(List<BatchDetail> list);

    void updateBatch(List<BatchDetail> batchDetailList);
}

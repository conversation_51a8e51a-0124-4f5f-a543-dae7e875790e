package com.jy.service;

import com.alibaba.fastjson.JSONObject;
import com.jy.util.FacadeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/5/9
 */
@Service
public class AsyncService {

    @Autowired
    private FacadeUtils facadeUtils;

    public String doPost(String url, String path, Map<String, String> headers, Map<String, String> querys, String bodys) throws Exception {
        JSONObject json = facadeUtils.doPost(url, path, querys, bodys);
       return json.toJSONString();
    }
}

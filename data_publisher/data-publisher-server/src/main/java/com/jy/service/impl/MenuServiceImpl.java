package com.jy.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jy.bean.po.Menu;
import com.jy.bean.po.MenuJson;
import com.jy.bean.po.MenuPo;
import com.jy.mapper.MenuMapper;
import com.jy.service.MenuService;
import com.jy.util.EmptyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/4/26
 */
@Service
public class MenuServiceImpl implements MenuService {

    @Autowired
    private MenuMapper menuMapper;

    @Override
    public MenuJson getOne() {
        MenuJson menuJson = new MenuJson();
        JSONArray menu = new JSONArray();

        List<Menu> menuList = menuMapper.getOne();
        //遍历menuList 将菜单列表中parent 和 child 重组
                for (int i = 0; i < menuList.size(); i++) {
                    JSONObject json = new JSONObject();
                    JSONArray parentSide = new JSONArray();
                    // 找出 父级id 为 000 的，为父级菜单
                    if ("000".equals(menuList.get(i).getMenuParentId())) {
                        json.put("parentName", menuList.get(i).getMenuName());
                        for (int k = 0; k < menuList.size(); k++) {
                            //找到匹配的 子级菜单
                            if (menuList.get(i).getId().equals(menuList.get(k).getMenuParentId())) {
                                JSONObject child = new JSONObject();
                                child.put("childName", menuList.get(k).getMenuName());
                                child.put("childPath", menuList.get(k).getMenuPath());
                                parentSide.add(child); //parentSide 数组添加二级菜单
                            }
                        }
                        json.put("parentSide", parentSide);
                menu.add(json);  //menu 数组添加，一级菜单
            }
        }
        menuJson.setMenu(menu);
        return menuJson;
    }
}
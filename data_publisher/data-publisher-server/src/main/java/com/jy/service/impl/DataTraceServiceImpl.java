package com.jy.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jy.bean.common.DataTraceMenu;
import com.jy.bean.po.DataTrace;
import com.jy.bean.po.DataTraceAgg;
import com.jy.bean.result.ResultStatus;
import com.jy.service.DataTraceService;
import com.jy.util.ElasticsearchUtil;
import com.jy.util.EmptyUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.entity.ContentType;
import org.apache.http.nio.entity.NStringEntity;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @Author: zy
 * @Date: Created in 2019/11/22
 */
@Service
public class DataTraceServiceImpl implements DataTraceService {

    @Override
    public List<DataTraceAgg> listDataTraceAggs(String batchNo) throws Exception {
        return listDataTraceAggs(batchNo, null);
    }

    @Override
    public List<DataTraceAgg> listDataTraceAggs(String batchNo, String dataSource) throws Exception {

        String dsMatch = "";
        if(StringUtils.isNotEmpty(dataSource)){
            dsMatch = ", {\n" +
                    "                \"match\": {\n" +
                    "                    \"dataSource\": \""+dataSource+"\"\n" +
                    "                }\n" +
                    "            }";
        }
        String jsonString = "{\n" +
                "    \"size\": 0,\n" +
                "    \"query\": {\n" +
                "        \"bool\": {\n" +
                "            \"must\": [{\n" +
                "                \"script\": {\n" +
                "                    \"script\": {\n" +
                "                        \"lang\": \"painless\",\n" +
                "                        \"source\": \"doc['clientCode'].size()>0 \"\n" +
                "                    }\n" +
                "                }\n" +
                "            }, {\n" +
                "                \"match\": {\n" +
                "                    \"batchNo\": \""+batchNo+"\"\n" +
                "                }\n" +
                "            }" + dsMatch + "]\n" +
                /*
                "            \",should\": [{\n" +
                "                \"term\": {\n" +
                "                    \"dataSource\": \""+dataSource+"\"\n" +
                "                }\n" +
                "            }, {\n" +
                "                \"bool\": {\n" +
                "                    \"must_not\": {\n" +
                "                        \"exists\": {\n" +
                "                            \"field\": \"dataSource\"\n" +
                "                        }\n" +
                "                    }\n" +
                "                }\n" +
                "            }]\n" +
                */
                "        }\n" +
                "    },\n" +
                "    \"aggs\": {\n" +
                "        \"aggs_terms\": {\n" +
                "            \"terms\": {\n" +
                "                \"script\": {\n" +
                "                             \"source\": \" def result = doc['serviceName'].value + '##' + doc['nodeName'].value + '##' + doc['status'].value + '##' + doc['sendTimes'].value + '##' + doc['clientCode'].value; if (doc.containsKey('dataSource')) { result += '##' + doc['dataSource'].value; } return result; \"" +
                "                }," +
                "                \"order\": {\n" +
                "                    \"startTime\": \"asc\"\n" +
                "                },\n" +
                "                \"size\": 2000\n" +
                "            },\n" +
                "            \"aggs\": {\n" +
                "                \"dataCount\": {\n" +
                "                    \"sum\": {\n" +
                "                        \"field\": \"num\"\n" +
                "                    }\n" +
                "                },\n" +
                "                \"endTime\": {\n" +
                "                    \"max\": {\n" +
                "                        \"field\": \"currentTime\"\n" +
                "                    }\n" +
                "                },\n" +
                "                \"startTime\": {\n" +
                "                    \"min\": {\n" +
                "                        \"field\": \"currentTime\"\n" +
                "                    }\n" +
                "                }\n" +
                "            }\n" +
                "        }\n" +
                "    }\n" +
                "}";
        HttpEntity entity = new NStringEntity(jsonString, ContentType.APPLICATION_JSON);
        String endPoint = "/data-trace-alias/_search";
        JSONObject jsonObject = ElasticsearchUtil.aggs(endPoint, entity);
        JSONArray jsonArray = jsonObject.getJSONObject("aggs_terms").getJSONArray("buckets");
        List<DataTraceAgg> returnList = new ArrayList<>();
        for(Object o : jsonArray){
            DataTraceAgg dataTraceAgg = new DataTraceAgg((JSONObject)o, dataSource);
            dataTraceAgg.setBatchNo(batchNo);
            returnList.add(dataTraceAgg);
        }
        return returnList;
    }
    public List<DataTraceAgg> getFlBatchInfo(String mainBatchNo, String batchNo, String clientCode, String nodeName,String dataSource) throws Exception {
        String jsonString = "{\n" +
                "    \"size\": 0,\n" +
                "    \"query\": {\n" +
                "        \"bool\": {\n" +
                "            \"must\": [{\n" +
                "                \"script\": {\n" +
                "                    \"script\": {\n" +
                "                        \"lang\": \"painless\",\n" +
                "                        \"source\": \"doc['clientCode'].size()>0 \"\n" +
                "                    }\n" +
                "                }\n" +
                "            }, {\n" +
                "                \"match\": {\n" +
                "                    \"batchNo\": \""+batchNo+"\"\n" +
                "                }\n" +
                "            }" + ", {\n" +
                "                \"match\": {\n" +
                "                    \"mainBatchNo\": \""+mainBatchNo+"\"\n" +
                "                }\n" +
                "            }"  + ", {\n" +
                "                \"match\": {\n" +
                "                    \"clientCode\": \""+clientCode+"\"\n" +
                "                }\n" +
                "            }" +  ", {\n" +
                "                \"match\": {\n" +
                "                    \"dataSource\": \""+dataSource+"\"\n" +
                "                }\n" +
                "            }" + ", {\n" +
                "                \"match\": {\n" +
                "                    \"nodeName\": \""+nodeName+"\"\n" +
                "                }\n" +
                "            }" + "]\n" +
                "        }\n" +
                "    },\n" +
                "    \"aggs\": {\n" +
                "        \"aggs_terms\": {\n" +
                "            \"terms\": {\n" +
                "                \"script\": {\n" +
                "                             \"source\": \" def result = doc['serviceName'].value + '##' + doc['nodeName'].value + '##' + doc['status'].value + '##' + doc['sendTimes'].value + '##' + doc['clientCode'].value; if (doc.containsKey('dataSource')) { result += '##' + doc['dataSource'].value; } return result; \"" +
                "                }," +
                "                \"order\": {\n" +
                "                    \"startTime\": \"desc\"\n" +
                "                },\n" +
                "                \"size\": 2000\n" +
                "            },\n" +
                "            \"aggs\": {\n" +
                "                \"dataCount\": {\n" +
                "                    \"sum\": {\n" +
                "                        \"field\": \"num\"\n" +
                "                    }\n" +
                "                },\n" +
                "                \"endTime\": {\n" +
                "                    \"max\": {\n" +
                "                        \"field\": \"currentTime\"\n" +
                "                    }\n" +
                "                },\n" +
                "                \"startTime\": {\n" +
                "                    \"min\": {\n" +
                "                        \"field\": \"currentTime\"\n" +
                "                    }\n" +
                "                }\n" +
                "            }\n" +
                "        }\n" +
                "    }\n" +
                "}";
        HttpEntity entity = new NStringEntity(jsonString, ContentType.APPLICATION_JSON);
        String endPoint = "/data-trace-alias/_search";
        JSONObject jsonObject = ElasticsearchUtil.aggs(endPoint, entity);
        JSONArray jsonArray = jsonObject.getJSONObject("aggs_terms").getJSONArray("buckets");
        List<DataTraceAgg> returnList = new ArrayList<>();
        for(Object o : jsonArray){
            DataTraceAgg dataTraceAgg = new DataTraceAgg((JSONObject)o);
            dataTraceAgg.setBatchNo(batchNo);
            returnList.add(dataTraceAgg);
        }
        return returnList;
    }

    @Override
    public List<DataTraceAgg> listTransferDataTraceAggs(String mainBatchNo,String batchNo,String nodeName, String dataSource) throws Exception {

        String dsMatch = "";
        if(StringUtils.isNotEmpty(dataSource)){
            dsMatch = ", {\n" +
                    "                \"match\": {\n" +
                    "                    \"dataSource\": \""+dataSource+"\"\n" +
                    "                }\n" +
                    "            }";
        }
        if(StringUtils.isNotEmpty(mainBatchNo)){
            dsMatch = ", {\n" +
                    "                \"match\": {\n" +
                    "                    \"mainBatchNo\": \""+mainBatchNo+"\"\n" +
                    "                }\n" +
                    "            }";
        }
        if(StringUtils.isNotEmpty(batchNo)){
            dsMatch = ", {\n" +
                    "                \"match\": {\n" +
                    "                    \"mainBatchNo\": \""+batchNo+"\"\n" +
                    "                }\n" +
                    "            }";
        }
        if(StringUtils.isNotEmpty(nodeName)){
            dsMatch = ", {\n" +
                    "                \"match\": {\n" +
                    "                    \"mainBatchNo\": \""+nodeName+"\"\n" +
                    "                }\n" +
                    "            }";
        }
        String jsonString = "{\n" +
                "    \"size\": 0,\n" +
                "    \"query\": {\n" +
                "        \"bool\": {\n" +
                "            \"must\": [{\n" +
                "                \"script\": {\n" +
                "                    \"script\": {\n" +
                "                        \"lang\": \"painless\",\n" +
                "                        \"source\": \"doc['clientCode'].size()>0 \"\n" +
                "                    }\n" +
                "                }\n" +
                "            }, {\n" +
                "                \"match\": {\n" +
                "                    \"batchNo\": \""+batchNo+"\"\n" +
                "                }\n" +
                "            }" + dsMatch + "]\n" +
                "        }\n" +
                "    },\n" +
                "    \"aggs\": {\n" +
                "        \"aggs_terms\": {\n" +
                "            \"terms\": {\n" +
                "                \"script\": {\n" +
                "                             \"source\": \" def result = doc['serviceName'].value + '##' + doc['nodeName'].value + '##' + doc['status'].value + '##' + doc['sendTimes'].value + '##' + doc['clientCode'].value; if (doc.containsKey('dataSource')) { result += '##' + doc['dataSource'].value; } return result; \"" +
                "                }," +
                "                \"order\": {\n" +
                "                    \"startTime\": \"asc\"\n" +
                "                },\n" +
                "                \"size\": 2000\n" +
                "            },\n" +
                "            \"aggs\": {\n" +
                "                \"dataCount\": {\n" +
                "                    \"sum\": {\n" +
                "                        \"field\": \"num\"\n" +
                "                    }\n" +
                "                },\n" +
                "                \"endTime\": {\n" +
                "                    \"max\": {\n" +
                "                        \"field\": \"currentTime\"\n" +
                "                    }\n" +
                "                },\n" +
                "                \"startTime\": {\n" +
                "                    \"min\": {\n" +
                "                        \"field\": \"currentTime\"\n" +
                "                    }\n" +
                "                }\n" +
                "            }\n" +
                "        }\n" +
                "    }\n" +
                "}";
        HttpEntity entity = new NStringEntity(jsonString, ContentType.APPLICATION_JSON);
        String endPoint = "/data-trace-alias/_search";
        JSONObject jsonObject = ElasticsearchUtil.aggs(endPoint, entity);
        JSONArray jsonArray = jsonObject.getJSONObject("aggs_terms").getJSONArray("buckets");
        List<DataTraceAgg> returnList = new ArrayList<>();
        for(Object o : jsonArray){
            DataTraceAgg dataTraceAgg = new DataTraceAgg((JSONObject)o);
            dataTraceAgg.setBatchNo(batchNo);
            returnList.add(dataTraceAgg);
        }
        return returnList;
    }

    @Override
    public List<DataTraceAgg> listDataTraceAggsByMainBatch(String mainBatchNo) throws Exception {
        String jsonString = "{\n" +
                "    \"size\": 0,\n" +
                "    \"query\": {\n" +
                "        \"bool\": {\n" +
                "            \"must_not\": [{\n" +
                "                \"match\": {\n" +
                "                    \"clientCode\": \"PICC_BBDZ_NEW\"\n" +
                "                }\n" +
                "            }, {\n" +
                "                \"match\": {\n" +
                "                    \"clientCode\": \"PICC_BBDZ\"\n" +
                "                }\n" +
                "            }, {\n" +
                "                \"match\": {\n" +
                "                    \"clientCode\": \"PICC_BBDZ_TEST\"\n" +
                "                }\n" +
                "            }, {\n" +
                "                \"match\": {\n" +
                "                    \"clientCode\": \"FACADE-PART-PRE\"\n" +
                "                }\n" +
                "            }],\n" +
                "            \"must\": [{\n" +
                "                \"match\": {\n" +
                "                    \"mainBatchNo\": \""+mainBatchNo+"\"\n" +
                "                }\n" +
                "            }]\n" +
                "        }\n" +
                "    },\n" +
                "    \"aggs\": {\n" +
                "        \"aggs_terms\": {\n" +
                "            \"terms\": {\n" +
                "                \"script\": \"doc['serviceName'].value +'##'+ doc['nodeName'].value \",\n" +
                "                \"order\": {\n" +
                "                    \"startTime\": \"asc\"\n" +
                "                },\n" +
                "                \"size\": 2000\n" +
                "            },\n" +
                "            \"aggs\": {\n" +
                "                \"dataCount\": {\n" +
                "                    \"sum\": {\n" +
                "                        \"field\": \"num\"\n" +
                "                    }\n" +
                "                },\n" +
                "                \"endTime\": {\n" +
                "                    \"max\": {\n" +
                "                        \"field\": \"currentTime\"\n" +
                "                    }\n" +
                "                },\n" +
                "                \"startTime\": {\n" +
                "                    \"min\": {\n" +
                "                        \"field\": \"currentTime\"\n" +
                "                    }\n" +
                "                }\n" +
                "            }\n" +
                "\n" +
                "        }\n" +
                "    }\n" +
                "}";
        HttpEntity entity = new NStringEntity(jsonString, ContentType.APPLICATION_JSON);
        String endPoint = "/data-trace-alias/_search";
        JSONObject jsonObject = ElasticsearchUtil.aggs(endPoint, entity);
        JSONArray jsonArray = jsonObject.getJSONObject("aggs_terms").getJSONArray("buckets");
        List<DataTraceAgg> returnList = new ArrayList<>();
        for(Object o : jsonArray){
            DataTraceAgg dataTraceAgg = new DataTraceAgg((JSONObject)o);
            dataTraceAgg.setMainBatchNo(mainBatchNo);
            returnList.add(dataTraceAgg);
        }
        return returnList;
    }

    @Override
    public List<DataTrace> listDataTraces(Map<String, Object> paramMap) throws Exception {

        String jsonString = "{ \n" +
                "  \"from\" : 0, \n" +
                "  \"size\" : 5,\n" +
                "  \"query\": {\n" +
                "    \"bool\": {\n" +
                "      \"must\": [\n";

        for(Map.Entry<String, Object> entry : paramMap.entrySet()){
            if(EmptyUtils.isNotEmpty(entry.getValue())){
                jsonString = jsonString + "{ \"match\": { \"" + entry.getKey() + "\":\"" + entry.getValue() + "\" }},";
            }
        }
        jsonString = jsonString.substring(0, jsonString.length() - 1);
        jsonString = jsonString +
                "      ]\n" +
                /*
                "            \",should\": [{\n" +
                "                \"term\": {\n" +
                "                    \"dataSource\": \"publisher\"\n" +
                "                }\n" +
                "            }, {\n" +
                "                \"bool\": {\n" +
                "                    \"must_not\": {\n" +
                "                        \"exists\": {\n" +
                "                            \"field\": \"dataSource\"\n" +
                "                        }\n" +
                "                    }\n" +
                "                }\n" +
                "            }]" +
                */
                "    }\n" +
                "  }\n" +
                "}";

        HttpEntity entity = new NStringEntity(jsonString, ContentType.APPLICATION_JSON);
        String endPoint = "/data-trace-alias/_search";
        JSONObject jsonObject = ElasticsearchUtil.search(endPoint, entity);
        JSONArray jsonArray = jsonObject.getJSONObject("hits").getJSONArray("hits");
        List<DataTrace> returnList = new ArrayList<>();
        for(Object o : jsonArray){
            DataTrace dataTrace = JSONObject.toJavaObject(((JSONObject)o).getJSONObject("_source"), DataTrace.class);
            returnList.add(dataTrace);
        }
        return returnList;
    }

    @Override
    public List<DataTrace> listDataTracesByClientCodeAndBatchNoAndStatus(String clientCode, String batchNo, String status) throws Exception {
      /*  {
            "size":100,
                "query":{
            "bool": {
                "must": [
                 { "match": { "clientCode":"FBL" }},
                { "match": { "batchNo":"MaketPrice_87_20200511_1_1" }},
                { "match": { "nodeName":"数据更新" }},
                { "match": { "serviceName":"客户端" }}
      ]
            }
        },
            "sort": { "currentTime": { "order": "desc" }}
        }*/
        Map<String, String> map = DataTraceMenu.nameof(status);
        String jsonString = "{ \n" +
                "  \"size\" : 100000,\n" +
                "  \"query\": {\n" +
                "    \"bool\": {\n" +
                "      \"must\": [\n" +
                "{ \"match\": { \"clientCode\":\"" + clientCode + "\" }}," +
                "{ \"match\": { \"batchNo\":\"" + batchNo + "\" }},";
                for(Map.Entry<String, String> entry : map.entrySet()){
                    if(EmptyUtils.isNotEmpty(entry.getValue())){
                        jsonString = jsonString + "{ \"match\": { \"" + entry.getKey() + "\":\"" + entry.getValue() + "\" }},";
                    }
                }
        jsonString = jsonString.substring(0, jsonString.length() - 1);
        jsonString = jsonString +
                "      ]\n" +
                /*
                "            \",should\": [{\n" +
                "                \"term\": {\n" +
                "                    \"dataSource\": \"publisher\"\n" +
                "                }\n" +
                "            }, {\n" +
                "                \"bool\": {\n" +
                "                    \"must_not\": {\n" +
                "                        \"exists\": {\n" +
                "                            \"field\": \"dataSource\"\n" +
                "                        }\n" +
                "                    }\n" +
                "                }\n" +
                "            }]" +
                */
                "    }\n" +
                "  },\n" +
                "\"sort\": { \"currentTime\": {\"order\": \"desc\"}}" +
                "}";
        HttpEntity entity = new NStringEntity(jsonString, ContentType.APPLICATION_JSON);
        String endPoint = "/data-trace-alias/_search";
        JSONObject jsonObject = ElasticsearchUtil.search(endPoint, entity);
        JSONArray jsonArray = jsonObject.getJSONObject("hits").getJSONArray("hits");
        List<DataTrace> returnList = new ArrayList<>();
        if(EmptyUtils.isNotEmpty(jsonArray)){
            int sendTimes = jsonArray.getJSONObject(0).getJSONObject("_source").getInteger("sendTimes");
            for(Object o : jsonArray){
                DataTrace dataTrace = JSONObject.toJavaObject(((JSONObject)o).getJSONObject("_source"), DataTrace.class);
                if(ResultStatus.SUCCESS.getStatus().equals(status) || ResultStatus.PROCESSING.getStatus().equals(status)){
                    returnList.add(dataTrace);
                } else{
                    if(sendTimes == dataTrace.getSendTimes()){
                        returnList.add(dataTrace);
                    } else {
                        break;
                    }
                }
            }
        }
        return returnList;
    }

    @Override
    public Integer getSendTimes(Map<String, Object> paramMap) throws Exception {
        int maxSendTimes = 0;
        String jsonString = "{ \n" +
                "  \"query\": {\n" +
                "    \"bool\": {\n" +
                "      \"must\": [\n";

        for(Map.Entry<String, Object> entry : paramMap.entrySet()){
            if(EmptyUtils.isNotEmpty(entry.getValue())){
                jsonString = jsonString + "{ \"match\": { \"" + entry.getKey() + "\":\"" + entry.getValue() + "\" }},";
            }
        }
        jsonString = jsonString.substring(0, jsonString.length() - 1);
        jsonString = jsonString +
                "      ]\n" +
                /*
                "            \",should\": [{\n" +
                "                \"term\": {\n" +
                "                    \"dataSource\": \"publisher\"\n" +
                "                }\n" +
                "            }, {\n" +
                "                \"bool\": {\n" +
                "                    \"must_not\": {\n" +
                "                        \"exists\": {\n" +
                "                            \"field\": \"dataSource\"\n" +
                "                        }\n" +
                "                    }\n" +
                "                }\n" +
                "            }]" +
                */
                "    }\n" +
                "  },\n" +
                "\"aggs\": {\n" +
                "    \"aggs_terms\": {\n" +
                "      \"terms\": {\n" +
                "        \"field\": \"num\"\n" +
                "      },\n" +
                "      \"aggs\": {\n" +
                "        \"maxNum\": {\n" +
                "            \"max\": {\n" +
                "                \"field\": \"num\"\n" +
                "            }\n" +
                "         }\n" +
                "      }\n" +
                "    }\n" +
                "  }\n" +
                "}";

        HttpEntity entity = new NStringEntity(jsonString, ContentType.APPLICATION_JSON);
        String endPoint = "/data-trace-alias/_search";
        JSONObject jsonObject = ElasticsearchUtil.aggs(endPoint, entity);
        JSONArray jsonArray = jsonObject.getJSONObject("aggs_terms").getJSONArray("buckets");
        List<DataTraceAgg> returnList = new ArrayList<>();
        for(Object o : jsonArray){
            maxSendTimes = ((JSONObject)o).getInteger("doc_count");
        }
        return maxSendTimes;
    }

    @Override
    public Integer getSendTimesByNodeNameAndBatchNo(String nodeName, String batchNo, String clientCode, String serviceName, String status) throws Exception {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("nodeName", nodeName);
        paramMap.put("batchNo", batchNo);
        paramMap.put("clientCode", clientCode);
        paramMap.put("serviceName", serviceName);
        paramMap.put("status", status);
        return this.getSendTimes(paramMap) + 1;
    }


    @Override
    public void deleteDataTrace(String batchNo, String id, Integer sendTimes, String nodeName, String dataSource) throws Exception {
        String jsonString = "{ \n" +
                "  \"query\": {\n" +
                "    \"bool\": {\n" +
                "      \"must\": [\n" +
                             "{ \"match\": { \"batchNo\":\"" + batchNo + "\" }},\n" +
                             "{ \"match\": { \"id\":\"" + id + "\" }},\n" +
                             "{ \"match\": { \"sendTimes\":\"" + sendTimes + "\" }},\n" +
                             "{ \"match\": { \"nodeName\":\"" + nodeName + "\" }},\n" +
                             "{ \"match\": { \"dataSource\":\"" + dataSource + "\" }}\n" +
                "      ]\n" +
                "    }\n" +
                "  }\n" +
                "}";

        HttpEntity entity = new NStringEntity(jsonString, ContentType.APPLICATION_JSON);
        String endPoint = "/data-trace-alias/_delete_by_query";
        JSONObject jsonObject = ElasticsearchUtil.search(endPoint, entity);
      //  System.out.println("--------------------_delete_by_query-----------------------" + jsonObject);
    }
}

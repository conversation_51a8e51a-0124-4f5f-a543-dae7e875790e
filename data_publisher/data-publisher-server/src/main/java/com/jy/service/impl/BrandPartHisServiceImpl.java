package com.jy.service.impl;

import com.jy.ann.MethodMonitor;
import com.jy.bean.po.BrandPartHis;
import com.jy.mapper.BrandPartHisMapper;
import com.jy.service.BrandPartHisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * Implementation of BrandPartHisService
 */
@Service
public class BrandPartHisServiceImpl implements BrandPartHisService {

    @Autowired
    private BrandPartHisMapper brandPartHisMapper;

    @Override
    @MethodMonitor
    public BrandPartHis getBrandPartHisById(Long id, String brandCode) {
        return brandPartHisMapper.getBrandPartHisById(id, brandCode);
    }

    @MethodMonitor
    @Override
    public BrandPartHis getBrandPartHisByOe(String oe, String brandCode) {
        return brandPartHisMapper.getBrandPartHisByOe(oe, brandCode);
    }

    @Override
    @MethodMonitor
    public BrandPartHis getBrandPartHisByUpdateId(Long updateId, String brandCode) {
        return brandPartHisMapper.getBrandPartHisByUpdateId(updateId, brandCode);
    }

    @Override
    @MethodMonitor
    public List<BrandPartHis> listBrandPartHis(Map<String, Object> params) {
        return brandPartHisMapper.listBrandPartHis(params);
    }

    @Override
    @MethodMonitor
    public List<BrandPartHis> listByBatchAndSupTableId(String batchNo, String supTableId, String brandCode) {
        return brandPartHisMapper.listByBatchAndSupTableId(batchNo, supTableId, brandCode);
    }

    @Override
    @MethodMonitor
    public List<BrandPartHis> queryBrandPartHisWithPage(Map<String, Object> params) {
        return brandPartHisMapper.queryBrandPartHisWithPage(params);
    }

    @Override
    @MethodMonitor
    public BrandPartHis save(BrandPartHis brandPartHis) throws Exception {
        brandPartHisMapper.saveBrandPartHis(brandPartHis);
        return brandPartHis;
    }

    @Override
    @MethodMonitor
    public BrandPartHis update(BrandPartHis brandPartHis) throws Exception {
        brandPartHisMapper.updateBrandPartHis(brandPartHis);
        return brandPartHis;
    }

    @Override
    @MethodMonitor
    public void delete(Long id, String brandCode) throws Exception {
        brandPartHisMapper.deleteBrandPartHis(id, brandCode);
    }

    @Override
    @MethodMonitor
    public Integer getBatchPartHisCount(String batchNo, String brandCode) {
        return brandPartHisMapper.getBatchPartHisCount(batchNo, brandCode);
    }

    @Override
    @MethodMonitor
    public boolean checkTableExists(String brandCode) {
        Integer result = brandPartHisMapper.checkTableExists(brandCode);
        return result != null && result.equals(1);
    }

    @Override
    @MethodMonitor
    public void createBrandPartHisTable(String brandCode) throws Exception {
        brandPartHisMapper.createBrandPartHisTable(brandCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @MethodMonitor
    public void createIndexes(String brandCode) throws Exception {
        brandPartHisMapper.createBatchNoAndOriginalPartCodeIndex(brandCode);
        brandPartHisMapper.createBatchNoOriginalPartCodeUtIndex(brandCode);
        brandPartHisMapper.createBatchNoAndSupTableIdIndex(brandCode);
        brandPartHisMapper.createOriginalPartCodeIndex(brandCode);
        brandPartHisMapper.createBatchNoIndex(brandCode);
        brandPartHisMapper.createUpdateIdIndex(brandCode);
    }

    @Override
    @MethodMonitor
    public int batchDirectUpdate(List<Map<String, Object>> updateParamsList) {
        int totalUpdated = 0;
        for (Map<String, Object> params : updateParamsList) {
            totalUpdated += brandPartHisMapper.directUpdateClientCodes(params);
        }
        return totalUpdated;
    }
}

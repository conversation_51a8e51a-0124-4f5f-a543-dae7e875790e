package com.jy.service;

import com.jy.bean.po.ClientTable;
import com.jy.bean.po.ClientUrlMp;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/5/23
 */
public interface ClientUrlMpService {

    List<ClientUrlMp> listClientUrlMp(Map<String, Object> map);

    ClientUrlMp getByClientCodeAndTableName(String clientCode, String tableName);

    List<ClientUrlMp> listByBaseClientCode(String baseClientCode);

    List<ClientUrlMp> listByClientCode(String clientCode);

    ClientUrlMp save(ClientUrlMp clientUrlMp) throws Exception;

    void delete(String id) throws Exception;

    ClientUrlMp update(ClientUrlMp clientUrlMp) throws Exception;

    ClientUrlMp clearCache(String id) throws Exception;
}

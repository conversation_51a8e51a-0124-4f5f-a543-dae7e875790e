package com.jy.service;

import com.jy.bean.po.ClientFilter;

import java.util.List;
import java.util.Map;

/**
 * @Author: zy
 * @Date: Created in 2018/4/16
 */
public interface ClientFilterService {

    List<ClientFilter> listClientFilter(Map<String, Object> map);

    List<ClientFilter> listByClientCodeAndTableName(String clientCode, String tableName);
    
    ClientFilter save(ClientFilter clientFilter) throws Exception;
    
    void delete(String id) throws Exception;
    
    ClientFilter update(ClientFilter clientFilter) throws Exception;
    
    ClientFilter clearCache(String id) throws Exception;
}

package com.jy.service;

import com.jy.bean.po.Dict;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/5/11
 */
public interface DictService {

    List<Dict> listDict(Map<String, Object> map);

    List<Dict> listByType(String type);

    Dict getByTypeAndCode(String type, String code);

    Dict save(Dict dict) throws Exception;

    void delete(String id) throws Exception;

    Dict update(Dict dict) throws Exception;

    String getMatchCode(String companyCode);
}

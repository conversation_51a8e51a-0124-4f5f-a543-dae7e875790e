package com.jy.service;

import com.jy.bean.po.ClientTable;

import java.util.List;
import java.util.Map;

/**
 * @Author: zy
 * @Date: Created in 2018/4/16
 */
public interface ClientTableService {

    Map<String, ClientTable> mapByBaseTableName(String baseTableName);

    List<ClientTable> listClientTable(Map<String, Object> map);

    List<ClientTable> listByClientCode(String clientCode);

    List<ClientTable> listBybaseTableName();
    /**
     * 保存客户端表
     * @param clientTable
     * @return
     * @throws Exception
     */
    ClientTable save(ClientTable clientTable) throws Exception;
    /**
     * 客户端列表
     * @param clientTables
     * @return
     * @throws Exception
     */
    List<ClientTable> saveBatch(List<ClientTable> clientTables) throws Exception;
    /**
     * 删除
     * @param id
     * @throws Exception
     */
    void delete(String id) throws Exception;
    /**
     * 更新客户端表
     * @param clientTable
     * @return
     * @throws Exception
     */
    ClientTable update(ClientTable clientTable) throws Exception;
    /**
     * 主键查询客户端表
     * @param id
     * @return
     */
    ClientTable clearCache(String id) throws Exception;
    /**
     * 查询客户端表
     * @param clientTable
     * @return
     */
    ClientTable getClientTable(ClientTable clientTable);

    /**
     * 获取facade-part表和字段的对应关系
     * @return
     */
    List<Map<String,Object>> getFacadePartTableField();
}

/*
package com.jy.service.impl;

import com.jy.ann.MethodMonitor;
import com.jy.bean.po.Client;
import com.jy.bean.po.PublishCollection;
import com.jy.bean.po.PublishTrail;
import com.jy.bean.po.PublishTrailVO;
import com.jy.bean.result.ResultStatus;
import com.jy.config.MongoDbConfig;
import com.jy.service.ClientService;
import com.jy.service.PublishTrailService;
import com.jy.util.DateUtil;
import com.jy.util.EmptyUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.CommandResult;
import com.mongodb.DBObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.*;

*/
/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2018/1/18
 *//*

@Service
public class PublishTrailServiceImpl implements PublishTrailService {

    @Autowired
    private ClientService clientService;
    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    @MethodMonitor
    public void save(PublishTrail trail) throws Exception{
        // mongoTemplate.save(trail);
        mongoTemplate.insert(trail);
    }

    @Override
    @MethodMonitor
    public void save(List<PublishTrail> trails) throws Exception {
        mongoTemplate.insertAll(trails);
    }

    @Override
    @MethodMonitor
    public void update(PublishTrail trail) throws Exception {
        mongoTemplate.updateFirst(new Query(Criteria.where("_id").is(trail.getId())), fitUpdate(trail), PublishTrail.class);
    }

    @Override
    @MethodMonitor
    public void update(List<PublishTrail> trails) {
        doBathUpdate("trail", trails);
    }

    @Override
    @MethodMonitor
    public PublishTrail getOne(String id){
        return mongoTemplate.findOne(Query.query(Criteria.where("_id").is(id)), PublishTrail.class);
    }

    @Override
    @MethodMonitor
    public List<PublishCollection> listByBatchNoAndQueueNameAndStatus(String batchNo, String queueName, String status, boolean type) {
        PublishTrailVO publishTrailVO = new PublishTrailVO();
        publishTrailVO.setStatus(status);
        publishTrailVO.setBatchNo(batchNo);
        publishTrailVO.setQueueName(queueName);
        Query query = new Query();
        query.addCriteria(fitCriteria(publishTrailVO));
        if(type){
            query.skip(0);// skip相当于从那条记录开始
            query.limit(100);// 从skip开始,取多少条记录
        }
        List<PublishCollection> publishTrails = mongoTemplate.find(query, PublishCollection.class,queueName);
*/
/*        Client client = clientService.getOneByCodeOrName(queueName, null);
        for(PublishTrail temp : publishTrails){
            temp.setClientName(client.getName());
        }*//*

        return publishTrails;
    }

    @Override
    @MethodMonitor
    public long listByBatchNoStatus(String batchNo, String queueName) {
        PublishTrailVO publishTrailVO = new PublishTrailVO();
        publishTrailVO.setUnStatus(ResultStatus.SUCCESS.getStatus());
        publishTrailVO.setBatchNo(batchNo);
        publishTrailVO.setQueueName(queueName);
        Query query = new Query();
        query.addCriteria(fitCriteria(publishTrailVO));

        return mongoTemplate.count(query, PublishCollection.class,queueName);
    }

    @Override
    @MethodMonitor
    public long listByQueueNameAndTimeNoStatus(String queueName, String createTime, String updateTime) {
        PublishTrailVO publishTrailVO = new PublishTrailVO();
        publishTrailVO.setUnStatus(ResultStatus.SUCCESS.getStatus());
        publishTrailVO.setCreateTime(createTime);
        publishTrailVO.setUpdateTime(updateTime);
        publishTrailVO.setQueueName(queueName);
        Query query = new Query();
        query.addCriteria(fitCriteria(publishTrailVO));

        return mongoTemplate.count(query, PublishCollection.class,queueName);
    }

    @Override
    @MethodMonitor
    public List<PublishTrailVO> listGroupByBatchNoAndQueueNameAndStatus(PublishTrailVO publishTrailVO) {
        List<PublishTrailVO> result = new ArrayList<>();
        //当没有选择查询的客户端时,查询所有的客户端
        if(publishTrailVO != null && (publishTrailVO.getQueueName() == null || "".equals(publishTrailVO.getQueueName()))){
            Set<String> collectionNames = MongoDbConfig.getCollectionNames();
            for (String collectionName : collectionNames){
                publishTrailVO.setQueueName(collectionName);
                result.addAll(mongoTemplate.aggregate(fitAggregation(publishTrailVO), publishTrailVO.getQueueName(), PublishTrailVO.class).getMappedResults());
            }
        }else{
            result = mongoTemplate.aggregate(fitAggregation(publishTrailVO), publishTrailVO.getQueueName(), PublishTrailVO.class).getMappedResults();
        }
        for(PublishTrailVO temp : result){
            Client client = clientService.getOneByCode(temp.getQueueName());
            temp.setClientName(client.getName());
            temp.setUpdateTime(DateUtil.convertDateToString(DateUtil.milliPattern, temp.getUTime()));
            temp.setCreateTime(DateUtil.convertDateToString(DateUtil.milliPattern, temp.getCTime()));
        }
        return result;
    }

    private int doBathUpdate(String collName, List<PublishTrail> options) {
        DBObject command = new BasicDBObject();
        command.put("update", collName);
        List<BasicDBObject> updateList = new ArrayList<BasicDBObject>();
        for (PublishTrail option : options) {
            BasicDBObject update = new BasicDBObject();
            update.put("q", new Query(Criteria.where("_id").is(option.getId())).getQueryObject());
            update.put("u", fitUpdate(option).getUpdateObject());
           */
/* update.put("upsert", option.isUpsert());
            update.put("multi", option.isMulti());*//*

            updateList.add(update);
        }
        command.put("updates", updateList);
        //  command.put("ordered", ordered);
        CommandResult commandResult = mongoTemplate.getCollection(collName).getDB().command(command);
        return Integer.parseInt(commandResult.get("n").toString());
    }

    private Aggregation fitAggregation(PublishTrailVO publishTrailVO){
        */
/*List<String> fields = new ArrayList<String>();
        if(EmptyUtils.isNotEmpty(publishTrailVO.getStatus())){
            fields.add("status");
        }
        if(EmptyUtils.isNotEmpty(publishTrailVO.getBatchNo())){
            fields.add("batchNo");
        }
        if(EmptyUtils.isNotEmpty(publishTrailVO.getQueueName())){
            fields.add("queueName");
        }*//*

        Aggregation agg = newAggregation(
                match(fitCriteria(publishTrailVO)),
                group("batchNo", "queueName", "status").min("cTime").as("cTime").max("uTime").as("uTime").count().as("errorCount"),
                project("batchNo", "queueName", "status", "errorCount", "cTime","uTime"),
                sort(Sort.Direction.ASC, "cTime", "batchNo")
        );
        return agg;
    }

    private Criteria fitCriteria(PublishTrailVO publishTrailVO){
        Criteria criatira = new Criteria();
        if(EmptyUtils.isNotEmpty(publishTrailVO.getId())){
            criatira.and("_id").is(publishTrailVO.getId());
        }
        if(EmptyUtils.isNotEmpty(publishTrailVO.getUnStatus())){
            criatira.and("status").ne(publishTrailVO.getUnStatus());
        }
        if(EmptyUtils.isNotEmpty(publishTrailVO.getStatus())){
            criatira.and("status").is(publishTrailVO.getStatus());
        }
        if(EmptyUtils.isNotEmpty(publishTrailVO.getBatchNo())){
            criatira.and("batchNo").is(publishTrailVO.getBatchNo());
        }
        if(EmptyUtils.isNotEmpty(publishTrailVO.getQueueName())){
            criatira.and("queueName").is(publishTrailVO.getQueueName());
        }
        if(EmptyUtils.isNotEmpty(publishTrailVO.getCreateTime()) && EmptyUtils.isNotEmpty(publishTrailVO.getUpdateTime())){
            Date cTime = DateUtil.convertStringToDate(publishTrailVO.getCreateTime());
            Date uTime = DateUtil.convertStringToDate(publishTrailVO.getUpdateTime());
            uTime.setHours(23);
            uTime.setMinutes(59);
            uTime.setSeconds(59);
            criatira.andOperator(
                    Criteria.where("cTime").gte(cTime),
                    Criteria.where("cTime").lte(uTime));
        }
        return criatira;
    }
    public Update fitUpdate(PublishTrail trail){
        Update update = new Update()
                .set("uTime", trail.getUTime());
        if(EmptyUtils.isNotEmpty(trail.getSendCount()) && trail.getSendCount() != 0){
            update.set("sendCount", trail.getSendCount());
        }
        if(EmptyUtils.isNotEmpty(trail.getStatus())){
            update.set("status", trail.getStatus());
        }*/
/* else {
            update.unset("status");
        }*//*

        if(EmptyUtils.isNotEmpty(trail.getMessage())){
            update.set("message", trail.getMessage());
        }*/
/* else {
            update.unset("message");
        }*//*

        return update;
    }

    */
/*public void updateState(DemoEntity demoEntity) {
        Query query = new Query(Criteria.where("id").is(demoEntity.getId()));

        Update update = new Update();
        update.set("title", demoEntity.getTitle());
        update.set("description", demoEntity.getDescription());
        update.set("by", demoEntity.getBy());
        update.set("url", demoEntity.getUrl());

        mongoTemplate.updateFirst(query, update, DemoEntity.class);
    }*//*

}
*/

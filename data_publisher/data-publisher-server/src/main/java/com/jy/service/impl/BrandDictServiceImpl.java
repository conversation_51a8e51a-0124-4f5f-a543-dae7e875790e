package com.jy.service.impl;

import com.jy.ann.MethodMonitor;
import com.jy.bean.po.BrandDict;
import com.jy.mapper.BrandDictMapper;
import com.jy.service.BrandDictService;
import com.jy.util.EmptyUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 品牌字典Service实现类
 */
@Service
public class BrandDictServiceImpl implements BrandDictService {

    private static final Logger logger = LogManager.getLogger(BrandDictServiceImpl.class);

    @Autowired
    private BrandDictMapper brandDictMapper;

    @Override
    @MethodMonitor
    public List<BrandDict> listBrandDictWithPage(Map<String, Object> params) {
        return brandDictMapper.listBrandDict(params);
    }

    @Override
    @MethodMonitor
    public List<BrandDict> listBrandDict(Map<String, Object> params) {
        return brandDictMapper.listBrandDict(params);
    }

    @Override
    @MethodMonitor
    public BrandDict getBrandDictByCode(String brandCode) {
        if (EmptyUtils.isEmpty(brandCode)) {
            return null;
        }
        return brandDictMapper.getBrandDictByCode(brandCode);
    }

    @Override
    @MethodMonitor
    public List<BrandDict> searchBrandDictByName(String brandName) {
        if (EmptyUtils.isEmpty(brandName)) {
            return null;
        }
        return brandDictMapper.searchBrandDictByName(brandName);
    }

    @Override
    @MethodMonitor
    public List<BrandDict> searchBrandDict(String keyword, int page, int size) {
        Map<String, Object> params = new HashMap<>();
        if (EmptyUtils.isNotEmpty(keyword)) {
            params.put("keyword", keyword);
        }
        params.put("page", page);
        params.put("size", size);
        params.put("offset", page * size);
        
        logger.debug("搜索品牌字典，关键词：{}，页码：{}，每页数量：{}", keyword, page, size);
        return brandDictMapper.listBrandDict(params);
    }
}

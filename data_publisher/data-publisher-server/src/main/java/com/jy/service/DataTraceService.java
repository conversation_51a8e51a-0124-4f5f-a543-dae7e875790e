package com.jy.service;

import com.jy.bean.po.DataTrace;
import com.jy.bean.po.DataTraceAgg;

import java.util.List;
import java.util.Map;

/**
 * @Author: zy
 * @Date: Created in 2019/11/22
 */
public interface DataTraceService {
    List<DataTraceAgg> listDataTraceAggs(String batchNo) throws Exception;

    List<DataTraceAgg> listDataTraceAggs(String batchNo, String dataSource) throws Exception;

    List<DataTraceAgg> listDataTraceAggsByMainBatch(String mainBatchNo) throws Exception;

    List<DataTrace> listDataTraces(Map<String,Object> paramMap) throws Exception;

    List<DataTrace> listDataTracesByClientCodeAndBatchNoAndStatus(String clientCode, String batchNo, String status) throws Exception;

    Integer getSendTimes(Map<String,Object> paramMap) throws Exception;

    Integer getSendTimesByNodeNameAndBatchNo(String nodeName, String batchNo, String clientCode, String serviceName, String status) throws Exception;

    void deleteDataTrace(String batchNo, String id, Integer sendTimes, String nodeName, String dataSource) throws Exception;

    List<DataTraceAgg> getFlBatchInfo(String mainBatchNo, String batchNo, String clientCode,String nodeName, String dataSource) throws Exception;

    List<DataTraceAgg> listTransferDataTraceAggs(String mainBatchNo,String batchNo,String nodeName, String dataSource) throws Exception;
}

package com.jy.service.impl;

import com.jy.bean.po.SysUser;
import com.jy.security.JwtUserFactory;
import com.jy.service.SysUserService;
import com.jy.util.EmptyUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

/**
 * Created by zy on 2017/10/23.
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    @Autowired
    private SysUserService sysUserService;

    @Override
    public UserDetails loadUserByUsername(@NotNull String username) throws UsernameNotFoundException {
        String lowcaseUsername = username.toLowerCase();
        SysUser sysUser = null;
        try {
            sysUser = sysUserService.listByUserName(lowcaseUsername);
            if(EmptyUtils.isEmpty(sysUser)){
                throw new UsernameNotFoundException("用户名不存在：" + lowcaseUsername);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new UsernameNotFoundException(e.getMessage(), e);
        }
        return  JwtUserFactory.create(sysUser);
    }

}

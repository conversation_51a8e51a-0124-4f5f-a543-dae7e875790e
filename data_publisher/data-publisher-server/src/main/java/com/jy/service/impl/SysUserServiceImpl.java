package com.jy.service.impl;

import com.jy.bean.common.ConstantMenu;
import com.jy.bean.po.SysRole;
import com.jy.bean.po.SysUser;
import com.jy.bean.po.SysUserRole;
import com.jy.mapper.SysUserMapper;
import com.jy.service.SysRoleService;
import com.jy.service.SysUserRoleService;
import com.jy.service.SysUserService;
import com.jy.util.EmptyUtils;
import com.jy.util.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.annotation.Transient;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.token.ConsumerTokenServices;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Created by zy on 2017/10/24.
 */
@Service
@CacheConfig(cacheNames = "sysUsers")
public class SysUserServiceImpl implements SysUserService {

    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysRoleService sysRoleService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysUserRoleService sysUserRoleService;
    @Autowired
    private RedisTokenStore redisTokenStore;
    @Autowired
    @Qualifier("consumerTokenServices")
    ConsumerTokenServices consumerTokenServices;


    @Override
    @Cacheable(key = "#root.targetClass + ':' + #root.methodName + ':' + #username")
    public SysUser listByUserName(@NotNull String username) throws Exception{

        Map<String, Object> sysUserMap = new HashMap<String, Object>();
        sysUserMap.put("username", username);
        List<SysUser> sysUsers = sysUserMapper.listSysUser(sysUserMap);
        SysUser sysUser = null;
        if(EmptyUtils.isNotEmpty(sysUsers)){
            sysUser = sysUsers.get(0);
            Set<SysRole> sysRoles = sysRoleService.listByUserId(sysUser.getId());
            sysUser.setRoles(sysRoles);
        }
        return sysUser;
    }

    @Override
    public List<SysUser> listSortFieldSysUser(Map<String, Object> map) throws Exception {
        return (List<SysUser>) sysUserMapper.listSortFieldSysUser(map);
    }

    @Override
    @Transient
    public SysUser save(SysUser sysUser) throws Exception {
        BCryptPasswordEncoder encoder =new BCryptPasswordEncoder();
        String password = StringUtils.MD5(sysUser.getUsername());
        sysUser.setId(StringUtils.getUUID());
        sysUser.setImageUrl(password);
        sysUser.setPassword(encoder.encode(password));
        sysUserMapper.save(sysUser);

        SysUserRole sysUserRole = new SysUserRole();
        sysUserRole.setUserId(sysUser.getId());
        sysUserRole.setRoleId(ConstantMenu.COMMON.getId());
        sysUserRoleService.save(sysUserRole);

        sysUser.setPassword(null);
        sysUser.setImageUrl(null);
        return sysUser;
    }

    @Override
    @Transient
    public void delete(String id) throws Exception {
        sysUserService.clearCache(id);
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        sysUserMapper.delete(map);
        sysUserRoleService.delete(id);
    }

    @Override
    public SysUser update(SysUser sysUser) throws Exception {
        sysUserService.clearCache(sysUser.getId());
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        boolean f = encoder.matches(sysUser.getUsername(), sysUser.getPassword());
        if(!f){
            sysUser.setImageUrl(sysUser.getPassword());
            sysUser.setPassword(encoder.encode(sysUser.getPassword()));
        }
        sysUserMapper.update(sysUser);

        sysUser.setPassword(null);
        sysUser.setImageUrl(null);
        return sysUser;
    }

    @Override
    @CacheEvict(key = "#root.targetClass + ':listByUserName:' + #result.username")
    public SysUser clearCache(String id) throws Exception {
        SysUser temp = null;
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("id", id);
        List<SysUser> sysUsers = sysUserMapper.listSysUser(map);
        if(EmptyUtils.isNotEmpty(sysUsers)){
            temp = sysUsers.get(0);
            removeToken(temp.getUsername());//清除token
            temp.setUsername(temp.getUsername().toLowerCase());
        }
        return temp;
    }

    @Override
    public void removeToken(String userName) throws Exception {
        Collection<OAuth2AccessToken> tokens =  redisTokenStore.findTokensByClientIdAndUserName("android", userName);
        for(OAuth2AccessToken token : tokens){
            consumerTokenServices.revokeToken(token.getValue());
        }
    }
}

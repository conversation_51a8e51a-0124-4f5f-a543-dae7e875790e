package com.jy.service.impl;

import com.jy.ann.MethodMonitor;
import com.jy.bean.po.Price;
import com.jy.mapper.TestDataMapper;
import com.jy.service.TestDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: zy
 * @Date: Created in 2018/4/16
 */
@Service
public class TestDataServiceImpl implements TestDataService {

    @Autowired
    private TestDataMapper testDataMapper;

    @Override
    @MethodMonitor
    public List<Price> listAll() {
        Map<String, Object> map = new HashMap<>();
        return testDataMapper.listPrice(map);
    }

    @Override
    public List<Price> listVehicleIdAll() {
        Map<String, Object> map = new HashMap<>();
        return testDataMapper.listVehicleId(map);
    }

    @Override
    public List<Price> listVcBaseAll() {
        Map<String, Object> map = new HashMap<>();
        return testDataMapper.listVcBaseApVehicleId(map);
    }
}

package com.jy.service.impl;

import com.jy.ann.MethodMonitor;
import com.jy.service.RedisService;
import com.jy.util.EmptyUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2018/4/19
 */
@Service
public class RedisServiceImpl implements RedisService {

    @Value("${clientConfig.cacheExpiration}")
    private  int cacheExpiration;
    @Value("${clientConfig.redisSetName}")
    private  String redisSetName;

    @Resource
    private RedisTemplate<String,Object> redisTemplate;

    @Override
    @MethodMonitor
    public void set(String key, Object value) {
        ValueOperations<String,Object> vo = redisTemplate.opsForValue();
        vo.set(key, value);
    }

    @Override
    @MethodMonitor
    public void set(String key, Object value, int seconds) {
        ValueOperations<String,Object> vo = redisTemplate.opsForValue();
        vo.set(key, value, seconds, TimeUnit.SECONDS);
    }

    @Override
    @MethodMonitor
    public void saveClient(String clientId) {
        Set<Object> temp = new HashSet<Object>();
        if(hasKey(redisSetName)){
            temp = (Set<Object>) get(redisSetName);
        }
        temp.add(clientId);
        set(redisSetName, temp);
        set(clientId, null, cacheExpiration);
    }

    @Override
    @MethodMonitor
    public boolean hasKey(String key) {
        if(redisTemplate.hasKey(key)){
           return true;
        }
        return false;
    }

    @Override
    @MethodMonitor
    public Object get(String key) {
        ValueOperations<String,Object> vo = redisTemplate.opsForValue();
        return vo.get(key);
    }

    @Override
    @MethodMonitor
    public Set<Object> getClient() {
        Set<Object> result = null;
        Object obj = get(redisSetName);
        if(EmptyUtils.isNotEmpty(obj)){
            result = (Set<Object>) obj;
        }
        return result;
    }

    @Override
    @MethodMonitor
    public void removeClient(String clientId) {
        Set<Object> temp = new HashSet<Object>();
        if(hasKey(redisSetName)){
            temp = (Set<Object>) get(redisSetName);
        }
        temp.remove(clientId);
        set(redisSetName, temp);
    }
}

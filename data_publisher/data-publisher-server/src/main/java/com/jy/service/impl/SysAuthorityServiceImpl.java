package com.jy.service.impl;

import com.jy.bean.po.SysAuthority;
import com.jy.mapper.SysAuthorityMapper;
import com.jy.service.SysAuthorityService;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2018/1/15
 */
@Service
public class SysAuthorityServiceImpl implements SysAuthorityService{

    @Autowired
    private SysAuthorityMapper sysAuthorityMapper;

    @Override
    public Set<SysAuthority> listByRoleIds(@NotNull String roleIds) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("roleIds", roleIds);
        return sysAuthorityMapper.listSysAuthorities(map);
    }
}

package com.jy.service;

import com.jy.bean.po.OrgMp;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Map;

/**
 * @Author: zy
 * @Date: Created in 2018/4/16
 */
public interface OrgMpService {

    Map<String ,OrgMp> mapByBaseOrgCode(String baseOrgCode);

    OrgMp getByClientCodeAndBaseOrgCode(String clientCode, String baseOrgCode);

    List<OrgMp> listOrgMp(Map<String, Object> map);

    List<OrgMp> listByClientCode(String clientCode);

    OrgMp save(OrgMp orgMp) throws Exception;

    void delete(String id) throws Exception;

    OrgMp update(OrgMp orgMp) throws Exception;

    OrgMp clearCache(String id) throws Exception;
}

package com.jy.service;

import com.jy.bean.po.ClientTableFieldMp;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/5/2
 */
public interface ClientTableFieldMpService {
    /**
     * 客户端表字段列表
     * @param map
     * @return
     */
    List<ClientTableFieldMp> listClientTableFieldMp(Map<String, Object> map);

    List<ClientTableFieldMp> listByClientCode(String clientCode);

    List<ClientTableFieldMp> listByClientCodeAndTableName(String clientCode, String tableName);

    Map<String,List<ClientTableFieldMp>> mapByClientCodeAndTableName(String clientCode, String tableName);
    /**
     * 客户端表字段新增
     * @param clientTableFieldMp
     * @return
     * @throws Exception
     */
    ClientTableFieldMp save(ClientTableFieldMp clientTableFieldMp) throws Exception;

    List<ClientTableFieldMp> saveBatch(List<ClientTableFieldMp> clientTableFieldMps) throws Exception;
    /**
     * 客户端表字段删除
     * @param id
     * @throws Exception
     */
    void delete(String id) throws Exception;
    /**
     * 客户端表字段更新
     * @param clientTableFieldMp
     * @return
     * @throws Exception
     */
    ClientTableFieldMp update(ClientTableFieldMp clientTableFieldMp) throws Exception;

    /**
     * 客户端表字段
     * @param id
     * @return
     * @throws Exception
     */
    ClientTableFieldMp clearCache(String id) throws Exception;
    /**
     * 客户端表字段总数查询
     * @param clientTableFieldMp
     * @return
     */
    int selectFieldCount(ClientTableFieldMp clientTableFieldMp);
}

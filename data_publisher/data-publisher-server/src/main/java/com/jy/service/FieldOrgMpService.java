package com.jy.service;

import com.jy.bean.po.FieldOrgMp;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/5/2
 */
public interface FieldOrgMpService {

    List<FieldOrgMp> listByClientCodeAndTableNameAndBaseOrgCode(String clientCode, String tableName, String baseOrgCode);
    List<FieldOrgMp> listByClientCodeAndTableNameAndToBaseOrgCode(String clientCode, String tableName, String toBaseOrgCode);

    List<FieldOrgMp> listClientFieldOrgMp(Map<String, Object> map);

    int getBatchNum(String batchNo, String clientCode);

    FieldOrgMp save(FieldOrgMp fieldOrgMp) throws Exception;

    List<FieldOrgMp> saveBatch(List<FieldOrgMp> fieldOrgMps) throws Exception;

    void delete(String id) throws Exception;

    FieldOrgMp update(FieldOrgMp fieldOrgMp) throws Exception;

    FieldOrgMp clearCache(String id) throws Exception;
}

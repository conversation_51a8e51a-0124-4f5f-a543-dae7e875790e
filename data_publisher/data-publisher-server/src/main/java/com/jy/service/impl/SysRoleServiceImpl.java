package com.jy.service.impl;

import com.jy.ann.MethodMonitor;
import com.jy.bean.po.SysAuthority;
import com.jy.bean.po.SysRole;
import com.jy.mapper.SysRoleMapper;
import com.jy.service.SysAuthorityService;
import com.jy.service.SysRoleService;
import com.jy.util.SqlUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * @Author: zy
 * @Description:
 * @Date: Created in 2018/1/15
 */
@Service
@CacheConfig(cacheNames = "sysRoles")
public class SysRoleServiceImpl implements SysRoleService{

    @Autowired
    private SysAuthorityService sysAuthorityService;
    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Override
    @Cacheable(key = "#root.targetClass + ':' + #root.methodName + ':' + #userId")
    @MethodMonitor
    public Set<SysRole> listByUserId(@NotNull String userId) throws Exception{
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("userId", userId);
        Set<SysRole> sysRoles = sysRoleMapper.listSysRoles(map);

        String roleIds = SqlUtils.Set2SqlInString(sysRoles, "id");
        Set<SysAuthority> sysAuthorities = sysAuthorityService.listByRoleIds(roleIds);

        for(SysRole sysRole : sysRoles){
            for(SysAuthority sysAuthority : sysAuthorities){
                if(sysRole.getId().equals(sysAuthority.getRoleId())){
                    sysRole.getAuthorities().add(sysAuthority);
                }
            }
        }
        return sysRoles;
    }
}

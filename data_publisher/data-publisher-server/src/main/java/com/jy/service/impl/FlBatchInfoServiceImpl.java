package com.jy.service.impl;

import com.jy.ann.MethodMonitor;
import com.jy.bean.po.BatchDetail;
import com.jy.bean.po.DataTraceAgg;
import com.jy.bean.po.FlBatchInfo;
import com.jy.bean.result.ResultStatus;
import com.jy.mapper.FlBatchInfoMapper;
import com.jy.service.DataTraceService;
import com.jy.service.FlBatchInfoService;
import com.jy.util.EmptyUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: zy
 * @Date: Created in 2019/11/22
 */
@Service
public class FlBatchInfoServiceImpl implements FlBatchInfoService {

    @Autowired
    private FlBatchInfoMapper flBatchInfoMapper;
    @Autowired
    private DataTraceService dataTraceService;

    @Override
    @MethodMonitor
    public void saveBacth(@NotNull List<FlBatchInfo> batchInfoList) throws Exception {
        flBatchInfoMapper.insertBatch(batchInfoList);
    }

    @Override
    @MethodMonitor
    public void save(@NotNull FlBatchInfo batchInfo) throws Exception {
        flBatchInfoMapper.insert(batchInfo);
    }

    @Override
    @MethodMonitor
    public void update(@NotNull FlBatchInfo batchInfo) throws Exception {
        flBatchInfoMapper.update(batchInfo);
    }

    @Override
    @MethodMonitor
    public void updateSelective(@NotNull FlBatchInfo batchInfo) throws Exception {
        flBatchInfoMapper.updateSelective(batchInfo);
    }

    @Override
    @MethodMonitor
    public void deleteByBacthNo(@NotNull String batchNo, String dataSource) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("batchNo", batchNo);
        map.put("dataSource", dataSource);
        flBatchInfoMapper.delete(map);
    }

    @Override
    @MethodMonitor
    public List<FlBatchInfo> listFlBatchInfo(Map<String, Object> map) throws Exception {
        List<FlBatchInfo> flBatchInfoList = flBatchInfoMapper.listFlBatchInfo(map);
        return flBatchInfoList;
    }

    @Override
    @MethodMonitor
    public void updateTraceStatus(FlBatchInfo batchInfo) throws Exception {
        flBatchInfoMapper.updateTraceStatus(batchInfo);
    }

    @Override
    public FlBatchInfo getById(Long id) {
        return flBatchInfoMapper.getById(id);
    }

    @Override
    public FlBatchInfo getFlBatchInfo(FlBatchInfo flBatchInfo) throws Exception {
        List<DataTraceAgg> dataTraceAggList = dataTraceService.getFlBatchInfo(flBatchInfo.getMainBatchNo(), flBatchInfo.getBatchNo(), flBatchInfo.getClientCode(),flBatchInfo.getNodeName(), flBatchInfo.getDataSource());
        if(EmptyUtils.isNotEmpty(dataTraceAggList) && EmptyUtils.isNotEmpty(dataTraceAggList.get(0))){
            DataTraceAgg dataTraceAgg = dataTraceAggList.get(0);
            flBatchInfo.setStatus(dataTraceAgg.getStatus());
            flBatchInfo.setMessage(dataTraceAgg.getMessage());
            flBatchInfo.setId(flBatchInfo.getId());
            return flBatchInfo;
        }else{
            throw new Exception("未找到轨迹记录");
        }
    }

    @Override
    public void saveOrUpdate(FlBatchInfo flBatchInfo) {
        if(EmptyUtils.isNotEmpty(flBatchInfo.getBatchNo())){
            Integer count = flBatchInfoMapper.countByBatchNo(flBatchInfo.getBatchNo(), flBatchInfo.getDataSource(), flBatchInfo.getNodeName());
            if(count>0){
                flBatchInfoMapper.updateSelectiveByBatchNo(flBatchInfo);
            }else{
                flBatchInfoMapper.insert(flBatchInfo);
            }
        }
    }

    @Override
    public void reset(String batchNo, Date startTime) {
        String status = ResultStatus.PROCESSING.getStatus();
        flBatchInfoMapper.reset(batchNo, startTime, status);
    }

    @Override
    public void resetBatch(List<BatchDetail> batchDetailList, Date startTime) {
        String status = ResultStatus.PROCESSING.getStatus();
        flBatchInfoMapper.resetBatch(batchDetailList, startTime, status);
    }
}

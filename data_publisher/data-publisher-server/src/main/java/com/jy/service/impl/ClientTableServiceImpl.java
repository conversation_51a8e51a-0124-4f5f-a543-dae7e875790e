package com.jy.service.impl;

import com.jy.ann.MethodMonitor;
import com.jy.bean.po.ClientTable;
import com.jy.mapper.ClientTableMapper;
import com.jy.service.ClientTableService;
import com.jy.util.EmptyUtils;
import com.jy.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: zy
 * @Date: Created in 2018/4/16
 */
@Service
@CacheConfig(cacheManager = "ehCacheCacheManager", cacheNames = "clientTable")
public class ClientTableServiceImpl implements ClientTableService {

    @Autowired
    private ClientTableMapper clientTableMapper;
    @Autowired
    private ClientTableService cientTableService;

    @Override
    @MethodMonitor
    @Cacheable(key = "#root.targetClass + ':' + #root.methodName + ':' + #baseTableName")
    public Map<String, ClientTable> mapByBaseTableName(String baseTableName) {
        Map<String, Object> map = new HashMap<>();
        map.put("baseTableName", baseTableName);
        List<ClientTable> clientTables = clientTableMapper.listClientTable(map);

        return clientTables.stream().collect(
                Collectors.toMap(ClientTable::getClientCode, clientTable -> clientTable));
    }

    @Override
    @MethodMonitor
    public List<ClientTable> listClientTable(Map<String, Object> map) {
        return clientTableMapper.listClientTable(map);
    }

    @Override
    @MethodMonitor
    @Cacheable(key = "#root.targetClass + ':' + #root.methodName + ':' + #clientCode")
    public List<ClientTable> listByClientCode(String clientCode) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("clientCode", clientCode);
        return clientTableMapper.listClientTable(map);
    }

    @Override
    @MethodMonitor
    public List<ClientTable> listBybaseTableName() {
        return clientTableMapper.listBybaseTableName();
    }

    @Override
    @MethodMonitor
    @Caching(evict = {
            @CacheEvict(key = "#root.targetClass + ':mapByBaseTableName:' + #result.baseTableName"),
            @CacheEvict(key = "#root.targetClass + ':listByClientCode:' + #result.clientCode")})
    public ClientTable save(ClientTable clientTable) throws Exception {
        clientTable.setId(StringUtils.getUUID());
        clientTableMapper.save(clientTable);
        return clientTable;
    }

    @Override
    @CacheEvict(value = "clientTable", allEntries=true)
    @Transactional
    @MethodMonitor
    public List<ClientTable> saveBatch(List<ClientTable> clientTables) throws Exception {
        clientTableMapper.saveBatch(clientTables);
        return clientTables;
    }

    @Override
    @MethodMonitor
    public void delete(String id) throws Exception {
        cientTableService.clearCache(id);
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        clientTableMapper.delete(map);
    }

    @Override
    @Caching(evict = {
            @CacheEvict(key = "#root.targetClass + ':mapByBaseTableName:' + #result.baseTableName"),
            @CacheEvict(key = "#root.targetClass + ':listByClientCode:' + #result.clientCode")})
    @MethodMonitor
    public ClientTable update(ClientTable clientTable) throws Exception {
        cientTableService.clearCache(clientTable.getId());
        clientTableMapper.update(clientTable);
        return clientTable;
    }

    @Override
    @Caching(evict = {
            @CacheEvict(key = "#root.targetClass + ':mapByBaseTableName:' + #result.baseTableName"),
            @CacheEvict(key = "#root.targetClass + ':listByClientCode:' + #result.clientCode")})
    @MethodMonitor
    public ClientTable clearCache(String id) throws Exception {
        ClientTable temp = null;
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("id", id);
        List<ClientTable> clientTables = clientTableMapper.listClientTable(map);
        if(EmptyUtils.isNotEmpty(clientTables)){
            temp = clientTables.get(0);
        }
        return temp;
    }

    @Override
    public ClientTable getClientTable(ClientTable clientTable) {
        return clientTableMapper.getClientTable(clientTable);
    }

    @Override
    public List<Map<String, Object>> getFacadePartTableField() {
        return clientTableMapper.getFacadePartTableField();
    }
}

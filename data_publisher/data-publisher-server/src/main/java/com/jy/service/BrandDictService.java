package com.jy.service;

import com.jy.bean.po.BrandDict;

import java.util.List;
import java.util.Map;

/**
 * 品牌字典Service接口
 */
public interface BrandDictService {
    
    /**
     * 分页查询品牌字典列表，支持搜索
     * @param params 查询参数，包含keyword、page、size等
     * @return 品牌字典列表
     */
    List<BrandDict> listBrandDictWithPage(Map<String, Object> params);
    
    /**
     * 查询品牌字典列表
     * @param params 查询参数
     * @return 品牌字典列表
     */
    List<BrandDict> listBrandDict(Map<String, Object> params);
    
    /**
     * 根据品牌编码查询品牌字典
     * @param brandCode 品牌编码
     * @return 品牌字典
     */
    BrandDict getBrandDictByCode(String brandCode);
    
    /**
     * 根据品牌名称模糊查询品牌字典
     * @param brandName 品牌名称
     * @return 品牌字典列表
     */
    List<BrandDict> searchBrandDictByName(String brandName);
    
    /**
     * 根据关键词搜索品牌字典（支持品牌编码和品牌名称）
     * @param keyword 搜索关键词
     * @param page 页码
     * @param size 每页数量
     * @return 品牌字典列表
     */
    List<BrandDict> searchBrandDict(String keyword, int page, int size);
}

package com.jy.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.jy.ann.MethodMonitor;
import com.jy.bean.dto.BaseDataDTO;
import com.jy.bean.po.*;
import com.jy.bean.result.ResultStatus;
import com.jy.mq.RabbitCommon;
import com.jy.service.*;
import com.jy.task.ClientDataTask;
import com.jy.transform.TransformFactory;
import com.jy.util.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.lang.reflect.Method;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * BaseData装配服务
 * @Author: zy
 * @Date: Created in 2018/3/29
 */
@Service
public class DataFitServiceImpl implements DataFitService {
    private static final Logger logger = LogManager.getLogger(DataFitServiceImpl.class);

    @Autowired
    private ClientDataTask clientDataTask;
    @Autowired
    private TransformFactory transformFactory;
    @Autowired
    private RabbitCommon rabbitCommon;
    @Autowired
    private OrgMpService orgMpService;
    @Autowired
    private ClientService clientService;
    @Autowired
    private ClientTableService clientTableService;
    @Autowired
    private FieldOrgMpService fieldOrgMpService;
    @Autowired
    private DataFitService dataFitService;
    @Autowired
    private TrailDetailService trailDetailService;
    @Autowired
    private FlBatchInfoService flBatchInfoService;
    @Autowired
    private ClientFilterService clientFilterService;


    /**
     * 基础数据装配到各个客户端上
     * @param baseDataDTO
     * @throws Exception
     */
    @Override
    @MethodMonitor
    public void fit(BaseDataDTO baseDataDTO)  throws Exception {
        List<Client> clients = dataFitService.listClient(baseDataDTO);
        logger.info("基础数据: {} \r\n装配 clients: {} " , baseDataDTO,  clients);

        for(Client client : clients){
            if(baseDataDTO.getBatchNoStatus()!= null){
                baseDataDTO.setId(StringUtils.getUUID());
                baseDataDTO.setTableName(null);
                TrailDetail trailDetail = new TrailDetail(baseDataDTO.getBatchNo(), client.getCode());
                trailDetailService.save(trailDetail);
                rabbitCommon.sendQueue(client.getCode(), JSONObject.toJSONString(baseDataDTO));
            }else{
                List<ClientFilter> clientFilters = clientFilterService.listByClientCodeAndTableName(client.getCode(), baseDataDTO.getTableName());
                /** 过滤数据 */
                boolean filterFlag = true;
                if(EmptyUtils.isNotEmpty(clientFilters)){
                    Map<String, List<ClientFilter>> filterMap = clientFilters.stream().collect(Collectors.groupingBy(ClientFilter::getFilterKey));
                    for (Map.Entry<String, List<ClientFilter>> entry : filterMap.entrySet()) {

                        String filterNode = entry.getValue().get(0).getFilterNode();
                        String filterRule = entry.getValue().get(0).getFilterRule();
                        String methodName = Beanutils.getGetterNameByFiledName(filterNode);
                        Method methdo = baseDataDTO.getClass().getMethod(methodName);
                        Map<String, Object> arg = (Map<String, Object>) methdo.invoke(baseDataDTO);
                        List<String> fieldValues = entry.getValue().stream().map(ClientFilter::getFilterValue).collect(Collectors.toList());
                        //filterRule值为approve，通过， 代表着必须clientFilter表配置的这一部分数据才可以通过，剩余全部拦截
                        if(EmptyUtils.isNotEmpty(arg) && "approve".equals(filterRule) && !fieldValues.contains(arg.get(entry.getKey()))){
                            filterFlag = false;
                        }
                        //filterRule值为intercept，拦截， 代表着必须clientFilter表配置的这一部分数据才可以拦截，剩余全部通过
                        if(EmptyUtils.isNotEmpty(arg) && "intercept".equals(filterRule) &&  fieldValues.contains(arg.get(entry.getKey()))){
                            filterFlag = false;
                        }
                    }
                }
                if(filterFlag){
                    BaseDataDTO baseTemp = baseDataDTO.clone();
                    //分省拆分
                    List<BaseDataDTO> list = dataFitService.splitByFieldOrgMp(baseTemp, client);
                    for(BaseDataDTO temp : list){
                        // 转换
                        JSONObject dataJson = transformFactory.create(client.getCode()).transform(temp, client);
                        // 发送
                        dataFitService.sendData(client, dataJson);
                    }
                }
            }
        }
    }


    @Override
    @MethodMonitor
    public void sendData(Client client,JSONObject dataJson) throws Exception {
        String data = dataJson.toJSONString();
        //2、判断数据是否推送
        JSONObject fieldsValue = (JSONObject) dataJson.get("fields");
        if(!"delete".equals(dataJson.get("operate")) && (EmptyUtils.isEmpty(fieldsValue) || (fieldsValue.size() == 1 && fieldsValue.containsKey("xgrq")))){
            logger.info("没有可更新匹配字段数据，不上传MQ数据" + dataJson);
            return;
        }
        JSONObject keysValue = (JSONObject) dataJson.get("keys");
        if(EmptyUtils.isEmpty(keysValue)){
            logger.info("没有可更新匹配条件数据，不上传MQ数据" + dataJson);
            return;
        }
        //3、推送数据至mq
        rabbitCommon.sendQueue(client.getCode(), data);

        //4、数据添加至队列，异步写入文件
        clientDataTask.addDataQueue(dataJson);
    }

    @Override
    public void resend(List<BaseDataDTO> list,String clientCode) {
        if(EmptyUtils.isEmpty(list)){
            return;
        }
        FlBatchInfo flBatchInfo = new FlBatchInfo(clientCode, list.get(0).getBatchNo(), ResultStatus.PROCESSING.getStatus());
        try {
            for (BaseDataDTO baseDataDTO : list){
                baseDataDTO.setSendTimes(baseDataDTO.getSendTimes() + 1);
                String data = JSONObject.toJSONString(baseDataDTO);
                if(EmptyUtils.isNotEmpty(data)){
                    rabbitCommon.sendQueue(clientCode, data);
                }
            }
            flBatchInfoService.update(flBatchInfo);
        }catch (Exception e){
            logger.error("重新发送mq失败,客户端:{},错误信息:{}", clientCode, ToolUtils.getExceptionMsg(e));
        }
    }

    @Override
    @MethodMonitor
    public List<BaseDataDTO> splitByFieldOrgMp(BaseDataDTO baseDataDTO, Client client)  throws Exception {
        List<BaseDataDTO> baseDataDTOs = new ArrayList<BaseDataDTO>();
        if(!"delete".equals(baseDataDTO.getOperate()) && "local_market_price".equals(baseDataDTO.getTableName())){
            //忽视分省字段
            List<FieldOrgMp> toBaseOrgMpListist = fieldOrgMpService.listByClientCodeAndTableNameAndToBaseOrgCode(client.getCode(), baseDataDTO.getTableName(), baseDataDTO.getOrgCode());
            if(toBaseOrgMpListist!=null && toBaseOrgMpListist.size()>0) {
                for(FieldOrgMp fieldOrgMp : toBaseOrgMpListist){
                    if(!"xgrq".equals(fieldOrgMp.getBaseTableField())){
                        baseDataDTO.getFields().remove(fieldOrgMp.getBaseTableField());
                    }
                }
            }
            baseDataDTOs.add(baseDataDTO);
            //分省字段转换
            List<FieldOrgMp> fieldOrgMpListist = fieldOrgMpService.listByClientCodeAndTableNameAndBaseOrgCode(client.getCode(), baseDataDTO.getTableName(), baseDataDTO.getOrgCode());
            if(fieldOrgMpListist!=null && fieldOrgMpListist.size()>0){
                Map<String, FieldOrgMp> fieldsMp = fieldOrgMpListist.stream().collect(Collectors.toMap(FieldOrgMp::getToBaseOrgCode, Function.identity(), (key1, key2) -> key2));
                fieldsMp.forEach((k,v)->{
                    if(!baseDataDTO.getOrgCode().equals(k)){
                        BaseDataDTO temp = fitByFieldOrgMp(baseDataDTO, k, fieldOrgMpListist);
                        if(EmptyUtils.isNotEmpty(temp)){
                            baseDataDTOs.add(temp);
                        }
                    }
                });
            }
        }else{
            baseDataDTOs.add(baseDataDTO);
        }
        return baseDataDTOs;
    }

    private BaseDataDTO fitByFieldOrgMp(BaseDataDTO baseDataDTO, String key, List<FieldOrgMp> fieldOrgMpListist){
        Map<String, String> fields = new HashMap<String, String>();
        for(FieldOrgMp fieldOrgMp : fieldOrgMpListist){
            if(key.equals(fieldOrgMp.getToBaseOrgCode()) && baseDataDTO.getFields().containsKey(fieldOrgMp.getBaseTableField())){
                fields.put(fieldOrgMp.getBaseTableField(), baseDataDTO.getFields().get(fieldOrgMp.getBaseTableField()));
            }
        }
        if(EmptyUtils.isNotEmpty(fields)){
            BaseDataDTO temp = baseDataDTO.clone();
            temp.setOrgCode(key);
            temp.setFields(fields);
            return temp;
        }
        return null;

    }

    @Override
    @MethodMonitor
    public List<Client> listClient(BaseDataDTO baseDataDTO) throws Exception {
        /** 查询出UP和DOWN状态的客户端 */
        List<Client> clients = clientService.listByNoOfflineStatus();
        /** 过滤，保留存在此表的client */
        Map<String, ClientTable> clientTableMap = clientTableService.mapByBaseTableName(baseDataDTO.getTableName());
        clients = clients.stream()
                .filter(client -> clientTableMap.containsKey(client.getCode()))
                .collect(Collectors.toList());

        /** 区域/4s店价格即时更新  facade和配件加工价格客户端 接收不带原厂零件id的价格 */
        if("system_price".equals(baseDataDTO.getTableName())
                || "local_region_price".equals(baseDataDTO.getTableName()) ){
            Map<String, String> keys = baseDataDTO.getKeys();
            if(EmptyUtils.isNotEmpty(keys) && !keys.containsKey("ycljid")){
                clients = clients.stream()
                        .filter(client -> "PART_PRICE".equals(client.getPath()) || "FACADE".equals(client.getPath()))
                        .collect(Collectors.toList());
            }
        }

        /** 过滤，保留启用此机构及时更新的client */
        if("local_market_price".equals(baseDataDTO.getTableName())){
            Map<String, OrgMp> orgMpMap = orgMpService.mapByBaseOrgCode(baseDataDTO.getOrgCode());
            clients = clients.stream()
                    .filter(client -> orgMpMap.containsKey(client.getCode()))
                    .collect(Collectors.toList());
        }

        /** 上游传递了clientCode 则过滤客户端 */
        if(EmptyUtils.isNotEmpty(baseDataDTO.getClientCode())){
            String[] clientCodes = baseDataDTO.getClientCode().split(",");
            List<String> list = Arrays.asList(clientCodes);
            clients = clients.stream()
                    .filter(client -> list.contains(client.getCode()))
                    .collect(Collectors.toList());
        }

        /** 自动化测试的数据，只推送至Facade */
        if(baseDataDTO.getBatchNo().contains("AutoTest")){
            clients = clients.stream().filter(client -> "FACADE".equals(client.getPath())).collect(Collectors.toList());
        }
        return clients;
    }
}


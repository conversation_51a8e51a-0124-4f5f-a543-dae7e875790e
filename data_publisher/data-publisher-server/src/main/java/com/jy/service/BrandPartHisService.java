package com.jy.service;

import com.jy.bean.po.BrandPartHis;

import java.util.List;
import java.util.Map;

/**
 * Service interface for BrandPartHis operations
 */
public interface BrandPartHisService {

    /**
     * Get brand part history by ID
     * @param id The ID of the brand part history
     * @param brandCode The brand code for table sharding
     * @return The brand part history entity
     */
    BrandPartHis getBrandPartHisById(Long id, String brandCode);

    /**
     * Get brand part history by OE
     * @param oe The OE code
     * @param brandCode The brand code for table sharding
     * @return The brand part history entity
     */
    BrandPartHis getBrandPartHisByOe(String oe, String brandCode);

    /**
     * Get brand part history by part ID
     * @param updateId The part ID
     * @param brandCode The brand code for table sharding
     * @return The brand part history entity
     */
    BrandPartHis getBrandPartHisByUpdateId(Long updateId, String brandCode);

    /**
     * List brand part histories by various criteria
     * @param params Map containing query parameters and brandCode for sharding
     * @return List of brand part histories
     */
    List<BrandPartHis> listBrandPartHis(Map<String, Object> params);

    /**
     * List brand part histories by batch number and sup table ID
     * @param batchNo The batch number
     * @param supTableId The supplier table ID
     * @param brandCode The brand code for table sharding
     * @return List of brand part histories
     */
    List<BrandPartHis> listByBatchAndSupTableId(String batchNo, String supTableId, String brandCode);

    /**
     * List brand part histories by brand code and sup table ID with pagination
     * @param params Map containing brandCode, supTableId, page, size parameters
     * @return List of brand part histories
     */
    List<BrandPartHis> queryBrandPartHisWithPage(Map<String, Object> params);

    /**
     * Save a new brand part history
     * @param brandPartHis The brand part history to save
     * @return The saved brand part history
     */
    BrandPartHis save(BrandPartHis brandPartHis) throws Exception;

    /**
     * Update an existing brand part history
     * @param brandPartHis The brand part history with updated values
     * @return The updated brand part history
     */
    BrandPartHis update(BrandPartHis brandPartHis) throws Exception;

    /**
     * Delete a brand part history by ID
     * @param id The ID of the brand part history to delete
     * @param brandCode The brand code for table sharding
     */
    void delete(Long id, String brandCode) throws Exception;

    /**
     * Get count of history parts for a batch
     * @param batchNo The batch number
     * @param brandCode The brand code for table sharding
     * @return Count of history parts
     */
    Integer getBatchPartHisCount(String batchNo, String brandCode);

    /**
     * Check if a table exists
     * @param brandCode The name of the table to check
     * @return true if the table exists, false otherwise
     */
    boolean checkTableExists(String brandCode);

    /**
     * Create brand part history table
     * @param brandCode The brand code for table sharding
     */
    void createBrandPartHisTable(String brandCode) throws Exception;

    /**
     * Create necessary indexes for the brand part history table
     * @param brandCode The brand code for table sharding
     */
    void createIndexes(String brandCode) throws Exception;

    /**
     * 直接批量更新品牌配件历史记录的客户端编码
     *
     * @param updateParamsList 更新参数列表，包含brandCode, batchNo, clientCode, updateId/supTableId
     * @return 更新的记录数量
     */
    int batchDirectUpdate(List<Map<String, Object>> updateParamsList);
}

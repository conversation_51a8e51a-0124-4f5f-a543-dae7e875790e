package com.jy.service.impl;

import com.jy.ann.MethodMonitor;
import com.jy.bean.po.BatchDetail;
import com.jy.bean.po.TrailDetail;
import com.jy.bean.result.ResultStatus;
import com.jy.mapper.TrailDetailMapper;
import com.jy.service.BatchDetailService;
import com.jy.service.TrailDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class TrailDetailServiceImpl implements TrailDetailService {

    @Autowired
    private TrailDetailMapper trailDetailMapper;
    @Autowired
    private TrailDetailService trailDetailService;
    @Autowired
    private BatchDetailService batchDetailService;

    @Override
    @MethodMonitor
    public List<TrailDetail> listTrailDetail(Map<String, Object> map) {
        return null;
    }

    @Override
    @MethodMonitor
    public List<TrailDetail> listByBatchNo(String batchNo) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("batchNo", batchNo);
        return trailDetailMapper.listTrailDetail(map);
    }

    @Override
    @MethodMonitor
    public void updateBatchNoState(String batchNo, String clientCode, String status) throws Exception {
        TrailDetail trailDetail = new TrailDetail(batchNo, clientCode, status);
        trailDetailService.update(trailDetail);
        List<TrailDetail> trailDetails = trailDetailService.listByBatchNo(batchNo);
        Set<String> statuses = trailDetails.stream().map(TrailDetail::getStatus).collect(Collectors.toSet());
        String batchStatus = ResultStatus.SUCCESS.getStatus();
        if(statuses.contains(ResultStatus.SUCCESS_ACCEPTED.getStatus())){
            batchStatus = ResultStatus.SUCCESS_ACCEPTED.getStatus();
        } else if(statuses.contains(ResultStatus.INTERNAL_SERVER_ERROR.getStatus())){
            batchStatus = ResultStatus.INTERNAL_SERVER_ERROR.getStatus();
        }
        BatchDetail batchDetail = new BatchDetail(batchNo, batchStatus);
        batchDetailService.update(batchDetail);
    }

    @Override
    @MethodMonitor
    public TrailDetail save(TrailDetail trailDetail) throws Exception {
        trailDetailMapper.save(trailDetail);
        return trailDetail;
    }

    @Override
    @MethodMonitor
    public void delete(String id) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("id", id);
        trailDetailMapper.delete(map);
    }

    @Override
    @MethodMonitor
    public TrailDetail update(TrailDetail trailDetail) throws Exception {
        trailDetailMapper.update(trailDetail);
        return trailDetail;
    }
}

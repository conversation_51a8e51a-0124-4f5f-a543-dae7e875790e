package com.jy.service.impl;

import com.jy.bean.po.SysUserRole;
import com.jy.mapper.SysUserRoleMapper;
import com.jy.service.SysUserRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/5/14
 */
@Service
public class SysUserRoleServiceImpl implements SysUserRoleService {

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Override
    public SysUserRole save(SysUserRole sysUserRole) throws Exception {
        sysUserRoleMapper.save(sysUserRole);
        return sysUserRole;
    }

    @Override
    public void delete(String userId) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("userId", userId);
        sysUserRoleMapper.delete(map);
    }
}

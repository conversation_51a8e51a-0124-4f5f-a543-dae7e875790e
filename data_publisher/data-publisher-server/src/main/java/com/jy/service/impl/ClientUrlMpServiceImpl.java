package com.jy.service.impl;

import com.jy.bean.po.ClientTable;
import com.jy.bean.po.ClientUrlMp;
import com.jy.mapper.ClientUrlMpMapper;
import com.jy.service.ClientUrlMpService;
import com.jy.util.EmptyUtils;
import com.jy.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/5/23
 */
@Service
@CacheConfig(cacheManager = "ehCacheCacheManager", cacheNames = "clientUrlMp")
public class ClientUrlMpServiceImpl implements ClientUrlMpService{

    @Autowired
    private ClientUrlMpService clientUrlMpService;
    @Autowired
    private ClientUrlMpMapper clientUrlMpMapper;

    @Override
    public List<ClientUrlMp> listClientUrlMp(Map<String, Object> map) {
        return clientUrlMpMapper.listClientUrlMp(map);
    }

    @Override
    @Cacheable(key = "#root.targetClass + ':' + #root.methodName + ':' + #clientCode + ':' + #tableName")
    public ClientUrlMp getByClientCodeAndTableName(String clientCode, String tableName) {
        Map<String, Object> map = new HashMap<>();
        map.put("clientCode", clientCode);
        map.put("tableName", tableName);
        List<ClientUrlMp> clientUrlMps = clientUrlMpMapper.listClientUrlMp(map);
        if(EmptyUtils.isNotEmpty(clientUrlMps)){
            return clientUrlMps.get(0);
        }
        return null;
    }

    @Override
    @Cacheable(key = "#root.targetClass + ':' + #root.methodName + ':' + #baseClientCode")
    public List<ClientUrlMp> listByBaseClientCode(String baseClientCode) {
        Map<String, Object> map = new HashMap<>();
        map.put("baseClientCode", baseClientCode);
        return clientUrlMpMapper.listCompareClient(map);
    }

    @Override
    @Cacheable(key = "#root.targetClass + ':' + #root.methodName + ':' + #clientCode")
    public List<ClientUrlMp> listByClientCode(String clientCode) {
        Map<String, Object> map = new HashMap<>();
        map.put("clientCode", clientCode);
        List<ClientUrlMp> clientUrlMps = clientUrlMpMapper.listClientUrlMp(map);
        if(EmptyUtils.isEmpty(clientUrlMps)){
            return null;
        }
        return clientUrlMps;
    }

    @Override
    @Caching(evict = {
            @CacheEvict(key = "#root.targetClass + ':listByClientCode:' + #result.clientCode"),
            @CacheEvict(key = "#root.targetClass + ':listByBaseClientCode:' + #result.baseClientCode"),
            @CacheEvict(key = "#root.targetClass + ':getByClientCodeAndTableName:' + #result.clientCode + ':' + #result.tableName")})
    public ClientUrlMp save(ClientUrlMp clientUrlMp) throws Exception {
        clientUrlMp.setId(StringUtils.getUUID());
        clientUrlMpMapper.save(clientUrlMp);
        return clientUrlMp;
    }

    @Override
    public void delete(String id) throws Exception {
        clientUrlMpService.clearCache(id);
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        clientUrlMpMapper.delete(map);
    }

    @Override
    @Caching(evict = {
            @CacheEvict(key = "#root.targetClass + ':listByClientCode:' + #result.clientCode"),
            @CacheEvict(key = "#root.targetClass + ':listByBaseClientCode:' + #result.baseClientCode"),
            @CacheEvict(key = "#root.targetClass + ':getByClientCodeAndTableName:' + #result.clientCode + ':' + #result.tableName")})
    public ClientUrlMp update(ClientUrlMp clientUrlMp) throws Exception {
        clientUrlMpService.clearCache(clientUrlMp.getId());
        clientUrlMpMapper.update(clientUrlMp);
        return clientUrlMp;
    }

    @Override
    @Caching(evict = {
            @CacheEvict(key = "#root.targetClass + ':listByClientCode:' + #result.clientCode"),
            @CacheEvict(key = "#root.targetClass + ':listByBaseClientCode:' + #result.baseClientCode"),
            @CacheEvict(key = "#root.targetClass + ':getByClientCodeAndTableName:' + #result.clientCode + ':' + #result.tableName")})
    public ClientUrlMp clearCache(String id) throws Exception {
        ClientUrlMp temp = null;
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("id", id);
        List<ClientUrlMp> clientUrlMps = clientUrlMpMapper.listClientUrlMp(map);
        if(EmptyUtils.isNotEmpty(clientUrlMps)){
            temp = clientUrlMps.get(0);
        }
        return temp;
    }
}

package com.jy.service;

import com.alibaba.fastjson.JSONObject;
import com.jy.bean.dto.BaseDataDTO;
import com.jy.bean.po.Client;
import java.util.List;

/**
 * BaseData装配服务
 * <AUTHOR>
 * @date 2018/3/29
 **/
public interface DataFitService {

    void fit(BaseDataDTO baseDataDTO) throws Exception;

    /**
     * 重新发送数据
     * @param list 需要重发的数据
     * @param clientCode 客户端名称
     * @throws Exception
     */
    void resend(List<BaseDataDTO> list,String clientCode) throws Exception;

    List<BaseDataDTO> splitByFieldOrgMp(BaseDataDTO baseDataDTO, Client client)  throws Exception;

    List<Client> listClient(BaseDataDTO baseDataDTO) throws Exception;

    /**
     * 发送数据到客户端
     * @param client 客户端信息
     * @param dataJson 发送的数据
     */
    void sendData(Client client,JSONObject dataJson) throws Exception;

}

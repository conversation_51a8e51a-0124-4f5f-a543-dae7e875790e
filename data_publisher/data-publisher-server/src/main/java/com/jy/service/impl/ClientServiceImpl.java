package com.jy.service.impl;

import com.jy.ann.MethodMonitor;
import com.jy.bean.common.ClientStatus;
import com.jy.bean.po.Client;
import com.jy.bean.po.SysUser;
import com.jy.mapper.ClientMapper;
import com.jy.mq.RabbitCommon;
import com.jy.service.ClientService;
import com.jy.service.SysUserService;
import com.jy.util.EmptyUtils;
import com.jy.util.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: zy
 * @Date: Created in 2018/4/16
 */
@Service
@CacheConfig(cacheManager = "ehCacheCacheManager", cacheNames = "client")
public class ClientServiceImpl implements ClientService {

    @Autowired
    private ClientMapper clientMapper;
    @Autowired
    private ClientService clientService;
    @Autowired
    private RabbitCommon rabbitCommon;
    @Autowired
    private SysUserService sysUserService;


    @Override
    @MethodMonitor
    public List<Client> listClient(Map<String, Object> map) {
        return clientMapper.listClient(map);
    }

    @Override
    @Cacheable(key = "#root.targetClass + ':' + #root.methodName + ':' + #path")
    public List<Client> listClientByPath(String path) {
        Map<String, Object> map = new HashMap<>();
        map.put("path", path);
        return clientService.listClient(map);
    }


    @Override
    @MethodMonitor
    public List<Client> listClientByBaseTable(String tableName) {
        Map<String, Object> map = new HashMap<>();
        map.put("baseTableName", tableName);
        return clientMapper.listClientByBaseTable(map);
    }

    @Override
    @Cacheable(key = "#root.targetClass + ':' + #root.methodName + ':' + #code")
    @MethodMonitor
    public Client getOneByCode(@NotNull String code) {
        Map<String, Object> map = new HashMap<>();
        map.put("code", code);
        List<Client> clients = clientMapper.listClient(map);
        if(EmptyUtils.isEmpty(clients)){
            return null;
        }
        return clients.get(0);
    }

    @Override
    @Cacheable(key = "#root.targetClass + ':' + #root.methodName + ':OFFLINE'")
    @MethodMonitor
    public List<Client> listByNoOfflineStatus() {
        Map<String, Object> map = new HashMap<>();
        map.put("removeStatus", ClientStatus.CLIENT_OFFLINE);
        map.put("removeCode", "TEST");
        map.put("removePath", "SRC");
        return clientMapper.listClient(map);
    }

    @Override
    @Caching(evict = {
            @CacheEvict(key = "#root.targetClass + ':listByNoOfflineStatus:OFFLINE'"),
            @CacheEvict(key = "#root.targetClass + ':listClientByPath:' + #result.path"),
            @CacheEvict(key = "#root.targetClass + ':getOneByCode:' + #result.code")})
    @MethodMonitor
    public Client save(Client client) throws Exception {
        client.setId(StringUtils.getUUID());
        clientMapper.save(client);

        rabbitCommon.createQueue(client.getCode());
        SysUser sysUser = sysUserService.listByUserName(client.getCode());
        if(EmptyUtils.isEmpty(sysUser)){
            sysUser = new SysUser();
            sysUser.setUsername(client.getCode());
            sysUser.setPassword(StringUtils.MD5(client.getCode().toLowerCase()));
            sysUserService.save(sysUser);
        }
        return client;
    }

    @Override
    @Caching(evict = {
            @CacheEvict(key = "#root.targetClass + ':listByNoOfflineStatus:OFFLINE'"),
            @CacheEvict(key = "#root.targetClass + ':listClientByPath:' + #result.path"),
            @CacheEvict(key = "#root.targetClass + ':getOneByCode:' + #result.code")})
    @MethodMonitor
    public Client delete(String id) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("id", id);
        List<Client> clients = clientMapper.listClient(map);
        Client client = null;
        if(EmptyUtils.isNotEmpty(clients)){
            client = clients.get(0);
        }
        clientMapper.delete(map);
        return client;
    }

    @Override
    @Caching(evict = {
            @CacheEvict(key = "#root.targetClass + ':listByNoOfflineStatus:OFFLINE'"),
            @CacheEvict(key = "#root.targetClass + ':listClientByPath:' + #result.path"),
            @CacheEvict(key = "#root.targetClass + ':getOneByCode:' + #result.code")})
    public Client update(Client client) throws Exception{
        clientMapper.update(client);
        return client;
    }
}

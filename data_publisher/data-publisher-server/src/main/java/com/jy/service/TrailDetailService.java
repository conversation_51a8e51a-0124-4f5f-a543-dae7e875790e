package com.jy.service;

import com.jy.bean.po.TrailDetail;

import java.util.List;
import java.util.Map;

public interface TrailDetailService {

    List<TrailDetail> listTrailDetail(Map<String, Object> map);

    List<TrailDetail> listByBatchNo(String batchNo);

    void updateBatchNoState(String batchNo, String clientCode, String status) throws Exception;

    TrailDetail save(TrailDetail trailDetail) throws Exception;

    void delete(String id) throws Exception;

    TrailDetail update(TrailDetail trailDetail) throws Exception;
}

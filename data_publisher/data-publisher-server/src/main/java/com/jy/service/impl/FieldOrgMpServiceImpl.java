package com.jy.service.impl;

import com.jy.ann.MethodMonitor;
import com.jy.bean.po.BatchDetail;
import com.jy.bean.po.FieldOrgMp;
import com.jy.mapper.FieldOrgMpMapper;
import com.jy.service.BatchDetailService;
import com.jy.service.FieldOrgMpService;
import com.jy.util.EmptyUtils;
import com.jy.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/5/2
 */
@Service
@CacheConfig(cacheManager = "ehCacheCacheManager", cacheNames = "fieldOrgMp")
public class FieldOrgMpServiceImpl implements FieldOrgMpService{

    @Autowired
    private FieldOrgMpMapper fieldOrgMpMapper;
    @Autowired
    private FieldOrgMpService fieldOrgMpService;
    @Autowired
    private BatchDetailService batchDetailService;

    @Override
    @Cacheable(key = "#root.targetClass + ':' + #root.methodName + ':' + #clientCode + ':' + #baseTableName + ':' + #baseOrgCode")
    @MethodMonitor
    public List<FieldOrgMp> listByClientCodeAndTableNameAndBaseOrgCode(String clientCode, String baseTableName, String baseOrgCode) {
        Map<String, Object> map = new HashMap<>();
        map.put("clientCode", clientCode);
        map.put("baseTableName", baseTableName);
        map.put("baseOrgCode", baseOrgCode);
        return fieldOrgMpMapper.listFieldOrgMp(map);
    }
    @Override
    @Cacheable(key = "#root.targetClass + ':' + #root.methodName + ':' + #clientCode + ':' + #baseTableName + ':' + #toBaseOrgCode")
    @MethodMonitor
    public List<FieldOrgMp> listByClientCodeAndTableNameAndToBaseOrgCode(String clientCode, String baseTableName, String toBaseOrgCode) {
        Map<String, Object> map = new HashMap<>();
        map.put("clientCode", clientCode);
        map.put("baseTableName", baseTableName);
        map.put("toBaseOrgCode", toBaseOrgCode);
        return fieldOrgMpMapper.listFieldOrgMp(map);
    }

    @Override
    @MethodMonitor
    public List<FieldOrgMp> listClientFieldOrgMp(Map<String, Object> map) {
        return fieldOrgMpMapper.listFieldOrgMp(map);
    }

    @Override
    public int getBatchNum(String batchNo, String clientCode) {
        BatchDetail batchDetail = batchDetailService.getByBatchNo(batchNo);
        int size = 1;
        if("local_market_price".equals(batchDetail.getTableName())){
            Map<String, Object> map = new HashMap<>();
            map.put("clientCode", clientCode);
            map.put("baseTableName", batchDetail.getTableName());
            map.put("baseOrgCode", batchDetail.getOrgCode());
            List<String> orgCodes = fieldOrgMpMapper.listOrgCode(map);
            size += orgCodes.size();
        }
        return  size;
    }

    @Override
    @CacheEvict(key = "#root.targetClass + ':listByClientCodeAndTableNameAndBaseOrgCode:'  + #result.clientCode + ':' + #result.baseTableName + ':' + #result.baseOrgCode")
    @MethodMonitor
    public FieldOrgMp save(FieldOrgMp fieldOrgMp) throws Exception {
        fieldOrgMp.setId(StringUtils.getUUID());
        fieldOrgMpMapper.save(fieldOrgMp);
        return fieldOrgMp;
    }

    @Override
    @CacheEvict(value = "fieldOrgMp", allEntries=true)
    @Transactional
    @MethodMonitor
    public List<FieldOrgMp> saveBatch(List<FieldOrgMp> fieldOrgMps) throws Exception {
        fieldOrgMpMapper.saveBatch(fieldOrgMps);
        return fieldOrgMps;
    }

    @Override
    @MethodMonitor
    public void delete(String id) throws Exception {
        fieldOrgMpService.clearCache(id);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("id", id);
        fieldOrgMpMapper.delete(map);
    }

    @Override
    @CacheEvict(key = "#root.targetClass + ':listByClientCodeAndTableNameAndBaseOrgCode:'  + #result.clientCode + ':' + #result.baseTableName + ':' + #result.baseOrgCode")
    @MethodMonitor
    public FieldOrgMp update(FieldOrgMp fieldOrgMp) throws Exception {
        fieldOrgMpService.clearCache(fieldOrgMp.getId());
        fieldOrgMpMapper.update(fieldOrgMp);
        return fieldOrgMp;
    }

    @Override
    @CacheEvict(key = "#root.targetClass + ':listByClientCodeAndTableNameAndBaseOrgCode:'  + #result.clientCode + ':' + #result.baseTableName + ':' + #result.baseOrgCode")
    public FieldOrgMp clearCache(String id) throws Exception {
        FieldOrgMp temp = null;
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("id", id);
        List<FieldOrgMp> fieldOrgMps = fieldOrgMpMapper.listFieldOrgMp(map);
        if(EmptyUtils.isNotEmpty(fieldOrgMps)){
            temp = fieldOrgMps.get(0);
        }
        return temp;
    }
}

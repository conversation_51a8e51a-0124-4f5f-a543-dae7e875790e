package com.jy.service.impl;

import com.jy.ann.MethodMonitor;
import com.jy.bean.po.ClientFilter;
import com.jy.mapper.ClientFilterMapper;
import com.jy.service.ClientFilterService;
import com.jy.util.EmptyUtils;
import com.jy.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/14
 */
@Service
@CacheConfig(cacheManager = "ehCacheCacheManager", cacheNames = "clientFilter")
public class ClientFilterServiceImpl implements ClientFilterService {

    @Autowired
    private ClientFilterMapper clientFilterMapper;
    @Autowired
    private ClientFilterService clientFilterService;

    @Override
    @MethodMonitor
    public List<ClientFilter> listClientFilter(Map<String, Object> map) {
        return clientFilterMapper.listClientFilter(map);
    }

    @Override
    @MethodMonitor
    @Cacheable(key = "#root.targetClass + ':' + #root.methodName + ':' + #clientCode + ':' + #tableName")
    public List<ClientFilter> listByClientCodeAndTableName(String clientCode, String tableName) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("clientCode", clientCode);
        map.put("tableName", tableName);
        return clientFilterMapper.listClientFilter(map);
        
    }

    @Override
    @Caching(evict = {
            @CacheEvict(key = "#root.targetClass + ':listByClientCodeAndTableName:' + #result.clientCode")})
    @MethodMonitor
    public ClientFilter save(ClientFilter clientFilter) throws Exception {
        clientFilter.setId(StringUtils.getUUID());
        clientFilterMapper.save(clientFilter);
        return clientFilter;
    }

    @Override
    @MethodMonitor
    public void delete(String id) throws Exception {
        clientFilterService.clearCache(id);
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        clientFilterMapper.delete(map);
    }

    @Override
    @Caching(evict = {
            @CacheEvict(key = "#root.targetClass + ':listByClientCodeAndTableName:' + #result.clientCode")})
    @MethodMonitor
    public ClientFilter update(ClientFilter clientFilter) throws Exception {
        clientFilterService.clearCache(clientFilter.getId());
        clientFilterMapper.update(clientFilter);
        return clientFilter;
    }

    @Override
    @Caching(evict = {
            @CacheEvict(key = "#root.targetClass + ':listByClientCodeAndTableName:' + #result.clientCode")})
    @MethodMonitor
    public ClientFilter clearCache(String id) throws Exception {
        ClientFilter temp = null;
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("id", id);
        List<ClientFilter> clientFilters = clientFilterMapper.listClientFilter(map);
        if(EmptyUtils.isNotEmpty(clientFilters)){
            temp = clientFilters.get(0);
        }
        return temp;
    }
}

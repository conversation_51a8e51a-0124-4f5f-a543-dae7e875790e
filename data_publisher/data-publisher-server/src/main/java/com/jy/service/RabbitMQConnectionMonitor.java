package com.jy.service;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.Connection;
import org.springframework.amqp.rabbit.connection.ConnectionListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * @program: data-publisher
 * @description:
 * @author: Ykuee
 * @create: 2024-12-31 14:24
 **/

@Service
public class RabbitMQConnectionMonitor {

    private static final Logger log = LogManager.getLogger(RabbitMQConnectionMonitor.class);

    private final CachingConnectionFactory connectionFactory;

    @Autowired
    private SendExpMsgService sendExpMsgService;

    public RabbitMQConnectionMonitor(CachingConnectionFactory connectionFactory) {
        this.connectionFactory = connectionFactory;
        setupConnectionListener();
    }

    private void setupConnectionListener() {
        connectionFactory.addConnectionListener(new ConnectionListener() {

            @Override
            public void onCreate(Connection connection) {
                sendAlert("与RabbitMQ的连接已建立！");
            }

            @Override
            public void onClose(Connection connection) {
                sendAlert("与RabbitMQ的连接已断开");
            }

        });
    }

    private void sendAlert(String message) {
        LocalDateTime startTime = LocalDateTime.now();
        try {
            log.error(message);
            sendExpMsgService.sendMsg("RabbitMQConnectionMonitor", startTime, message);
        } catch (Exception e) {
            log.error("及时更新发送RabbitMQ连接警告时发生异常", e);
        }
    }
}

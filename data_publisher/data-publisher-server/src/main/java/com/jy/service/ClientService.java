package com.jy.service;

import com.jy.bean.po.Client;

import java.util.List;
import java.util.Map;

/**
 * @Author: zy
 * @Date: Created in 2018/4/16
 */
public interface ClientService {

    List<Client> listClient(Map<String, Object> map);

    List<Client> listClientByPath(String path);

    List<Client> listClientByBaseTable(String tableName);

    Client getOneByCode(String code);

    Client save(Client client) throws Exception;

    List<Client> listByNoOfflineStatus();

    Client delete(String id) throws Exception;

    Client update(Client client) throws Exception;

}

package com.jy.service;

import com.jy.ann.ExceptionMonitor;
import com.jy.exception.BusinessException;
import freemarker.template.TemplateException;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Date;

public interface SendExpMsgService {

    void sendMsg(ExceptionMonitor monitor, LocalDateTime startTime, LocalDateTime endTime, Object[] args, Exception e) throws Exception;

    void sendMsg(ExceptionMonitor monitor, LocalDateTime startTime, LocalDateTime endTime, Object[] args, String msg) throws Exception;

    void sendMsg(String methodName, LocalDateTime startTime, String msg) throws Exception;
}

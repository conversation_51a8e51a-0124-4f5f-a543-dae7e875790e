package com.jy.service.impl;

import com.jy.ann.MethodMonitor;
import com.jy.bean.po.BrandPart;
import com.jy.mapper.BrandPartMapper;
import com.jy.service.BrandPartService;
import com.jy.util.EmptyUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Implementation of BrandPartService
 */
@Service
public class BrandPartServiceImpl implements BrandPartService {

    private static final Logger logger = LogManager.getLogger(BrandPartServiceImpl.class);

    @Autowired
    private BrandPartMapper brandPartMapper;

    @Override
    @MethodMonitor
    public BrandPart getBrandPartById(Long id, String brandCode) {
        return brandPartMapper.getBrandPartById(id, brandCode);
    }

    @Override
    @MethodMonitor
    public BrandPart getBrandPartByOe(String oe, String brandCode) {
        return brandPartMapper.getBrandPartByOe(oe, brandCode);
    }

    @Override
    @MethodMonitor
    public List<BrandPart> listBrandPart(Map<String, Object> params) {
        return brandPartMapper.listBrandPart(params);
    }

    @Override
    @MethodMonitor
    public List<BrandPart> listByBatchAndCodeAndTime(String batchNo, String originalPartCode, Date updateTime, String brandCode) {
        return brandPartMapper.listByBatchAndCodeAndTime(batchNo, originalPartCode, updateTime, brandCode);
    }

    @Override
    @MethodMonitor
    public BrandPart save(BrandPart brandPart) throws Exception {
        brandPartMapper.saveBrandPart(brandPart);
        return brandPart;
    }

    @Override
    @MethodMonitor
    public BrandPart update(BrandPart brandPart) throws Exception {
        brandPartMapper.updateBrandPart(brandPart);
        return brandPart;
    }

    @Override
    @MethodMonitor
    public void delete(Long id, String brandCode) throws Exception {
        brandPartMapper.deleteBrandPart(id, brandCode);
    }

    @Override
    @MethodMonitor
    public Integer getBatchPartCount(String batchNo, String brandCode) {
        return brandPartMapper.getBatchPartCount(batchNo, brandCode);
    }

    @Override
    @MethodMonitor
    public BrandPart getBrandPartBySupTableId(String supTableId, String brandCode) {
        return brandPartMapper.getBrandPartBySupTableId(supTableId, brandCode);
    }

    @Override
    @MethodMonitor
    public BrandPart saveOrUpdate(BrandPart brandPart) throws Exception {
        // 检查是否存在
        if (EmptyUtils.isNotEmpty(brandPart.getSupTableId())) {
            BrandPart existingPart = getBrandPartBySupTableId(brandPart.getSupTableId(), brandPart.getBrandCode());
            if (existingPart != null) {
                // 存在则更新
                brandPart.setId(existingPart.getId()); // 设置ID以确保更新正确的记录
                logger.debug("existingPart不为空，需要更新：{}", existingPart);
                return update(brandPart);
            }
        }
        // 不存在则新增
        return save(brandPart);
    }

    @Override
    @MethodMonitor
    public boolean checkTableExists(String brandCode) {
        Integer result = brandPartMapper.checkTableExists(brandCode);
        logger.debug("brandCode:{}, checkTableExists：{}", brandCode, result);
        return result != null && result.equals(1);
    }

    @Override
    @MethodMonitor
    public void createBrandPartTable(String brandCode) throws Exception {
        brandPartMapper.createBrandPartTable(brandCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @MethodMonitor
    public void createIndexes(String brandCode) throws Exception {
        brandPartMapper.createBatchNoAndOriginalPartCodeIndex(brandCode);
        brandPartMapper.createBatchNoOriginalPartCodeUtIndex(brandCode);
        brandPartMapper.createBatchNoAndSupTableIdIndex(brandCode);
        brandPartMapper.createOriginalPartCodeIndex(brandCode);
        brandPartMapper.createBatchNoIndex(brandCode);
    }

    @Override
    @MethodMonitor
    public List<BrandPart> queryBrandPartWithPage(Map<String, Object> params) {
        return brandPartMapper.queryBrandPartWithPage(params);
    }

    @Override
    @MethodMonitor
    public List<BrandPart> queryMultipleBrandPartsWithPage(Map<String, Object> params) {
        return brandPartMapper.queryMultipleBrandPartsWithPage(params);
    }

}

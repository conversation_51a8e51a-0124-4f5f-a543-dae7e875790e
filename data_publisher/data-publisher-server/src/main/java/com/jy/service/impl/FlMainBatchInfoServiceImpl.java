package com.jy.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.jy.bean.dto.DashBordDailyDTO;
import com.jy.bean.po.BatchDetail;
import com.jy.bean.po.FlMainBatchInfo;
import com.jy.bean.result.ResultStatus;
import com.jy.mapper.BatchDetailMapper;
import com.jy.mapper.FlMainBatchInfoMapper;
import com.jy.service.FlMainBatchInfoService;
import com.jy.util.DateUtil;
import com.jy.util.HttpUtils;
import com.jy.util.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: zy
 * @Date: Created in 2019/11/22
 */
@Service
public class FlMainBatchInfoServiceImpl implements FlMainBatchInfoService {

    @Autowired
    private FlMainBatchInfoMapper flMainBatchInfoMapper;
    @Autowired
    private BatchDetailMapper batchDetailMapper;

    @Value("${transfer.transferOrder.get}")
    private String transferOrderGetUrl;
    @Value("${transfer.transferOrder.update}")
    private String transferOrderUpdateUrl;

    @Override
    public void saveBacth(@NotNull List<FlMainBatchInfo> mainBatchInfoList) throws Exception {
        flMainBatchInfoMapper.insertBatch(mainBatchInfoList);
    }

    @Override
    public void save(@NotNull FlMainBatchInfo mainBatchInfo) throws Exception {
        flMainBatchInfoMapper.insert(mainBatchInfo);
    }

    @Override
    public void update(@NotNull FlMainBatchInfo mainBatchInfo) throws Exception {
        flMainBatchInfoMapper.update(mainBatchInfo);
    }

    @Override
    public List<FlMainBatchInfo> listFlMainBatchInfo(Map<String, Object> map) throws Exception {
        List<FlMainBatchInfo> flMainBatchInfoList = flMainBatchInfoMapper.listFlMainBatchInfo(map);
        return flMainBatchInfoList;
    }

    @Override
    public DashBordDailyDTO getDailyData(Pageable pageable, String mainBatchNo, String status, String clientCode) throws Exception {
        LocalDate today = LocalDate.now();
        // 当天零点
        String startOfDay = DateUtil.convertDateToString(today.atStartOfDay());
        // 当天23:59
        String endOfDay = DateUtil.convertDateToString(today.atTime(23, 59, 59));
        DashBordDailyDTO dto = flMainBatchInfoMapper.selectDaily(startOfDay, endOfDay);

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("startTime", startOfDay);
        paramMap.put("endTime", endOfDay);
        paramMap.put("clientCode", clientCode);
        paramMap.put("mainBatchNo", mainBatchNo);
        paramMap.put("status", status);
        List<FlMainBatchInfo> flMainBatchInfoList = flMainBatchInfoMapper.listDailyMainBatchInfo(paramMap);
        dto.setMainBatchList(flMainBatchInfoList);
        return dto;
    }

    @Override
    public Integer transferOrder(String mainBatchNo) throws Exception {
        Map<String, String> querys = new HashMap<String, String>();
        querys.put("mainBatchNo", mainBatchNo);
        String response = HttpUtils.doGet(transferOrderGetUrl, null, new HashMap<>(), querys);
        JSONObject jsonObject = JSONObject.parseObject(response);
        if(!ResultStatus.SUCCESS.getStatus().equals(jsonObject.get("status"))){
            throw new Exception("获取转换平台优先级排序发生异常："+jsonObject.get("message"));
        }
        return NumberUtils.String2Int(jsonObject.getString("result"),0);
    }

    @Override
    public void updateTransferOrder(String mainBatchNo, Integer transferOrder) throws Exception {
        Map<String, String> bodyMap = new HashMap<String, String>();
        bodyMap.put("mainBatchNo", mainBatchNo);
        bodyMap.put("transferOrder", String.valueOf(transferOrder));
        String response = HttpUtils.doPost(transferOrderUpdateUrl, null, null, null, JSONObject.toJSONString(bodyMap));
        JSONObject jsonObject = JSONObject.parseObject(response);
        if(!ResultStatus.SUCCESS.getStatus().equals(jsonObject.get("status"))){
            throw new Exception("获取转换平台优先级排序发生异常："+jsonObject.get("message"));
        }
    }

    @Override
    public Integer publishOrder(String mainBatchNo) {
        return batchDetailMapper.selectMaxOrder(mainBatchNo);
    }

    @Override
    public void updatePublishOrder(String mainBatchNo, Integer order) {
        BatchDetail batchDetail = new BatchDetail();
        batchDetail.setMainBatchNo(mainBatchNo);
        batchDetail.setBatchOrder(order);
        batchDetailMapper.updateByMainBatchNo(batchDetail);
    }

    @Override
    public FlMainBatchInfo getFlMainBatchInfo(String mainBatchNo) {
        return flMainBatchInfoMapper.selectFlMainBatchInfo(mainBatchNo);
    }

    @Override
    public void reset(String mainBatchNo, Date startTime) {
        String status = ResultStatus.PROCESSING.getStatus();
        flMainBatchInfoMapper.reset(mainBatchNo, startTime, status);
    }

    @Override
    public void resetBatch(List<BatchDetail> batchDetailList, Date startTime) {
        String status = ResultStatus.PROCESSING.getStatus();
        flMainBatchInfoMapper.resetBatch(batchDetailList, startTime, status);
    }
}

package com.jy.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.jy.bean.dto.DashBordDailyDTO;
import com.jy.bean.po.BatchDetail;
import com.jy.bean.po.FlMainBatchInfo;
import com.jy.bean.result.ResultStatus;
import com.jy.mapper.BatchDetailMapper;
import com.jy.mapper.FlMainBatchInfoMapper;
import com.jy.service.FlMainBatchInfoService;
import com.jy.util.DateUtil;
import com.jy.util.HttpUtils;
import com.jy.util.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.stream.Collectors;
import java.util.ArrayList;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: zy
 * @Date: Created in 2019/11/22
 */
@Service
public class FlMainBatchInfoServiceImpl implements FlMainBatchInfoService {

    @Autowired
    private FlMainBatchInfoMapper flMainBatchInfoMapper;
    @Autowired
    private BatchDetailMapper batchDetailMapper;

    @Value("${transfer.transferOrder.get}")
    private String transferOrderGetUrl;
    @Value("${transfer.transferOrder.update}")
    private String transferOrderUpdateUrl;

    @Override
    public void saveBacth(@NotNull List<FlMainBatchInfo> mainBatchInfoList) throws Exception {
        flMainBatchInfoMapper.insertBatch(mainBatchInfoList);
    }

    @Override
    public void save(@NotNull FlMainBatchInfo mainBatchInfo) throws Exception {
        flMainBatchInfoMapper.insert(mainBatchInfo);
    }

    @Override
    public void update(@NotNull FlMainBatchInfo mainBatchInfo) throws Exception {
        flMainBatchInfoMapper.update(mainBatchInfo);
    }

    @Override
    public List<FlMainBatchInfo> listFlMainBatchInfo(Map<String, Object> map) throws Exception {
        List<FlMainBatchInfo> flMainBatchInfoList = flMainBatchInfoMapper.listFlMainBatchInfo(map);
        return flMainBatchInfoList;
    }

    @Override
    public DashBordDailyDTO getDailyStats() throws Exception {
        LocalDate today = LocalDate.now();
        // 当天零点
        String startOfDay = DateUtil.convertDateToString(today.atStartOfDay());
        // 当天23:59
        String endOfDay = DateUtil.convertDateToString(today.atTime(23, 59, 59));
        DashBordDailyDTO dto = flMainBatchInfoMapper.selectDaily(startOfDay, endOfDay);
        return dto;
    }

    @Override
    public List<FlMainBatchInfo> getDailyData(Pageable pageable, String mainBatchNo, String status, String clientCode) throws Exception {
        LocalDate today = LocalDate.now();
        // 当天零点
        String startOfDay = DateUtil.convertDateToString(today.atStartOfDay());
        // 当天23:59
        String endOfDay = DateUtil.convertDateToString(today.atTime(23, 59, 59));

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("startTime", startOfDay);
        paramMap.put("endTime", endOfDay);
        paramMap.put("clientCode", clientCode);
        paramMap.put("mainBatchNo", mainBatchNo);
        paramMap.put("status", status);
        PageHelper.startPage(pageable.getPageNumber(), pageable.getPageSize());
        List<FlMainBatchInfo> flMainBatchInfoList = flMainBatchInfoMapper.listDailyMainBatchInfo(paramMap);
        return flMainBatchInfoList;
    }

    /**
     * 高性能版本：分离查询逻辑，先查主表数据，再批量查询状态
     */
    public List<FlMainBatchInfo> getDailyDataOptimized(Pageable pageable, String mainBatchNo, String status, String clientCode) throws Exception {
        LocalDate today = LocalDate.now();
        // 当天零点
        String startOfDay = DateUtil.convertDateToString(today.atStartOfDay());
        // 当天23:59
        String endOfDay = DateUtil.convertDateToString(today.atTime(23, 59, 59));

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("startTime", startOfDay);
        paramMap.put("endTime", endOfDay);
        paramMap.put("clientCode", clientCode);
        paramMap.put("mainBatchNo", mainBatchNo);
        // 注意：优化版本中，status过滤在应用层处理
        if (status != null && !status.startsWith("t") && !status.startsWith("p")) {
            paramMap.put("status", status);
        }

        PageHelper.startPage(pageable.getPageNumber(), pageable.getPageSize());

        // 1. 先查询主表数据（无JOIN，速度快）
        List<FlMainBatchInfo> mainBatchList = flMainBatchInfoMapper.listDailyMainBatchInfoOptimized(paramMap);

        if (mainBatchList.isEmpty()) {
            return mainBatchList;
        }

        // 2. 提取主批次号列表
        List<String> mainBatchNos = mainBatchList.stream()
                .map(FlMainBatchInfo::getMainBatchNo)
                .collect(Collectors.toList());

        // 3. 批量查询状态信息
        List<Map<String, Object>> batchStatusList = flMainBatchInfoMapper.getBatchStatusByMainBatchNos(mainBatchNos);
        Map<String, Map<String, Object>> statusMap = batchStatusList.stream()
                .collect(Collectors.toMap(
                    map -> (String) map.get("main_batch_no"),
                    map -> map
                ));

        // 4. 计算状态并设置到结果中
        List<FlMainBatchInfo> resultList = new ArrayList<>();
        for (FlMainBatchInfo batch : mainBatchList) {
            Map<String, Object> statusInfo = statusMap.get(batch.getMainBatchNo());
            String calculatedStatus;
            String dataSource;

            if (statusInfo != null) {
                calculatedStatus = calculateBatchStatus(batch.getStatus(), statusInfo);
                dataSource = calculateDataSource(statusInfo);
            } else {
                // 没有子批次信息，使用默认状态
                calculatedStatus = "101".equals(batch.getStatus()) ? "t01" : batch.getStatus();
                dataSource = "transfer";
            }

            // 应用状态过滤
            if (status == null || status.equals(calculatedStatus)) {
                batch.setStatus(calculatedStatus);
                batch.setDataSource(dataSource);
                resultList.add(batch);
            }
        }

        return resultList;
    }

    /**
     * 计算批次状态
     */
    private String calculateBatchStatus(String originalStatus, Map<String, Object> statusInfo) {
        Long transferCnt = getLongValue(statusInfo.get("transfer_cnt"));
        Long publisherCnt = getLongValue(statusInfo.get("publisher_cnt"));
        Long publisherErrorCnt = getLongValue(statusInfo.get("publisher_error_cnt"));

        if ("101".equals(originalStatus)) {
            if (transferCnt == 0 && publisherCnt == 0) {
                return "t01";
            } else if (transferCnt > 0 && publisherCnt == 0) {
                return "t02";
            } else if (publisherCnt > 0) {
                return "p01";
            }
        }

        if (publisherErrorCnt > 0) {
            return "p03";
        }

        if ("200".equals(originalStatus)) {
            return "p02";
        }

        return originalStatus;
    }

    /**
     * 计算数据源
     */
    private String calculateDataSource(Map<String, Object> statusInfo) {
        Long transferCnt = getLongValue(statusInfo.get("transfer_cnt"));
        Long publisherCnt = getLongValue(statusInfo.get("publisher_cnt"));

        if (transferCnt == 0 && publisherCnt > 0) {
            return "publisher";
        }
        return "transfer";
    }

    /**
     * 安全获取Long值
     */
    private Long getLongValue(Object value) {
        if (value == null) {
            return 0L;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        return 0L;
    }

    @Override
    public Integer transferOrder(String mainBatchNo) throws Exception {
        Map<String, String> querys = new HashMap<String, String>();
        querys.put("mainBatchNo", mainBatchNo);
        String response = HttpUtils.doGet(transferOrderGetUrl, null, new HashMap<>(), querys);
        JSONObject jsonObject = JSONObject.parseObject(response);
        if(!ResultStatus.SUCCESS.getStatus().equals(jsonObject.get("status"))){
            throw new Exception("获取转换平台优先级排序发生异常："+jsonObject.get("message"));
        }
        return NumberUtils.String2Int(jsonObject.getString("result"),0);
    }

    @Override
    public void updateTransferOrder(String mainBatchNo, Integer transferOrder) throws Exception {
        Map<String, String> bodyMap = new HashMap<String, String>();
        bodyMap.put("mainBatchNo", mainBatchNo);
        bodyMap.put("transferOrder", String.valueOf(transferOrder));
        String response = HttpUtils.doPost(transferOrderUpdateUrl, null, null, null, JSONObject.toJSONString(bodyMap));
        JSONObject jsonObject = JSONObject.parseObject(response);
        if(!ResultStatus.SUCCESS.getStatus().equals(jsonObject.get("status"))){
            throw new Exception("获取转换平台优先级排序发生异常："+jsonObject.get("message"));
        }
    }

    @Override
    public Integer publishOrder(String mainBatchNo) {
        return batchDetailMapper.selectMaxOrder(mainBatchNo);
    }

    @Override
    public void updatePublishOrder(String mainBatchNo, Integer order) {
        BatchDetail batchDetail = new BatchDetail();
        batchDetail.setMainBatchNo(mainBatchNo);
        batchDetail.setBatchOrder(order);
        batchDetailMapper.updateByMainBatchNo(batchDetail);
    }

    @Override
    public FlMainBatchInfo getFlMainBatchInfo(String mainBatchNo) {
        return flMainBatchInfoMapper.selectFlMainBatchInfo(mainBatchNo);
    }

    @Override
    public void reset(String mainBatchNo, Date startTime) {
        String status = ResultStatus.PROCESSING.getStatus();
        flMainBatchInfoMapper.reset(mainBatchNo, startTime, status);
    }

    @Override
    public void resetBatch(List<BatchDetail> batchDetailList, Date startTime) {
        String status = ResultStatus.PROCESSING.getStatus();
        flMainBatchInfoMapper.resetBatch(batchDetailList, startTime, status);
    }
}

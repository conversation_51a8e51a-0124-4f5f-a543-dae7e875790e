package com.jy.service.impl;

import com.jy.bean.result.JsonResult;
import com.jy.bean.result.ResultStatus;
import com.jy.mapper.MonitorMapper;
import com.jy.mq.RabbitCommon;
import com.jy.service.MonitorService;
import com.jy.service.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 各组件监控
 * @Author: zy
 * @Date: Created in 2018/5/18
 */
@Service
public class MonitorServiceImpl implements MonitorService{

    @Autowired
    private MonitorMapper monitorMapper;
    @Autowired
    private RabbitCommon rabbitCommon;
    @Resource
    private RedisService redisService;

    @Override
    public JsonResult testMysql(){
        JsonResult jsonResult = new JsonResult();
        jsonResult.setResult("Mysql : healthy ");
        try {
            String testMysql = monitorMapper.testMysql();
            System.out.println(testMysql);
        } catch (Exception e) {
            e.printStackTrace();
            jsonResult.setResultStatus(ResultStatus.INTERNAL_SERVER_ERROR);
            jsonResult.setResult("Mysql : down , " + e.getMessage());
        }
        return jsonResult;
    }

    @Override
    public JsonResult testMq(){
        JsonResult jsonResult = new JsonResult();
        jsonResult.setResult("MQ : healthy ");
        try {
            int count = rabbitCommon.getCount("baseData");
            System.out.println(count);
        } catch (Exception e) {
            e.printStackTrace();
            jsonResult.setResultStatus(ResultStatus.INTERNAL_SERVER_ERROR);
            jsonResult.setResult("MQ : down , " +e.getMessage());
        }
        return jsonResult;
    }

  /*  @Override
    public JsonResult testMongoDB(){
        JsonResult jsonResult = new JsonResult();
        jsonResult.setResult("MongoDB : healthy ");
        try {
            Query query = new Query();
            long count = mongoTemplate.count(query, "monitor");
            System.out.println(count);
        } catch (Exception e) {
            e.printStackTrace();
            jsonResult.setResultStatus(ResultStatus.INTERNAL_SERVER_ERROR);
            jsonResult.setResult("MongoDB : down , " +  e.getMessage());
        }
        return jsonResult;
    }*/

    @Override
    public JsonResult testRedis(){
        JsonResult jsonResult = new JsonResult();
        jsonResult.setResult("Redis : healthy ");
        try {
            Object object = redisService.get("1");
            System.out.println(object);
        } catch (Exception e) {
            e.printStackTrace();
            jsonResult.setResultStatus(ResultStatus.INTERNAL_SERVER_ERROR);
            jsonResult.setResult("Redis : down , " + e.getMessage());
        }
        return jsonResult;
    }
}

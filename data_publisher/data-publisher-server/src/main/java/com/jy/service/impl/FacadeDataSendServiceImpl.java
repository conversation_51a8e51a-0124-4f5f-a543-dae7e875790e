package com.jy.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jy.ann.ExceptionMonitor;
import com.jy.ann.MethodMonitor;
import com.jy.bean.common.DataTraceMenu;
import com.jy.bean.dto.BaseDataDTO;
import com.jy.bean.result.ResultStatus;
import com.jy.service.FacadeDataSendService;
import com.jy.util.*;
import com.jy.util.rabbitmq.DataTraceUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2018/5/23
 */
@Service
@EnableScheduling
public class FacadeDataSendServiceImpl implements FacadeDataSendService {
    private static final Logger logger = LogManager.getLogger(FacadeDataSendServiceImpl.class);

    @Autowired
    private FacadeUtils facadeUtils;

    @Async("asyncServiceExecutor")
    @ExceptionMonitor(methodName = "FacadeDataSendService.receiveSwitch", remark = "定时任务")
    @Override
    public void receiveSwitch(JSONObject obj, String clientCode) throws Exception {
        if(EmptyUtils.isEmpty(obj.get("batchNoStatus"))){//facade更新
            try {
                logger.info("client:{}发送 FacadeDataSend", clientCode);
                JSONObject result = sendData(obj);
                String status = !ResultStatus.SUCCESS.getStatus().equals(result.getString("status")) ? ResultStatus.INTERNAL_SERVER_ERROR.getStatus() : result.getString("status");
                DataTraceUtils.sendTrace(StringUtils.getUUID(), obj, DataTraceMenu.CLIENT_DEAL_DESC.getCode(), clientCode, status, status + ":" + result.getString("message"));
            } catch (Exception e) {
                String message = ToolUtils.getExceptionMsg(e);
                DataTraceUtils.sendTrace(StringUtils.getUUID(), obj, DataTraceMenu.CLIENT_DEAL_DESC.getCode(), clientCode, ResultStatus.INTERNAL_SERVER_ERROR.getStatus(), message);
                logger.error("Facade数据更新出错：{}, message: {}", obj, message);
                throw e;
            }
        }
    }

    @Override
    @MethodMonitor
    public JSONObject sendData(JSONObject obj) throws Exception {
        BaseDataDTO baseDataDTO = JSON.toJavaObject(obj, BaseDataDTO.class);
        Map<String, Object> result = new HashMap<>();     //数据提取
        result.putAll(baseDataDTO.getFields());
        result.putAll(baseDataDTO.getKeys());
        if(EmptyUtils.isNotEmpty(baseDataDTO.getArrays())){
            result.putAll(baseDataDTO.getArrays());
        }

        JSONObject json = null;
        //logger.info("发送给facade的数据为:{}", JSON.toJSONString(result));
        if("insert".equals(baseDataDTO.getOperate())){
            json = facadeUtils.doPost(baseDataDTO.getClientUrl(), baseDataDTO.getClientPath(), null, JSON.toJSONString(result));
        } else if("update".equals(baseDataDTO.getOperate())){
            json = facadeUtils.doPut(baseDataDTO.getClientUrl(), baseDataDTO.getClientPath(), null, JSON.toJSONString(result));
        } else if("delete".equals(baseDataDTO.getOperate())){
            Map<String, String> result1 = new HashMap<>();
            result1.putAll(baseDataDTO.getFields());
            result1.putAll(baseDataDTO.getKeys());
            json = facadeUtils.doDelete(baseDataDTO.getClientUrl(), baseDataDTO.getClientPath(), result1);
        }
        return json;
    }

}

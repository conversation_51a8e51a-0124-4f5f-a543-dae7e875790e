package com.jy.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jy.ann.ExceptionMonitor;
import com.jy.bean.dto.ExpMsgDTO;
import com.jy.service.SendExpMsgService;
import com.jy.util.HttpUtils;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * @program: data-publisher-server
 * @description:
 * @author: Ykuee
 * @create: 2024-04-29 15:15
 **/
@Service
public class SendExpMsgServiceImpl implements SendExpMsgService {

    private static final Logger log = LogManager.getLogger(SendExpMsgServiceImpl.class);

    @Value("${feishu.bot.host}")
    private String host;
    @Value("${feishu.bot.path}")
    private String path;
    @Value("${feishu.bot.secret}")
    private String secret;
    @Value("${feishu.bot.hostName}")
    private String hostName;

    @Value("${feishu.proxy.host}")
    private String proxyHost;
    @Value("${feishu.proxy.port}")
    private String proxyPort;
    @Value("${feishu.proxy.userName}")
    private String proxyUserName;
    @Value("${feishu.proxy.password}")
    private String proxyPassword;

    private DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    @Autowired
    private FreeMarkerConfigurer configurer;

    private HashMap<String, Long> errMsgMap = new HashMap();

    @Override
    public void sendMsg(ExceptionMonitor monitor, LocalDateTime startTime, LocalDateTime endTime, Object[] args, Exception e) throws Exception {
        String expStr = getExceptionStr(args, e);
        sendMsg(monitor.methodName(), monitor.remark(), startTime, endTime, expStr);
    }

    @Override
    public void sendMsg(ExceptionMonitor monitor, LocalDateTime startTime, LocalDateTime endTime, Object[] args, String msg) throws Exception {
        String expStr = getExceptionStr(args, msg);
        sendMsg(monitor.methodName(), monitor.remark(), startTime, endTime, expStr);
    }

    @Override
    public void sendMsg(String methodName, LocalDateTime startTime, String msg) throws Exception {
        sendMsg(methodName, "来自及时更新紧急通知", startTime, startTime, msg);
    }

    public void sendMsg(String methodName, String remark, LocalDateTime startTime, LocalDateTime endTime, String expMsg) throws Exception {
        if(StringUtils.isNotEmpty(expMsg)) {
            //判断expMsg是否前后包含双引号，如果不包含则添加双引号
            if (!expMsg.startsWith("\"")) {
                expMsg = "\"" + expMsg;
            }
            if (!expMsg.endsWith("\"")) {
                expMsg = expMsg + "\"";
            }
            //去除换行符
            expMsg = StringUtils.remove(expMsg, "\n");
            expMsg = StringUtils.remove(expMsg, "\t");
            expMsg = StringUtils.remove(expMsg, "\r");
            long timestamp = Instant.now().getEpochSecond();
            ExpMsgDTO dto = new ExpMsgDTO();
            dto.setMethodName(methodName);
            dto.setRemark(remark);
            dto.setStartTime(startTime.format(formatter));
            dto.setEndTime(endTime.format(formatter));
            dto.setHostName(hostName);
            dto.setMsg(expMsg);
            dto.setTimestamp(String.valueOf(timestamp));
            dto.setSign(GenSign(secret, timestamp));
            String reqStr = getRequestJson(dto);
            HttpUtils.doPost(host, path, null, null, reqStr, getProxyConfig());
        }
    }

    private Map<String, String> getProxyConfig() {
        Map<String, String> proxyConfig = new HashMap<>();
        proxyConfig.put("proxyHost", proxyHost);
        proxyConfig.put("proxyPort", proxyPort);
        proxyConfig.put("proxyUserName", proxyUserName);
        proxyConfig.put("proxyPassword", proxyPassword);
        return proxyConfig;
    }

    public String getExceptionStr(Object[] args, Exception e) throws Exception {
        //读取异常栈信息
        ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
        e.printStackTrace(new PrintStream(arrayOutputStream));
        //通过ByteArray转换输入输出流
        BufferedReader fr = new BufferedReader(new InputStreamReader(new ByteArrayInputStream(arrayOutputStream.toByteArray())));
        String str;
        StringBuilder exceptionStr = new StringBuilder();
        StringBuilder exceptionParamsStr = new StringBuilder();
        try {
            exceptionStr.append("\n堆栈信息：\n");
            while ((str = fr.readLine()) != null) {
                exceptionStr.append(str);
            }
        } catch (Exception exception) {
            exception.printStackTrace();
        } finally {
            if (fr != null) {
                fr.close();
            }
        }
        long timestamp = Instant.now().getEpochSecond();
        if (!needSend(exceptionStr.toString(), timestamp)) {
            return null;
        }
        if(args != null && args.length > 0) {
            exceptionParamsStr.append("参数信息：\n");
            for (Object arg : args) {
                if(arg != null) {
                    exceptionParamsStr.append(JSON.toJSONString(arg, SerializerFeature.WriteDateUseDateFormat));
                }else{
                    exceptionParamsStr.append("空参数");
                }
            }
        }
        return JSON.toJSONString(exceptionParamsStr.append(exceptionStr), SerializerFeature.WriteDateUseDateFormat);
    }
    public String getExceptionStr(Object[] args, String msg) throws Exception {
        long timestamp = Instant.now().getEpochSecond();
        if (!needSend(msg, timestamp)) {
            return null;
        }
        //读取异常栈信息
        StringBuilder exceptionStr = new StringBuilder();
        if(args != null && args.length > 0) {
            exceptionStr.append("参数信息：\n");
            for (Object arg : args) {
                if(arg != null) {
                    exceptionStr.append(JSON.toJSONString(arg, SerializerFeature.WriteDateUseDateFormat));
                }else{
                    exceptionStr.append("空参数");
                }
            }
        }
        exceptionStr.append("\n异常信息：\n").append(msg);
        return JSON.toJSONString(exceptionStr, SerializerFeature.WriteDateUseDateFormat);
    }

    public String getRequestJson(ExpMsgDTO expMsgDTO) throws IOException, TemplateException {
        Map<String, Object> map = new HashMap<>();
        map.put("dto", expMsgDTO);
        Template template = configurer.getConfiguration().getTemplate("feishumsg.json");
        String resStr = FreeMarkerTemplateUtils.processTemplateIntoString(template, map);
        return resStr;
    }

    private static String GenSign(String secret, long timestamp) throws NoSuchAlgorithmException, InvalidKeyException {
        //把timestamp+"\n"+密钥当做签名字符串
        String stringToSign = timestamp + "\n" + secret;
        //使用HmacSHA256算法计算签名
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(stringToSign.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
        byte[] signData = mac.doFinal(new byte[]{});
        return new String(Base64.encodeBase64(signData));
    }

    private boolean needSend(String excptionStr, long timestamp) throws Exception {
        if (StringUtils.isBlank(excptionStr)) {
            return false;
        }
        cleanUpExpiredEntries(timestamp);
        MessageDigest sha256 = MessageDigest.getInstance("SHA-256");
        // 使用hash()方法计算字符串的哈希值
        byte[] hash = sha256.digest(excptionStr.getBytes("UTF-8"));

        // 将哈希值转换为十六进制字符串
        StringBuilder hexString = new StringBuilder();
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        String key = hexString.toString();
        if (errMsgMap.containsKey(key)) {
            //600秒 超过10分钟
            if (Math.abs(errMsgMap.get(key) - timestamp) > 300) {
                errMsgMap.put(key, timestamp);
                return true;
            }
            return false;
        } else {
            errMsgMap.put(key, timestamp);
            return true;
        }
    }

    // 清理超过十分钟的条目
    private void cleanUpExpiredEntries(long currentTime) {
        Iterator<Map.Entry<String, Long>> iterator = errMsgMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Long> entry = iterator.next();
            if (currentTime - entry.getValue() > 600) {
                // 如果条目的时间超过十分钟，移除
                iterator.remove();
            }
        }
    }

    /*
    public static void main(String[] args) throws Exception {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        long timestamp = Instant.now().getEpochSecond();
        ExpMsgDTO dto = new ExpMsgDTO();
        dto.setMethodName("methodName");
        dto.setRemark("test remark");
        dto.setStartTime(LocalDateTime.now().format(formatter));
        dto.setEndTime(LocalDateTime.now().format(formatter));
        dto.setHostName("127.0.0.1");
        dto.setMsg("测试消息");
        dto.setTimestamp(String.valueOf(timestamp));
        //dto.setSign(GenSign("17kn0DekgKctYGPlsB7wqg", timestamp));
        dto.setSign(GenSign("kaBZCa2G5coMT1mr2K2bX", timestamp));
        String reqStr = getMsg(dto);
        //HttpUtils.doPost("https://open.feishu.cn", "/open-apis/bot/v2/hook/808c2ec6-efb1-457a-94bb-427d08221345", null, null, reqStr, new HashMap<>());
        HttpUtils.doPost("https://open.feishu.cn", "/open-apis/bot/v2/hook/c6937e87-60b4-471b-824a-bf8ae675b069", null, null, reqStr, new HashMap<>());
        System.exit(0);
    }

    public static String getMsg(ExpMsgDTO dto) {
        return "{\n" +
                "  \"timestamp\": \""+dto.getTimestamp()+"\",\n" +
                "  \"sign\": \""+dto.getSign()+"\",\n" +
                "  \"msg_type\": \"interactive\",\n" +
                "  \"card\": {\n" +
                "    \"config\": {\n" +
                "      \"wide_screen_mode\": true\n" +
                "    },\n" +
                "    \"elements\": [{\n" +
                "      \"fields\": [{\n" +
                "        \"is_short\": true,\n" +
                "        \"text\": {\n" +
                "          \"content\": \"**开始时间**\\n"+dto.getStartTime()+"\",\n" +
                "          \"tag\": \"lark_md\"\n" +
                "        }\n" +
                "      },{\n" +
                "        \"is_short\": true,\n" +
                "        \"text\": {\n" +
                "          \"content\": \"**异常时间**\\n"+dto.getEndTime()+"\",\n" +
                "          \"tag\": \"lark_md\"\n" +
                "        }\n" +
                "      }, {\n" +
                "        \"is_short\": true,\n" +
                "        \"text\": {\n" +
                "          \"content\": \"**服务器**\\n"+dto.getHostName()+"\",\n" +
                "          \"tag\": \"lark_md\"\n" +
                "        }\n" +
                "      }, {\n" +
                "        \"is_short\": true,\n" +
                "        \"text\": {\n" +
                "          \"content\": \"**方法名**\\n"+dto.getMethodName()+"\",\n" +
                "          \"tag\": \"lark_md\"\n" +
                "        }\n" +
                "      }],\n" +
                "\n" +
                "      \"tag\": \"div\"\n" +
                "    }, {\n" +
                "      \"tag\": \"div\",\n" +
                "      \"text\": {\n" +
                "        \"content\": \"**异常信息**\\n\",\n" +
                "        \"tag\": \"lark_md\"\n" +
                "      }\n" +
                "    }, {\n" +
                "      \"tag\": \"div\",\n" +
                "      \"text\": {\n" +
                "        \"content\": \""+dto.getMsg()+"\",\n" +
                "        \"tag\": \"lark_md\"\n" +
                "      }\n" +
                "    }, {\n" +
                "      \"tag\": \"hr\"\n" +
                "    }, {\n" +
                "      \"elements\": [{\n" +
                "        \"content\": \"来自及时更新紧急通知\",\n" +
                "        \"tag\": \"lark_md\"\n" +
                "      }],\n" +
                "      \"tag\": \"note\"\n" +
                "    }],\n" +
                "    \"header\": {\n" +
                "      \"template\": \"red\",\n" +
                "      \"title\": {\n" +
                "        \"content\": \"【应急通知】及时更新发生异常\",\n" +
                "        \"tag\": \"plain_text\"\n" +
                "      }\n" +
                "    }\n" +
                "  }\n" +
                "}\n";
    }
    */
}

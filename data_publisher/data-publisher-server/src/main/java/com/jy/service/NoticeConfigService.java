package com.jy.service;

import com.jy.bean.po.NoticeConfig;

import java.util.List;

/**
 * @Author: caolt
 * @Description:
 * @Version:
 * @Date: Created in  2020/07/23
 */
public interface NoticeConfigService {

    List<NoticeConfig> listByClientCodes(String clientCodes);

    NoticeConfig save(NoticeConfig noticeConfig) throws Exception;

    void delete(String id) throws Exception;

    NoticeConfig update(NoticeConfig noticeConfig) throws Exception;

}

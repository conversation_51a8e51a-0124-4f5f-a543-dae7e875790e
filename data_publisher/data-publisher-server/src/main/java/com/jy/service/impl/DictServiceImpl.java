package com.jy.service.impl;

import com.jy.ann.MethodMonitor;
import com.jy.bean.po.Dict;
import com.jy.mapper.DictMapper;
import com.jy.service.DictService;
import com.jy.util.EmptyUtils;
import com.jy.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/5/11
 */
@Service
public class DictServiceImpl implements DictService {

    @Autowired
    private DictMapper dictMapper;

    @Override
    @MethodMonitor
    public List<Dict> listDict(Map<String, Object> map) {
        return dictMapper.listDict(map);
    }

    @Override
    @MethodMonitor
    public List<Dict> listByType(String type) {
        Map<String, Object> map = new HashMap<>();
        map.put("type", type);
        return dictMapper.listDict(map);
    }

    @Override
    public Dict getByTypeAndCode(String type, String code) {
        Map<String, Object> map = new HashMap<>();
        map.put("type", type);
        map.put("code", code);
        List<Dict> dict = dictMapper.listDict(map);
        if(EmptyUtils.isNotEmpty(dict)){
            return  dict.get(0);
        }
        return null;
    }

    @Override
    @MethodMonitor
    public Dict save(Dict dict) throws Exception {
        dict.setId(StringUtils.getUUID());
        dictMapper.save(dict);
        return dict;
    }

    @Override
    @MethodMonitor
    public void delete(String id) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("id", id);
        dictMapper.delete(map);
    }

    @Override
    @MethodMonitor
    public Dict update(Dict dict) throws Exception {
        dictMapper.update(dict);
        return dict;
    }

    @Override
    public String getMatchCode(String companyCode) {
        return dictMapper.getMatchCode(companyCode);
    }
}

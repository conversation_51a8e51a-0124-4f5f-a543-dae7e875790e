package com.jy.thread;

import org.apache.tomcat.util.threads.ThreadPoolExecutor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * <AUTHOR>
 * @date 2018/10/18
 */
@Configuration
public class ExecutorConfig {

    @Value("${thread.corePoolSize}")
    private int corePoolSize;
    @Value("${thread.maxPoolSize}")
    private int maxPoolSize;
    @Value("${thread.queueCapacity}")
    private int queueCapacity;
    @Value("${thread.keepAliveSeconds}")
    private int keepAliveSeconds;
    @Value("${thread.threadName}")
    private String threadName;
    /**
     * 默认线程池线程池
     *
     * @return Executor
     */
    @Bean(name = "asyncServiceExecutor")
    public ThreadPoolTaskExecutor asyncServiceExecutor() {
        ThreadPoolTaskExecutor executor = new VisiableThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize); //配置最大线程数
        executor.setMaxPoolSize(maxPoolSize); //配置队列大小
        executor.setQueueCapacity(queueCapacity); //配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix(threadName);
        // 对拒绝task的处理策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy()); //线程空闲后的最大存活时间
         executor.setKeepAliveSeconds(keepAliveSeconds); //加载
         executor.initialize();
        return executor;
     }
}
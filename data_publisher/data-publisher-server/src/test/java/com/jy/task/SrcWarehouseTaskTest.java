package com.jy.task;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = com.jy.DataPublisherApplication.class)
public class SrcWarehouseTaskTest {

    @TestConfiguration
    static class RabbitMQTestConfig {
        @Bean
        public CachingConnectionFactory connectionFactory() {
            CachingConnectionFactory factory = new CachingConnectionFactory();
            factory.setHost("**************"); // Set your RabbitMQ host
            factory.setPort(5672);        // Set your RabbitMQ port
            factory.setUsername("guest"); // Set your RabbitMQ username
            factory.setPassword("guest"); // Set your RabbitMQ password
            return factory;
        }

        @Bean
        public RabbitTemplate rabbitTemplate(CachingConnectionFactory connectionFactory) {
            return new RabbitTemplate(connectionFactory);
        }
    }

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Test
    public void testSendJsonToSrcWarehouse() {
        String jsonStr = "{\"batchNo\":\"EsPart_100_20250702_114240_2_1\"," +
                "\"operate\":\"insert\"," +
                "\"keys\":{\"tableId\":\"000000002\"}," +
                "\"mainBatchNo\":\"EsPart_100_20250702_114240_2\"," +
                "\"fields\":{\"oeId\":\"00000000OE2\"," +
                "\"oe\":\"testOe2\",\"stdPartCode\":\"999999\"," +
                "\"brandId\":\"002\",\"brandCode\":\"ADA1\"," +
                "\"stdPartName\":\"testName1\",\"searchOe\":\"testOe2\"," +
                "\"targetTableName\":\"partDetailBrand\"," +
                "\"seriesId\":\"4028d06d93d909e501955f5ec41f3d56\"," +
                "\"oeName\":\"testName2\"," +
                "\"tableName\":\"part_prod.part_detail_group\"}," +
                "\"tableName\":\"partDetailBrand\"}";

        // Send to SRC_WAREHOUSE queue
        rabbitTemplate.convertAndSend("SRC_WAREHOUSE", jsonStr);

        System.out.println("JSON message sent to SRC_WAREHOUSE queue successfully");
    }
}

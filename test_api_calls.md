# API 测试说明

## 改造后的接口调用方式

### 1. 统计数据接口
**接口**: `GET /flMainBatchInfo/dailyStats`
**用途**: 获取今日统计数据
**返回**: 
```json
{
  "status": "200",
  "result": {
    "totalCount": 100,
    "totalProcessing": 20,
    "publisherProcessing": 10,
    "transferProcessing": 8,
    "transferReceive": 2
  }
}
```

### 2. 批次列表接口
**接口**: `GET /flMainBatchInfo/daily`
**用途**: 获取分页的批次列表
**参数**: 
- page: 页码（从0开始）
- size: 每页大小
- mainBatchNo: 主批次号（可选）
- status: 状态（可选）
- clientCode: 客户端代码（可选）

**请求头**: `isPage: "1"`

**返回**: 
```json
{
  "status": "200",
  "result": [
    {
      "mainBatchNo": "BATCH001",
      "clientCode": "CLIENT001",
      "status": "p01",
      "startTime": "2023-12-07 09:00:00",
      "endTime": "2023-12-07 10:00:00"
    }
  ],
  "pageInfo": {
    "total": 100,
    "size": 10,
    "page": 0
  }
}
```

## 前端调用逻辑

### 页面初始化
1. 调用 `/flMainBatchInfo/dailyStats` 获取统计数据
2. 显示统计信息在页面顶部
3. 批次列表区域为空

### 用户点击查询
1. 调用 `/flMainBatchInfo/daily` 获取批次列表
2. 传递分页参数和查询条件
3. 显示分页的批次列表

## 测试步骤

1. 启动应用程序
2. 访问 dashBoard.html 页面
3. 验证页面加载时只显示统计数据
4. 点击查询按钮验证批次列表查询和分页功能

-- 数据库索引优化建议
-- 用于提升 /flMainBatchInfo/daily 接口查询性能

-- 1. fl_mainBatch_info 表索引优化
-- 主要查询字段：c_time, start_time, end_time, client_code, main_batch_no, status

-- 创建复合索引：时间范围查询优化
CREATE INDEX idx_fl_mainbatch_time_range ON fl_mainBatch_info (start_time, end_time, c_time);

-- 创建复合索引：客户端代码 + 时间
CREATE INDEX idx_fl_mainbatch_client_time ON fl_mainBatch_info (client_code, c_time);

-- 创建复合索引：状态 + 时间
CREATE INDEX idx_fl_mainbatch_status_time ON fl_mainBatch_info (status, c_time);

-- 创建复合索引：主批次号（精确查询）
CREATE INDEX idx_fl_mainbatch_no ON fl_mainBatch_info (main_batch_no);

-- 2. fl_batch_info 表索引优化
-- 主要查询字段：main_batch_no, data_source, status

-- 创建复合索引：主批次号 + 数据源
CREATE INDEX idx_fl_batch_main_datasource ON fl_batch_info (main_batch_no, data_source);

-- 创建复合索引：主批次号 + 数据源 + 状态（用于错误统计）
CREATE INDEX idx_fl_batch_main_datasource_status ON fl_batch_info (main_batch_no, data_source, status);

-- 3. 查看现有索引（检查是否已存在）
-- SHOW INDEX FROM fl_mainBatch_info;
-- SHOW INDEX FROM fl_batch_info;

-- 4. 分析查询执行计划
-- EXPLAIN SELECT ... FROM fl_mainBatch_info WHERE ...;

-- 5. 表统计信息更新（MySQL）
-- ANALYZE TABLE fl_mainBatch_info;
-- ANALYZE TABLE fl_batch_info;

-- 注意事项：
-- 1. 在生产环境执行前，请先在测试环境验证
-- 2. 创建索引可能需要较长时间，建议在业务低峰期执行
-- 3. 索引会占用额外存储空间，需要权衡查询性能和存储成本
-- 4. 过多索引可能影响写入性能，需要监控DML操作的性能变化

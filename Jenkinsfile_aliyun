pipeline {
  agent {
    node {
      label 'maven'
    }

  }
  stages {
    stage('clone code') {
      agent none
      steps {
        container('base') {
          git(url: 'http://**************/facade/data-publisher.git', credentialsId: 'gitlab', branch: 'aliyun', changelog: true, poll: false)
        }

      }
    }

    stage('build & push') {
      agent none
      steps {
        container('maven') {
          sh '''mvn clean package -Dmaven.test.skip=true -f client-server/pom.xml
'''
          withCredentials([usernamePassword(credentialsId : 'aliyun-registry' ,passwordVariable : 'DOCKER_PASSWORD' ,usernameVariable : 'DOCKER_USERNAME' ,)]) {
            sh 'echo "$DOCKER_PASSWORD" | docker login https://registry.cn-hongkong.aliyuncs.com -u "$DOCKER_USERNAME" --password-stdin'
            sh '''
              # version
              RELEASE_VERSION=1.0.0
              # build
              docker build -t registry.cn-hongkong.aliyuncs.com/facade-oversea/client-server:$RELEASE_VERSION ./client-server
              # push
              docker push registry.cn-hongkong.aliyuncs.com/facade-oversea/client-server:$RELEASE_VERSION
            '''
          }

        }

      }
    }
  }
  post {
    success {
        container('maven') {
          sh '''curl  --proxy **************:80 \'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=bcb8bde0-5d77-4fcd-bb5c-628ff1dfaf76\' \\
            -H \'Content-Type: application/json\' \\
            -d \'{
                    "msgtype": "markdown",
                    "markdown": {
                      "content": "<font color=\'info\'>生产环境：及时更新 client-server 海外项目，打包成功！</font>"
                    }
                  }\'
          '''
        }
    }
    failure {
        container('maven') {
          sh '''curl  --proxy **************:80 \'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=bcb8bde0-5d77-4fcd-bb5c-628ff1dfaf76\' \\
            -H \'Content-Type: application/json\' \\
            -d \'{
                    "msgtype": "markdown",
                    "markdown": {
                      "content": "<font color=\'warning\'>生产环境：及时更新 client-server 海外项目，打包成功！</font>"
                    }
                  }\'
          '''
        }
    }
    unstable {
        echo '只有当前Pipeline具有“不稳定”状态，通常由测试失败，代码违例等引起，才能运行。通常在具有黄色指示的Web UI中表示。'
    }
    aborted {
        echo '只有当前Pipeline处于“中止”状态时，才会运行，通常是由于Pipeline被手动中止。通常在具有灰色指示的Web UI中表示。'
    }
    always {
        echo 'always 总是会运行的'
    }
    changed {
        echo '和上次的执行状态不一样才会报警，用于失败告警和失败解决通知'
    }
  }
  
  environment {
    DOCKER_CREDENTIAL_ID = 'dockerhub-id'
    GITHUB_CREDENTIAL_ID = 'github-id'
    KUBECONFIG_CREDENTIAL_ID = 'demo-kubeconfig'
    REGISTRY = 'docker.io'
    DOCKERHUB_NAMESPACE = 'docker_username'
    GITHUB_ACCOUNT = 'kubesphere'
    APP_NAME = 'devops-java-sample'
  }
  parameters {
    string(name: 'TAG_NAME', defaultValue: '', description: '')
  }
}